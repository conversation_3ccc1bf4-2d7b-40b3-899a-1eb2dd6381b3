{"app/features/courses/scheduling/utils.py": {"ts": 1753711439227, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-65e15c36/ZUk3.py", "type": "history", "dt": "2025-07-28 22:03:59"}, "docs/重构总结/TIME_SLOTS_REFACTORING_SUMMARY.md": {"ts": 1753695149926, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_重构总结_TIME_SLOTS_REFACTORING_SUMMARY.md-1753695144912-336f26c6-43bb-4111-b323-ff30dd526a02.json", "type": "checkpoint", "dt": "2025-07-28 17:32:29"}, "app/features/teachers/time_slots_operations_service.py": {"ts": 1753694197703, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_teachers_time_slots_operations_service.py-1753690102192-0144fdfc-7b7e-4bb7-adbe-987877eaecb9.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "app/api/v1/api.py": {"ts": 1753755952992, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/534a5e49/kVh3.py", "type": "history", "dt": "2025-07-29 10:25:52"}, "app/features/members/fixed_lock_service.py": {"ts": 1753721274039, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-2c2f2dab/L0Hz.py", "type": "history", "dt": "2025-07-29 00:47:54"}, "app/features/teachers/time_slots_exceptions.py": {"ts": 1753698964272, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-29c0892b/aJ5C.py", "type": "history", "dt": "2025-07-28 18:36:04"}, "tests/unit/features/courses/test_fixed_schedule_algorithm.py": {"ts": 1753712175740, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-4240b378/zKQN.py", "type": "history", "dt": "2025-07-28 22:16:15"}, "app/api/v1/admin/teachers_fixed_slots.py": {"ts": 1753727422158, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-34d1b12/jMEP.py", "type": "history", "dt": "2025-07-29 02:30:22"}, "app/features/courses/operations/models.py": {"ts": 1753722931279, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/77218d81/OtLq.py", "type": "history", "dt": "2025-07-29 01:15:31"}, "app/api/v1/admin/teacher_time_slots.py": {"ts": 1753696890135, "src": "/Users/<USER>/Library/Application Support/Code/User/History/575b97aa/yP94.py", "type": "history", "dt": "2025-07-28 18:01:30"}, "app/api/v1/admin/members_fixed_locks.py": {"ts": 1753766013367, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-d4cc0f4/4HRj.py", "type": "history", "dt": "2025-07-29 13:13:33"}, "app/features/members/fixed_lock_schemas.py": {"ts": 1753765957241, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-1f03ee67/wf6v.py", "type": "history", "dt": "2025-07-29 13:12:37"}, "scripts/simple_acceptance_test.py": {"ts": 1753698679875, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_scripts_simple_acceptance_test.py-1753696102497-006dde91-7899-473a-8938-b16b1f6c18c8.json", "type": "checkpoint", "dt": "2025-07-28 18:31:19"}, "app/features/teachers/fixed_slots_models.py": {"ts": 1753727247666, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/63f5b6da/j952.py", "type": "history", "dt": "2025-07-29 02:27:27"}, "app/features/courses/scheduling/algorithm.py": {"ts": 1753711378567, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-3e53d2b4/V4Kz.py", "type": "history", "dt": "2025-07-28 22:02:58"}, "app/features/teachers/time_slots_operations_schemas.py": {"ts": 1753694197699, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_teachers_time_slots_operations_schemas.py-1753689675537-835141e9-2333-4390-9c51-2ebfa00737d6.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "docs/数据库设计/固定课排课索引优化方案.md": {"ts": 1753710053618, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-37b23eb4/Mf97.md", "type": "history", "dt": "2025-07-28 21:40:53"}, "app/api/v1/admin/teachers.py": {"ts": 1753696047078, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-40a08447/w9sA.py", "type": "history", "dt": "2025-07-28 17:47:27"}, "debug_model_dump.py": {"ts": 1753724943643, "src": "/Users/<USER>/Library/Application Support/Code/User/History/2b35ebfd/HZ61.py", "type": "history", "dt": "2025-07-29 01:49:03"}, "tests/unit/features/teachers/test_fixed_slots_optimized_queries.py": {"ts": 1753694197680, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_unit_features_teachers_test_fixed_slots_optimized_queries.py-1753683756246-322ec754-d40f-4277-ab42-6e85d332044d.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "tests/unit/features/courses/operations/test_operation_logger.py": {"ts": 1753719363345, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_unit_features_courses_operations_test_operation_logger.py-1753719358333-7a03a385-de12-414f-b9ce-739125cfda87.json", "type": "checkpoint", "dt": "2025-07-29 00:16:03"}, "app/features/teachers/time_slots_service.py": {"ts": 1753694197690, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_teachers_time_slots_service.py-1753688927467-7663eddf-bc17-46de-ad39-176a6f44dff1.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "app/api/v1/member/fixed_courses.py": {"ts": 1753694197682, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_api_v1_member_fixed_courses.py-1753683827579-09641eb9-6040-4581-839f-b4a6fee80485.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "app/features/teachers/time_slots_operations_models.py": {"ts": 1753694197695, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_teachers_time_slots_operations_models.py-1753689578164-8e30084f-bfe6-4858-bfd1-a999b7766729.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "app/features/teachers/fixed_slots_service.py": {"ts": 1753725833349, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/357fdeb5/hfhU.py", "type": "history", "dt": "2025-07-29 02:03:53"}, "app/features/teachers/fixed_slots_optimized_service.py": {"ts": 1753711885509, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/682a910d/zlq4.py", "type": "history", "dt": "2025-07-28 22:11:25"}, "app/api/v1/admin/members.py": {"ts": 1753696020264, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_api_v1_admin_members.py-1753696015252-6d32cc1e-338d-4f3e-b8e8-9edc3c745abd.json", "type": "checkpoint", "dt": "2025-07-28 17:47:00"}, "docs/操作日志记录功能使用指南.md": {"ts": 1753719466109, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_操作日志记录功能使用指南.md-1753719461103-26a4dbfb-b276-4875-a8db-1aa040ff1649.json", "type": "checkpoint", "dt": "2025-07-29 00:17:46"}, "tests/integration/api/v1/admin/test_teacher_time_slots.py": {"ts": 1753694594826, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_integration_api_v1_admin_test_teacher_time_slots.py-1753694589817-de89ef5e-857f-4ab0-bb8a-3f8e8a5db8fc.json", "type": "checkpoint", "dt": "2025-07-28 17:23:14"}, "tests/unit/features/teachers/test_teacher_fixed_slot_service.py": {"ts": 1753694893413, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_unit_features_teachers_test_teacher_fixed_slot_service.py-0-eda59429-7204-482e-83ca-038da4d06714.json", "type": "checkpoint", "dt": "2025-07-28 17:28:13"}, "tests/integration/api/v1/member/test_time_slots.py": {"ts": 1753694647323, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_integration_api_v1_member_test_time_slots.py-1753694642314-6849cab8-a888-496f-b26c-f5642254b915.json", "type": "checkpoint", "dt": "2025-07-28 17:24:07"}, "docs/数据库设计/索引设计最佳实践分析.md": {"ts": 1753710375561, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_数据库设计_索引设计最佳实践分析.md-1753710022366-b2ae682e-7df4-4ea3-affa-bc0a0a255837.json", "type": "checkpoint", "dt": "2025-07-28 21:46:15"}, "tests/unit/features/members/test_fixed_lock_redundancy_sync.py": {"ts": 1753694197667, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_unit_features_members_test_fixed_lock_redundancy_sync.py-1753683359198-a1b42acf-e920-4bfa-bcd8-c4779e903dea.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "app/models/__init__.py": {"ts": 1753695887743, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_models___init__.py-0-6a99b4ac-30e0-49fd-aad2-965073f20255.json", "type": "checkpoint", "dt": "2025-07-28 17:44:47"}, "app/features/courses/scheduling/optimized_algorithm.py": {"ts": 1753711387484, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-1308879c/Z4ql.py", "type": "history", "dt": "2025-07-28 22:03:07"}, "tests/integration/test_fixed_schedule_refactored.py": {"ts": 1753694197687, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_integration_test_fixed_schedule_refactored.py-1753684066230-bfddb2cc-9967-49e7-b5e9-2cc4eb4a72fc.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "tests/fixtures/business/teacher_fixed_slot.py": {"ts": 1753721727600, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-a639a8b/tijO.py", "type": "history", "dt": "2025-07-29 00:55:27"}, "app/features/courses/operations/operation_logger.py": {"ts": 1753719227457, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-4e6b5d3d/uWTk.py", "type": "history", "dt": "2025-07-29 00:13:47"}, "app/features/members/fixed_lock_models.py": {"ts": 1753722262416, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-3c1830c6/hJh0.py", "type": "history", "dt": "2025-07-29 01:04:22"}, "app/features/teachers/fixed_slots_schemas.py": {"ts": 1753694197671, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_teachers_fixed_slots_schemas.py-1753683615745-bc524406-342b-4e26-ae98-671a22651c58.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "migrations/add_redundant_fields_to_teacher_fixed_slots.sql": {"ts": 1753694197654, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_migrations_add_redundant_fields_to_teacher_fixed_slots.sql-1753682685532-814c2d12-87e2-4884-8bcf-fe06d5cd5ca8.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "app/features/teachers/time_slots_models.py": {"ts": 1753698781507, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-64e1ba51/GniX.py", "type": "history", "dt": "2025-07-28 18:33:01"}, "app/features/members/fixed_lock_exceptions.py": {"ts": 1753713531495, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/4c342e0/e5ic.py", "type": "history", "dt": "2025-07-28 22:38:51"}, "docs/操作日志优化方案.md": {"ts": 1753720847707, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_操作日志优化方案.md-1753720842695-06492a2d-e0a0-4098-aa19-ab0cd097f704.json", "type": "checkpoint", "dt": "2025-07-29 00:40:47"}, "tests/integration/test_tenant_isolation_simple.py": {"ts": 1753712141015, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/73730bdf/7aoO.py", "type": "history", "dt": "2025-07-28 22:15:41"}, "tests/fixtures/business/member_fixed_slot_lock.py": {"ts": 1753721829260, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-d369401/lOKO.py", "type": "history", "dt": "2025-07-29 00:57:09"}, "docs/数据库设计/操作日志表优化分析.md": {"ts": 1753717259056, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_数据库设计_操作日志表优化分析.md-1753717254041-58caef97-b5a1-4765-bf11-7b7aa4c020b7.json", "type": "checkpoint", "dt": "2025-07-28 23:40:59"}, "tests/unit/features/member/test_member_fixed_slot_lock_service.py": {"ts": 1753722601758, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/a6712f4/IeEw.py", "type": "history", "dt": "2025-07-29 01:10:01"}, "migrations/fixed_course_index_optimization.sql": {"ts": 1753706291232, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_migrations_fixed_course_index_optimization.sql-1753706286218-95b6c97f-6490-4005-87e7-deecae528543.json", "type": "checkpoint", "dt": "2025-07-28 20:38:11"}, "tests/integration/features/courses/operations/test_operation_logging_integration.py": {"ts": 1753719413294, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_integration_features_courses_operations_test_operation_logging_integration.py-1753719408281-dba9dc78-dd5c-444d-a737-3931ec462dc6.json", "type": "checkpoint", "dt": "2025-07-29 00:16:53"}, "app/features/members/fixed_lock_service_simplified.py": {"ts": 1753712734671, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-47e813fe/a7Dc.py", "type": "history", "dt": "2025-07-28 22:25:34"}, "tests/integration/api/v1/admin/test_member_fixed_locks.py": {"ts": 1753714214045, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/48ab64c4/GMB5.py", "type": "history", "dt": "2025-07-28 22:50:14"}, "app/features/teachers/time_slots_schemas.py": {"ts": 1753694197688, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_teachers_time_slots_schemas.py-1753688878317-68459e61-25e0-49ca-868b-f4cbf6323f2a.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "app/api/v1/member/time_slots.py": {"ts": 1753694307162, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_api_v1_member_time_slots.py-1753694302151-b4a348dc-c8d0-4b67-a22c-4aad68ba74e6.json", "type": "checkpoint", "dt": "2025-07-28 17:18:27"}, "tests/integration/api/v1/member/test_fixed_courses.py": {"ts": 1753694197686, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_integration_api_v1_member_test_fixed_courses.py-1753683999572-cf9c2dcc-cf2b-4d42-8f36-9c4f5500fab9.json", "type": "checkpoint", "dt": "2025-07-28 17:16:37"}, "migrations/add_member_info_to_fixed_locks.sql": {"ts": 1753717093917, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_migrations_add_member_info_to_fixed_locks.sql-1753717088904-fa1f4cd9-0744-4fd6-86ac-3ea035e54626.json", "type": "checkpoint", "dt": "2025-07-28 23:38:13"}, "app/features/teachers/fixed_slots_exceptions.py": {"ts": 1753694839923, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_teachers_fixed_slots_exceptions.py-0-c2b6000b-a49f-4985-8ff6-3aea5da71ba0.json", "type": "checkpoint", "dt": "2025-07-28 17:27:19"}, "tests/integration/api/v1/admin/test_teacher_fixed_slots.py": {"ts": 1753722697071, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/32c60865/VDeN.py", "type": "history", "dt": "2025-07-29 01:11:37"}, "docs/数据库设计/固定课排课索引优化实施指南.md": {"ts": 1753706472985, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_数据库设计_固定课排课索引优化实施指南.md-1753706467970-1ca36978-9d0a-4d59-81d5-088159a4e647.json", "type": "checkpoint", "dt": "2025-07-28 20:41:12"}, "tests/unit/features/teachers/test_time_slots_service.py": {"ts": 1753694536998, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_unit_features_teachers_test_time_slots_service.py-1753694531989-74e35973-be19-463d-a779-4e7640194c50.json", "type": "checkpoint", "dt": "2025-07-28 17:22:16"}, "tests/fixtures/business/time_slots.py": {"ts": 1753694708572, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_fixtures_business_time_slots.py-1753694703564-7a3fa4f8-621a-420d-a2e4-96d9c9ffaba8.json", "type": "checkpoint", "dt": "2025-07-28 17:25:08"}, "app/api/v1/admin/time_slot_operations.py": {"ts": 1753694372012, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_api_v1_admin_time_slot_operations.py-1753694367004-07e4f71b-08bb-4dc7-8521-bc609ba4ea5e.json", "type": "checkpoint", "dt": "2025-07-28 17:19:32"}, "tests/performance/test_fixed_course_index_optimization.py": {"ts": 1753706422365, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/c34e0a2f-2abf-4113-a8fc-373136dfbfac/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_performance_test_fixed_course_index_optimization.py-1753706417346-af9e92e4-00f2-4d0c-bfd1-42ca2940e725.json", "type": "checkpoint", "dt": "2025-07-28 20:40:22"}, "app/features/tags/service.py": {"ts": 1753598252909, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-1ba80248/3uiP.py", "type": "history", "dt": "2025-07-27 14:37:32"}, "app/api/v1/admin/tags.py": {"ts": 1753598303768, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-7db17b0f/UbiE.py", "type": "history", "dt": "2025-07-27 14:38:23"}, "tests/unit/features/tags/test_tag_service.py": {"ts": 1753597300649, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-551f9ac9/rJrz.py", "type": "history", "dt": "2025-07-27 14:21:40"}, "app/features/members/schemas.py": {"ts": 1753574886455, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_members_schemas.py-1753489666384-bd36523f-75c1-40d3-8794-428374f795f5.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "tests/integration/api/v1/admin/test_tags.py": {"ts": 1753580211872, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_integration_api_v1_admin_test_tags.py-1753580206864-9d2e269b-8d53-4bed-a4e1-4dfb3eaa5fcd.json", "type": "checkpoint", "dt": "2025-07-27 09:36:51"}, "docs/fix-member-cards-feature/demo_usage.py": {"ts": 1753574886489, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_fix-member-cards-feature_demo_usage.py-0-4f348093-3ad6-4c52-bf9b-711a06f0034f.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "MEMBER_CARDS_FEATURE.md": {"ts": 1753574886484, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_MEMBER_CARDS_FEATURE.md-1753489927532-85cd9760-1817-4bca-99f4-37f80a613d88.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "app/features/tags/models.py": {"ts": 1753574931651, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_tags_models.py-0-4e866e3b-4769-4a27-bb1d-de2e79b5ec7a.json", "type": "checkpoint", "dt": "2025-07-27 08:08:51"}, "docs/fix-member-cards-feature/verify_changes.py": {"ts": 1753574886488, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_fix-member-cards-feature_verify_changes.py-1753490472840-960ea929-a03e-4ffe-bd4e-674cdf5a32d6.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "app/features/members/service.py": {"ts": 1753574886471, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_members_service.py-1753489068438-f9e3ec2b-d992-43ef-8d34-e58e8866ddd5.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "verify_api_schema.py": {"ts": 1753574886486, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_verify_api_schema.py-1753489991188-237bff05-a80a-4998-87a7-418898375973.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "test_tag_teacher_count.py": {"ts": 1753598360503, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_test_tag_teacher_count.py-1753598355497-75f663e4-0f41-4e80-b653-20c438bfa2fa.json", "type": "checkpoint", "dt": "2025-07-27 14:39:20"}, "app/features/teachers/service.py": {"ts": 1753630978998, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/32379c6a-dc18-416f-a75c-91b1207d0de9/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_teachers_service.py-0-77572cc9-74b5-49d0-88ed-3f82dc3ffad6.json", "type": "checkpoint", "dt": "2025-07-27 23:42:58"}, "tests/integration/api/v1/admin/test_members.py": {"ts": 1753574886479, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_tests_integration_api_v1_admin_test_members.py-0-c7af1702-8dbe-428c-8b8d-fdebb8d76d80.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "app/features/tags/schemas.py": {"ts": 1753598211435, "src": "/Users/<USER>/Library/Application Support/Code/User/History/3077005b/zz7Z.py", "type": "history", "dt": "2025-07-27 14:36:51"}, "docs/fix-member-cards-feature/WORKFLOW_SUMMARY.md": {"ts": 1753574886490, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_fix-member-cards-feature_WORKFLOW_SUMMARY.md-1753490523575-fb6f3879-8033-4045-9140-fc8916fdaed3.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "test_category_optimization.py": {"ts": 1753596821347, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_test_category_optimization.py-1753596816333-76b371df-28e3-42e9-8b9e-274a9ca44c1e.json", "type": "checkpoint", "dt": "2025-07-27 14:13:41"}, "demo_member_cards.py": {"ts": 1753574886481, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_demo_member_cards.py-1753489449425-952d1232-f284-49a0-b5ae-e45233c799c6.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "migrations/add_category_name_to_tags.py": {"ts": 1753575352889, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_migrations_add_category_name_to_tags.py-1753575347882-b382bbc5-2665-4ff6-9181-770e52c068e8.json", "type": "checkpoint", "dt": "2025-07-27 08:15:52"}, "docs/changelog/fix-tag-category-name-redundancy/README.md": {"ts": 1753575504433, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_changelog_fix-tag-category-name-redundancy_README.md-1753575499426-64628584-e0b2-4511-95bb-c817df436b8f.json", "type": "checkpoint", "dt": "2025-07-27 08:18:24"}, "docs/API_CHANGE_WORKFLOW.md": {"ts": 1753574886487, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_API_CHANGE_WORKFLOW.md-1753491041962-250f86a6-4bb6-4bf1-8984-4f8af054566c.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "app/features/teachers/schemas.py": {"ts": 1753574886493, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_app_features_teachers_schemas.py-1753537368254-e1a629b6-6cbf-46c4-9b80-dcf517b5c2e4.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "docs/changelog/fix-teacher-api-params/README.md": {"ts": 1753574886499, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/d5cd8267-48ac-4444-b740-070c5a19ca5e/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_docs_changelog_fix-teacher-api-params_README.md-1753537517266-9df89070-88ad-4f9a-8a4a-674e78ac3126.json", "type": "checkpoint", "dt": "2025-07-27 08:08:06"}, "scripts/demo_data/main.py": {"ts": 1753718128965, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-1e575777/3yoB.py", "type": "history", "dt": "2025-07-28 23:55:28"}, "scripts/demo_data/creators.py": {"ts": 1753718247950, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/5a21783b/NsNn.py", "type": "history", "dt": "2025-07-28 23:57:27"}, "test_api_get_all_teachers.py": {"ts": 1753631128505, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-581bcacf/kBfh.py", "type": "history", "dt": "2025-07-27 23:45:28"}, "verify_teacher_tags.py": {"ts": 1753630871948, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/32379c6a-dc18-416f-a75c-91b1207d0de9/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_verify_teacher_tags.py-1753598985489-5c399e96-dc9c-4364-82ec-a3aac73230f1.json", "type": "checkpoint", "dt": "2025-07-27 23:41:11"}, "test_get_all_teachers_api.py": {"ts": 1753631155244, "src": "/Users/<USER>/Documents/vscode_augment_backup/Code/User/workspaceStorage/d18f02a116f718ff1058d28c674e570c/Augment.vscode-augment/augment-user-assets/checkpoint-documents/32379c6a-dc18-416f-a75c-91b1207d0de9/document-_Users_shin_Downloads_同步空间_折腾_Ai编程_Project_中后台管理系统调研_project_backend-feature-augment_test_get_all_teachers_api.py-1753631150241-d283c626-4e64-40fe-9daf-6c53885564f0.json", "type": "checkpoint", "dt": "2025-07-27 23:45:55"}, "app/api/common/docs.py": {"ts": 1753084665920, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-462f0c60/ZPUJ.py", "type": "history", "dt": "2025-07-21 15:57:45"}, "tests/fixtures/business/user.py": {"ts": 1752764231807, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-405c1311/HptA.py", "type": "history", "dt": "2025-07-17 22:57:11"}, "tasks/database-design/markdown/core-relationships-md.md": {"ts": 1752299263941, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/271c0aa9/CQ3s.md", "type": "history", "dt": "2025-07-12 13:47:43"}, "tests/e2e/scenarios/test_data_consistency.py": {"ts": 1753507709282, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-6f53ff03/H8s0.py", "type": "history", "dt": "2025-07-26 13:28:29"}, "tests/unit/features/courses/test_fixed_schedule_service.py": {"ts": 1752410424369, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/99f4f23/cach.py", "type": "history", "dt": "2025-07-13 20:40:24"}, "tests/integration/api/v1/admin/test_users.py": {"ts": 1753312880434, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/72fa80eb/fUIv.py", "type": "history", "dt": "2025-07-24 07:21:20"}, "tasks/creator/process-task-list.md": {"ts": 1752299279736, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-c41597a/zZnA.md", "type": "history", "dt": "2025-07-12 13:47:59"}, "app/features/users/models.py": {"ts": 1753310304618, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/7196147/cIy2.py", "type": "history", "dt": "2025-07-24 06:38:24"}, "docs/TODO.md": {"ts": 1752310900293, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-511c6370/P1iu.md", "type": "history", "dt": "2025-07-12 17:01:40"}, "scripts/create_demo_data.py": {"ts": 1753718165961, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/1099ec88/xoEy.py", "type": "history", "dt": "2025-07-28 23:56:05"}, "scripts/demo_data/config.py": {"ts": 1753718091954, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-4288c60/wOfR.py", "type": "history", "dt": "2025-07-28 23:54:51"}, "tests/performance/analysis/business_logic_performance_analysis.py": {"ts": 1753605791030, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-759452d2/3JQj.py", "type": "history", "dt": "2025-07-27 16:43:11"}, "tests/unit/features/member_cards/test_card_service.py": {"ts": 1753509866359, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/511ff05b/qrU0.py", "type": "history", "dt": "2025-07-26 14:04:26"}, "app/features/courses/scheduling/service.py": {"ts": 1752410555706, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/5f38f2e7/Q46H.py", "type": "history", "dt": "2025-07-13 20:42:35"}, "tests/fixtures/business/member_card.py": {"ts": 1753506864971, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-6ecdee41/UiwZ.py", "type": "history", "dt": "2025-07-26 13:14:24"}, "app/features/members/models.py": {"ts": 1753310223192, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/68d79ed8/wxhq.py", "type": "history", "dt": "2025-07-24 06:37:03"}, "requirements.txt": {"ts": 1752802990935, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/504871b7/kC4H.txt", "type": "history", "dt": "2025-07-18 09:43:10"}, "scripts/test.py": {"ts": 1752372749118, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/127f2899/YtCv.py", "type": "history", "dt": "2025-07-13 10:12:29"}, "tasks/database-design/markdown/module-scheduling-system-md.md": {"ts": 1752299269014, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-1739653d/WyyZ.md", "type": "history", "dt": "2025-07-12 13:47:49"}, "tests/performance/scheduling/test_algorithm_optimization.py": {"ts": 1752450529683, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/6c9dc176/Hq5E.py", "type": "history", "dt": "2025-07-14 07:48:49"}, "doc_guide_line(现有的项目文档指导)/数据库设计/future-database-tables-structure.md": {"ts": 1752300539369, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/72b32a10/JQKj.md", "type": "history", "dt": "2025-07-12 14:08:59"}, "app/features/base/query_utils.py": {"ts": 1752410704990, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/7d7908fc/Rcvd.py", "type": "history", "dt": "2025-07-13 20:45:04"}, "app/features/member_cards/models.py": {"ts": 1753715826124, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/7ec2569c/hwIr.py", "type": "history", "dt": "2025-07-28 23:17:06"}, "tasks/tasks-prd-ks-english-admin-backend-complete.md": {"ts": 1752296258876, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-bd3bb9d/Q8bk.md", "type": "history", "dt": "2025-07-12 12:57:38"}, "app/api/v1/admin/operation_logs.py": {"ts": 1752396744364, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-3c843d5c/Yqy4.py", "type": "history", "dt": "2025-07-13 16:52:24"}, "docs/前端联调.md": {"ts": 1753000990697, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-32af74b0/Kwbt.md", "type": "history", "dt": "2025-07-20 16:43:10"}, "docs/快速API开发指南.md": {"ts": 1753755647953, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-7655fc39/WACP.md", "type": "history", "dt": "2025-07-29 10:20:47"}, "docs/API_SALESMAN_LIST.md": {"ts": 1753000104417, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/db4316a/RFC2.md", "type": "history", "dt": "2025-07-20 16:28:24"}, "tests/unit/features/teachers/test_teacher_service.py": {"ts": 1753535604965, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/6a9f8b88/FoWs.py", "type": "history", "dt": "2025-07-26 21:13:24"}, "app/main.py": {"ts": 1752832182521, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/a199fab/QdIO.py", "type": "history", "dt": "2025-07-18 17:49:42"}, "tasks/development-tips/fastapi-route-order-guide.md": {"ts": 1752377592943, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-7d8f1d86/2YRm.md", "type": "history", "dt": "2025-07-13 11:33:12"}, "app/features/member_cards/card_service.py": {"ts": 1753497800678, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/5749a6ff/O60x.py", "type": "history", "dt": "2025-07-26 10:43:20"}, "app/api/v1/admin/users.py": {"ts": 1753312805164, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/3eb7965b/LKNA.py", "type": "history", "dt": "2025-07-24 07:20:05"}, "doc_guide_line(现有的项目文档指导)/数据库设计/future-database-design.md": {"ts": 1752300365152, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-39cf903/CV8R.md", "type": "history", "dt": "2025-07-12 14:06:05"}, "scripts/demo_data/CHANGELOG.md": {"ts": 1753718338100, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-83842fa/fv6z.md", "type": "history", "dt": "2025-07-28 23:58:58"}, "tasks/creator/create-tasks-simple.md": {"ts": 1752294626419, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/13dbe60c/gZxj.md", "type": "history", "dt": "2025-07-12 12:30:26"}, "tests/unit/features/courses/test_scheduled_class_service.py": {"ts": 1753713676127, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/37fdaf78/n07B.py", "type": "history", "dt": "2025-07-28 22:41:16"}, "docs/API集成文档.md": {"ts": 1753000278333, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/5ff8915b/CGG6.md", "type": "history", "dt": "2025-07-20 16:31:18"}, "docs/产品功能文档.md": {"ts": 1753310412450, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/74e78227/eH40.md", "type": "history", "dt": "2025-07-24 06:40:12"}, "tasks/database-design/eraser.io/module-scheduling-system.md": {"ts": 1752299253948, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-91f474e/SSmr.md", "type": "history", "dt": "2025-07-12 13:47:33"}, "tests/e2e/scenarios/test_direct_booking_flow.py": {"ts": 1752414966731, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/5cb025c4/UEk7.py", "type": "history", "dt": "2025-07-13 21:56:06"}, "app/features/courses/operations/service.py": {"ts": 1753716706613, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-78312312/rhXZ.py", "type": "history", "dt": "2025-07-28 23:31:46"}, "app/features/member_cards/schemas.py": {"ts": 1753509538300, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-23ff3cc9/hm3J.py", "type": "history", "dt": "2025-07-26 13:58:58"}, "docs/模块实现一致性分析报告.md": {"ts": 1752377448434, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/121a6f96/cDbn.md", "type": "history", "dt": "2025-07-13 11:30:48"}, "scripts/generate_frontend_types.py": {"ts": 1752802990961, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-133ed153/ibbm.py", "type": "history", "dt": "2025-07-18 09:43:10"}, "tasks/creator/generate-tasks-v2.md": {"ts": 1752296340717, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-2915c5/7EPe.md", "type": "history", "dt": "2025-07-12 12:59:00"}, "app/features/member_cards/consumption_service.py": {"ts": 1753500812047, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/480be8b8/KZ93.py", "type": "history", "dt": "2025-07-26 11:33:32"}, "README.md": {"ts": 1752802991225, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-35df1c2e/57Ow.md", "type": "history", "dt": "2025-07-18 09:43:11"}, "types/README.md": {"ts": 1752802991173, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-33532a84/MXO5.md", "type": "history", "dt": "2025-07-18 09:43:11"}, "scripts/demo_data/example_usage.py": {"ts": 1753718308964, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-dcf5520/MLDk.py", "type": "history", "dt": "2025-07-28 23:58:28"}, "tests/e2e/scenarios/test_fixed_schedule_flow.py": {"ts": 1752416876861, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/2cfc63c3/caRv.py", "type": "history", "dt": "2025-07-13 22:27:56"}, "app/core/config.py": {"ts": 1753083339635, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/64ba7fd8/GGwo.py", "type": "history", "dt": "2025-07-21 15:35:39"}, ".env": {"ts": 1753083380997, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/38cfe324/Clxa", "type": "history", "dt": "2025-07-21 15:36:20"}, "docs/todo/auth_optimization.md": {"ts": 1752794430580, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/32e7095b/LnLd.md", "type": "history", "dt": "2025-07-18 07:20:30"}, "docs/TODO/Tips.md": {"ts": 1753452980263, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-62fb5a8d/KGls.md", "type": "history", "dt": "2025-07-25 22:16:20"}, "tasks/prd-ks-english-admin-backend-complete.md": {"ts": 1752298778850, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-1e61da7c/umdP.md", "type": "history", "dt": "2025-07-12 13:39:38"}, "app/api/common/utils.py": {"ts": 1752802990935, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/78b977e2/ondo.py", "type": "history", "dt": "2025-07-18 09:43:10"}, "scripts/demo_data/README.md": {"ts": 1753718208457, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-6fbc7ebe/vYyE.md", "type": "history", "dt": "2025-07-28 23:56:48"}, "tests/integration/api/v1/public/test_auth.py": {"ts": 1752941966189, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-746849c9/6EoS.py", "type": "history", "dt": "2025-07-20 00:19:26"}, "tests/integration/api/v1/admin/test_member_cards.py": {"ts": 1753507769425, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-517ea443/6ikr.py", "type": "history", "dt": "2025-07-26 13:29:29"}, "scripts/test_demo_data.py": {"ts": 1753718184475, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-2f3bd64e/Lbgj.py", "type": "history", "dt": "2025-07-28 23:56:24"}, "tasks/database-design/markdown/module-member-management-md.md": {"ts": 1753310835503, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-51feeaef/9JW0.md", "type": "history", "dt": "2025-07-24 06:47:15"}, "scripts/demo_data/database.py": {"ts": 1752279582114, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-69e41ff9/A2Ib.py", "type": "history", "dt": "2025-07-12 08:19:42"}, "docs/前端对接指南.md": {"ts": 1752846108644, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/727b8e88/IN65.md", "type": "history", "dt": "2025-07-18 21:41:48"}, "app/api/v1/public/auth.py": {"ts": 1753025975781, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/f8f7255/TEQM.py", "type": "history", "dt": "2025-07-20 23:39:35"}, "docs/数据库设计/current-database-tables-structure.md": {"ts": 1753426531563, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-2bc25dfd/gnKS.md", "type": "history", "dt": "2025-07-25 14:55:31"}, "app/features/member_cards/__init__.py": {"ts": 1753367416518, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-47ee768b/6Z5a.py", "type": "history", "dt": "2025-07-24 22:30:16"}, "scripts/demo_data/utils.py": {"ts": 1753005163205, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/7f8e6b9b/2L9m.py", "type": "history", "dt": "2025-07-20 17:52:43"}, "scripts/demo_data/COURSE_DATA_GUIDE.md": {"ts": 1753718290401, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/7ede816f/R1wv.md", "type": "history", "dt": "2025-07-28 23:58:10"}, "app/api/v1/admin/courses.py": {"ts": 1753672117097, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/7209e7ab/yujw.py", "type": "history", "dt": "2025-07-28 11:08:37"}, "app/features/tenants/schemas.py": {"ts": 1752396744381, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-318040d6/VIsb.py", "type": "history", "dt": "2025-07-13 16:52:24"}, "docs/README_old.md": {"ts": 1752484976857, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-2d673a00/YWmH.md", "type": "history", "dt": "2025-07-14 17:22:56"}, "tests/unit/features/member_cards/test_recharge_consumption.py": {"ts": 1753510948027, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/246be3be/534p.py", "type": "history", "dt": "2025-07-26 14:22:28"}, "docs/前端API开发指南.md": {"ts": 1752802991173, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-472e135b/2Cgu.md", "type": "history", "dt": "2025-07-18 09:43:11"}, "app/api/v1/admin/fixed_schedule.py": {"ts": 1752396744381, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-2a0880b7/e9CP.py", "type": "history", "dt": "2025-07-13 16:52:24"}, "app/features/member_cards/template_service.py": {"ts": 1753367670049, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/27733855/BjGB.py", "type": "history", "dt": "2025-07-24 22:34:30"}, "tests/integration/api/v1/admin/test_teachers.py": {"ts": 1753605729095, "src": "/Users/<USER>/Library/Application Support/Code/User/History/714ec18b/w5Kz.py", "type": "history", "dt": "2025-07-27 16:42:09"}, "tasks/creator/create-tasks.md": {"ts": 1752296289286, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/6e48acbf/JOPS.md", "type": "history", "dt": "2025-07-12 12:58:09"}, "scripts/frontend_api_analyzer.py": {"ts": 1752802990961, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/438bf04b/NeIZ.py", "type": "history", "dt": "2025-07-18 09:43:10"}, "app/features/courses/operations/schemas.py": {"ts": 1753716666332, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-2c12206f/0M4F.py", "type": "history", "dt": "2025-07-28 23:31:06"}, "tasks/creator/create-tasks-detailed.md": {"ts": 1752296403786, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/69dd300e/qZfj.md", "type": "history", "dt": "2025-07-12 13:00:03"}, "app/features/users/service.py": {"ts": 1753310314646, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-92e7e18/CjE8.py", "type": "history", "dt": "2025-07-24 06:38:34"}, "tests/unit/features/member_cards/test_template_service.py": {"ts": 1753367677539, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/500d73b1/KFnA.py", "type": "history", "dt": "2025-07-24 22:34:37"}, "tests/performance/scheduling/test_fixed_schedule_performance.py": {"ts": 1752450425654, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-c0c780/xipS.py", "type": "history", "dt": "2025-07-14 07:47:05"}, "app/features/member_cards/recharge_service.py": {"ts": 1753510934155, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/5f925808/XoTQ.py", "type": "history", "dt": "2025-07-26 14:22:14"}, "app/api/v1/admin/member_cards.py": {"ts": 1753509619899, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/-70a9b7b3/3DUh.py", "type": "history", "dt": "2025-07-26 14:00:19"}, "tasks/development-tips/development-tips.md": {"ts": 1752396744412, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/74892fbc/meqn.md", "type": "history", "dt": "2025-07-13 16:52:24"}, "tests/integration/api/v1/admin/test_fixed_schedule.py": {"ts": 1752377865066, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-29548947/Ms6O.py", "type": "history", "dt": "2025-07-13 11:37:45"}, "docs/Api设计/API_DESIGN_GUIDE.md": {"ts": 1752846108061, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/57d5262e/ovlT.md", "type": "history", "dt": "2025-07-18 21:41:48"}, "docs/changelog/fix-member-cards-feature/README.md": {"ts": 1753500821411, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/1fe59543/dIcl.md", "type": "history", "dt": "2025-07-26 11:33:41"}, "app/features/member_cards/exceptions.py": {"ts": 1753497706201, "src": "/Users/<USER>/Library/Application Support/Cursor/User/History/7b003841/pasT.py", "type": "history", "dt": "2025-07-26 10:41:46"}, "docs/数据库设计/索引相关.md": {"ts": 1753710004504, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-53b73342/O5ct.md", "type": "history", "dt": "2025-07-28 21:40:04"}, "app/db/rls_v2.py": {"ts": 1753697475710, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-5c83f8ea/lpFC.py", "type": "history", "dt": "2025-07-28 18:11:15"}, "scripts/create_demo_data_v2.py": {"ts": 1752278124402, "src": "/Users/<USER>/Library/Application Support/Code/User/History/3c1ede4b/aP4D.py", "type": "history", "dt": "2025-07-12 07:55:24"}, "doc_guide_line(现有的项目文档指导)/Api设计/API_DESIGN_GUIDE.md": {"ts": 1752282398552, "src": "/Users/<USER>/Library/Application Support/Code/User/History/75088273/Fz7J.md", "type": "history", "dt": "2025-07-12 09:06:38"}, "app/features/courses/__init__.py": {"ts": 1752336079652, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-308c8bef/dXQR.py", "type": "history", "dt": "2025-07-13 00:01:19"}, "docs_old/teachers-module-guide.md": {"ts": 1752482407941, "src": "/Users/<USER>/Library/Application Support/Code/User/History/6ebd6675/B6sa.md", "type": "history", "dt": "2025-07-14 16:40:07"}, ".cursor/rules/task-progress-guideline.md": {"ts": 1752286842052, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-1bd55ceb/3OIM.md", "type": "history", "dt": "2025-07-12 10:20:42"}, "docs_old/README.md": {"ts": 1752482447295, "src": "/Users/<USER>/Library/Application Support/Code/User/History/158c9150/d7xT.md", "type": "history", "dt": "2025-07-14 16:40:47"}, "tasks/creator/generate-tasks.md": {"ts": 1752293655514, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-4715395a/lkuQ.md", "type": "history", "dt": "2025-07-12 12:14:15"}, "scripts/demo_acceptance_test.py": {"ts": 1752288453937, "src": "/Users/<USER>/Library/Application Support/Code/User/History/1175928d/N2z9.py", "type": "history", "dt": "2025-07-12 10:47:33"}, "docs/数据库设计/future-database-design.md": {"ts": 1752482425665, "src": "/Users/<USER>/Library/Application Support/Code/User/History/6fbab178/fvYx.md", "type": "history", "dt": "2025-07-14 16:40:25"}, "doc_guide_line(现有的项目文档指导)/README.md": {"ts": 1752282344832, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-dee49d3/S9pA.md", "type": "history", "dt": "2025-07-12 09:05:44"}, "tests/integration/test_fixed_schedule_comprehensive.py": {"ts": 1752418648537, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-25ae40bf/E85A.py", "type": "history", "dt": "2025-07-13 22:57:28"}, "conftest.py": {"ts": 1753697182529, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-51611303/h4Zp.py", "type": "history", "dt": "2025-07-28 18:06:22"}, "doc_guide_line(现有的项目文档指导)/数据库设计/database-design-issues-and-recommendations.md": {"ts": 1752282164020, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-43392cab/tVgV.md", "type": "history", "dt": "2025-07-12 09:02:44"}, "app/features/courses/scheduled_classes_schemas.py": {"ts": 1752414606331, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-4e3e15da/jz59.py", "type": "history", "dt": "2025-07-13 21:50:06"}, "tests/unit/features/courses/test_operation_service.py": {"ts": 1753715373526, "src": "/Users/<USER>/Library/Application Support/Code/User/History/2d2bf097/thU1.py", "type": "history", "dt": "2025-07-28 23:09:33"}, "tests/e2e/README.md": {"ts": 1752379012659, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-47cf6d13/Gf0S.md", "type": "history", "dt": "2025-07-13 11:56:52"}, ".cursor/rules/general.mdc": {"ts": 1753488432876, "src": "/Users/<USER>/Library/Application Support/Code/User/History/5852f0ff/crMj.mdc", "type": "history", "dt": "2025-07-26 08:07:12"}, ".cursor/rules/create-prd.md": {"ts": 1752293505845, "src": "/Users/<USER>/Library/Application Support/Code/User/History/11aeb2ec/K4im.md", "type": "history", "dt": "2025-07-12 12:11:45"}, "frontend/src/api/client.ts": {"ts": 1752795676301, "src": "/Users/<USER>/Library/Application Support/Code/User/History/55b0018a/DPLf.ts", "type": "history", "dt": "2025-07-18 07:41:16"}, "docs/README.md": {"ts": 1752484977074, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-62bbfa18/7QOB.md", "type": "history", "dt": "2025-07-14 17:22:57"}, "docs/数据库设计/future-database-tables-structure.md": {"ts": 1752482425665, "src": "/Users/<USER>/Library/Application Support/Code/User/History/4de036cb/9eEH.md", "type": "history", "dt": "2025-07-14 16:40:25"}, "docs_old/courses-module-guide.md": {"ts": 1752482407941, "src": "/Users/<USER>/Library/Application Support/Code/User/History/26f1fbae/rRVr.md", "type": "history", "dt": "2025-07-14 16:40:07"}, "docs/changlog/fix-member-cards-feature/WORKFLOW_SUMMARY.md": {"ts": 1753490790224, "src": "/Users/<USER>/Library/Application Support/Code/User/History/740ae7f1/YKY4.md", "type": "history", "dt": "2025-07-26 08:46:30"}, "tests/unit/features/statistics/test_statistics_service.py": {"ts": 1752340487024, "src": "/Users/<USER>/Library/Application Support/Code/User/History/780ce3a3/Lnoy.py", "type": "history", "dt": "2025-07-13 01:14:47"}, "app/features/teachers/models.py": {"ts": 1752411622443, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-35a36fd2/4eUE.py", "type": "history", "dt": "2025-07-13 21:00:22"}, "app/features/courses/scheduling/schemas.py": {"ts": 1752343257450, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-54a80a76/4Eml.py", "type": "history", "dt": "2025-07-13 02:00:57"}, "docs/changlog/fix-member-cards-feature/verify_changes.py": {"ts": 1753490727116, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-5a569074/y30B.py", "type": "history", "dt": "2025-07-26 08:45:27"}, "tests/performance/analysis/deep_bottleneck_analysis.py": {"ts": 1753605837706, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-71faa44e/qCvq.py", "type": "history", "dt": "2025-07-27 16:43:57"}, "tests/e2e/run_core_business_tests.py": {"ts": 1752379878041, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-3c9c402d/01RE.py", "type": "history", "dt": "2025-07-13 12:11:18"}, "tasks/tasks-prd-ks-english-admin-backend-phase2.md": {"ts": 1752482552800, "src": "/Users/<USER>/Library/Application Support/Code/User/History/694dcc85/ppZs.md", "type": "history", "dt": "2025-07-14 16:42:32"}, "tasks/creator/task-progress-guideline.md": {"ts": 1752482447303, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-5e694aca/dBOk.md", "type": "history", "dt": "2025-07-14 16:40:47"}, "docs/changlog/fix-member-cards-feature/demo_usage.py": {"ts": 1753490678579, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-2ce18bbc/TYO0.py", "type": "history", "dt": "2025-07-26 08:44:38"}, "docs/changelog/fix-member-cards-feature/verify_changes.py": {"ts": 1753491316524, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-34f91539/JTZM.py", "type": "history", "dt": "2025-07-26 08:55:16"}, "tasks/creator/task-progress-guideline_v2.md": {"ts": 1752482447297, "src": "/Users/<USER>/Library/Application Support/Code/User/History/-6aefff47/hEAc.md", "type": "history", "dt": "2025-07-14 16:40:47"}, "tests/fixtures/business/fixed_schedule_task.py": {"ts": 1752343190525, "src": "/Users/<USER>/Library/Application Support/Code/User/History/4fcdc692/lCFT.py", "type": "history", "dt": "2025-07-13 01:59:50"}, "docs/数据库设计/current-database-design.md": {"ts": 1752482425665, "src": "/Users/<USER>/Library/Application Support/Code/User/History/7b9242b0/xxYj.md", "type": "history", "dt": "2025-07-14 16:40:25"}}