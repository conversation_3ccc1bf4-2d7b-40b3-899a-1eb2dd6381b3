#!/usr/bin/env python3
"""
增强版测试调试脚本
支持输出重定向、日志过滤、结构化输出等功能
"""
import os
import sys
import argparse
import subprocess
import datetime
from pathlib import Path
from typing import List, Optional


def setup_test_env(enable_route_logging: bool = False):
    """设置测试环境"""
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "INFO"
    
    # 设置路由日志记录
    if enable_route_logging:
        os.environ["ENABLE_ROUTE_LOGGING"] = "true"
    else:
        os.environ["ENABLE_ROUTE_LOGGING"] = "false"


def create_log_directory():
    """创建日志目录"""
    log_dir = Path("logs/tests")
    log_dir.mkdir(parents=True, exist_ok=True)
    return log_dir


def get_timestamp():
    """获取时间戳"""
    return datetime.datetime.now().strftime("%Y%m%d_%H%M%S")


def build_pytest_command(
    test_path: str,
    verbose: bool = True,
    capture: str = "no",
    tb_style: str = "short",
    keep_test_data: bool = False,
    extra_args: Optional[List[str]] = None
) -> List[str]:
    """构建pytest命令"""
    cmd = ["python", "-m", "pytest"]
    
    if verbose:
        cmd.append("-v")
    
    # 控制输出捕获
    if capture == "no":
        cmd.append("-s")  # 不捕获输出
    elif capture == "sys":
        cmd.append("--capture=sys")
    
    # 设置traceback样式
    cmd.append(f"--tb={tb_style}")

    # 添加保留测试数据参数
    if keep_test_data:
        cmd.append("--keep-test-data")
    
    # 添加测试路径
    cmd.append(test_path)
    
    # 添加额外参数
    if extra_args:
        cmd.extend(extra_args)
    
    return cmd


def filter_logs(content: str, filter_db: bool = True, filter_warnings: bool = True) -> str:
    """过滤日志内容"""
    lines = content.split('\n')
    filtered_lines = []

    for line in lines:
        # 过滤数据库日志 - 更全面的过滤规则
        if filter_db:
            # 检查是否包含 SQLAlchemy 相关的日志
            if any(db_keyword in line for db_keyword in [
                "sqlalchemy.engine.Engine",
                "INFO     sqlalchemy.engine.Engine",
                "| sqlalchemy.engine.Engine |",
                "| INFO | base.py:",
                "| INFO | engine.py:",
                "SELECT pg_catalog",
                "FROM pg_catalog",
                "WHERE pg_catalog",
                "DROP POLICY",
                "CREATE POLICY",
                "ALTER TABLE",
                "BEGIN (implicit)",
                "COMMIT",
                "ROLLBACK",
                "show standard_conforming_strings",
                "select current_schema()",
                "select pg_catalog.version()",
                "[raw sql] {}",
                "[generated in",
                "[cached since"
            ]):
                continue

            # 过滤包含 SQL 语句的行
            if any(sql_pattern in line.upper() for sql_pattern in [
                "SELECT ", "INSERT ", "UPDATE ", "DELETE ", "CREATE ", "DROP ", "ALTER "
            ]) and ("sqlalchemy" in line.lower() or "INFO" in line):
                continue

        # 过滤警告
        if filter_warnings and any(warning_keyword in line for warning_keyword in [
            "PytestAssertRewriteWarning",
            "DeprecationWarning",
            "PydanticDeprecatedSince20",
            "warnings summary",
            "-- Docs: https://docs.pytest.org",
            "/site-packages/",
            "self.import_plugin(import_spec)",
            "from crypt import crypt as _crypt",
            "return fac()"
        ]):
            continue

        filtered_lines.append(line)

    return '\n'.join(filtered_lines)


def run_test_with_logging(
    test_path: str,
    base_log_file: Path,
    show_filtered: bool = True,
    verbose: bool = True,
    tb_style: str = "long",
    keep_test_data: bool = False,
    enable_route_logging: bool = False
) -> tuple[bool, str, Path, Path]:
    """运行测试并记录日志，生成完整和过滤后的两个日志文件"""

    # 生成两个日志文件路径
    base_name = base_log_file.stem
    log_dir = base_log_file.parent

    full_log_file = log_dir / f"{base_name}_full.log"
    filtered_log_file = log_dir / f"{base_name}_filtered.log"

    # 构建命令
    cmd = build_pytest_command(
        test_path=test_path,
        verbose=verbose,
        capture="no",  # 不捕获输出，这样可以看到完整信息
        tb_style=tb_style,
        keep_test_data=keep_test_data
    )

    print(f"🚀 运行测试: {' '.join(cmd)}")
    print(f"📝 完整日志: {full_log_file}")
    print(f"📝 过滤日志: {filtered_log_file}")
    # 显示保留测试数据的提示
    if keep_test_data:
        print("⚠️  警告: 使用了 --keep-test-data 参数，测试数据将被保留在数据库中")
        print("💡 提示: 测试完成后可以连接到测试数据库查看数据进行调试")
    
    # 显示路由日志记录的提示
    if enable_route_logging:
        print("📊 启用路由日志记录，将显示详细的路由匹配信息")

    print("=" * 60)

    try:
        # 运行测试并捕获输出
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=Path.cwd()
        )

        # 合并stdout和stderr
        full_output = result.stdout + result.stderr

        # 过滤输出
        filtered_output = filter_logs(full_output, filter_db=True, filter_warnings=True)

        # 写入完整日志文件
        with open(full_log_file, 'w', encoding='utf-8') as f:
            f.write(f"测试命令: {' '.join(cmd)}\n")
            f.write(f"执行时间: {datetime.datetime.now()}\n")
            f.write(f"返回码: {result.returncode}\n")
            f.write("=" * 60 + "\n")
            f.write(full_output)

        # 写入过滤后日志文件
        with open(filtered_log_file, 'w', encoding='utf-8') as f:
            f.write(f"测试命令: {' '.join(cmd)}\n")
            f.write(f"执行时间: {datetime.datetime.now()}\n")
            f.write(f"返回码: {result.returncode}\n")
            f.write("=" * 60 + "\n")
            f.write(filtered_output)

        # 显示输出（根据参数决定显示完整还是过滤后的）
        display_output = filtered_output if show_filtered else full_output
        print(display_output)

        success = result.returncode == 0
        return success, display_output, full_log_file, filtered_log_file

    except Exception as e:
        error_msg = f"运行测试时出错: {e}"
        print(error_msg)

        # 写入错误到两个日志文件
        for log_file in [full_log_file, filtered_log_file]:
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write(f"错误: {error_msg}\n")

        return False, error_msg, full_log_file, filtered_log_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强版测试调试脚本")
    parser.add_argument("test_path", help="测试路径 (文件或目录)")
    parser.add_argument("--no-filter", action="store_true", help="不过滤日志输出")
    parser.add_argument("--tb", choices=["short", "long", "line", "no"], default="long", help="Traceback样式")
    parser.add_argument("--quiet", action="store_true", help="静默模式")
    parser.add_argument("--log-name", help="自定义日志文件名")
    parser.add_argument("--keep-test-data", action="store_true", help="保留测试数据")
    parser.add_argument("--enable-route-logging", action="store_true", help="启用路由日志记录")

    args = parser.parse_args()
    
    # 设置环境
    setup_test_env(enable_route_logging=args.enable_route_logging)
    
    # 创建日志目录
    log_dir = create_log_directory()
    
    # 生成日志文件名
    if args.log_name:
        log_filename = f"{args.log_name}_{get_timestamp()}.log"
    else:
        # 从测试路径生成文件名
        test_name = Path(args.test_path).stem
        log_filename = f"test_{test_name}_{get_timestamp()}.log"
    
    log_file = log_dir / log_filename
    
    # 运行测试
    success, output, full_log_file, filtered_log_file = run_test_with_logging(
        test_path=args.test_path,
        base_log_file=log_file,
        show_filtered=not args.no_filter,  # --no-filter 时显示完整输出
        verbose=not args.quiet,
        tb_style=args.tb,
        keep_test_data=args.keep_test_data,
        enable_route_logging=args.enable_route_logging
    )

    print("\n" + "=" * 60)
    if success:
        print("✅ 测试通过！")
    else:
        print("❌ 测试失败！")

    print(f"📋 完整日志文件: {full_log_file}")
    print(f"📋 过滤日志文件: {filtered_log_file}")
    print("")
    print("🔍 快速查看命令:")
    print(f"  查看过滤日志: cat '{filtered_log_file}'")
    print(f"  查看完整日志: cat '{full_log_file}'")
    print(f"  查找错误信息: grep -n 'ERROR\\|FAILED\\|Exception' '{filtered_log_file}'")

    # 返回适当的退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
