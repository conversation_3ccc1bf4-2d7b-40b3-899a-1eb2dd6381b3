#!/usr/bin/env python3
"""
一键测试环境设置脚本
简化新开发者的测试环境配置
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ Python版本需要3.8+")
        return False
    print(f"✅ Python版本: {sys.version}")
    return True


def check_postgresql():
    """检查PostgreSQL连接"""
    try:
        result = subprocess.run(
            ["psql", "--version"], 
            capture_output=True, 
            text=True
        )
        if result.returncode == 0:
            print(f"✅ PostgreSQL: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ PostgreSQL未安装或不在PATH中")
    print("💡 请安装PostgreSQL: brew install postgresql (macOS)")
    return False


def check_database_connection():
    """检查数据库连接"""
    db_url = "postgresql://admin_alice:123qwe1985alice@localhost:5432/postgres"
    
    try:
        result = subprocess.run([
            "psql", db_url, "-c", "SELECT 1;"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 数据库连接正常")
            return True
        else:
            print(f"❌ 数据库连接失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 数据库连接检查失败: {e}")
        return False


def setup_environment():
    """设置环境变量"""
    env_vars = {
        "SECRET_KEY": "test-secret-key",
        "DEBUG": "True",
        "DATABASE_URL": "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db",
        "PYTHONWARNINGS": "ignore::DeprecationWarning:passlib.utils"
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    print("✅ 环境变量已设置")


def install_dependencies():
    """检查测试依赖"""
    try:
        # 检查关键依赖是否已安装
        import pytest
        import fastapi
        import sqlmodel
        print("✅ 关键依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少关键依赖: {e}")
        print("💡 请运行: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ 依赖检查异常: {e}")
        return False


def run_quick_test():
    """运行快速测试验证环境"""
    print("\n🧪 运行快速测试验证环境...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest",
            "tests/unit/features/tenant/test_tenant_service_new.py::TestTenantService::test_create_tenant",
            "-v", "--tb=short"
        ], capture_output=False)
        
        if result.returncode == 0:
            print("✅ 测试环境验证成功")
            return True
        else:
            print("❌ 测试环境验证失败")
            return False
    except Exception as e:
        print(f"❌ 测试运行异常: {e}")
        return False


def print_usage_guide():
    """打印使用指南"""
    print("\n" + "="*50)
    print("🎉 测试环境设置完成！")
    print("="*50)
    print("\n📋 常用测试命令:")
    print("  # 运行所有单元测试")
    print("  python scripts/test.py unit")
    print("\n  # 运行所有API测试")
    print("  python scripts/test.py api")
    print("\n  # 运行特定测试")
    print("  python -m pytest tests/unit/features/members/ -v")
    print("\n  # 调试模式（保留测试数据）")
    print("  python -m pytest tests/unit/features/members/ --keep-test-data")
    print("\n📖 详细指南: doc_guide_line/测试相关/QUICK_TEST_GUIDE.md")


def main():
    """主函数"""
    print("🚀 开始设置测试环境...")
    print("="*50)
    
    # 检查基础环境
    checks = [
        ("Python版本", check_python_version),
        ("PostgreSQL", check_postgresql),
        ("数据库连接", check_database_connection),
    ]
    
    for name, check_func in checks:
        print(f"\n🔍 检查{name}...")
        if not check_func():
            print(f"\n❌ {name}检查失败，请修复后重试")
            sys.exit(1)
    
    # 设置环境
    print(f"\n🔧 设置环境...")
    setup_environment()
    
    # 检查依赖
    print(f"\n📦 检查依赖...")
    if not install_dependencies():
        print("\n❌ 依赖检查失败，请安装所需依赖")
        sys.exit(1)
    
    # 验证环境
    if not run_quick_test():
        print("\n❌ 环境验证失败，请检查配置")
        sys.exit(1)
    
    # 打印使用指南
    print_usage_guide()


if __name__ == "__main__":
    main()
