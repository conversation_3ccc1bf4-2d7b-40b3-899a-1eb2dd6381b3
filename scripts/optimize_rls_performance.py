#!/usr/bin/env python3
"""
优化RLS策略性能脚本

修复问题：
- user_sessions和teacher_tags表使用复杂子查询的RLS策略
- 改为使用直接的tenant_id字段过滤，提升查询性能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlmodel import Session, text
from app.db.session import engine
from app.db.rls_v2 import setup_rls_policies_v2
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_current_rls_policies():
    """检查当前的RLS策略"""
    logger.info("🔍 检查当前RLS策略...")
    
    with Session(engine) as session:
        # 检查user_sessions表的RLS策略
        user_sessions_policies = session.exec(text("""
            SELECT policyname, qual, with_check
            FROM pg_policies 
            WHERE tablename = 'user_sessions'
        """)).all()
        
        logger.info(f"user_sessions表当前策略数量: {len(user_sessions_policies)}")
        for policy in user_sessions_policies:
            logger.info(f"  策略: {policy[0]}")
            logger.info(f"  USING: {policy[1]}")
            logger.info(f"  WITH CHECK: {policy[2]}")
        
        # 检查teacher_tags表的RLS策略
        teacher_tags_policies = session.exec(text("""
            SELECT policyname, qual, with_check
            FROM pg_policies 
            WHERE tablename = 'teacher_tags'
        """)).all()
        
        logger.info(f"teacher_tags表当前策略数量: {len(teacher_tags_policies)}")
        for policy in teacher_tags_policies:
            logger.info(f"  策略: {policy[0]}")
            logger.info(f"  USING: {policy[1]}")
            logger.info(f"  WITH CHECK: {policy[2]}")


def test_query_performance():
    """测试查询性能"""
    logger.info("🚀 测试查询性能...")
    
    with Session(engine) as session:
        # 设置租户上下文
        session.exec(text("SET app.current_tenant_id = '1'"))
        
        # 测试user_sessions查询
        logger.info("测试user_sessions查询...")
        session.exec(text("EXPLAIN ANALYZE SELECT * FROM user_sessions LIMIT 10")).all()
        
        # 测试teacher_tags查询
        logger.info("测试teacher_tags查询...")
        session.exec(text("EXPLAIN ANALYZE SELECT * FROM teacher_tags LIMIT 10")).all()


def apply_optimized_rls():
    """应用优化的RLS策略"""
    logger.info("🔧 应用优化的RLS策略...")
    
    try:
        # 使用新的RLS策略设置
        setup_rls_policies_v2()
        logger.info("✅ RLS策略优化完成")
        
        # 验证新策略
        check_current_rls_policies()
        
    except Exception as e:
        logger.error(f"❌ RLS策略优化失败: {e}")
        raise


def verify_rls_functionality():
    """验证RLS功能正常"""
    logger.info("🧪 验证RLS功能...")
    
    with Session(engine) as session:
        # 测试全局模式
        session.exec(text("RESET app.current_tenant_id"))
        global_count = session.exec(text("SELECT COUNT(*) FROM user_sessions")).first()
        logger.info(f"全局模式 - user_sessions记录数: {global_count}")
        
        # 测试租户模式
        session.exec(text("SET app.current_tenant_id = '1'"))
        tenant_count = session.exec(text("SELECT COUNT(*) FROM user_sessions")).first()
        logger.info(f"租户1模式 - user_sessions记录数: {tenant_count}")
        
        # 测试teacher_tags
        session.exec(text("RESET app.current_tenant_id"))
        global_tags_count = session.exec(text("SELECT COUNT(*) FROM teacher_tags")).first()
        logger.info(f"全局模式 - teacher_tags记录数: {global_tags_count}")
        
        session.exec(text("SET app.current_tenant_id = '1'"))
        tenant_tags_count = session.exec(text("SELECT COUNT(*) FROM teacher_tags")).first()
        logger.info(f"租户1模式 - teacher_tags记录数: {tenant_tags_count}")


def main():
    """主函数"""
    logger.info("🎯 开始RLS性能优化...")
    
    try:
        # 1. 检查当前策略
        check_current_rls_policies()
        
        # 2. 应用优化策略
        apply_optimized_rls()
        
        # 3. 验证功能
        verify_rls_functionality()
        
        # 4. 测试性能
        test_query_performance()
        
        logger.info("🎉 RLS性能优化完成！")
        
    except Exception as e:
        logger.error(f"❌ 优化过程失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
