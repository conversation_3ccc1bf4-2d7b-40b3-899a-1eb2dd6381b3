#!/usr/bin/env python3
"""
演示数据创建脚本

简洁高效的演示数据创建工具，支持多种场景和自定义配置。

使用示例:
    python scripts/create_demo_data.py minimal --force
    python scripts/create_demo_data.py basic --test-db my_demo_db
    python scripts/create_demo_data.py custom --tenants 2 --teachers 10 --members 20 --force
    python scripts/create_demo_data.py full --test-db stress_test_db --force
"""

import os
import sys
import argparse
import logging

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

# 添加 scripts 目录到路径以便导入 demo_data 模块
scripts_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(scripts_dir)

from demo_data import create_demo_data, SCENARIOS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def main():
    parser = argparse.ArgumentParser(
        description="演示数据创建工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s minimal --force                                    # 创建最小演示数据
  %(prog)s basic --test-db my_demo_db                         # 创建基础演示数据到指定数据库
  %(prog)s custom --tenants 2 --teachers 10 --members 20     # 创建自定义规模的演示数据
  %(prog)s custom --fixed-slots-per-teacher 5 --member-lock-ratio 0.4 --classes-per-teacher 8  # 创建更多课程数据
  %(prog)s full --test-db stress_test_db --force              # 创建完整演示数据（适合压力测试）
  %(prog)s --list-scenarios                                   # 列出所有可用场景

🎯 使用说明:
  1. 使用上述账号登录系统进行功能测试
  2. 系统已包含基础的会员卡模板和课程配置
  3. 可以在此基础上添加更多业务数据
        """
    )
    
    # 场景参数
    parser.add_argument(
        "scenario",
        nargs="?",
        choices=list(SCENARIOS.keys()) + ["custom"],
        help="演示数据场景"
    )
    
    # 数据库配置
    parser.add_argument(
        "--test-db",
        default="ks_english_test_demo",
        help="测试数据库名称 (默认: ks_english_test_demo)"
    )
    
    parser.add_argument(
        "--force",
        action="store_true",
        help="跳过确认提示，直接执行"
    )
    
    # 自定义场景参数
    parser.add_argument(
        "--tenants",
        type=int,
        help="租户数量 (仅用于 custom 场景)"
    )
    
    parser.add_argument(
        "--teachers",
        type=int,
        help="每个租户的教师数量 (仅用于 custom 场景)"
    )
    
    parser.add_argument(
        "--members",
        type=int,
        help="每个租户的会员数量 (仅用于 custom 场景)"
    )
    
    parser.add_argument(
        "--card-templates",
        type=int,
        help="每个租户的会员卡模板数量 (仅用于 custom 场景)"
    )
    
    parser.add_argument(
        "--tag-categories",
        type=int,
        help="每个租户的标签分类数量 (仅用于 custom 场景)"
    )
    
    parser.add_argument(
        "--tags-per-category",
        type=int,
        help="每个分类的标签数量 (仅用于 custom 场景)"
    )
    
    parser.add_argument(
        "--fixed-slots-per-teacher",
        type=int,
        help="每个教师的固定时间段数量 (仅用于 custom 场景)"
    )
    
    parser.add_argument(
        "--member-lock-ratio",
        type=float,
        help="会员锁定固定课位的比例 (0.0-1.0, 仅用于 custom 场景)"
    )
    
    parser.add_argument(
        "--classes-per-teacher",
        type=int,
        help="每个教师的已排课数量 (仅用于 custom 场景)"
    )
    
    # 工具选项
    parser.add_argument(
        "--list-scenarios",
        action="store_true",
        help="列出所有可用场景"
    )
    
    args = parser.parse_args()
    
    # 列出场景
    if args.list_scenarios:
        print("\n📋 可用的演示数据场景:")
        print("=" * 50)
        for name, config in SCENARIOS.items():
            print(f"🎯 {name:10} - {config['description']}")
            print(f"   租户: {config['tenants']}, 教师: {config['teachers_per_tenant']}, 会员: {config['members_per_tenant']}")
            print(f"   固定时段/教师: {config.get('fixed_slots_per_teacher', 3)}, 锁定比例: {config.get('member_lock_ratio', 0.3)}, 排课/教师: {config.get('classes_per_teacher', 5)}")
        print(f"🎯 {'custom':10} - 自定义演示数据")
        print("   可通过参数自定义各种数据量")
        print("=" * 50)
        return
    
    # 检查场景参数
    if not args.scenario:
        parser.error("请指定场景名称，或使用 --list-scenarios 查看可用场景")
    
    # 准备自定义配置
    custom_config = {}
    if args.scenario == "custom":
        if args.tenants is not None:
            custom_config["tenants"] = args.tenants
        if args.teachers is not None:
            custom_config["teachers"] = args.teachers
        if args.members is not None:
            custom_config["members"] = args.members
        if args.card_templates is not None:
            custom_config["card_templates"] = args.card_templates
        if args.tag_categories is not None:
            custom_config["tag_categories"] = args.tag_categories
        if args.tags_per_category is not None:
            custom_config["tags_per_category"] = args.tags_per_category
        if args.fixed_slots_per_teacher is not None:
            custom_config["fixed_slots_per_teacher"] = args.fixed_slots_per_teacher
        if args.member_lock_ratio is not None:
            custom_config["member_lock_ratio"] = args.member_lock_ratio
        if args.classes_per_teacher is not None:
            custom_config["classes_per_teacher"] = args.classes_per_teacher
    
    try:
        # 创建演示数据
        result = create_demo_data(
            scenario=args.scenario,
            test_db_name=args.test_db,
            force=args.force,
            **custom_config
        )
        
        if "error" in result:
            print(f"❌ 创建失败: {result['error']}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
