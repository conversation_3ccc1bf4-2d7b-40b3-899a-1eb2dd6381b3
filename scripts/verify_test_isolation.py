#!/usr/bin/env python3
"""
测试数据隔离验证脚本
验证测试之间的数据独立性和清理完整性
"""

import subprocess
import sys
import time
from pathlib import Path


def run_test_and_check_isolation():
    """运行测试并检查数据隔离"""
    print("🧪 验证测试数据隔离...")
    
    # 运行同一个测试两次，检查是否有数据依赖
    test_path = "tests/integration/api/v1/admin/test_members.py::TestAdminMemberAPI::test_create_member_success"
    
    print(f"📋 第一次运行测试...")
    result1 = subprocess.run([
        sys.executable, "-m", "pytest", test_path, "-v", "--tb=short"
    ], capture_output=True, text=True)
    
    print(f"📋 第二次运行测试...")
    result2 = subprocess.run([
        sys.executable, "-m", "pytest", test_path, "-v", "--tb=short"
    ], capture_output=True, text=True)
    
    if result1.returncode == 0 and result2.returncode == 0:
        print("✅ 测试数据隔离正常 - 两次运行都成功")
        return True
    else:
        print("❌ 测试数据隔离可能有问题")
        if result1.returncode != 0:
            print(f"第一次运行失败: {result1.stderr}")
        if result2.returncode != 0:
            print(f"第二次运行失败: {result2.stderr}")
        return False


def run_sequential_test_batch():
    """运行连续测试批次验证"""
    print("\n🔄 运行连续测试批次...")

    # 运行多个测试，检查是否有数据依赖问题
    tests = [
        "tests/integration/api/v1/admin/test_members.py::TestAdminMemberAPI::test_create_member_success",
        "tests/integration/api/v1/admin/test_members.py::TestAdminMemberAPI::test_get_members_success",
        "tests/integration/api/v1/admin/test_members.py::TestAdminMemberAPI::test_create_member_duplicate_phone"
    ]

    all_passed = True
    for i, test in enumerate(tests, 1):
        test_name = test.split('::')[-1]
        print(f"🚀 运行测试 {i}/{len(tests)}: {test_name}")

        result = subprocess.run([
            sys.executable, "-m", "pytest", test, "-v", "--tb=short"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✅ {test_name} - 成功")
        else:
            print(f"❌ {test_name} - 失败")
            print(f"   错误: {result.stderr}")
            all_passed = False

    if all_passed:
        print("✅ 连续测试批次成功 - 无数据依赖问题")
        return True
    else:
        print("❌ 连续测试批次发现问题")
        return False


def check_test_database_cleanup():
    """检查测试数据库清理"""
    print("\n🗑️ 检查测试数据库清理...")
    
    # 运行一个会创建数据的测试
    test_path = "tests/integration/api/v1/admin/test_members.py::TestAdminMemberAPI::test_create_member_success"
    
    print("📋 运行测试（正常模式）...")
    result = subprocess.run([
        sys.executable, "-m", "pytest", test_path, "-v", "--tb=short"
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ 测试运行成功")
        
        # 检查是否有残留数据的方法：
        # 1. 运行相同测试，如果有唯一约束冲突说明数据没清理
        # 2. 检查测试数据库是否被正确清理
        
        print("📋 验证数据清理...")
        result2 = subprocess.run([
            sys.executable, "-m", "pytest", test_path, "-v", "--tb=short"
        ], capture_output=True, text=True)
        
        if result2.returncode == 0:
            print("✅ 数据清理验证成功 - 无残留数据")
            return True
        else:
            print("❌ 可能存在数据残留问题")
            print(f"   错误: {result2.stderr}")
            return False
    else:
        print(f"❌ 测试运行失败: {result.stderr}")
        return False


def run_data_retention_test():
    """测试数据保留功能"""
    print("\n💾 测试数据保留功能...")
    
    test_path = "tests/integration/api/v1/admin/test_members.py::TestAdminMemberAPI::test_create_member_success"
    
    print("📋 运行测试（数据保留模式）...")
    result = subprocess.run([
        sys.executable, "-m", "pytest", test_path, 
        "--keep-test-data", "-v", "--tb=short"
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ 数据保留模式测试成功")
        print("💡 提示: 使用 --keep-test-data 可以保留测试数据用于调试")
        return True
    else:
        print(f"❌ 数据保留模式测试失败: {result.stderr}")
        return False


def print_isolation_report():
    """打印隔离验证报告"""
    print("\n" + "="*60)
    print("📊 测试数据隔离验证报告")
    print("="*60)
    print("\n✅ 验证项目:")
    print("  1. 测试数据独立性 - 同一测试多次运行")
    print("  2. 连续测试兼容性 - 多个测试顺序运行")
    print("  3. 数据清理完整性 - 测试后无残留数据")
    print("  4. 数据保留功能 - 调试模式数据保留")
    print("\n💡 建议:")
    print("  - 定期运行此脚本验证测试隔离性")
    print("  - 在CI/CD中集成此验证")
    print("  - 发现问题时使用 --keep-test-data 调试")


def main():
    """主函数"""
    print("🔍 开始验证测试数据隔离...")
    print("="*50)
    
    checks = [
        ("测试数据隔离", run_test_and_check_isolation),
        ("连续测试批次", run_sequential_test_batch),
        ("数据库清理", check_test_database_cleanup),
        ("数据保留功能", run_data_retention_test),
    ]
    
    all_passed = True
    for name, check_func in checks:
        print(f"\n🔍 验证{name}...")
        if not check_func():
            all_passed = False
    
    print_isolation_report()
    
    if all_passed:
        print("\n🎉 所有验证通过！测试隔离性良好")
        sys.exit(0)
    else:
        print("\n⚠️ 发现潜在问题，建议检查测试配置")
        sys.exit(1)


if __name__ == "__main__":
    main()
