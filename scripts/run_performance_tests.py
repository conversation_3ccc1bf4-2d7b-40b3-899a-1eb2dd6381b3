#!/usr/bin/env python3
"""
性能测试运行脚本

统一的性能测试执行脚本，支持不同类型的性能测试
"""

import os
import sys
import subprocess
import argparse
import time
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_command(cmd: list, timeout: int = 300) -> tuple:
    """运行命令并返回结果"""
    try:
        print(f"🚀 执行命令: {' '.join(cmd)}")
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=project_root
        )
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", f"命令执行超时 ({timeout}秒)"
    except Exception as e:
        return -1, "", str(e)


def run_api_performance_tests():
    """运行API性能测试"""
    print("\n" + "="*60)
    print("📊 API性能测试")
    print("="*60)
    
    cmd = [
        "python", "-m", "pytest",
        "tests/performance/api/",
        "-v", "-s",
        "--tb=short"
    ]
    
    return_code, stdout, stderr = run_command(cmd, timeout=600)
    
    if return_code == 0:
        print("✅ API性能测试完成")
    else:
        print("❌ API性能测试失败")
        if stderr:
            print(f"错误信息: {stderr}")
    
    return return_code == 0


def run_database_performance_tests():
    """运行数据库性能测试"""
    print("\n" + "="*60)
    print("🗄️ 数据库性能测试")
    print("="*60)
    
    cmd = [
        "python", "-m", "pytest",
        "tests/performance/database/",
        "-v", "-s",
        "--tb=short"
    ]
    
    return_code, stdout, stderr = run_command(cmd, timeout=600)
    
    if return_code == 0:
        print("✅ 数据库性能测试完成")
    else:
        print("❌ 数据库性能测试失败")
        if stderr:
            print(f"错误信息: {stderr}")
    
    return return_code == 0


def run_concurrent_performance_tests():
    """运行并发性能测试"""
    print("\n" + "="*60)
    print("🔄 并发性能测试")
    print("="*60)
    
    cmd = [
        "python", "-m", "pytest",
        "tests/performance/concurrent/",
        "-v", "-s",
        "--tb=short"
    ]
    
    return_code, stdout, stderr = run_command(cmd, timeout=600)
    
    if return_code == 0:
        print("✅ 并发性能测试完成")
    else:
        print("❌ 并发性能测试失败")
        if stderr:
            print(f"错误信息: {stderr}")
    
    return return_code == 0


def run_benchmark_tests():
    """运行性能基准测试"""
    print("\n" + "="*60)
    print("⚡ 性能基准测试")
    print("="*60)
    
    cmd = [
        "python", "-m", "pytest",
        "tests/performance/",
        "-m", "benchmark",
        "--benchmark-only",
        "--benchmark-sort=mean",
        "-v"
    ]
    
    return_code, stdout, stderr = run_command(cmd, timeout=600)
    
    if return_code == 0:
        print("✅ 性能基准测试完成")
    else:
        print("❌ 性能基准测试失败")
        if stderr:
            print(f"错误信息: {stderr}")
    
    return return_code == 0


def run_stress_tests():
    """运行压力测试"""
    print("\n" + "="*60)
    print("💪 压力测试")
    print("="*60)
    
    cmd = [
        "python", "-m", "pytest",
        "tests/performance/",
        "-m", "slow_performance",
        "-v", "-s",
        "--tb=short"
    ]
    
    return_code, stdout, stderr = run_command(cmd, timeout=1200)  # 20分钟超时
    
    if return_code == 0:
        print("✅ 压力测试完成")
    else:
        print("❌ 压力测试失败")
        if stderr:
            print(f"错误信息: {stderr}")
    
    return return_code == 0


def run_all_performance_tests():
    """运行所有性能测试"""
    print("\n" + "="*60)
    print("🎯 完整性能测试套件")
    print("="*60)
    
    start_time = time.time()
    
    results = {
        "API性能测试": run_api_performance_tests(),
        "数据库性能测试": run_database_performance_tests(),
        "并发性能测试": run_concurrent_performance_tests(),
        "性能基准测试": run_benchmark_tests(),
    }
    
    end_time = time.time()
    total_time = end_time - start_time
    
    # 生成测试报告
    print("\n" + "="*60)
    print("📋 性能测试总结报告")
    print("="*60)
    print(f"测试开始时间: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试结束时间: {datetime.fromtimestamp(end_time).strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总耗时: {total_time:.2f}秒 ({total_time/60:.1f}分钟)")
    print()
    
    passed_tests = 0
    total_tests = len(results)
    
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if passed:
            passed_tests += 1
    
    print()
    print(f"测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("🎉 所有性能测试通过！")
        return True
    else:
        print("⚠️ 部分性能测试失败，请检查上述错误信息")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="性能测试运行脚本")
    parser.add_argument(
        "test_type",
        choices=["all", "api", "database", "concurrent", "benchmark", "stress"],
        help="要运行的测试类型"
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=600,
        help="单个测试的超时时间（秒），默认600秒"
    )
    
    args = parser.parse_args()
    
    print("🧪 KS English Admin Backend - 性能测试套件")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 测试类型: {args.test_type}")
    print()
    
    # 检查环境
    if not os.path.exists("app"):
        print("❌ 错误: 请在项目根目录下运行此脚本")
        sys.exit(1)
    
    # 运行对应的测试
    success = False
    
    if args.test_type == "all":
        success = run_all_performance_tests()
    elif args.test_type == "api":
        success = run_api_performance_tests()
    elif args.test_type == "database":
        success = run_database_performance_tests()
    elif args.test_type == "concurrent":
        success = run_concurrent_performance_tests()
    elif args.test_type == "benchmark":
        success = run_benchmark_tests()
    elif args.test_type == "stress":
        success = run_stress_tests()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
