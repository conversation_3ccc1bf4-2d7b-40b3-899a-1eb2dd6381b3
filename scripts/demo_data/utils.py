"""
演示数据创建工具函数
"""

import logging
from typing import Dict, Any, Tuple, Type, TypeVar
from sqlmodel import Session, select, text

logger = logging.getLogger(__name__)

# 定义泛型类型变量
T = TypeVar('T')

# ==================== 工具函数 ====================

def set_tenant_context(session: Session, tenant_id: int = None):
    """设置租户上下文"""
    if tenant_id is None:
        session.exec(text("RESET app.current_tenant_id"))
        logger.debug("设置为全局上下文")
    else:
        session.exec(text(f"SET app.current_tenant_id = {tenant_id}"))
        logger.debug(f"设置租户上下文: {tenant_id}")

def get_or_create(session: Session, model_class: Type[T], defaults: Dict[str, Any], **kwargs) -> Tuple[T, bool]:
    """
    获取或创建对象，返回 (对象, 是否新创建)
    
    此函数实现了幂等性操作，确保即使多次运行也不会创建重复数据。
    工作原理：
    1. 首先尝试根据kwargs中的条件查询对象是否存在
    2. 如果存在，直接返回该对象
    3. 如果不存在，则创建新对象（使用kwargs和defaults合并的数据）
    4. 如果创建过程中发生唯一约束冲突，可能是并发操作，再次尝试查询
    
    Args:
        session: 数据库会话
        model_class: 模型类
        defaults: 创建对象时使用的默认值（仅当创建新对象时使用）
        **kwargs: 查询条件，用于确定对象是否存在
        
    Returns:
        Tuple[对象, 是否新创建]
    """
    model_name = model_class.__name__
    query_conditions = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
    
    try:
        # 构建查询条件
        conditions = [getattr(model_class, key) == value for key, value in kwargs.items()]
        statement = select(model_class).where(*conditions)
        existing = session.exec(statement).first()
        
        if existing:
            logger.debug(f"找到现有{model_name}对象: {query_conditions}")
            return existing, False
        
        # 创建新对象
        create_data = {**kwargs, **defaults}
        new_obj = model_class(**create_data)
        session.add(new_obj)
        session.flush()  # 只 flush，不 commit，让调用者控制事务
        logger.debug(f"创建新{model_name}对象: {query_conditions}")
        return new_obj, True
        
    except Exception as e:
        session.rollback()
        # 如果是唯一约束冲突，再次尝试查询（可能是并发创建）
        if "duplicate key" in str(e) or "unique constraint" in str(e):
            logger.warning(f"创建{model_name}时发生唯一约束冲突，尝试重新查询: {e}")
            conditions = [getattr(model_class, key) == value for key, value in kwargs.items()]
            statement = select(model_class).where(*conditions)
            existing = session.exec(statement).first()
            if existing:
                logger.debug(f"冲突后找到现有{model_name}对象: {query_conditions}")
                return existing, False
        logger.error(f"创建{model_name}失败: {e}")
        raise
