"""
数据库管理工具

提供安全的测试数据库创建、删除和配置功能
"""

import logging
from sqlmodel import create_engine, Session, text
from app.core.config import settings

logger = logging.getLogger(__name__)

# ==================== 数据库安全配置 ====================

def get_test_database_url(test_db_name: str = "ks_english_test_demo") -> str:
    """获取测试数据库URL"""
    # 解析原始数据库URL
    original_url = settings.DATABASE_URL
    # 替换数据库名为测试数据库名
    if "course_booking_db" in original_url:
        test_url = original_url.replace("course_booking_db", test_db_name)
    else:
        # 如果URL格式不同，手动构建
        parts = original_url.split("/")
        parts[-1] = test_db_name
        test_url = "/".join(parts)
    
    return test_url

def create_test_engine(test_db_name: str = "ks_english_test_demo"):
    """创建测试数据库引擎"""
    test_url = get_test_database_url(test_db_name)
    
    logger.info(f"🔧 创建测试数据库引擎")
    logger.info(f"   原始数据库: {settings.DATABASE_URL}")
    logger.info(f"   测试数据库: {test_url}")
    
    return create_engine(
        test_url,
        echo=False,  # 演示数据创建时不需要详细SQL日志
        pool_pre_ping=True,
        pool_size=5,
        max_overflow=10,
        connect_args={"options": "-c timezone=Asia/Shanghai"}
    )

def drop_test_database(test_db_name: str = "ks_english_test_demo"):
    """删除测试数据库（安全措施）"""
    import psycopg2
    from urllib.parse import urlparse
    
    # 解析数据库连接信息
    parsed = urlparse(settings.DATABASE_URL)
    
    # 连接到 postgres 数据库来删除测试数据库
    conn = psycopg2.connect(
        host=parsed.hostname,
        port=parsed.port,
        user=parsed.username,
        password=parsed.password,
        database="postgres"  # 连接到默认数据库
    )
    conn.autocommit = True
    
    try:
        with conn.cursor() as cursor:
            # 终止所有连接到测试数据库的会话
            cursor.execute(f"""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = '{test_db_name}' AND pid <> pg_backend_pid()
            """)
            
            # 删除数据库
            cursor.execute(f'DROP DATABASE IF EXISTS "{test_db_name}"')
            logger.info(f"🗑️  删除测试数据库: {test_db_name}")
            
    except Exception as e:
        logger.warning(f"⚠️  删除数据库时出现警告: {e}")
    finally:
        conn.close()

def create_test_database(test_db_name: str = "ks_english_test_demo"):
    """创建测试数据库"""
    import psycopg2
    from urllib.parse import urlparse
    
    # 解析数据库连接信息
    parsed = urlparse(settings.DATABASE_URL)
    
    # 连接到 postgres 数据库来创建测试数据库
    conn = psycopg2.connect(
        host=parsed.hostname,
        port=parsed.port,
        user=parsed.username,
        password=parsed.password,
        database="postgres"
    )
    conn.autocommit = True
    
    try:
        with conn.cursor() as cursor:
            cursor.execute(f'CREATE DATABASE "{test_db_name}"')
            logger.info(f"✅ 创建测试数据库: {test_db_name}")
    except psycopg2.errors.DuplicateDatabase:
        logger.info(f"✅ 测试数据库已存在: {test_db_name}")
    except Exception as e:
        logger.error(f"❌ 创建数据库失败: {e}")
        raise
    finally:
        conn.close()

def confirm_database_operation(test_db_name: str) -> bool:
    """确认数据库操作"""
    print("\n" + "="*60)
    print("🚨 数据库操作确认")
    print("="*60)
    print(f"即将操作的数据库: {test_db_name}")
    print(f"原始配置数据库: {settings.DATABASE_URL.split('/')[-1]}")
    print("\n⚠️  注意事项:")
    print("1. 这将删除并重新创建测试数据库")
    print("2. 测试数据库中的所有数据将被清除")
    print("3. 请确认这是您想要的操作")
    print("="*60)
    
    while True:
        response = input("是否继续？(yes/no): ").lower().strip()
        if response in ['yes', 'y']:
            return True
        elif response in ['no', 'n']:
            return False
        else:
            print("请输入 'yes' 或 'no'")

def setup_test_database(test_db_name: str = "ks_english_test_demo", force: bool = False):
    """设置测试数据库"""
    if not force and not confirm_database_operation(test_db_name):
        logger.info("❌ 用户取消操作")
        return None
    
    logger.info("🚀 开始设置测试数据库...")
    
    # 1. 删除现有测试数据库
    drop_test_database(test_db_name)
    
    # 2. 创建新的测试数据库
    create_test_database(test_db_name)
    
    # 3. 创建测试数据库引擎
    test_engine = create_test_engine(test_db_name)
    
    logger.info("✅ 测试数据库设置完成")
    return test_engine

def initialize_test_database(test_engine):
    """初始化测试数据库结构"""
    logger.info("📋 初始化测试数据库结构...")
    
    # 1. 创建表结构
    from sqlmodel import SQLModel
    # 导入所有模型以确保表被创建
    import app.models

    SQLModel.metadata.create_all(test_engine)
    logger.info("Database tables created")
    
    # 2. 设置行级安全（使用测试数据库）
    from app.db.rls_v2 import setup_rls_policies_v2
    
    # 临时替换引擎以便 RLS 设置使用测试数据库
    import app.db.session
    original_engine = app.db.session.engine
    app.db.session.engine = test_engine
    
    try:
        setup_rls_policies_v2()
        logger.info("Row Level Security setup completed using RLS v2")
    except Exception as e:
        logger.warning(f"RLS setup failed: {e}")
    finally:
        # 恢复原始引擎
        app.db.session.engine = original_engine
    
    # 3. 初始化全局数据（仅检查，不创建）
    session = Session(test_engine)
    session.exec(text("RESET app.current_tenant_id"))
    
    # 检查系统表（如果不存在会有警告，但不影响演示数据创建）
    try:
        session.exec(text("SELECT count(*) FROM system_configs")).first()
    except Exception as e:
        logger.warning(f"Could not check system configs: {e}")
    
    try:
        session.exec(text("SELECT * FROM users WHERE username = 'kenshin'")).first()
    except Exception as e:
        logger.warning(f"Could not check super admin: {e}")
    
    session.commit()
    session.close()
    logger.info("Global data initialization completed")
