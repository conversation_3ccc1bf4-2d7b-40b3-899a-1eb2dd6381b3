"""
演示数据场景配置
"""

# ==================== 场景配置 ====================

SCENARIOS = {
    "basic": {
        "description": "基础演示数据 - 适合功能演示",
        "tenants": 1,
        "users_per_tenant": 2,
        "agents_per_tenant": 5,
        "teachers_per_tenant": 3,
        "members_per_tenant": 5,
        "card_templates_per_tenant": 3,
        "tag_categories_per_tenant": 2,
        "tags_per_category": 3,
        "fixed_slots_per_teacher": 3,
        "member_lock_ratio": 0.3,
        "classes_per_teacher": 5,
    },
    "full": {
        "description": "完整演示数据 - 适合压力测试",
        "tenants": 3,
        "users_per_tenant": 2,
        "agents_per_tenant": 2,
        "teachers_per_tenant": 10,
        "members_per_tenant": 20,
        "card_templates_per_tenant": 5,
        "tag_categories_per_tenant": 3,
        "tags_per_category": 5,
        "fixed_slots_per_teacher": 5,
        "member_lock_ratio": 0.4,
        "classes_per_teacher": 8,
    },
    "minimal": {
        "description": "最小演示数据 - 仅核心功能",
        "tenants": 1,
        "users_per_tenant": 1,
        "agents_per_tenant": 1,
        "teachers_per_tenant": 1,
        "members_per_tenant": 2,
        "card_templates_per_tenant": 1,
        "tag_categories_per_tenant": 1,
        "tags_per_category": 2,
        "fixed_slots_per_teacher": 2,
        "member_lock_ratio": 0.2,
        "classes_per_teacher": 3,
    }
}
