# 演示数据创建工具

## 概述

本工具用于创建 KS English Admin Backend 系统的演示数据，包括租户、用户、教师、会员、课程等完整的业务数据。

## 新增功能

### 1. 教师固定时段数据

- 为每个教师创建固定时间段
- 支持设置可用性和可见性
- 包含未被会员预定的时间段

### 2. 会员固定课位锁定数据

- 会员锁定教师的固定时间段
- 支持自定义锁定比例
- 包含已被会员预定的时间段

### 3. 教师已排课数据

- 创建具体的课程安排
- 包含未被会员预定的课程（available 状态）
- 包含已被会员预定的课程（booked 状态）
- 支持直接约课和固定约课两种类型

## 使用方法

### 基本使用

```bash
# 创建基础演示数据
python scripts/create_demo_data.py basic --force

# 创建完整演示数据
python scripts/create_demo_data.py full --force

# 创建最小演示数据
python scripts/create_demo_data.py minimal --force
```

### 自定义参数

```bash
# 自定义数据量
python scripts/create_demo_data.py custom \
  --tenants 2 \
  --teachers 10 \
  --members 20 \
  --fixed-slots-per-teacher 5 \
  --member-lock-ratio 0.4 \
  --classes-per-teacher 8 \
  --force
```

### 参数说明

| 参数                        | 说明                             | 默认值 |
| --------------------------- | -------------------------------- | ------ |
| `--fixed-slots-per-teacher` | 每个教师的固定时间段数量         | 3      |
| `--member-lock-ratio`       | 会员锁定固定课位的比例 (0.0-1.0) | 0.3    |
| `--classes-per-teacher`     | 每个教师的已排课数量             | 5      |

## 场景配置

### basic 场景

- 适合功能演示
- 包含基础的课程数据
- 数据量适中，启动快速

### full 场景

- 适合压力测试
- 包含大量课程数据
- 数据量较大，测试全面

### minimal 场景

- 仅核心功能
- 最小数据量
- 快速验证基本功能

## 数据统计

创建完成后会显示详细的数据统计信息：

```
📊 演示数据创建统计
============================================================
  tenants: 1
  admins: 1
  users: 2
  agents: 5
  teachers: 3
  members: 5
  member_cards: 5
  card_templates: 3
  tag_categories: 2
  tags: 6
  course_configs: 1
  teacher_fixed_slots: 9
  member_fixed_locks: 3
  scheduled_classes: 15
============================================================
```

## 测试功能

运行测试脚本验证功能：

```bash
python scripts/test_demo_data.py
```

## 数据结构

### 教师固定时段 (teacher_fixed_slots)

- 教师 ID、星期几、开始时间
- 可用性状态、可见性状态
- 支持 25 分钟标准课时

### 会员固定课位锁定 (member_fixed_slot_locks)

- 会员 ID、教师固定时段 ID
- 锁定时间、状态信息
- 确保时间段唯一性

### 已排课表 (scheduled_classes)

- 教师 ID、会员 ID（可选）
- 具体上课时间、课程类型
- 预约状态、会员卡信息
- 教材信息、备注信息

## 注意事项

1. **时间冲突检测**: 系统会自动检测并避免时间冲突
2. **数据一致性**: 确保关联数据的完整性和一致性
3. **租户隔离**: 所有数据都遵循多租户隔离原则
4. **状态管理**: 正确设置各种业务状态

## 故障排除

### 常见问题

1. **数据库连接失败**

   - 检查 PostgreSQL 服务是否运行
   - 确认数据库连接配置正确

2. **权限错误**

   - 确保数据库用户有创建数据库的权限
   - 检查 RLS 策略配置

3. **数据创建失败**
   - 查看详细错误日志
   - 检查业务规则约束

### 调试模式

启用详细日志输出：

```bash
export PYTHONPATH=/path/to/project
python -u scripts/create_demo_data.py basic --force
```

## 扩展开发

### 添加新的数据类型

1. 在 `creators.py` 中添加创建函数
2. 在 `config.py` 中添加配置参数
3. 在 `main.py` 中集成到主流程
4. 更新统计信息和结果记录

### 自定义业务规则

修改 `creators.py` 中的创建函数，调整：

- 数据生成逻辑
- 业务规则验证
- 关联关系处理
- 状态设置逻辑
