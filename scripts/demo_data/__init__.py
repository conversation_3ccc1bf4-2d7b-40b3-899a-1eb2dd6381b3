"""
演示数据创建模块

提供简洁高效的演示数据创建功能，支持多种场景：
- minimal: 最小演示数据
- basic: 基础演示数据  
- full: 完整演示数据
- custom: 自定义演示数据

特点：
- 安全的测试数据库隔离
- 配置驱动的多场景支持
- 正确的依赖关系处理
- 幂等性操作
"""

from .main import create_demo_data, create_basic_demo, create_full_demo, create_minimal_demo, create_custom_demo
from .config import SCENARIOS

__all__ = [
    "create_demo_data",
    "create_basic_demo", 
    "create_full_demo",
    "create_minimal_demo",
    "create_custom_demo",
    "SCENARIOS"
]
