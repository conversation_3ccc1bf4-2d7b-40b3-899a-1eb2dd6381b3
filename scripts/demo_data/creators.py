"""
演示数据创建器

包含各种业务实体的创建函数
"""

import logging
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime, timedelta, time
from sqlmodel import Session, select, and_

from .utils import set_tenant_context, get_or_create

logger = logging.getLogger(__name__)

# ==================== 核心创建函数 ====================

def create_tenant(session: Session, name: str, code: str) -> Tuple[Any, bool]:
    """创建或获取租户"""
    from app.features.tenants.models import Tenant, TenantStatus, PlanType, BillingCycle
    
    set_tenant_context(session, None)  # 全局上下文
    
    defaults = {
        "name": name,
        "display_name": name,
        "description": f"{name} - 演示数据",
        "status": TenantStatus.ACTIVE,
        "plan_type": PlanType.STANDARD,
        "billing_cycle": BillingCycle.MONTHLY,
        "max_teachers": 50,
        "max_members": 1000,
        "max_storage_gb": 10,
        "price_per_class": 100,
        "monthly_fee": 299,
        "setup_fee": 0,
        "trial_expires_at": datetime.now() + timedelta(days=30),
        "subscription_expires_at": datetime.now() + timedelta(days=365),
        "contact_name": "演示联系人",
        "contact_phone": "13800138000",
        "contact_email": f"demo@{code}.com",
        "created_by": 1,
    }
    
    tenant, created = get_or_create(session, Tenant, defaults, code=code)
    
    if created:
        logger.info(f"✅ 创建新租户: {tenant.name} (ID: {tenant.id})")
    else:
        logger.info(f"✅ 找到现有租户: {tenant.name} (ID: {tenant.id})")
    
    return tenant, created

def create_admin_user(session: Session, tenant_id: int, username: str = "demo_admin", password: str = "demo123456") -> Tuple[Any, bool]:
    """创建或获取管理员用户"""
    from app.features.users.models import User, UserRole, UserStatus, Gender
    from app.utils.security import get_password_hash
    import random
    
    set_tenant_context(session, tenant_id)
    
    # 确保email唯一 - 基于用户名
    email = f"{username}@demo.com"
    
    # 确保phone唯一 - 使用随机生成的11位手机号
    phone = f"13800{''.join(random.choice('0123456789') for _ in range(6))}"
    
    defaults = {
        "tenant_id": tenant_id,
        "email": email,
        "phone": phone,
        "password_hash": get_password_hash(password),
        "real_name": "演示管理员",
        "role": UserRole.ADMIN,
        "status": UserStatus.ACTIVE,
        "gender": Gender.OTHER,
        "created_by": 1,
    }
    
    user, created = get_or_create(session, User, defaults, username=username)
    
    if created:
        logger.info(f"✅ 创建新管理员: {user.username} (电话: {user.phone}, 邮箱: {user.email})")
    else:
        logger.info(f"✅ 找到现有管理员: {user.username}")
    
    return user, created

def create_regular_user(session: Session, tenant_id: int, username: str, password: str = "demo123456") -> Tuple[Any, bool]:
    """创建或获取普通用户"""
    from app.features.users.models import User, UserRole, UserStatus, Gender
    from app.utils.security import get_password_hash
    import random
    
    set_tenant_context(session, tenant_id)
    
    # 确保email唯一 - 基于用户名
    email = f"{username}@demo.com"
    
    # 确保phone唯一 - 使用随机生成的11位手机号
    phone = f"13801{''.join(random.choice('0123456789') for _ in range(6))}"
    
    defaults = {
        "tenant_id": tenant_id,
        "email": email,
        "phone": phone,
        "password_hash": get_password_hash(password),
        "real_name": f"演示用户{username.split('_')[-1]}",
        "role": UserRole.ADMIN,
        "status": UserStatus.ACTIVE,
        "gender": Gender.OTHER,
        "created_by": 1,
    }
    
    user, created = get_or_create(session, User, defaults, username=username)
    
    if created:
        logger.info(f"✅ 创建普通用户: {user.username} (电话: {user.phone}, 邮箱: {user.email})")
    else:
        logger.info(f"✅ 找到现有普通用户: {user.username}")
    
    return user, created

def create_agent_user(session: Session, tenant_id: int, username: str, password: str = "demo123456") -> Tuple[Any, bool]:
    """创建或获取代理用户"""
    from app.features.users.models import User, UserRole, UserStatus, Gender
    from app.utils.security import get_password_hash
    import random
    
    set_tenant_context(session, tenant_id)
    
    # 确保email唯一 - 基于用户名
    email = f"{username}@demo.com"
    
    # 确保phone唯一 - 使用随机生成的11位手机号
    phone = f"13900{''.join(random.choice('0123456789') for _ in range(6))}"
    
    defaults = {
        "tenant_id": tenant_id,
        "email": email,
        "phone": phone,
        "password_hash": get_password_hash(password),
        "real_name": f"演示代理{username.split('_')[-1]}",
        "role": UserRole.AGENT,
        "status": UserStatus.ACTIVE,
        "gender": Gender.OTHER,
        "created_by": 1,
    }
    
    user, created = get_or_create(session, User, defaults, username=username)
    
    if created:
        logger.info(f"✅ 创建代理用户: {user.username} (电话: {user.phone}, 邮箱: {user.email})")
    else:
        logger.info(f"✅ 找到现有代理用户: {user.username}")
    
    return user, created

def create_card_templates(session: Session, tenant_id: int, count: int = 3) -> List[Any]:
    """创建会员卡模板"""
    from app.features.member_cards.models import MemberCardTemplate, CardType
    
    set_tenant_context(session, tenant_id)
    
    templates_config = [
        {
            "name": "体验卡",
            "card_type": CardType.VALUE_LIMITED,
            "sale_price": 0,
            "available_balance": 300,
            "validity_days": 30,
            "description": "新用户体验卡，包含3节试听课"
        },
        {
            "name": "标准储值卡",
            "card_type": CardType.VALUE_LIMITED,
            "sale_price": 2000,
            "available_balance": 2200,
            "validity_days": 365,
            "description": "标准储值卡，适合长期学习"
        },
        {
            "name": "次数卡(20次)",
            "card_type": CardType.TIMES_LIMITED,
            "sale_price": 1800,
            "available_balance": 20,
            "validity_days": 180,
            "description": "20次课程卡，灵活使用",
            "allow_renewal": False
        },
        {
            "name": "VIP储值卡",
            "card_type": CardType.VALUE_LIMITED,
            "sale_price": 5000,
            "available_balance": 5500,
            "validity_days": 730,
            "description": "VIP储值卡，超值优惠"
        },
        {
            "name": "次数卡(50次)",
            "card_type": CardType.TIMES_LIMITED,
            "sale_price": 4000,
            "available_balance": 50,
            "validity_days": 365,
            "description": "50次课程卡，长期学习",
            "allow_renewal": False
        }
    ]
    
    templates = []
    for i in range(min(count, len(templates_config))):
        config = templates_config[i]
        defaults = {
            "tenant_id": tenant_id,
            "is_active": True,
            "is_online_purchasable": True,
            "is_agent_exclusive": False,
            "allow_repeat_purchase": True,
            "allow_renewal": config.get("allow_renewal", True),
            "created_by": 1,
            **config
        }
        
        template, created = get_or_create(
            session, MemberCardTemplate, defaults, 
            tenant_id=tenant_id, name=config["name"]
        )
        templates.append(template)
        
        if created:
            logger.info(f"✅ 创建会员卡模板: {template.name}")
    
    logger.info(f"✅ 会员卡模板总数: {len(templates)}")
    return templates

def create_teachers(session: Session, tenant_id: int, count: int = 3) -> List[Any]:
    """创建教师"""
    from app.features.teachers.models import Teacher, TeacherStatus, TeacherCategory, TeacherRegion
    import random
    
    set_tenant_context(session, tenant_id)
    
    teacher_configs = [
        {"name": "Alice Johnson", "category": TeacherCategory.EUROPEAN, "region": TeacherRegion.EUROPE, "price": 150},
        {"name": "Bob Smith", "category": TeacherCategory.EUROPEAN, "region": TeacherRegion.NORTH_AMERICA, "price": 160},
        {"name": "Maria Santos", "category": TeacherCategory.FILIPINO, "region": TeacherRegion.PHILIPPINES, "price": 80},
        {"name": "John Williams", "category": TeacherCategory.SOUTH_AFRICAN, "region": TeacherRegion.SOUTH_AFRICA, "price": 100},
        {"name": "李老师", "category": TeacherCategory.CHINESE, "region": TeacherRegion.CHINA, "price": 120},
        {"name": "Emma Davis", "category": TeacherCategory.EUROPEAN, "region": TeacherRegion.EUROPE, "price": 140},
        {"name": "Michael Brown", "category": TeacherCategory.EUROPEAN, "region": TeacherRegion.NORTH_AMERICA, "price": 155},
        {"name": "Sofia Garcia", "category": TeacherCategory.FILIPINO, "region": TeacherRegion.PHILIPPINES, "price": 85},
        {"name": "David Wilson", "category": TeacherCategory.SOUTH_AFRICAN, "region": TeacherRegion.SOUTH_AFRICA, "price": 95},
        {"name": "王老师", "category": TeacherCategory.CHINESE, "region": TeacherRegion.CHINA, "price": 110},
    ]
    
    # 记录已使用的邮箱和电话
    used_emails = set()
    used_phones = set()
    
    teachers = []
    for i in range(min(count, len(teacher_configs))):
        config = teacher_configs[i]
        
        # 生成唯一电话 - 使用138前缀加8位随机数字
        while True:
            phone_suffix = ''.join(random.choice('0123456789') for _ in range(8))
            phone = f"138{phone_suffix}"
            if phone not in used_phones:
                break
        used_phones.add(phone)
        
        # 生成唯一邮箱 - 基于教师姓名
        name_parts = config["name"].lower().split()
        if len(name_parts) > 1:
            email_prefix = f"{name_parts[0]}.{name_parts[-1]}"
        else:
            email_prefix = name_parts[0]
            
        email = f"{email_prefix}@demo.com"
        email_counter = 1
        while email in used_emails:
            email = f"{email_prefix}{email_counter}@demo.com"
            email_counter += 1
        used_emails.add(email)
        
        defaults = {
            "tenant_id": tenant_id,
            "phone": phone,
            "email": email,
            "price_per_class": config["price"],
            "teacher_category": config["category"],
            "region": config["region"],
            "status": TeacherStatus.ACTIVE,
            "show_to_members": True,
            "wechat_bound": False,
            "created_by": 1,
        }
        
        teacher, created = get_or_create(
            session, Teacher, defaults,
            tenant_id=tenant_id, name=config["name"]
        )
        teachers.append(teacher)
        
        if created:
            logger.info(f"✅ 创建教师: {teacher.name} (电话: {teacher.phone}, 邮箱: {teacher.email})")
        else:
            logger.info(f"✅ 找到现有教师: {teacher.name}")
    
    logger.info(f"✅ 教师总数: {len(teachers)}")
    return teachers

def create_members_with_cards(
    session: Session, 
    tenant_id: int, 
    count: int, 
    default_template: Any, 
    agent_id: Optional[int] = None
) -> List[Tuple[Any, Any]]:
    """创建会员和对应的会员卡（处理循环依赖）"""
    from app.features.members.models import Member, MemberType, MemberStatus
    from app.features.member_cards.models import MemberCard, CardStatus
    import random
    import string

    set_tenant_context(session, tenant_id)

    member_names = [
        "张小明", "李小红", "王小华", "刘小强", "陈小美",
        "杨小刚", "赵小丽", "孙小军", "周小芳", "吴小东",
        "郑小燕", "冯小伟", "陈小静", "褚小勇", "卫小娟",
        "蒋小涛", "沈小霞", "韩小峰", "杨小玲", "朱小斌"
    ]

    results = []
    
    # 记录已创建的会员名称和电话，避免重复
    used_names = set()
    used_phones = set()
    used_emails = set()

    for i in range(count):
        # 1. 生成唯一的会员名称
        base_name = member_names[i % len(member_names)]
        
        # 添加随机后缀，确保名称唯一
        name = base_name
        if agent_id:
            name = f"{name}_agent{agent_id}_{i+1}"
        while name in used_names:
            # 生成2-4位随机字符作为后缀
            suffix_length = random.randint(2, 4)
            suffix = ''.join(random.choice(string.ascii_lowercase) for _ in range(suffix_length))
            name = f"{base_name}{suffix}"
        
        used_names.add(name)
        
        # 2. 生成唯一的电话号码 - 使用更随机的方式
        # 使用139作为前缀，后面跟8位随机数字
        while True:
            phone_suffix = ''.join(random.choice('0123456789') for _ in range(8))
            phone = f"139{phone_suffix}"
            if phone not in used_phones:
                break
        
        used_phones.add(phone)
        
        # 3. 生成唯一的邮箱
        email_prefix = f"member_{i+1}"
        if agent_id:
            email_prefix = f"member_agent{agent_id}_{i+1}"
            
        # 添加随机后缀确保唯一性
        email = f"{email_prefix}@demo.com"
        while email in used_emails:
            random_suffix = ''.join(random.choice(string.ascii_lowercase) for _ in range(4))
            email = f"{email_prefix}_{random_suffix}@demo.com"
            
        used_emails.add(email)

        # 4. 创建会员（不设置 primary_member_card_id）
        member_defaults = {
            "tenant_id": tenant_id,
            "name": name,
            "phone": phone,
            "email": email,
            "member_type": MemberType.FORMAL,
            "member_status": MemberStatus.ACTIVE,
            "source_channel": "演示数据",
            "country": "China",
            "created_by": 1,
        }
        
        # 如果指定了代理ID，则设置代理关联
        if agent_id:
            member_defaults["agent_id"] = agent_id
            member_defaults["source_channel"] = "代理推荐"
            
        # 为代理会员添加唯一标识，确保不会因为phone查询到已有会员
        if agent_id:
            member_query_key = {"tenant_id": tenant_id, "name": name, "agent_id": agent_id}
        else:
            member_query_key = {"tenant_id": tenant_id, "name": name}

        member, member_created = get_or_create(
            session, Member, member_defaults,
            **member_query_key
        )

        # 5. 创建默认会员卡
        card_number = f"CARD{tenant_id:03d}{member.id:06d}{default_template.id:03d}"
        card_defaults = {
            "name": default_template.name,
            "tenant_id": tenant_id,
            "member_id": member.id,
            "template_id": default_template.id,
            "card_number": card_number,
            "card_type": default_template.card_type,
            "balance": default_template.available_balance,
            "total_recharged": default_template.available_balance,
            "status": CardStatus.ACTIVE,
            "expires_at": datetime.now() + timedelta(days=default_template.validity_days),
            "created_by": 1,
        }

        card, card_created = get_or_create(
            session, MemberCard, card_defaults,
            card_number=card_number
        )

        # 6. 更新会员的主要会员卡关联（如果会员是新创建的）
        if member_created and not member.primary_member_card_id:
            member.primary_member_card_id = card.id
            session.add(member)
            session.flush()  # 只 flush，不 commit

        results.append((member, card))

        agent_info = f"(代理: {agent_id})" if agent_id else ""
        creation_status = "创建" if member_created else "复用"
        logger.info(f"✅ {creation_status}会员: {member.name} (电话: {member.phone}, 卡号: {card.card_number}) {agent_info}")

    logger.info(f"✅ 会员总数: {len(results)}")
    return results

def create_course_config(session: Session, tenant_id: int) -> Tuple[Any, bool]:
    """创建课程系统配置"""
    from app.features.courses.config_models import CourseSystemConfig

    set_tenant_context(session, tenant_id)

    defaults = {
        "tenant_id": tenant_id,
        "default_slot_duration_minutes": 25,
        "default_slot_interval_minutes": 5,
        "direct_booking_enabled": True,
        "max_advance_days": 30,
        "booking_deadline_hours": 2,
        "cancel_deadline_hours": 2,
        "require_material": True,
        "teacher_can_add_slots": True,
        "teacher_can_delete_empty_slots": True,
        "teacher_can_cancel_booking": False,
        "teacher_need_confirm": False,
        "fixed_booking_enabled": True,
        "auto_schedule_enabled": True,
        "auto_schedule_day": 22,
        "auto_schedule_time": time(14, 0),
        "default_schedule_weeks": 4,
        "interrupt_on_conflict": True,
        "skip_insufficient_balance": True,
        "remove_insufficient_locks": True,
        "created_by": 1,
    }

    config, created = get_or_create(
        session, CourseSystemConfig, defaults,
        tenant_id=tenant_id
    )

    if created:
        logger.info(f"✅ 创建课程系统配置")
    else:
        logger.info(f"✅ 找到现有课程系统配置")

    return config, created

def create_tag_categories_and_tags(session: Session, tenant_id: int, categories_count: int = 2, tags_per_category: int = 3) -> Dict[str, List[Any]]:
    """创建标签分类和标签"""
    from app.features.tags.models import TagCategory, Tag, TagStatus

    set_tenant_context(session, tenant_id)

    categories_config = [
        {"name": "教学特长", "description": "教师的教学专业领域"},
        {"name": "学生水平", "description": "学生的英语水平分类"},
        {"name": "课程类型", "description": "不同类型的课程分类"},
    ]

    tags_config = {
        "教学特长": ["口语专家", "语法专家", "商务英语", "雅思托福", "少儿英语"],
        "学生水平": ["初级", "中级", "高级", "零基础", "进阶"],
        "课程类型": ["一对一", "小班课", "大班课", "试听课", "复习课"],
    }

    result = {}

    for i in range(min(categories_count, len(categories_config))):
        category_config = categories_config[i]

        # 创建标签分类
        category_defaults = {
            "tenant_id": tenant_id,
            "description": category_config["description"],
            "sort_order": i,
            "created_by": 1,
        }

        category, created = get_or_create(
            session, TagCategory, category_defaults,
            tenant_id=tenant_id, name=category_config["name"]
        )

        if created:
            logger.info(f"✅ 创建标签分类: {category.name}")

        # 创建该分类下的标签
        category_tags = []
        tag_names = tags_config.get(category.name, [])

        for j in range(min(tags_per_category, len(tag_names))):
            tag_name = tag_names[j]
            tag_defaults = {
                "tenant_id": tenant_id,
                "category_id": category.id,
                "category_name": category.name,
                "name": tag_name,
                "description": f"{tag_name} - {category.name}",
                "status": TagStatus.ACTIVE,
                "created_by": 1,
            }

            tag, tag_created = get_or_create(
                session, Tag, tag_defaults,
                tenant_id=tenant_id, category_id=category.id, name=tag_name
            )
            category_tags.append(tag)

            if tag_created:
                logger.info(f"✅ 创建标签: {tag.name}")

        result[category.name] = category_tags

    total_tags = sum(len(tags) for tags in result.values())
    logger.info(f"✅ 标签分类总数: {len(result)}, 标签总数: {total_tags}")
    return result

def assign_tags_to_teachers(session: Session, tenant_id: int, teachers: List[Any], tags_by_category: Dict[str, List[Any]]) -> None:
    """给教师分配标签，确保至少两个教师有标签"""
    from app.features.teachers.models import TeacherTag

    set_tenant_context(session, tenant_id)

    if not teachers or not tags_by_category:
        logger.warning("⚠️ 没有教师或标签可供分配")
        return

    # 确保至少给前两个教师分配标签
    teachers_to_tag = teachers[:max(2, len(teachers))]

    # 获取"教学特长"分类的标签，这是最适合教师的标签分类
    teaching_specialty_tags = tags_by_category.get("教学特长", [])

    if not teaching_specialty_tags:
        # 如果没有"教学特长"标签，使用第一个可用的分类
        for category_name, tags in tags_by_category.items():
            if tags:
                teaching_specialty_tags = tags
                break

    if not teaching_specialty_tags:
        logger.warning("⚠️ 没有可用的标签进行分配")
        return

    assigned_count = 0

    for i, teacher in enumerate(teachers_to_tag):
        # 为每个教师分配1-2个标签
        tags_to_assign = []

        if i < len(teaching_specialty_tags):
            # 给第一个教师分配第一个标签，第二个教师分配第二个标签，以此类推
            tags_to_assign.append(teaching_specialty_tags[i])

            # 如果有足够的标签，给前两个教师额外分配一个标签
            if i < 2 and len(teaching_specialty_tags) > len(teachers_to_tag):
                additional_tag_index = (i + len(teachers_to_tag)) % len(teaching_specialty_tags)
                if additional_tag_index != i:  # 避免重复分配同一个标签
                    tags_to_assign.append(teaching_specialty_tags[additional_tag_index])

        # 创建教师标签关联
        for tag in tags_to_assign:
            # 检查是否已经存在该关联
            existing_teacher_tag = session.exec(
                select(TeacherTag).where(
                    and_(TeacherTag.teacher_id == teacher.id, TeacherTag.tag_id == tag.id)
                )
            ).first()

            if not existing_teacher_tag:
                teacher_tag = TeacherTag(
                    tenant_id=tenant_id,
                    teacher_id=teacher.id,
                    tag_id=tag.id,
                    created_by=1,
                    created_at=datetime.now()
                )
                session.add(teacher_tag)
                assigned_count += 1
                logger.info(f"✅ 给教师 {teacher.name} 分配标签: {tag.name}")

    # 提交标签分配
    session.commit()
    logger.info(f"✅ 教师标签分配完成，共分配 {assigned_count} 个标签给 {len(teachers_to_tag)} 个教师")

def create_teacher_fixed_slots(session: Session, tenant_id: int, teachers: List[Any], slots_per_teacher: int = 3) -> List[Any]:
    """创建教师固定时间段"""
    from app.features.teachers.fixed_slots_models import TeacherFixedSlot, Weekday
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotCreate
    from app.features.teachers.fixed_slots_service import TeacherFixedSlotService
    import random
    
    set_tenant_context(session, tenant_id)
    
    # 定义常用的时间段
    common_time_slots = [
        (Weekday.MONDAY, "09:00"), (Weekday.MONDAY, "14:00"), (Weekday.MONDAY, "19:00"),
        (Weekday.TUESDAY, "09:00"), (Weekday.TUESDAY, "14:00"), (Weekday.TUESDAY, "19:00"),
        (Weekday.WEDNESDAY, "09:00"), (Weekday.WEDNESDAY, "14:00"), (Weekday.WEDNESDAY, "19:00"),
        (Weekday.THURSDAY, "09:00"), (Weekday.THURSDAY, "14:00"), (Weekday.THURSDAY, "19:00"),
        (Weekday.FRIDAY, "09:00"), (Weekday.FRIDAY, "14:00"), (Weekday.FRIDAY, "19:00"),
        (Weekday.SATURDAY, "10:00"), (Weekday.SATURDAY, "15:00"), (Weekday.SATURDAY, "20:00"),
        (Weekday.SUNDAY, "10:00"), (Weekday.SUNDAY, "15:00"), (Weekday.SUNDAY, "20:00"),
    ]
    
    service = TeacherFixedSlotService(session, tenant_id)
    all_slots = []
    
    for teacher in teachers:
        logger.info(f"为教师 {teacher.name} 创建固定时间段...")
        
        # 为每个教师随机选择时间段
        teacher_slots = random.sample(common_time_slots, min(slots_per_teacher, len(common_time_slots)))
        
        for weekday, time_str in teacher_slots:
            # 解析时间字符串
            hour, minute = map(int, time_str.split(':'))
            start_time = time(hour, minute)
            
            # 随机设置一些时间段为不可用或不可见
            is_available = random.choice([True, True, True, False])  # 75%概率可用
            is_visible_to_members = random.choice([True, True, True, False])  # 75%概率可见
            
            slot_data = TeacherFixedSlotCreate(
                teacher_id=teacher.id,
                weekday=weekday,
                start_time=start_time,
                duration_minutes=25,  # 默认25分钟
                is_available=is_available,
                is_visible_to_members=is_visible_to_members,
                created_by=1
            )
            
            try:
                slot = service.create_slot(slot_data, created_by=1, operator_name="系统管理员")
                all_slots.append(slot)
                logger.info(f"  ✅ 创建时间段: {weekday.name} {time_str} (可用: {is_available}, 可见: {is_visible_to_members})")
            except Exception as e:
                logger.warning(f"  ⚠️ 创建时间段失败: {weekday.name} {time_str} - {e}")
    
    logger.info(f"✅ 教师固定时间段总数: {len(all_slots)}")
    return all_slots

def create_member_fixed_slot_locks(session: Session, tenant_id: int, members_with_cards: List[Tuple[Any, Any]], fixed_slots: List[Any], lock_ratio: float = 0.3) -> List[Any]:
    """创建会员固定课位锁定"""
    from app.features.members.fixed_lock_models import MemberFixedSlotLock
    from app.features.members.fixed_lock_service import MemberFixedSlotLockService
    from app.features.members.fixed_lock_schemas import MemberFixedSlotLockCreate
    import random
    
    set_tenant_context(session, tenant_id)
    
    service = MemberFixedSlotLockService(session, tenant_id)
    all_locks = []
    
    # 筛选可用的固定时间段
    available_slots = [slot for slot in fixed_slots if slot.is_available and slot.is_visible_to_members]
    logger.info(f"有 {len(available_slots)} 个可用固定时间段")
    if not available_slots:
        logger.warning("⚠️ 没有可用的固定时间段，跳过创建会员锁定")
        return all_locks
    
    # 计算要锁定的数量
    lock_count = int(len(available_slots) * lock_ratio)
    slots_to_lock = random.sample(available_slots, min(lock_count, len(available_slots)))
    
    logger.info(f"为 {len(slots_to_lock)} 个时间段创建会员锁定...")
    
    for i, slot in enumerate(slots_to_lock):
        # 随机选择一个会员
        member, card = random.choice(members_with_cards)
        
        # 创建锁定数据
        lock_data = MemberFixedSlotLockCreate(
            member_id=member.id,
            teacher_fixed_slot_id=slot.id,
            teacher_id=slot.teacher_id,
            weekday=slot.weekday,
            start_time=slot.start_time,
            member_name=member.name,
            member_phone=member.phone,
            created_by=1
        )
        
        try:
            lock = service.create_lock(lock_data, created_by=1, operator_name="系统管理员")
            all_locks.append(lock)
            logger.info(f"  ✅ 会员 {member.name} 锁定时间段: {slot.weekday.name} {slot.start_time}")
        except Exception as e:
            logger.warning(f"  ⚠️ 创建锁定失败: {member.name} - {slot.weekday.name} {slot.start_time} - {e}")
    
    logger.info(f"✅ 会员固定课位锁定总数: {len(all_locks)}")
    return all_locks

def create_scheduled_classes(session: Session, tenant_id: int, teachers: List[Any], members_with_cards: List[Tuple[Any, Any]], classes_per_teacher: int = 5) -> List[Any]:
    """创建教师已排课数据"""
    from app.features.courses.scheduled_classes_models import ScheduledClass, ClassType, ClassStatus
    from app.features.courses.scheduled_classes_schemas import ScheduledClassCreate
    from app.features.courses.scheduled_classes_service import ScheduledClassService
    from datetime import datetime, timedelta
    import random
    
    set_tenant_context(session, tenant_id)
    
    service = ScheduledClassService(session, tenant_id)
    all_classes = []
    
    # 定义常用的上课时间
    common_hours = [9, 10, 11, 14, 15, 16, 19, 20, 21]
    common_minutes = [0, 15, 30, 45]
    
    # 从明天开始，创建未来两周的课程
    start_date = datetime.now().date() + timedelta(days=1)
    
    for teacher in teachers:
        logger.info(f"为教师 {teacher.name} 创建已排课数据...")
        
        # 为每个教师创建多个课程
        for i in range(classes_per_teacher):
            # 随机选择日期（未来1-14天）
            days_ahead = random.randint(1, 14)
            class_date = start_date + timedelta(days=days_ahead)
            
            # 随机选择时间
            hour = random.choice(common_hours)
            minute = random.choice(common_minutes)
            class_datetime = datetime.combine(class_date, datetime.min.time().replace(hour=hour, minute=minute))
            
            # 随机决定是否被会员预约
            is_booked = random.choice([True, True, False])  # 67%概率被预约
            
            if is_booked and members_with_cards:
                # 随机选择一个会员
                member, card = random.choice(members_with_cards)
                member_id = member.id
                member_card_id = card.id
                member_card_name = card.name
                status = ClassStatus.BOOKED
                booking_remark = random.choice([
                    "希望练习口语", "准备考试", "日常练习", "商务英语", "旅游英语", None
                ])
            else:
                member_id = None
                member_card_id = None
                member_card_name = None
                status = ClassStatus.AVAILABLE
                booking_remark = None
            
            # 随机选择课程类型
            class_type = random.choice([ClassType.DIRECT, ClassType.FIXED])
            
            # 创建课程数据
            class_data = ScheduledClassCreate(
                teacher_id=teacher.id,
                member_id=member_id,
                class_datetime=class_datetime,
                duration_minutes=25,
                class_type=class_type,
                price=teacher.price_per_class,
                member_card_id=member_card_id,
                member_card_name=member_card_name,
                booking_remark=booking_remark,
                member_no_cancel=random.choice([True, False]),
                material_name=random.choice([
                    "新概念英语", "剑桥英语", "商务英语", "旅游英语", "考试英语", None
                ]),
                is_visible_to_member=True,
                operator_name="系统管理员"
            )
            
            try:
                scheduled_class = service.create_scheduled_class(class_data, created_by=1)
                all_classes.append(scheduled_class)
                
                status_text = "已预约" if is_booked else "可预约"
                member_text = f" (会员: {member.name})" if is_booked else ""
                logger.info(f"  ✅ 创建课程: {class_datetime.strftime('%m-%d %H:%M')} - {status_text}{member_text}")
            except Exception as e:
                logger.warning(f"  ⚠️ 创建课程失败: {class_datetime.strftime('%m-%d %H:%M')} - {e}")
    
    logger.info(f"✅ 已排课总数: {len(all_classes)}")
    return all_classes
