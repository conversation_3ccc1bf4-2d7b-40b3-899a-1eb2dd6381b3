"""
演示数据创建主模块

提供统一的演示数据创建接口
"""

import logging
from typing import Dict, Any, Optional
from sqlmodel import Session

from .config import SCENARIOS
from .database import setup_test_database, initialize_test_database, get_test_database_url
from .creators import (
    create_tenant, create_admin_user, create_agent_user, create_regular_user, create_card_templates,
    create_teachers, create_members_with_cards, create_course_config,
    create_tag_categories_and_tags, assign_tags_to_teachers,
    create_teacher_fixed_slots, create_member_fixed_slot_locks, create_scheduled_classes
)

logger = logging.getLogger(__name__)

# ==================== 主要创建函数 ====================

def create_demo_data(
    scenario: str = "basic",
    test_db_name: str = "ks_english_test_demo",
    force: bool = False,
    **custom_config
) -> Dict[str, Any]:
    """
    创建演示数据
    
    Args:
        scenario: 场景名称 (basic/full/minimal/custom)
        test_db_name: 测试数据库名称
        force: 是否跳过确认提示
        **custom_config: 自定义配置（用于 custom 场景）
    
    Returns:
        创建结果字典
    """
    
    # 获取场景配置
    if scenario == "custom":
        config = {
            "description": "自定义演示数据",
            "tenants": custom_config.get("tenants", 1),
            "users_per_tenant": custom_config.get("users_per_tenant", 1),
            "agents_per_tenant": custom_config.get("agents_per_tenant", 1),
            "teachers_per_tenant": custom_config.get("teachers", 3),
            "members_per_tenant": custom_config.get("members", 5),
            "card_templates_per_tenant": custom_config.get("card_templates", 3),
            "tag_categories_per_tenant": custom_config.get("tag_categories", 2),
            "tags_per_category": custom_config.get("tags_per_category", 3),
        }
    else:
        if scenario not in SCENARIOS:
            raise ValueError(f"未知场景: {scenario}. 可用场景: {list(SCENARIOS.keys())}")
        config = SCENARIOS[scenario].copy()
    
    logger.info(f"🚀 开始创建演示数据 - 场景: {scenario}")
    logger.info(f"📋 场景描述: {config['description']}")
    logger.info("=" * 60)
    
    # 设置测试数据库
    test_engine = setup_test_database(test_db_name, force)
    if test_engine is None:
        return {"error": "用户取消操作"}
    
    # 初始化数据库结构
    initialize_test_database(test_engine)
    logger.info("✅ 数据库初始化完成")
    
    results = {
        "scenario": scenario,
        "config": config,
        "tenants": [],
        "stats": {
            "tenants": 0,
            "admins": 0,
            "users": 0,
            "agents": 0,
            "teachers": 0,
            "members": 0,
            "member_cards": 0,
            "card_templates": 0,
            "tag_categories": 0,
            "tags": 0,
            "course_configs": 0,
            "teacher_fixed_slots": 0,
            "member_fixed_locks": 0,
            "scheduled_classes": 0,
        }
    }

    try:
        session = Session(test_engine)
        
        # 创建租户数据
        for i in range(config["tenants"]):
            tenant_name = f"演示机构{i+1}" if config["tenants"] > 1 else "演示机构"
            tenant_code = f"demo_tenant_{i+1}" if config["tenants"] > 1 else "demo_tenant"
            
            logger.info(f"🏢 创建租户 {i+1}/{config['tenants']}: {tenant_name}")
            
            # 1. 创建租户
            tenant, _ = create_tenant(session, tenant_name, tenant_code)
            results["stats"]["tenants"] += 1
            
            # 2. 创建管理员用户
            admin_username = f"demo_admin_{i+1}" if config["tenants"] > 1 else "demo_admin"
            admin, _ = create_admin_user(session, tenant.id, admin_username)
            results["stats"]["admins"] += 1
            
            # 3. 创建普通用户-普通用户也是管理员
            users = []
            for j in range(config["users_per_tenant"]):
                user_username = f"demo_user_{i+1}_{j+1}" if config["tenants"] > 1 else f"demo_user_{j+1}"
                user, _ = create_regular_user(session, tenant.id, user_username)
                users.append(user)
                results["stats"]["users"] += 1
            
            # 4. 创建会员卡模板
            templates = create_card_templates(session, tenant.id, config["card_templates_per_tenant"])
            results["stats"]["card_templates"] += len(templates)
            
            # 5. 创建教师
            teachers = create_teachers(session, tenant.id, config["teachers_per_tenant"])
            results["stats"]["teachers"] += len(teachers)
            
            # 6. 创建会员和会员卡
            default_template = templates[0]  # 使用第一个模板作为默认模板
            members_with_cards = create_members_with_cards(
                session, tenant.id, config["members_per_tenant"], default_template
            )
            results["stats"]["members"] += len(members_with_cards)
            results["stats"]["member_cards"] += len(members_with_cards)
            
            # 7. 创建代理用户及其会员
            agents = []
            for j in range(config["agents_per_tenant"]):
                agent_username = f"demo_agent_{i+1}_{j+1}" if config["tenants"] > 1 else f"demo_agent_{j+1}"
                agent, _ = create_agent_user(session, tenant.id, agent_username)
                agents.append(agent)
                results["stats"]["agents"] += 1
                
                # 为第一个代理创建至少2个会员
                if j == 0:
                    agent_members_count = max(2, config["members_per_tenant"] // config["agents_per_tenant"])
                else:
                    agent_members_count = config["members_per_tenant"] // config["agents_per_tenant"]
                
                logger.info(f"创建代理用户 {agent.username} (ID: {agent.id}) 及其 {agent_members_count} 个会员")
                agent_members_with_cards = create_members_with_cards(
                    session, tenant.id, agent_members_count, default_template, agent.id
                )
                
                # 记录创建的会员详情
                logger.info(f"代理 {agent.username} 的会员列表:")
                for idx, (member, card) in enumerate(agent_members_with_cards):
                    logger.info(f"  {idx+1}. {member.name} (ID: {member.id}, 电话: {member.phone}, 卡号: {card.card_number})")
                
                results["stats"]["members"] += len(agent_members_with_cards)
                results["stats"]["member_cards"] += len(agent_members_with_cards)
            
            # 8. 创建课程系统配置
            course_config, _ = create_course_config(session, tenant.id)
            results["stats"]["course_configs"] += 1
            
            # 9. 创建标签分类和标签
            tags_by_category = create_tag_categories_and_tags(
                session, tenant.id,
                config["tag_categories_per_tenant"],
                config["tags_per_category"]
            )
            results["stats"]["tag_categories"] += len(tags_by_category)
            results["stats"]["tags"] += sum(len(tags) for tags in tags_by_category.values())

            # 10. 给教师分配标签（确保至少两个教师有标签）
            assign_tags_to_teachers(session, tenant.id, teachers, tags_by_category)
            
            # 11. 创建教师固定时段
            fixed_slots = create_teacher_fixed_slots(session, tenant.id, teachers, config.get("fixed_slots_per_teacher", 3))
            results["stats"]["teacher_fixed_slots"] += len(fixed_slots)

            # 12. 创建会员固定时段锁定
            member_locks = create_member_fixed_slot_locks(session, tenant.id, members_with_cards, fixed_slots, config.get("member_lock_ratio", 0.3))
            results["stats"]["member_fixed_locks"] += len(member_locks)

            # 13. 创建排课
            scheduled_classes = create_scheduled_classes(session, tenant.id, teachers, members_with_cards, config.get("classes_per_teacher", 5))
            results["stats"]["scheduled_classes"] += len(scheduled_classes)
            
            # 记录结果（保存需要的信息，避免 session 关闭后访问对象属性）
            tenant_result = {
                "tenant": {"id": tenant.id, "name": tenant.name, "code": tenant.code},
                "admin": {"id": admin.id, "username": admin.username, "real_name": admin.real_name},
                "users": [{"id": u.id, "username": u.username, "real_name": u.real_name} for u in users],
                "agents": [{"id": a.id, "username": a.username, "real_name": a.real_name} for a in agents],
                "templates": [{"id": t.id, "name": t.name} for t in templates],
                "teachers": [{"id": t.id, "name": t.name} for t in teachers],
                "members_with_cards": [{"member": {"id": m.id, "name": m.name}, "card": {"id": c.id, "card_number": c.card_number}} for m, c in members_with_cards],
                "course_config": {"id": course_config.id},
                "tags_by_category": {cat: [{"id": tag.id, "name": tag.name} for tag in tags] for cat, tags in tags_by_category.items()},
            }
            results["tenants"].append(tenant_result)
            
            # 提交当前租户的所有数据
            session.commit()
            logger.info(f"✅ 租户 {tenant_name} 创建完成")
            logger.info("-" * 40)

        # 在关闭 session 前保存需要的信息
        login_info = None
        if results["tenants"]:
            first_tenant = results["tenants"][0]
            login_info = {
                "tenant_name": first_tenant['tenant']['name'],
                "username": first_tenant['admin']['username'],
            }

        session.close()

        # 输出统计信息
        logger.info("=" * 60)
        logger.info("📊 演示数据创建统计")
        logger.info("=" * 60)
        for key, value in results["stats"].items():
            logger.info(f"  {key}: {value}")
        logger.info("=" * 60)
        logger.info("🎉 演示数据创建完成！")

        # 输出数据库连接信息
        logger.info("")
        logger.info("🔗 数据库连接信息:")
        logger.info(f"  - 测试数据库: {test_db_name}")
        logger.info(f"  - 连接URL: {get_test_database_url(test_db_name)}")

        # 输出登录信息
        if login_info:
            logger.info("")
            logger.info("💡 登录信息:")
            logger.info(f"  - 租户: {login_info['tenant_name']}")
            logger.info(f"  - 用户名: {login_info['username']}")
            logger.info(f"  - 密码: demo123456")
            logger.info(f"  - 角色: 管理员")

        return results

    except Exception as e:
        if 'session' in locals():
            session.rollback()
            session.close()
        logger.error(f"❌ 创建演示数据失败: {e}")
        raise

# ==================== 便捷函数 ====================

def create_basic_demo(test_db_name: str = "ks_english_test_demo", force: bool = False):
    """创建基础演示数据"""
    return create_demo_data("basic", test_db_name, force)

def create_full_demo(test_db_name: str = "ks_english_test_demo", force: bool = False):
    """创建完整演示数据"""
    return create_demo_data("full", test_db_name, force)

def create_minimal_demo(test_db_name: str = "ks_english_test_demo", force: bool = False):
    """创建最小演示数据"""
    return create_demo_data("minimal", test_db_name, force)

def create_custom_demo(test_db_name: str = "ks_english_test_demo", force: bool = False, **kwargs):
    """创建自定义演示数据"""
    return create_demo_data("custom", test_db_name, force, **kwargs)
