#!/usr/bin/env python3
"""
演示数据创建使用示例

展示如何使用新的课程数据创建功能
"""

import os
import sys
import logging

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from demo_data import create_demo_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def example_basic_usage():
    """基础使用示例"""
    print("🎯 基础使用示例")
    print("=" * 50)
    
    # 创建基础演示数据（包含课程数据）
    result = create_demo_data(
        scenario="basic",
        test_db_name="ks_english_demo_basic",
        force=True
    )
    
    print(f"✅ 创建成功！")
    print(f"📊 数据统计:")
    print(f"   - 租户: {result['stats']['tenants']}")
    print(f"   - 教师: {result['stats']['teachers']}")
    print(f"   - 会员: {result['stats']['members']}")
    print(f"   - 教师固定时段: {result['stats']['teacher_fixed_slots']}")
    print(f"   - 会员锁定: {result['stats']['member_fixed_locks']}")
    print(f"   - 已排课: {result['stats']['scheduled_classes']}")
    print()

def example_custom_usage():
    """自定义使用示例"""
    print("🎯 自定义使用示例")
    print("=" * 50)
    
    # 创建自定义演示数据
    result = create_demo_data(
        scenario="custom",
        test_db_name="ks_english_demo_custom",
        force=True,
        tenants=2,
        teachers_per_tenant=5,
        members_per_tenant=10,
        fixed_slots_per_teacher=6,
        member_lock_ratio=0.4,
        classes_per_teacher=8
    )
    
    print(f"✅ 创建成功！")
    print(f"📊 数据统计:")
    print(f"   - 租户: {result['stats']['tenants']}")
    print(f"   - 教师: {result['stats']['teachers']}")
    print(f"   - 会员: {result['stats']['members']}")
    print(f"   - 教师固定时段: {result['stats']['teacher_fixed_slots']}")
    print(f"   - 会员锁定: {result['stats']['member_fixed_locks']}")
    print(f"   - 已排课: {result['stats']['scheduled_classes']}")
    print()

def example_minimal_usage():
    """最小使用示例"""
    print("🎯 最小使用示例")
    print("=" * 50)
    
    # 创建最小演示数据
    result = create_demo_data(
        scenario="minimal",
        test_db_name="ks_english_demo_minimal",
        force=True
    )
    
    print(f"✅ 创建成功！")
    print(f"📊 数据统计:")
    print(f"   - 租户: {result['stats']['tenants']}")
    print(f"   - 教师: {result['stats']['teachers']}")
    print(f"   - 会员: {result['stats']['members']}")
    print(f"   - 教师固定时段: {result['stats']['teacher_fixed_slots']}")
    print(f"   - 会员锁定: {result['stats']['member_fixed_locks']}")
    print(f"   - 已排课: {result['stats']['scheduled_classes']}")
    print()

def example_full_usage():
    """完整使用示例"""
    print("🎯 完整使用示例")
    print("=" * 50)
    
    # 创建完整演示数据
    result = create_demo_data(
        scenario="full",
        test_db_name="ks_english_demo_full",
        force=True
    )
    
    print(f"✅ 创建成功！")
    print(f"📊 数据统计:")
    print(f"   - 租户: {result['stats']['tenants']}")
    print(f"   - 教师: {result['stats']['teachers']}")
    print(f"   - 会员: {result['stats']['members']}")
    print(f"   - 教师固定时段: {result['stats']['teacher_fixed_slots']}")
    print(f"   - 会员锁定: {result['stats']['member_fixed_locks']}")
    print(f"   - 已排课: {result['stats']['scheduled_classes']}")
    print()

def main():
    """主函数"""
    print("🚀 演示数据创建使用示例")
    print("=" * 60)
    print()
    
    try:
        # 运行各种示例
        example_minimal_usage()
        example_basic_usage()
        example_custom_usage()
        example_full_usage()
        
        print("🎉 所有示例运行完成！")
        print()
        print("💡 提示:")
        print("   - 可以使用 --list-scenarios 查看所有可用场景")
        print("   - 可以使用自定义参数调整数据量")
        print("   - 测试完成后记得清理演示数据库")
        
    except Exception as e:
        print(f"❌ 运行示例时出错: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 