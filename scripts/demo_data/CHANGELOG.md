# 演示数据创建工具更新日志

## 版本 2.0.0 - 2024-01-XX

### 🎉 新增功能

#### 1. 教师固定时段数据创建

- **功能描述**: 为教师创建固定可用时间段
- **实现文件**: `scripts/demo_data/creators.py`
- **函数**: `create_teacher_fixed_slots()`
- **特性**:
  - 支持自定义每个教师的固定时段数量
  - 时间段分布在周一到周日
  - 常用时间段：09:00, 14:00, 19:00
  - 支持设置可用性和可见性
  - 包含未被会员预定的时间段

#### 2. 会员固定课位锁定数据创建

- **功能描述**: 创建会员锁定教师的固定时间段
- **实现文件**: `scripts/demo_data/creators.py`
- **函数**: `create_member_fixed_slot_locks()`
- **特性**:
  - 支持自定义锁定比例 (0.0-1.0)
  - 随机分配会员到固定时段
  - 确保每个会员最多锁定一个时段
  - 包含已被会员预定的时间段

#### 3. 教师已排课数据创建

- **功能描述**: 创建具体的课程安排数据
- **实现文件**: `scripts/demo_data/creators.py`
- **函数**: `create_scheduled_classes()`
- **特性**:
  - 支持自定义每个教师的排课数量
  - 排课日期在未来 30 天内随机分布
  - 约 50%的课程为未被预定状态 (available)
  - 约 50%的课程为已被预定状态 (booked)
  - 约 60%为直接约课 (direct)，40%为固定约课 (fixed)

### 🔧 配置更新

#### 场景配置增强

- **文件**: `scripts/demo_data/config.py`
- **新增参数**:
  - `fixed_slots_per_teacher`: 每个教师的固定时段数量
  - `member_lock_ratio`: 会员锁定固定课位的比例
  - `classes_per_teacher`: 每个教师的已排课数量

#### 场景预设

- **minimal**: 最小演示数据 (固定时段/教师: 2, 锁定比例: 0.2, 排课/教师: 3)
- **basic**: 基础演示数据 (固定时段/教师: 3, 锁定比例: 0.3, 排课/教师: 5)
- **full**: 完整演示数据 (固定时段/教师: 5, 锁定比例: 0.4, 排课/教师: 8)

### 🚀 命令行工具增强

#### 新增命令行参数

- **文件**: `scripts/create_demo_data.py`
- **新增参数**:
  - `--fixed-slots-per-teacher`: 每个教师的固定时间段数量
  - `--member-lock-ratio`: 会员锁定固定课位的比例
  - `--classes-per-teacher`: 每个教师的已排课数量

#### 使用示例更新

```bash
# 创建包含课程数据的基础演示数据
python scripts/create_demo_data.py basic --force

# 创建自定义课程数据
python scripts/create_demo_data.py custom \
  --fixed-slots-per-teacher 8 \
  --member-lock-ratio 0.5 \
  --classes-per-teacher 10 \
  --force \
  --test-db course_booking_db
```

### 📊 统计信息增强

#### 新增统计指标

- **文件**: `scripts/demo_data/main.py`
- **新增统计**:
  - `teacher_fixed_slots`: 教师固定时段数量
  - `member_fixed_locks`: 会员固定课位锁定数量
  - `scheduled_classes`: 已排课数量

### 📚 文档更新

#### 新增文档

- **COURSE_DATA_GUIDE.md**: 详细的课程数据创建指南
- **README.md**: 更新使用说明和功能介绍
- **example_usage.py**: 使用示例脚本

#### 文档内容

- 数据模型详细说明
- 数据创建逻辑说明
- 使用示例和最佳实践
- 故障排除指南

### 🧪 测试工具

#### 新增测试脚本

- **文件**: `scripts/test_demo_data.py`
- **功能**: 验证新功能的正确性
- **测试内容**:
  - 最小演示数据创建测试
  - 基础演示数据创建测试
  - 自定义参数测试

### 🔍 数据质量保证

#### 数据验证

- 外键关系正确性检查
- 时间格式验证
- 状态值范围验证
- 数据隔离验证

#### 错误处理

- 完善的异常处理机制
- 详细的错误信息输出
- 数据回滚机制

### 🎯 使用场景

#### 功能演示

- 适合展示课程管理功能
- 包含完整的业务流程数据
- 支持多租户数据隔离

#### 压力测试

- 支持大规模数据创建
- 可自定义数据量
- 适合性能测试

#### 开发测试

- 快速创建测试数据
- 支持自定义参数
- 便于功能验证

### 🔄 向后兼容性

- 保持原有 API 接口不变
- 新增功能为可选参数
- 默认值确保向后兼容

### 📝 注意事项

1. **数据隔离**: 所有数据都通过租户 ID 进行隔离
2. **时间设置**: 排课时间设置为未来日期，避免过期
3. **状态一致性**: 确保锁定和排课状态的一致性
4. **数据量控制**: 避免创建过多数据影响性能
5. **清理数据**: 测试完成后及时清理演示数据

### 🚀 下一步计划

- [ ] 支持更多课程类型
- [ ] 添加数据导出功能
- [ ] 支持数据模板定制
- [ ] 添加数据验证工具
- [ ] 支持增量数据更新
