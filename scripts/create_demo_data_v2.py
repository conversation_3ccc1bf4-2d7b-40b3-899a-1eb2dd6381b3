#!/usr/bin/env python3
"""
演示数据创建脚本 V2 - 简洁高效版本

支持多种场景的演示数据创建：
- basic: 基础演示数据（1个租户，少量数据）
- full: 完整演示数据（多个租户，丰富数据）
- custom: 自定义数据量

特点：
- 简洁的脚本式设计，易于理解和修改
- 配置驱动，支持多种场景
- 正确处理依赖关系和循环依赖
- 幂等性操作，可重复运行
"""

import sys
import os
import logging
from datetime import datetime, timedelta, time, date
from typing import List, Dict, Any, Optional, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlmodel import Session, text, select, create_engine
from app.core.config import settings
from app.db.base import create_db_and_tables, init_global_data

# 导入模型
from app.features.tenants.models import Tenant, TenantStatus, PlanType, BillingCycle
from app.features.users.models import User, UserRole, UserStatus, Gender
from app.features.teachers.models import Teacher, TeacherStatus, TeacherCategory, TeacherRegion
from app.features.members.models import Member, MemberType, MemberStatus
from app.features.member_cards.models import MemberCardTemplate, MemberCard, CardType, CardStatus
from app.features.courses.config_models import CourseSystemConfig
from app.features.courses.scheduled_classes_models import ScheduledClass
from app.features.tags.models import TagCategory, Tag, TagStatus
from app.utils.security import get_password_hash

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ==================== 数据库安全配置 ====================

def get_test_database_url(test_db_name: str = "ks_english_test_demo") -> str:
    """获取测试数据库URL"""
    # 解析原始数据库URL
    original_url = settings.DATABASE_URL
    # 替换数据库名为测试数据库名
    if "course_booking_db" in original_url:
        test_url = original_url.replace("course_booking_db", test_db_name)
    else:
        # 如果URL格式不同，手动构建
        parts = original_url.split("/")
        parts[-1] = test_db_name
        test_url = "/".join(parts)

    return test_url

def create_test_engine(test_db_name: str = "ks_english_test_demo"):
    """创建测试数据库引擎"""
    test_url = get_test_database_url(test_db_name)

    logger.info(f"🔧 创建测试数据库引擎")
    logger.info(f"   原始数据库: {settings.DATABASE_URL}")
    logger.info(f"   测试数据库: {test_url}")

    return create_engine(
        test_url,
        echo=False,  # 演示数据创建时不需要详细SQL日志
        pool_pre_ping=True,
        pool_size=5,
        max_overflow=10,
        connect_args={"options": "-c timezone=Asia/Shanghai"}
    )

def drop_test_database(test_db_name: str = "ks_english_test_demo"):
    """删除测试数据库（安全措施）"""
    import psycopg2
    from urllib.parse import urlparse

    # 解析数据库连接信息
    parsed = urlparse(settings.DATABASE_URL)

    # 连接到 postgres 数据库来删除测试数据库
    conn = psycopg2.connect(
        host=parsed.hostname,
        port=parsed.port,
        user=parsed.username,
        password=parsed.password,
        database="postgres"  # 连接到默认数据库
    )
    conn.autocommit = True

    try:
        with conn.cursor() as cursor:
            # 终止所有连接到测试数据库的会话
            cursor.execute(f"""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = '{test_db_name}' AND pid <> pg_backend_pid()
            """)

            # 删除数据库
            cursor.execute(f'DROP DATABASE IF EXISTS "{test_db_name}"')
            logger.info(f"🗑️  删除测试数据库: {test_db_name}")

    except Exception as e:
        logger.warning(f"⚠️  删除数据库时出现警告: {e}")
    finally:
        conn.close()

def create_test_database(test_db_name: str = "ks_english_test_demo"):
    """创建测试数据库"""
    import psycopg2
    from urllib.parse import urlparse

    # 解析数据库连接信息
    parsed = urlparse(settings.DATABASE_URL)

    # 连接到 postgres 数据库来创建测试数据库
    conn = psycopg2.connect(
        host=parsed.hostname,
        port=parsed.port,
        user=parsed.username,
        password=parsed.password,
        database="postgres"
    )
    conn.autocommit = True

    try:
        with conn.cursor() as cursor:
            cursor.execute(f'CREATE DATABASE "{test_db_name}"')
            logger.info(f"✅ 创建测试数据库: {test_db_name}")
    except psycopg2.errors.DuplicateDatabase:
        logger.info(f"✅ 测试数据库已存在: {test_db_name}")
    except Exception as e:
        logger.error(f"❌ 创建数据库失败: {e}")
        raise
    finally:
        conn.close()

def confirm_database_operation(test_db_name: str) -> bool:
    """确认数据库操作"""
    print("\n" + "="*60)
    print("🚨 数据库操作确认")
    print("="*60)
    print(f"即将操作的数据库: {test_db_name}")
    print(f"原始配置数据库: {settings.DATABASE_URL.split('/')[-1]}")
    print("\n⚠️  注意事项:")
    print("1. 这将删除并重新创建测试数据库")
    print("2. 测试数据库中的所有数据将被清除")
    print("3. 请确认这是您想要的操作")
    print("="*60)

    while True:
        response = input("是否继续？(yes/no): ").lower().strip()
        if response in ['yes', 'y']:
            return True
        elif response in ['no', 'n']:
            return False
        else:
            print("请输入 'yes' 或 'no'")

def setup_test_database(test_db_name: str = "ks_english_test_demo", force: bool = False):
    """设置测试数据库"""
    if not force and not confirm_database_operation(test_db_name):
        logger.info("❌ 用户取消操作")
        return None

    logger.info("🚀 开始设置测试数据库...")

    # 1. 删除现有测试数据库
    drop_test_database(test_db_name)

    # 2. 创建新的测试数据库
    create_test_database(test_db_name)

    # 3. 创建测试数据库引擎
    test_engine = create_test_engine(test_db_name)

    logger.info("✅ 测试数据库设置完成")
    return test_engine

# ==================== 场景配置 ====================

SCENARIOS = {
    "basic": {
        "description": "基础演示数据 - 适合功能演示",
        "tenants": 1,
        "users_per_tenant": 1,
        "teachers_per_tenant": 3,
        "members_per_tenant": 5,
        "card_templates_per_tenant": 3,
        "tag_categories_per_tenant": 2,
        "tags_per_category": 3,
    },
    "full": {
        "description": "完整演示数据 - 适合压力测试",
        "tenants": 3,
        "users_per_tenant": 2,
        "teachers_per_tenant": 10,
        "members_per_tenant": 20,
        "card_templates_per_tenant": 5,
        "tag_categories_per_tenant": 3,
        "tags_per_category": 5,
    },
    "minimal": {
        "description": "最小演示数据 - 仅核心功能",
        "tenants": 1,
        "users_per_tenant": 1,
        "teachers_per_tenant": 1,
        "members_per_tenant": 2,
        "card_templates_per_tenant": 1,
        "tag_categories_per_tenant": 1,
        "tags_per_category": 2,
    }
}

# ==================== 工具函数 ====================

def set_tenant_context(session: Session, tenant_id: Optional[int] = None):
    """设置租户上下文"""
    if tenant_id is None:
        session.exec(text("RESET app.current_tenant_id"))
        logger.debug("设置为全局上下文")
    else:
        session.exec(text(f"SET app.current_tenant_id = {tenant_id}"))
        logger.debug(f"设置租户上下文: {tenant_id}")

def get_or_create(session: Session, model_class, defaults: Dict[str, Any], **kwargs) -> Tuple[Any, bool]:
    """获取或创建对象，返回 (对象, 是否新创建)"""
    try:
        # 构建查询条件
        conditions = [getattr(model_class, key) == value for key, value in kwargs.items()]
        statement = select(model_class).where(*conditions)
        existing = session.exec(statement).first()

        if existing:
            return existing, False

        # 创建新对象
        create_data = {**kwargs, **defaults}
        new_obj = model_class(**create_data)
        session.add(new_obj)
        session.flush()  # 只 flush，不 commit，让调用者控制事务
        return new_obj, True

    except Exception as e:
        session.rollback()
        # 如果是唯一约束冲突，再次尝试查询（可能是并发创建）
        if "duplicate key" in str(e) or "unique constraint" in str(e):
            conditions = [getattr(model_class, key) == value for key, value in kwargs.items()]
            statement = select(model_class).where(*conditions)
            existing = session.exec(statement).first()
            if existing:
                return existing, False
        raise

# ==================== 核心创建函数 ====================

def create_tenant(session: Session, name: str, code: str) -> Tuple[Tenant, bool]:
    """创建或获取租户"""
    set_tenant_context(session, None)  # 全局上下文
    
    defaults = {
        "name": name,
        "display_name": name,
        "description": f"{name} - 演示数据",
        "status": TenantStatus.ACTIVE,
        "plan_type": PlanType.STANDARD,
        "billing_cycle": BillingCycle.MONTHLY,
        "max_teachers": 50,
        "max_members": 1000,
        "max_storage_gb": 10,
        "price_per_class": 100,
        "monthly_fee": 299,
        "setup_fee": 0,
        "trial_expires_at": datetime.now() + timedelta(days=30),
        "subscription_expires_at": datetime.now() + timedelta(days=365),
        "contact_name": "演示联系人",
        "contact_phone": "13800138000",
        "contact_email": f"demo@{code}.com",
        "created_by": 1,
    }
    
    tenant, created = get_or_create(session, Tenant, defaults, code=code)
    
    if created:
        logger.info(f"✅ 创建新租户: {tenant.name} (ID: {tenant.id})")
    else:
        logger.info(f"✅ 找到现有租户: {tenant.name} (ID: {tenant.id})")
    
    return tenant, created

def create_admin_user(session: Session, tenant_id: int, username: str = "demo_admin", password: str = "demo123456") -> Tuple[User, bool]:
    """创建或获取管理员用户"""
    set_tenant_context(session, tenant_id)
    
    defaults = {
        "tenant_id": tenant_id,
        "email": f"{username}@demo.com",
        "phone": "13800138001",
        "password_hash": get_password_hash(password),
        "real_name": "演示管理员",
        "role": UserRole.ADMIN,
        "status": UserStatus.ACTIVE,
        "gender": Gender.OTHER,
        "created_by": 1,
    }
    
    user, created = get_or_create(session, User, defaults, username=username)
    
    if created:
        logger.info(f"✅ 创建新管理员: {user.username}")
    else:
        logger.info(f"✅ 找到现有管理员: {user.username}")
    
    return user, created

def create_card_templates(session: Session, tenant_id: int, count: int = 3) -> List[MemberCardTemplate]:
    """创建会员卡模板"""
    set_tenant_context(session, tenant_id)

    templates_config = [
        {
            "name": "体验卡",
            "card_type": CardType.VALUE_LIMITED,
            "sale_price": 0,
            "available_balance": 300,
            "validity_days": 30,
            "description": "新用户体验卡，包含3节试听课"
        },
        {
            "name": "标准储值卡",
            "card_type": CardType.VALUE_LIMITED,
            "sale_price": 2000,
            "available_balance": 2200,
            "validity_days": 365,
            "description": "标准储值卡，适合长期学习"
        },
        {
            "name": "次数卡(20次)",
            "card_type": CardType.TIMES_LIMITED,
            "sale_price": 1800,
            "available_balance": 20,
            "validity_days": 180,
            "description": "20次课程卡，灵活使用",
            "allow_online_renewal": False
        },
        {
            "name": "VIP储值卡",
            "card_type": CardType.VALUE_LIMITED,
            "sale_price": 5000,
            "available_balance": 5500,
            "validity_days": 730,
            "description": "VIP储值卡，超值优惠"
        },
        {
            "name": "次数卡(50次)",
            "card_type": CardType.TIMES_LIMITED,
            "sale_price": 4000,
            "available_balance": 50,
            "validity_days": 365,
            "description": "50次课程卡，长期学习",
            "allow_online_renewal": False
        }
    ]

    templates = []
    for i in range(min(count, len(templates_config))):
        config = templates_config[i]
        defaults = {
            "tenant_id": tenant_id,
            "is_active": True,
            "is_online_purchasable": True,
            "is_agent_exclusive": False,
            "allow_repeat_purchase": True,
            "allow_online_renewal": config.get("allow_online_renewal", True),
            "created_by": 1,
            **config
        }

        template, created = get_or_create(
            session, MemberCardTemplate, defaults,
            tenant_id=tenant_id, name=config["name"]
        )
        templates.append(template)

        if created:
            logger.info(f"✅ 创建会员卡模板: {template.name}")

    logger.info(f"✅ 会员卡模板总数: {len(templates)}")
    return templates

def create_teachers(session: Session, tenant_id: int, count: int = 3) -> List[Teacher]:
    """创建教师"""
    set_tenant_context(session, tenant_id)

    teacher_configs = [
        {"name": "Alice Johnson", "category": TeacherCategory.EUROPEAN, "region": TeacherRegion.EUROPE, "price": 150},
        {"name": "Bob Smith", "category": TeacherCategory.EUROPEAN, "region": TeacherRegion.NORTH_AMERICA, "price": 160},
        {"name": "Maria Santos", "category": TeacherCategory.FILIPINO, "region": TeacherRegion.PHILIPPINES, "price": 80},
        {"name": "John Williams", "category": TeacherCategory.SOUTH_AFRICAN, "region": TeacherRegion.SOUTH_AFRICA, "price": 100},
        {"name": "李老师", "category": TeacherCategory.CHINESE, "region": TeacherRegion.CHINA, "price": 120},
        {"name": "Emma Davis", "category": TeacherCategory.EUROPEAN, "region": TeacherRegion.EUROPE, "price": 140},
        {"name": "Michael Brown", "category": TeacherCategory.EUROPEAN, "region": TeacherRegion.NORTH_AMERICA, "price": 155},
        {"name": "Sofia Garcia", "category": TeacherCategory.FILIPINO, "region": TeacherRegion.PHILIPPINES, "price": 85},
        {"name": "David Wilson", "category": TeacherCategory.SOUTH_AFRICAN, "region": TeacherRegion.SOUTH_AFRICA, "price": 95},
        {"name": "王老师", "category": TeacherCategory.CHINESE, "region": TeacherRegion.CHINA, "price": 110},
    ]

    teachers = []
    for i in range(min(count, len(teacher_configs))):
        config = teacher_configs[i]
        defaults = {
            "tenant_id": tenant_id,
            "phone": f"138{tenant_id:04d}{i:04d}",
            "email": f"teacher{i+1}@demo.com",
            "price_per_class": config["price"],
            "teacher_category": config["category"],
            "region": config["region"],
            "status": TeacherStatus.ACTIVE,
            "show_to_members": True,
            "wechat_bound": False,
            "created_by": 1,
        }

        teacher, created = get_or_create(
            session, Teacher, defaults,
            tenant_id=tenant_id, name=config["name"]
        )
        teachers.append(teacher)

        if created:
            logger.info(f"✅ 创建教师: {teacher.name}")

    logger.info(f"✅ 教师总数: {len(teachers)}")
    return teachers

def create_members_with_cards(session: Session, tenant_id: int, count: int, default_template: MemberCardTemplate) -> List[Tuple[Member, MemberCard]]:
    """创建会员和对应的会员卡（处理循环依赖）"""
    set_tenant_context(session, tenant_id)

    member_names = [
        "张小明", "李小红", "王小华", "刘小强", "陈小美",
        "杨小刚", "赵小丽", "孙小军", "周小芳", "吴小东",
        "郑小燕", "冯小伟", "陈小静", "褚小勇", "卫小娟",
        "蒋小涛", "沈小霞", "韩小峰", "杨小玲", "朱小斌"
    ]

    results = []

    for i in range(count):
        name = member_names[i % len(member_names)]
        if count > len(member_names):
            name = f"{name}{i//len(member_names)+1}"

        # 1. 创建会员（不设置 primary_member_card_id）
        member_defaults = {
            "tenant_id": tenant_id,
            "name": name,
            "phone": f"139{tenant_id:04d}{i:04d}",
            "email": f"member{i+1}@demo.com",
            "member_type": MemberType.FORMAL,
            "member_status": MemberStatus.ACTIVE,
            "source_channel": "演示数据",
            "country": "China",
            "created_by": 1,
        }

        member, member_created = get_or_create(
            session, Member, member_defaults,
            tenant_id=tenant_id, phone=member_defaults["phone"]
        )

        # 2. 创建默认会员卡
        card_number = f"CARD{tenant_id:03d}{member.id:06d}{default_template.id:03d}"
        card_defaults = {
            "tenant_id": tenant_id,
            "member_id": member.id,
            "template_id": default_template.id,
            "card_number": card_number,
            "card_type": default_template.card_type,
            "balance": default_template.available_balance,
            "total_recharged": default_template.available_balance,
            "status": CardStatus.ACTIVE,
            "expires_at": datetime.now() + timedelta(days=default_template.validity_days),
            "created_by": 1,
        }

        card, card_created = get_or_create(
            session, MemberCard, card_defaults,
            card_number=card_number
        )

        # 3. 更新会员的主要会员卡关联（如果会员是新创建的）
        if member_created and not member.primary_member_card_id:
            member.primary_member_card_id = card.id
            # 注意：member 模型中可能没有 primary_member_card_name 字段，先不设置
            session.add(member)
            session.flush()  # 只 flush，不 commit

        results.append((member, card))

        if member_created:
            logger.info(f"✅ 创建会员: {member.name} (卡号: {card.card_number})")

    logger.info(f"✅ 会员总数: {len(results)}")
    return results

def create_course_config(session: Session, tenant_id: int) -> Tuple[CourseSystemConfig, bool]:
    """创建课程系统配置"""
    set_tenant_context(session, tenant_id)

    defaults = {
        "tenant_id": tenant_id,
        "default_slot_duration_minutes": 25,
        "default_slot_interval_minutes": 5,
        "direct_booking_enabled": True,
        "max_advance_days": 30,
        "booking_deadline_hours": 2,
        "cancel_deadline_hours": 2,
        "require_material": True,
        "teacher_can_add_slots": True,
        "teacher_can_delete_empty_slots": True,
        "teacher_can_cancel_booking": False,
        "teacher_need_confirm": False,
        "fixed_booking_enabled": True,
        "auto_schedule_enabled": True,
        "auto_schedule_day": 22,
        "auto_schedule_time": time(14, 0),
        "default_schedule_weeks": 4,
        "interrupt_on_conflict": True,
        "skip_insufficient_balance": True,
        "remove_insufficient_locks": True,
        "created_by": 1,
    }

    config, created = get_or_create(
        session, CourseSystemConfig, defaults,
        tenant_id=tenant_id
    )

    if created:
        logger.info(f"✅ 创建课程系统配置")
    else:
        logger.info(f"✅ 找到现有课程系统配置")

    return config, created

def create_tag_categories_and_tags(session: Session, tenant_id: int, categories_count: int = 2, tags_per_category: int = 3) -> Dict[str, List[Tag]]:
    """创建标签分类和标签"""
    set_tenant_context(session, tenant_id)

    categories_config = [
        {"name": "教学特长", "description": "教师的教学专业领域"},
        {"name": "学生水平", "description": "学生的英语水平分类"},
        {"name": "课程类型", "description": "不同类型的课程分类"},
    ]

    tags_config = {
        "教学特长": ["口语专家", "语法专家", "商务英语", "雅思托福", "少儿英语"],
        "学生水平": ["初级", "中级", "高级", "零基础", "进阶"],
        "课程类型": ["一对一", "小班课", "大班课", "试听课", "复习课"],
    }

    result = {}

    for i in range(min(categories_count, len(categories_config))):
        category_config = categories_config[i]

        # 创建标签分类
        category_defaults = {
            "tenant_id": tenant_id,
            "description": category_config["description"],
            "sort_order": i,
            "created_by": 1,
        }

        category, created = get_or_create(
            session, TagCategory, category_defaults,
            tenant_id=tenant_id, name=category_config["name"]
        )

        if created:
            logger.info(f"✅ 创建标签分类: {category.name}")

        # 创建该分类下的标签
        category_tags = []
        tag_names = tags_config.get(category.name, [])

        for j in range(min(tags_per_category, len(tag_names))):
            tag_name = tag_names[j]
            tag_defaults = {
                "tenant_id": tenant_id,
                "category_id": category.id,
                "name": tag_name,
                "description": f"{tag_name} - {category.name}",
                "status": TagStatus.ACTIVE,
                "created_by": 1,
            }

            tag, tag_created = get_or_create(
                session, Tag, tag_defaults,
                tenant_id=tenant_id, category_id=category.id, name=tag_name
            )
            category_tags.append(tag)

            if tag_created:
                logger.info(f"✅ 创建标签: {tag.name}")

        result[category.name] = category_tags

    total_tags = sum(len(tags) for tags in result.values())
    logger.info(f"✅ 标签分类总数: {len(result)}, 标签总数: {total_tags}")
    return result

# ==================== 主要场景创建函数 ====================

def create_demo_data(scenario: str = "basic", test_db_name: str = "ks_english_test_demo", force: bool = False) -> Dict[str, Any]:
    """创建演示数据的主函数"""
    if scenario not in SCENARIOS:
        raise ValueError(f"未知场景: {scenario}. 可用场景: {list(SCENARIOS.keys())}")

    config = SCENARIOS[scenario]
    logger.info(f"🚀 开始创建演示数据 - 场景: {scenario}")
    logger.info(f"📋 场景描述: {config['description']}")
    logger.info("=" * 60)

    # 设置测试数据库
    test_engine = setup_test_database(test_db_name, force)
    if test_engine is None:
        return {"error": "用户取消操作"}

    # 使用测试数据库引擎初始化数据库
    logger.info("📋 初始化测试数据库结构...")

    # 直接使用测试数据库引擎创建表结构，避免影响生产环境
    # 1. 创建表结构
    from sqlmodel import SQLModel
    import app.models  # 导入所有模型以确保表被创建

    SQLModel.metadata.create_all(test_engine)
    logger.info("Database tables created")

    # 2. 设置行级安全（使用测试数据库）
    from app.db.rls_v2 import setup_rls_policies_v2

    # 临时替换引擎以便 RLS 设置使用测试数据库
    import app.db.session
    original_engine = app.db.session.engine
    app.db.session.engine = test_engine

    try:
        setup_rls_policies_v2()
        logger.info("Row Level Security setup completed using RLS v2")
    except Exception as e:
        logger.warning(f"RLS setup failed: {e}")
    finally:
        # 恢复原始引擎
        app.db.session.engine = original_engine

    # 3. 初始化全局数据（仅检查，不创建）
    session = Session(test_engine)
    session.exec(text("RESET app.current_tenant_id"))

    # 检查系统表（如果不存在会有警告，但不影响演示数据创建）
    try:
        session.exec(text("SELECT count(*) FROM system_configs")).first()
    except Exception as e:
        logger.warning(f"Could not check system configs: {e}")

    try:
        session.exec(text("SELECT * FROM users WHERE username = 'kenshin'")).first()
    except Exception as e:
        logger.warning(f"Could not check super admin: {e}")

    session.commit()
    logger.info("Global data initialization completed")

    results = {
        "scenario": scenario,
        "config": config,
        "tenants": [],
        "stats": {
            "tenants": 0,
            "users": 0,
            "teachers": 0,
            "members": 0,
            "member_cards": 0,
            "card_templates": 0,
            "tag_categories": 0,
            "tags": 0,
            "course_configs": 0,
        }
    }

    try:
            # 创建租户和相关数据
            for i in range(config["tenants"]):
                tenant_name = f"演示机构{i+1}" if config["tenants"] > 1 else "演示机构"
                tenant_code = f"demo_tenant_{i+1}" if config["tenants"] > 1 else "demo_tenant"

                logger.info(f"🏢 创建租户 {i+1}/{config['tenants']}: {tenant_name}")

                # 1. 创建租户
                tenant, _ = create_tenant(session, tenant_name, tenant_code)

                # 2. 创建管理员用户
                admin_username = f"demo_admin_{i+1}" if config["tenants"] > 1 else "demo_admin"
                admin, _ = create_admin_user(session, tenant.id, admin_username)

                # 3. 创建会员卡模板
                templates = create_card_templates(session, tenant.id, config["card_templates_per_tenant"])

                # 4. 创建教师
                teachers = create_teachers(session, tenant.id, config["teachers_per_tenant"])

                # 5. 创建会员和会员卡
                members_with_cards = create_members_with_cards(
                    session, tenant.id, config["members_per_tenant"], templates[0]
                )

                # 6. 创建课程配置
                course_config, _ = create_course_config(session, tenant.id)

                # 7. 创建标签分类和标签
                tags_by_category = create_tag_categories_and_tags(
                    session, tenant.id,
                    config["tag_categories_per_tenant"],
                    config["tags_per_category"]
                )

                # 记录结果（保存需要的信息，避免 session 关闭后访问对象属性）
                tenant_result = {
                    "tenant": {"id": tenant.id, "name": tenant.name, "code": tenant.code},
                    "admin": {"id": admin.id, "username": admin.username, "real_name": admin.real_name},
                    "templates": [{"id": t.id, "name": t.name} for t in templates],
                    "teachers": [{"id": t.id, "name": t.name} for t in teachers],
                    "members_with_cards": [{"member": {"id": m.id, "name": m.name}, "card": {"id": c.id, "card_number": c.card_number}} for m, c in members_with_cards],
                    "course_config": {"id": course_config.id},
                    "tags_by_category": {cat: [{"id": tag.id, "name": tag.name} for tag in tags] for cat, tags in tags_by_category.items()},
                }
                results["tenants"].append(tenant_result)

                # 更新统计
                results["stats"]["tenants"] += 1
                results["stats"]["users"] += 1
                results["stats"]["teachers"] += len(teachers)
                results["stats"]["members"] += len(members_with_cards)
                results["stats"]["member_cards"] += len(members_with_cards)
                results["stats"]["card_templates"] += len(templates)
                results["stats"]["tag_categories"] += len(tags_by_category)
                results["stats"]["tags"] += sum(len(tags) for tags in tags_by_category.values())
                results["stats"]["course_configs"] += 1

                # 提交当前租户的所有数据
                session.commit()
                logger.info(f"✅ 租户 {tenant_name} 创建完成")
                logger.info("-" * 40)

            # 在关闭 session 前保存需要的信息
            login_info = None
            if results["tenants"]:
                first_tenant = results["tenants"][0]
                login_info = {
                    "tenant_name": first_tenant['tenant']['name'],
                    "username": first_tenant['admin']['username'],
                }

            session.close()

            # 输出统计信息
            logger.info("=" * 60)
            logger.info("📊 演示数据创建统计")
            logger.info("=" * 60)
            for key, value in results["stats"].items():
                logger.info(f"  {key}: {value}")
            logger.info("=" * 60)
            logger.info("🎉 演示数据创建完成！")

            # 输出数据库连接信息
            logger.info("")
            logger.info("🔗 数据库连接信息:")
            logger.info(f"  - 测试数据库: {test_db_name}")
            logger.info(f"  - 连接URL: {get_test_database_url(test_db_name)}")

            # 输出登录信息
            if login_info:
                logger.info("")
                logger.info("💡 登录信息:")
                logger.info(f"  - 租户: {login_info['tenant_name']}")
                logger.info(f"  - 用户名: {login_info['username']}")
                logger.info(f"  - 密码: demo123456")
                logger.info(f"  - 角色: 管理员")

            return results

    except Exception as e:
        if 'session' in locals():
            session.rollback()
            session.close()
        logger.error(f"❌ 创建演示数据失败: {e}")
        raise

# ==================== 便捷函数 ====================

def create_basic_demo(test_db_name: str = "ks_english_test_demo", force: bool = False):
    """创建基础演示数据"""
    return create_demo_data("basic", test_db_name, force)

def create_full_demo(test_db_name: str = "ks_english_test_demo", force: bool = False):
    """创建完整演示数据"""
    return create_demo_data("full", test_db_name, force)

def create_minimal_demo(test_db_name: str = "ks_english_test_demo", force: bool = False):
    """创建最小演示数据"""
    return create_demo_data("minimal", test_db_name, force)

def create_custom_demo(tenants=1, teachers_per_tenant=3, members_per_tenant=5, test_db_name: str = "ks_english_test_demo", force: bool = False, **kwargs):
    """创建自定义演示数据"""
    custom_config = {
        "description": "自定义演示数据",
        "tenants": tenants,
        "users_per_tenant": 1,
        "teachers_per_tenant": teachers_per_tenant,
        "members_per_tenant": members_per_tenant,
        "card_templates_per_tenant": 3,
        "tag_categories_per_tenant": 2,
        "tags_per_category": 3,
        **kwargs
    }

    # 临时添加到场景配置
    SCENARIOS["custom"] = custom_config
    try:
        return create_demo_data("custom", test_db_name, force)
    finally:
        # 清理临时配置
        SCENARIOS.pop("custom", None)

# ==================== 命令行接口 ====================

def main():
    """命令行主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="演示数据创建脚本 V2")
    parser.add_argument(
        "scenario",
        nargs="?",
        default="basic",
        choices=list(SCENARIOS.keys()) + ["custom"],
        help="演示数据场景 (默认: basic)"
    )
    parser.add_argument("--tenants", type=int, help="租户数量 (仅用于 custom 场景)")
    parser.add_argument("--teachers", type=int, help="每个租户的教师数量 (仅用于 custom 场景)")
    parser.add_argument("--members", type=int, help="每个租户的会员数量 (仅用于 custom 场景)")
    parser.add_argument("--test-db", type=str, default="ks_english_test_demo", help="测试数据库名称")
    parser.add_argument("--force", action="store_true", help="跳过确认提示，强制执行")
    parser.add_argument("--list-scenarios", action="store_true", help="列出所有可用场景")

    args = parser.parse_args()

    if args.list_scenarios:
        print("可用的演示数据场景:")
        print("=" * 50)
        for name, config in SCENARIOS.items():
            print(f"📋 {name}: {config['description']}")
            print(f"   - 租户: {config['tenants']}")
            print(f"   - 教师/租户: {config['teachers_per_tenant']}")
            print(f"   - 会员/租户: {config['members_per_tenant']}")
            print()
        print("💡 使用方法:")
        print("   python create_demo_data_v2.py basic")
        print("   python create_demo_data_v2.py full --test-db my_test_db")
        print("   python create_demo_data_v2.py custom --tenants 2 --teachers 5 --members 10 --force")
        print("   python create_demo_data_v2.py minimal --test-db ks_test --force")
        return

    try:
        if args.scenario == "custom":
            kwargs = {}
            if args.tenants:
                kwargs["tenants"] = args.tenants
            if args.teachers:
                kwargs["teachers_per_tenant"] = args.teachers
            if args.members:
                kwargs["members_per_tenant"] = args.members

            if not kwargs:
                print("❌ 使用 custom 场景时，请至少指定一个参数 (--tenants, --teachers, --members)")
                return

            result = create_custom_demo(test_db_name=args.test_db, force=args.force, **kwargs)
        else:
            result = create_demo_data(args.scenario, args.test_db, args.force)

        print("\n🎯 使用说明:")
        print("  1. 使用上述账号登录系统进行功能测试")
        print("  2. 系统已包含基础的会员卡模板和课程配置")
        print("  3. 可以在此基础上添加更多业务数据")

    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
