"""
全局测试配置和fixture导入

这个文件负责：
1. 全局pytest配置
2. 导入所有分离的fixtures
3. 提供全局常量和配置
"""
import pytest
import os
import warnings
from typing import Generator
from fastapi.testclient import TestClient
from sqlmodel import SQLModel, create_engine, Session
from sqlalchemy import text

from app.main import app
from app.db.session import get_global_session
from app.core.config import settings

# 过滤特定警告
# warnings.filterwarnings("ignore", message="'crypt' is deprecated and slated for removal in Python 3.13")

# 测试数据库配置
TEST_POSTGRES_URL = "postgresql://admin_alice:123qwe1985alice@localhost:5432/course_booking_test_db"
TEST_DATABASE_URL = TEST_POSTGRES_URL

# 注意：不在这里直接导入fixtures，而是通过pytest_plugins机制加载
# 这样可以避免PytestAssertRewriteWarning警告

# pytest配置
pytest_plugins = [
    "tests.fixtures.database",
    "tests.fixtures.client",
    "tests.fixtures.business.tenant",
    "tests.fixtures.business.user",
    "tests.fixtures.business.member",
    "tests.fixtures.business.member_card",
    "tests.fixtures.business.tag",
    "tests.fixtures.business.teacher",
    "tests.fixtures.business.course_config",
    "tests.fixtures.business.scheduled_class",
    "tests.fixtures.business.fixed_schedule_task",
    "tests.fixtures.business.teacher_fixed_slot",
    "tests.fixtures.business.member_fixed_slot_lock",
    "tests.performance.utils.benchmark_fixtures",  # 性能测试fixtures
]

# 全局测试配置
def pytest_configure(config):
    """pytest配置钩子"""
    # 添加自定义标记
    config.addinivalue_line("markers", "unit: 单元测试")
    config.addinivalue_line("markers", "integration: 集成测试")
    config.addinivalue_line("markers", "e2e: 端到端测试")
    config.addinivalue_line("markers", "performance: 性能测试")
    config.addinivalue_line("markers", "benchmark: 性能基准测试")
    config.addinivalue_line("markers", "concurrent: 并发测试")
    config.addinivalue_line("markers", "stress: 压力测试")
    config.addinivalue_line("markers", "load: 负载测试")
    config.addinivalue_line("markers", "slow: 慢速测试")

    # 过滤警告
    # warnings.filterwarnings("ignore", category=DeprecationWarning, message="'crypt' is deprecated")


def pytest_collection_modifyitems(config, items):
    """修改测试收集项"""
    # 为不同目录的测试自动添加标记
    for item in items:
        if "unit/" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration/" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "e2e/" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
        elif "performance/" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
            # 为性能测试子目录添加更具体的标记
            if "performance/api/" in str(item.fspath):
                item.add_marker(pytest.mark.benchmark)
            elif "performance/concurrent/" in str(item.fspath):
                item.add_marker(pytest.mark.concurrent)
            elif "performance/database/" in str(item.fspath):
                item.add_marker(pytest.mark.benchmark)


# 注意：所有fixtures已经移动到tests/fixtures/目录下的独立文件中
# 这里只保留必要的导入和配置

