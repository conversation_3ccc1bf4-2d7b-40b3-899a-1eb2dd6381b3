[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    -p no:warnings
markers =
    unit: 单元测试
    integration: 集成测试
    api: API测试
    auth: 认证相关测试
    manual: 手动测试场景
    slow: 慢速测试
    database: 需要数据库的测试
    postgres: 使用PostgreSQL数据库的测试
    performance: 性能测试
    benchmark: 性能基准测试
    concurrent: 并发测试
    stress: 压力测试
    load: 负载测试
filterwarnings =
    # 保留重要的警告，但过滤已知且无法修复的特定警告
    ignore:transaction already deassociated from connection:sqlalchemy.exc.SAWarning
    ignore:.*'crypt' is deprecated.*:DeprecationWarning
    ignore:datetime.datetime.utcnow.*:DeprecationWarning:pydantic.*
    ignore:datetime.datetime.utcnow.*:DeprecationWarning:jose.*