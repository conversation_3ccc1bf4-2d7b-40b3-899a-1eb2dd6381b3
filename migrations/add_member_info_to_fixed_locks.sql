-- 添加会员信息冗余字段到MemberFixedSlotLock表
-- 创建时间: 2024-07-28
-- 目标: 在MemberFixedSlotLock表中添加member_name和member_phone冗余字段

-- ==================== 开始事务 ====================
BEGIN;

-- ==================== 添加新字段 ====================

-- 添加会员姓名字段
ALTER TABLE member_fixed_slot_locks 
ADD COLUMN member_name VARCHAR(50);

-- 添加会员手机号字段
ALTER TABLE member_fixed_slot_locks 
ADD COLUMN member_phone VARCHAR(20);

-- ==================== 同步现有数据 ====================

-- 从members表同步会员信息到现有的锁定记录
UPDATE member_fixed_slot_locks 
SET 
    member_name = m.name,
    member_phone = m.phone,
    updated_at = NOW()
FROM members m 
WHERE member_fixed_slot_locks.member_id = m.id
  AND member_fixed_slot_locks.tenant_id = m.tenant_id;

-- ==================== 更新索引 ====================

-- 删除旧的覆盖索引
DROP INDEX IF EXISTS idx_member_locks_teacher_slot_full;

-- 重新创建包含会员信息的覆盖索引
CREATE INDEX CONCURRENTLY idx_member_locks_teacher_slot_full 
ON member_fixed_slot_locks(tenant_id, teacher_fixed_slot_id)
INCLUDE (member_id, member_name, member_phone, locked_at);

-- ==================== 创建数据同步函数 ====================

-- 创建触发器函数，当members表更新时自动同步到锁定记录
CREATE OR REPLACE FUNCTION sync_member_info_to_locks()
RETURNS TRIGGER AS $$
BEGIN
    -- 当会员姓名或手机号发生变化时，同步到锁定记录
    IF OLD.name IS DISTINCT FROM NEW.name OR OLD.phone IS DISTINCT FROM NEW.phone THEN
        UPDATE member_fixed_slot_locks 
        SET 
            member_name = NEW.name,
            member_phone = NEW.phone,
            updated_at = NOW()
        WHERE member_id = NEW.id 
          AND tenant_id = NEW.tenant_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS trigger_sync_member_info_to_locks ON members;
CREATE TRIGGER trigger_sync_member_info_to_locks
    AFTER UPDATE ON members
    FOR EACH ROW
    EXECUTE FUNCTION sync_member_info_to_locks();

-- ==================== 创建数据一致性检查函数 ====================

-- 创建函数检查会员信息一致性
CREATE OR REPLACE FUNCTION check_member_info_consistency()
RETURNS TABLE(
    lock_id INTEGER,
    member_id INTEGER,
    lock_member_name VARCHAR(50),
    actual_member_name VARCHAR(50),
    lock_member_phone VARCHAR(20),
    actual_member_phone VARCHAR(20),
    is_consistent BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.id as lock_id,
        l.member_id,
        l.member_name as lock_member_name,
        m.name as actual_member_name,
        l.member_phone as lock_member_phone,
        m.phone as actual_member_phone,
        (l.member_name = m.name AND l.member_phone = m.phone) as is_consistent
    FROM member_fixed_slot_locks l
    JOIN members m ON l.member_id = m.id AND l.tenant_id = m.tenant_id
    WHERE l.member_name != m.name OR l.member_phone != m.phone;
END;
$$ LANGUAGE plpgsql;

-- ==================== 验证数据一致性 ====================

-- 检查是否有不一致的数据
DO $$
DECLARE
    inconsistent_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO inconsistent_count
    FROM check_member_info_consistency()
    WHERE NOT is_consistent;
    
    IF inconsistent_count > 0 THEN
        RAISE NOTICE '发现 % 条不一致的记录，请检查数据', inconsistent_count;
    ELSE
        RAISE NOTICE '所有数据一致性检查通过';
    END IF;
END;
$$;

-- ==================== 提交事务 ====================
COMMIT;

-- ==================== 使用说明 ====================
/*
迁移完成后的使用说明：

1. 检查数据一致性：
   SELECT * FROM check_member_info_consistency();

2. 手动同步特定会员的信息：
   -- 在应用代码中调用
   service.sync_member_info(member_id)

3. 监控触发器执行：
   SELECT * FROM pg_stat_user_triggers WHERE schemaname = 'public';

4. 如果需要禁用自动同步触发器：
   DROP TRIGGER IF EXISTS trigger_sync_member_info_to_locks ON members;

5. 如果需要重新启用触发器：
   CREATE TRIGGER trigger_sync_member_info_to_locks
       AFTER UPDATE ON members
       FOR EACH ROW
       EXECUTE FUNCTION sync_member_info_to_locks();

注意事项：
- 触发器会在每次更新members表时自动同步数据
- 如果需要批量更新会员信息，建议先禁用触发器，更新完成后手动同步
- 定期运行一致性检查确保数据同步正常
*/
