#!/usr/bin/env python3
"""
数据库迁移脚本：为Tag表添加category_name冗余字段

这个脚本会：
1. 为tags表添加category_name字段
2. 从tag_categories表同步数据填充category_name字段
3. 验证数据完整性

运行方式：
python migrations/add_category_name_to_tags.py
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlmodel import Session, select, text, update
from app.core.database import get_engine
from app.features.tags.models import Tag, TagCategory


def add_category_name_column():
    """添加category_name列到tags表"""
    engine = get_engine()
    
    with engine.connect() as connection:
        # 检查列是否已存在
        result = connection.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'tags' AND column_name = 'category_name'
        """))
        
        if result.fetchone():
            print("✅ category_name列已存在，跳过创建")
            return
        
        # 添加category_name列
        print("📝 添加category_name列到tags表...")
        connection.execute(text("""
            ALTER TABLE tags 
            ADD COLUMN category_name VARCHAR(50)
        """))
        
        connection.commit()
        print("✅ category_name列添加成功")


def populate_category_name_data():
    """填充category_name数据"""
    engine = get_engine()
    
    with Session(engine) as session:
        print("📝 开始填充category_name数据...")
        
        # 获取所有标签分类
        categories = session.exec(select(TagCategory)).all()
        category_dict = {cat.id: cat.name for cat in categories}
        
        print(f"📊 找到 {len(categories)} 个标签分类")
        
        # 批量更新标签的category_name
        updated_count = 0
        for category_id, category_name in category_dict.items():
            # 更新该分类下的所有标签
            statement = (
                update(Tag)
                .where(Tag.category_id == category_id)
                .values(category_name=category_name)
            )
            result = session.exec(statement)
            updated_count += result.rowcount
            
        session.commit()
        print(f"✅ 成功更新 {updated_count} 个标签的category_name字段")


def verify_data_integrity():
    """验证数据完整性"""
    engine = get_engine()
    
    with Session(engine) as session:
        print("🔍 验证数据完整性...")
        
        # 检查是否有category_name为空的标签
        null_count = session.exec(
            select(Tag).where(Tag.category_name.is_(None))
        ).all()
        
        if null_count:
            print(f"⚠️  发现 {len(null_count)} 个标签的category_name为空")
            for tag in null_count:
                print(f"   - 标签ID: {tag.id}, 名称: {tag.name}, 分类ID: {tag.category_id}")
            return False
        
        # 检查category_name与实际分类名称是否一致
        inconsistent_tags = session.exec(
            select(Tag, TagCategory)
            .join(TagCategory, Tag.category_id == TagCategory.id)
            .where(Tag.category_name != TagCategory.name)
        ).all()
        
        if inconsistent_tags:
            print(f"⚠️  发现 {len(inconsistent_tags)} 个标签的category_name与实际分类名称不一致")
            for tag, category in inconsistent_tags:
                print(f"   - 标签ID: {tag.id}, category_name: {tag.category_name}, 实际分类名: {category.name}")
            return False
        
        # 统计总数
        total_tags = session.exec(select(Tag)).all()
        total_categories = session.exec(select(TagCategory)).all()
        
        print(f"✅ 数据完整性验证通过")
        print(f"📊 总标签数: {len(total_tags)}")
        print(f"📊 总分类数: {len(total_categories)}")
        
        return True


def rollback_migration():
    """回滚迁移（移除category_name列）"""
    engine = get_engine()
    
    print("⚠️  开始回滚迁移...")
    
    with engine.connect() as connection:
        # 检查列是否存在
        result = connection.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'tags' AND column_name = 'category_name'
        """))
        
        if not result.fetchone():
            print("✅ category_name列不存在，无需回滚")
            return
        
        # 移除category_name列
        connection.execute(text("""
            ALTER TABLE tags 
            DROP COLUMN category_name
        """))
        
        connection.commit()
        print("✅ category_name列已移除")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Tag表category_name字段迁移脚本")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")
    parser.add_argument("--verify-only", action="store_true", help="仅验证数据完整性")
    
    args = parser.parse_args()
    
    try:
        if args.rollback:
            rollback_migration()
            return
        
        if args.verify_only:
            verify_data_integrity()
            return
        
        print("🚀 开始Tag表category_name字段迁移...")
        print("=" * 50)
        
        # 1. 添加列
        add_category_name_column()
        
        # 2. 填充数据
        populate_category_name_data()
        
        # 3. 验证数据
        if verify_data_integrity():
            print("=" * 50)
            print("🎉 迁移完成！")
            print("\n📋 迁移总结:")
            print("   ✅ 添加了category_name列")
            print("   ✅ 填充了所有标签的category_name数据")
            print("   ✅ 验证了数据完整性")
            print("\n💡 现在可以享受优化后的查询性能了！")
        else:
            print("❌ 数据验证失败，请检查上述问题")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 迁移过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
