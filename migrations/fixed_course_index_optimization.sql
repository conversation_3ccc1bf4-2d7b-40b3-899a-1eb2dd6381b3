-- 固定课排课系统索引优化迁移脚本
-- 创建时间: 2024-07-28
-- 目标: 优化固定课排课系统的查询性能

-- ==================== 开始事务 ====================
BEGIN;

-- ==================== TeacherFixedSlot表索引优化 ====================

-- 删除旧的索引（如果存在）
DROP INDEX IF EXISTS idx_teacher_fixed_slots_weekday;
DROP INDEX IF EXISTS idx_teacher_fixed_slots_available;
DROP INDEX IF EXISTS idx_teacher_fixed_slots_visible;
DROP INDEX IF EXISTS idx_teacher_fixed_slots_teacher_weekday;
DROP INDEX IF EXISTS idx_teacher_fixed_slots_time_range;

-- 1. 按教师查询固定时段（管理端核心需求）
-- 支持: WHERE teacher_id = ? ORDER BY weekday, start_time
CREATE INDEX CONCURRENTLY idx_teacher_slots_teacher_schedule 
ON teacher_fixed_slots(teacher_id, weekday, start_time);

-- 2. 按时间范围筛选可用教师（管理端核心需求）
-- 支持: WHERE weekday IN (...) AND start_time BETWEEN ... AND is_available = true
CREATE INDEX CONCURRENTLY idx_teacher_slots_time_available 
ON teacher_fixed_slots(weekday, start_time, is_available, is_visible_to_members);

-- 3. 租户级别的时间段查询
-- 支持: WHERE tenant_id = ? AND weekday = ? AND start_time >= ?
CREATE INDEX CONCURRENTLY idx_teacher_slots_tenant_time 
ON teacher_fixed_slots(tenant_id, weekday, start_time);

-- 4. 可用性状态查询
-- 支持: WHERE tenant_id = ? AND is_available = ? AND is_visible_to_members = ?
CREATE INDEX CONCURRENTLY idx_teacher_slots_tenant_availability 
ON teacher_fixed_slots(tenant_id, is_available, is_visible_to_members);

-- 5. 教师时间段完整信息覆盖索引（PostgreSQL特性）
-- 包含查询中需要的所有字段，避免回表
CREATE INDEX CONCURRENTLY idx_teacher_slots_full_info 
ON teacher_fixed_slots(teacher_id, weekday, start_time) 
INCLUDE (id, duration_minutes, is_available, is_visible_to_members);

-- 6. 时间范围查询覆盖索引
-- 支持按时间筛选教师的复杂查询
CREATE INDEX CONCURRENTLY idx_teacher_slots_time_range_cover 
ON teacher_fixed_slots(weekday, start_time, is_available, is_visible_to_members)
INCLUDE (teacher_id, id, duration_minutes);

-- ==================== MemberFixedSlotLock表索引优化 ====================

-- 删除旧的索引（如果存在）
DROP INDEX IF EXISTS idx_member_locks_teacher_time;
DROP INDEX IF EXISTS idx_member_locks_status;
DROP INDEX IF EXISTS idx_member_locks_teacher;
DROP INDEX IF EXISTS idx_member_locks_weekday;

-- 1. 会员课表查询（会员端核心需求）
-- 支持: WHERE member_id = ? AND status = 'ACTIVE' ORDER BY weekday, start_time
CREATE INDEX CONCURRENTLY idx_member_locks_schedule 
ON member_fixed_slot_locks(member_id, status, weekday, start_time);

-- 2. 教师时段锁定状态查询（管理端核心需求）
-- 支持: WHERE teacher_fixed_slot_id = ? AND status = 'ACTIVE'
CREATE INDEX CONCURRENTLY idx_member_locks_slot_status 
ON member_fixed_slot_locks(teacher_fixed_slot_id, status);

-- 3. 按教师查询所有锁定（管理端需求）
-- 支持: WHERE teacher_id = ? AND status = 'ACTIVE'
CREATE INDEX CONCURRENTLY idx_member_locks_teacher_active 
ON member_fixed_slot_locks(teacher_id, status, weekday, start_time);

-- 4. 租户级别的锁定查询
-- 支持: WHERE tenant_id = ? AND status = ?
CREATE INDEX CONCURRENTLY idx_member_locks_tenant_status 
ON member_fixed_slot_locks(tenant_id, status);

-- 5. 时间范围锁定查询
-- 支持: WHERE weekday IN (...) AND start_time BETWEEN ... AND status = 'ACTIVE'
CREATE INDEX CONCURRENTLY idx_member_locks_time_range 
ON member_fixed_slot_locks(weekday, start_time, status);

-- 6. 会员课表完整信息覆盖索引
-- 避免JOIN members表获取会员信息
CREATE INDEX CONCURRENTLY idx_member_locks_schedule_full 
ON member_fixed_slot_locks(member_id, status, weekday, start_time)
INCLUDE (teacher_id, teacher_fixed_slot_id, locked_at);

-- 7. 教师时段锁定信息覆盖索引
-- 支持管理端查询教师时段占用状态，避免多次JOIN
CREATE INDEX CONCURRENTLY idx_member_locks_teacher_slot_full 
ON member_fixed_slot_locks(teacher_fixed_slot_id, status)
INCLUDE (member_id, teacher_id, weekday, start_time, locked_at);

-- 8. 按教师查询锁定状态覆盖索引
-- 支持: 查询教师的所有时段锁定情况
CREATE INDEX CONCURRENTLY idx_member_locks_teacher_full 
ON member_fixed_slot_locks(teacher_id, status, weekday, start_time)
INCLUDE (member_id, teacher_fixed_slot_id, locked_at);

-- ==================== 创建查询性能测试视图 ====================

-- 创建一个视图来测试优化后的查询性能
CREATE OR REPLACE VIEW v_teacher_schedule_with_locks AS
SELECT 
    t.id as slot_id,
    t.teacher_id,
    t.weekday,
    t.start_time,
    t.duration_minutes,
    t.is_available,
    t.is_visible_to_members,
    m.member_id,
    m.status as lock_status,
    m.locked_at,
    CASE 
        WHEN m.member_id IS NOT NULL AND m.status = 'ACTIVE' THEN 'LOCKED'
        WHEN t.is_available = false THEN 'UNAVAILABLE'
        WHEN t.is_visible_to_members = false THEN 'HIDDEN'
        ELSE 'AVAILABLE'
    END as slot_status
FROM teacher_fixed_slots t
LEFT JOIN member_fixed_slot_locks m 
    ON t.id = m.teacher_fixed_slot_id 
    AND m.status = 'ACTIVE';

-- 创建会员课表视图
CREATE OR REPLACE VIEW v_member_schedule AS
SELECT 
    m.member_id,
    m.weekday,
    m.start_time,
    m.teacher_id,
    m.locked_at,
    t.duration_minutes,
    t.id as slot_id
FROM member_fixed_slot_locks m
JOIN teacher_fixed_slots t ON t.id = m.teacher_fixed_slot_id
WHERE m.status = 'ACTIVE';

-- ==================== 添加索引使用统计 ====================

-- 创建一个函数来检查索引使用情况
CREATE OR REPLACE FUNCTION check_fixed_course_index_usage()
RETURNS TABLE(
    table_name text,
    index_name text,
    index_size text,
    index_scans bigint,
    tuples_read bigint,
    tuples_fetched bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        schemaname||'.'||tablename as table_name,
        indexname as index_name,
        pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
        idx_scan as index_scans,
        idx_tup_read as tuples_read,
        idx_tup_fetch as tuples_fetched
    FROM pg_stat_user_indexes 
    WHERE schemaname = 'public' 
      AND (tablename = 'teacher_fixed_slots' OR tablename = 'member_fixed_slot_locks')
      AND indexname LIKE '%teacher_slots%' OR indexname LIKE '%member_locks%'
    ORDER BY tablename, idx_scan DESC;
END;
$$ LANGUAGE plpgsql;

-- ==================== 提交事务 ====================
COMMIT;

-- ==================== 使用说明 ====================
/*
索引创建完成后，可以使用以下查询来验证性能：

1. 测试按教师查询时间表：
   SELECT * FROM v_teacher_schedule_with_locks WHERE teacher_id = 1;

2. 测试按时间筛选教师：
   SELECT DISTINCT teacher_id 
   FROM teacher_fixed_slots 
   WHERE weekday BETWEEN 1 AND 3 
     AND start_time BETWEEN '10:00' AND '14:00'
     AND is_available = true;

3. 测试会员课表查询：
   SELECT * FROM v_member_schedule WHERE member_id = 1;

4. 检查索引使用情况：
   SELECT * FROM check_fixed_course_index_usage();

5. 查看查询执行计划：
   EXPLAIN (ANALYZE, BUFFERS) SELECT ...;
*/
