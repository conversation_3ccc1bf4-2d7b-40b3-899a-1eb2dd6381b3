# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Development Commands

### Quick Start

```bash
# 开发环境快速启动 (推荐)
python quick_start.py

# 手动启动
uvicorn app.main:app --host 0.0.0.0 --port 8012 --reload

# 生产环境启动
python run.py
```

### Testing Commands

```bash
# 统一测试脚本 (推荐使用)
python scripts/test.py unit          # 单元测试
python scripts/test.py api           # API集成测试
python scripts/test.py integration   # 所有集成测试
python scripts/test.py e2e           # 端到端测试
python scripts/test.py coverage      # 覆盖率测试
python scripts/test.py quick         # 快速测试(跳过慢速测试)
python scripts/test.py manual        # 手动测试场景

# 运行特定测试
python scripts/test.py unit -k "test_create"
python scripts/test.py api -k "teacher"

# 测试配置选项
python scripts/test.py unit --enable-route-logging  # 启用路由日志
python scripts/test.py api --keep-test-data         # 保留测试数据
```

### Database Management

```bash
# 数据库初始化 (自动在应用启动时执行)
python scripts/init_database.py
```

## 🏗️ Architecture Overview

### Multi-tenant SaaS Architecture

- **Multi-tenancy Strategy**: 共享数据库 + RLS行级安全 (PostgreSQL)
- **Core Entities**: Tenant (机构) → User (管理员) → Member (客户)
- **Database**: PostgreSQL with Row Level Security (RLS)
- **Framework**: FastAPI + SQLModel + Pydantic
- **Authentication**: JWT with tenant context

### Project Structure

```
app/
├── main.py                 # FastAPI应用入口，生命周期管理
├── core/                   # 核心组件
│   ├── config.py          # 配置管理
│   ├── dependencies.py    # 依赖注入
│   ├── security.py        # 安全工具
│   ├── context.py         # 上下文管理
│   └── logging.py         # 日志配置
├── db/                     # 数据库层
│   ├── base.py            # 数据库基础配置
│   ├── session.py         # 会话管理
│   └── rls_v2.py          # RLS策略管理
├── features/               # 垂直分层业务模块
│   ├── base/              # 基础服务类
│   │   ├── base_service.py     # 通用CRUD基类
│   │   ├── query_utils.py      # 查询工具
│   │   └── example_service.py  # 服务示例
│   ├── auth/              # 认证模块
│   ├── tenants/           # 租户管理
│   ├── users/             # 用户管理
│   ├── members/           # 会员管理
│   ├── teachers/          # 教师管理
│   ├── tags/              # 标签管理
│   ├── courses/           # 课程系统
│   └── member_cards/      # 会员卡系统
├── api/                   # API路由
│   ├── common/            # 通用API组件
│   │   ├── responses.py   # 统一响应格式
│   │   ├── exceptions.py  # 异常处理
│   │   ├── pagination.py  # 分页工具
│   │   └── docs.py        # API文档配置
│   └── v1/                # API v1
│       ├── api.py         # 路由聚合
│       ├── admin/         # 管理端API
│       ├── member/        # 会员端API
│       └── public/        # 公共API
├── models/                # 数据模型
│   └── shared/            # 共享模型
│       ├── audit.py       # 审计字段
│       ├── system.py      # 系统字段
│       └── usage_stats.py # 使用统计
└── utils/                 # 工具函数
```

### Feature Module Pattern

每个业务模块采用垂直分层架构：

- `models.py` - SQLModel数据库模型
- `schemas.py` - Pydantic API请求/响应模型
- `service.py` - 业务逻辑服务层
- `exceptions.py` - 模块特定异常

**注意**: API路由已迁移到`app/api/v1/`下，按角色分离：
- `app/api/v1/admin/` - 管理端API
- `app/api/v1/member/` - 会员端API  
- `app/api/v1/public/` - 公共API

## 🧪 Testing Architecture

### Test Structure

```
tests/
├── unit/                  # 单元测试 (业务逻辑)
│   └── features/          # 按业务模块组织
├── integration/           # 集成测试
│   ├── api/v1/           # API端点测试
│   │   ├── admin/        # 管理端API测试
│   │   ├── member/       # 会员端API测试
│   │   └── public/       # 公共API测试
│   └── database/         # 数据库集成测试
├── e2e/                  # 端到端测试
│   └── scenarios/        # 业务场景测试
├── fixtures/             # 测试夹具
│   ├── database.py       # 数据库fixtures
│   ├── client.py         # API客户端fixtures
│   └── business/         # 业务实体fixtures
└── conftest.py           # 全局测试配置
```

### Test Configuration

- **Database**: PostgreSQL测试数据库
- **Isolation**: 每个测试独立事务回滚
- **Fixtures**: 丰富的业务数据fixtures
- **Markers**: `@pytest.mark.unit`, `@pytest.mark.api`, `@pytest.mark.slow`

## 🔑 Key Development Patterns

### Multi-tenant Data Isolation

- **RLS Policies**: 所有业务表启用行级安全
- **Tenant Context**: 服务层自动设置租户上下文
- **Base Service**: 统一的BaseService处理CRUD和租户过滤
- **Super Admin**: 支持全局管理员模式

### Authentication & Authorization

- **JWT Authentication**: 基于JWT的无状态认证
- **Tenant Context**: Token包含租户信息
- **Role-based Access**: 管理员、会员分离的权限体系
- **Multi-endpoint Auth**: 管理端和会员端独立认证

### Service Layer Pattern

```python
# 基础服务使用示例
class UserService(TenantAwareService[User]):
    model_class = User
    
    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)
    
    def create_user(self, user_data: UserCreate) -> User:
        return self.create(user_data)
```

### Error Handling

- **统一异常处理**: 全局异常捕获和标准化响应
- **业务异常**: 模块特定异常类型
- **API响应格式**: `{"success": bool, "data": any, "message": str}`

### Database Models

- **SQLModel**: 类型安全的ORM，结合Pydantic验证
- **Audit Fields**: 标准化的审计字段（created_at, updated_at, tenant_id）
- **RLS Support**: 内置租户隔离支持

## 🔧 Development Configuration

### Environment Variables

```bash
DATABASE_URL=postgresql://user:pass@localhost:5432/course_booking_db
SECRET_KEY=your-secret-key
DEBUG=True
ENABLE_ROUTE_LOGGING=false  # 开发时可启用路由日志
```

### Common Development Tasks

1. **添加新业务模块**:
   - 在`app/features/`创建模块目录
   - 实现models, schemas, service, exceptions
   - 在`app/api/v1/admin/`或`member/`或`public/`添加API路由
   - 在`app/api/v1/api.py`中注册路由
   - 编写单元测试和API测试

2. **数据库模型变更**:
   - 修改SQLModel模型
   - 应用启动时自动同步表结构
   - 生产环境建议使用Alembic迁移

3. **API开发**:
   - 遵循RESTful规范
   - 使用统一响应格式
   - 添加适当的错误处理
   - 编写API文档

## 📊 Testing Guidelines

### Test Commands Reference

```bash
# 日常开发测试
python scripts/test.py unit     # 快速单元测试
python scripts/test.py api      # API集成测试

# 特定模块测试
python scripts/test.py unit -k "member"
python scripts/test.py api -k "auth"

# 调试相关
python scripts/test.py unit --enable-route-logging
python scripts/test.py api --keep-test-data
```

### Test Writing

- **Unit Tests**: 测试业务逻辑，使用mock隔离依赖
- **API Tests**: 测试端点功能，使用真实数据库
- **Fixtures**: 使用`tests/fixtures/business/`中的数据工厂
- **Isolation**: 每个测试独立，使用事务回滚

## 🚨 Important Notes

### 开发约定

- **不要运行全量测试**: 除非明确指示，优先使用`unit`和`api`测试
- **路由日志**: 开发时可用`--enable-route-logging`调试路由匹配
- **数据库**: 测试使用独立的PostgreSQL数据库
- **多租户**: 所有业务操作必须考虑租户隔离

### 性能考虑

- **RLS策略**: 数据库层自动处理租户过滤
- **连接池**: 使用SQLAlchemy连接池管理
- **异步支持**: FastAPI原生异步支持

### 安全要求

- **JWT安全**: 生产环境使用强密钥
- **数据隔离**: RLS确保租户数据完全隔离
- **密码加密**: 使用bcrypt加密用户密码
- **API验证**: 所有输入使用Pydantic验证

## 📝 API Documentation

- **OpenAPI文档**: http://localhost:8012/docs
- **ReDoc**: http://localhost:8012/redoc
- **健康检查**: http://localhost:8012/health

API按业务模块组织，支持管理端、会员端和公共接口的分离访问。