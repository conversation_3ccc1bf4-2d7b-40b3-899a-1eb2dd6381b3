/**
 * API客户端基础类
 * 提供类型安全的API调用方法
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// 基础响应类型
export interface BaseResponse<T = any> {
  success: boolean;
  message: string;
  http_code: number;
  business_code: string;
  data?: T;
  timestamp?: string;
}

export interface PageResponse<T = any> extends BaseResponse<T[]> {
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface ListResponse<T = any> extends BaseResponse<T[]> {
  total: number;
}

// 错误响应类型
export interface ErrorResponse extends BaseResponse {
  success: false;
  level: 'error' | 'warning' | 'info';
  details?: Record<string, any>;
}

// API客户端配置
export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
  onTokenExpired?: () => void;
  onError?: (error: any) => void;
}

/**
 * API客户端基础类
 */
export class ApiClient {
  private instance: AxiosInstance;
  private config: ApiClientConfig;

  constructor(config: ApiClientConfig) {
    this.config = config;
    this.instance = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 10000,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers
      }
    });

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 自动添加认证token
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        const data = response.data;
        
        // 检查业务逻辑错误
        if (data && !data.success) {
          const error = new ApiError(data.message, data.business_code, data);
          return Promise.reject(error);
        }
        
        return response;
      },
      (error) => {
        // 处理HTTP错误
        if (error.response) {
          const { status, data } = error.response;
          
          // Token过期处理
          if (status === 401) {
            this.handleTokenExpired();
            return Promise.reject(new ApiError('认证失败，请重新登录', 'AUTHENTICATION_FAILED'));
          }
          
          // 权限不足
          if (status === 403) {
            return Promise.reject(new ApiError('权限不足', 'PERMISSION_DENIED'));
          }
          
          // 业务错误
          if (data && !data.success) {
            return Promise.reject(new ApiError(data.message, data.business_code, data));
          }
        }
        
        // 网络错误
        if (error.code === 'NETWORK_ERROR') {
          return Promise.reject(new ApiError('网络连接失败', 'NETWORK_ERROR'));
        }
        
        // 超时错误
        if (error.code === 'ECONNABORTED') {
          return Promise.reject(new ApiError('请求超时', 'TIMEOUT'));
        }
        
        return Promise.reject(error);
      }
    );
  }

  /**
   * 获取存储的token
   */
  private getToken(): string | null {
    return localStorage.getItem('access_token');
  }

  /**
   * 处理token过期
   */
  private handleTokenExpired() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    
    if (this.config.onTokenExpired) {
      this.config.onTokenExpired();
    }
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.get(url, config);
    return response.data.data;
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.post(url, data, config);
    return response.data.data;
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.put(url, data, config);
    return response.data.data;
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.instance.delete(url, config);
    return response.data.data;
  }

  /**
   * 分页请求
   */
  async getPage<T = any>(url: string, params?: Record<string, any>): Promise<PageResponse<T>> {
    const response = await this.instance.get(url, { params });
    return response.data;
  }

  /**
   * 列表请求
   */
  async getList<T = any>(url: string, params?: Record<string, any>): Promise<ListResponse<T>> {
    const response = await this.instance.get(url, { params });
    return response.data;
  }

  /**
   * 上传文件
   */
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.instance.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      }
    });

    return response.data.data;
  }

  /**
   * 设置认证token
   */
  setToken(token: string) {
    localStorage.setItem('access_token', token);
  }

  /**
   * 清除认证token
   */
  clearToken() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }
}

/**
 * API错误类
 */
export class ApiError extends Error {
  public code: string;
  public details?: any;

  constructor(message: string, code: string = 'UNKNOWN_ERROR', details?: any) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.details = details;
  }
}

// 默认API客户端实例
export const apiClient = new ApiClient({
  baseURL: process.env.VUE_APP_API_BASE_URL || 'http://localhost:3000/api/v1',
  timeout: 10000,
  onTokenExpired: () => {
    // 可以在这里处理token过期逻辑，比如跳转到登录页
    console.warn('Token已过期，请重新登录');
  }
});

/**
 * 创建类型安全的API服务工厂
 */
export function createApiService<T extends Record<string, any>>(client: ApiClient) {
  return {
    /**
     * 类型安全的GET请求
     */
    get: <K extends keyof T>(
      path: K,
      params?: T[K] extends { parameters: { query: infer Q } } ? Q : never
    ): Promise<T[K] extends { responses: { 200: { content: { 'application/json': infer R } } } } ? R : any> => {
      return client.get(path as string, { params });
    },

    /**
     * 类型安全的POST请求
     */
    post: <K extends keyof T>(
      path: K,
      data?: T[K] extends { requestBody: { content: { 'application/json': infer B } } } ? B : never
    ): Promise<T[K] extends { responses: { 200: { content: { 'application/json': infer R } } } } ? R : any> => {
      return client.post(path as string, data);
    },

    /**
     * 类型安全的PUT请求
     */
    put: <K extends keyof T>(
      path: K,
      data?: T[K] extends { requestBody: { content: { 'application/json': infer B } } } ? B : never
    ): Promise<T[K] extends { responses: { 200: { content: { 'application/json': infer R } } } } ? R : any> => {
      return client.put(path as string, data);
    },

    /**
     * 类型安全的DELETE请求
     */
    delete: <K extends keyof T>(
      path: K
    ): Promise<T[K] extends { responses: { 200: { content: { 'application/json': infer R } } } } ? R : any> => {
      return client.delete(path as string);
    }
  };
}
