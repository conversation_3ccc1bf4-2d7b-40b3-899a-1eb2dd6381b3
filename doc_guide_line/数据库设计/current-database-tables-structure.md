# 当前数据库表结构详细定义

## 概述

本文档以列表形式详细描述当前系统中所有数据库表的结构定义，包括字段类型、约束、外键关系和枚举值定义。

## 核心实体表概览

| 表名                       | 图标 | 说明             | 主要字段数 | 模块       |
| -------------------------- | ---- | ---------------- | ---------- | ---------- |
| **tenants**                | 🏢   | 租户表           | 25+        | 全局管理   |
| **users**                  | 👤   | 用户表           | 15+        | 用户认证   |
| **members**                | 👥   | 会员表           | 30+        | 会员管理   |
| **teachers**               | 🎓   | 教师表           | 25+        | 教师管理   |
| **scheduled_classes**      | 🕐   | 已排课表         | 20+        | 课程调度   |
| **member_cards**           | 💳   | 会员卡表         | 15+        | 会员卡管理 |
| **member_card_operations** | 📝   | 会员卡操作记录表 | 25+        | 会员卡管理 |
| **teacher_fixed_slots**    | 📅   | 教师固定时间段表 | 10+        | 课程调度   |

## 详细表结构定义

### 1. tenants (租户表)

| 字段名                  | 类型         | 约束             | 默认值            | 说明             |
| ----------------------- | ------------ | ---------------- | ----------------- | ---------------- |
| id                      | int          | PK               | AUTO_INCREMENT    | 主键             |
| name                    | varchar(100) | NOT NULL         | -                 | 机构名称         |
| code                    | varchar(50)  | UNIQUE, NOT NULL | -                 | 机构代码         |
| display_name            | varchar(100) | -                | NULL              | 显示名称         |
| description             | text         | -                | NULL              | 机构描述         |
| domain                  | varchar(100) | UNIQUE           | NULL              | 自定义域名       |
| subdomain               | varchar(50)  | UNIQUE           | NULL              | 子域名           |
| logo_url                | varchar(500) | -                | NULL              | 机构 logo        |
| favicon_url             | varchar(500) | -                | NULL              | 网站图标         |
| contact_name            | varchar(50)  | -                | NULL              | 联系人姓名       |
| contact_phone           | varchar(20)  | -                | NULL              | 联系电话         |
| contact_email           | varchar(100) | -                | NULL              | 联系邮箱         |
| address                 | text         | -                | NULL              | 机构地址         |
| plan_type               | enum         | NOT NULL         | 'TRIAL'           | 套餐类型         |
| max_teachers            | int          | NOT NULL         | 5                 | 最大教师数量     |
| max_members             | int          | NOT NULL         | 50                | 最大会员数量     |
| max_storage_gb          | int          | NOT NULL         | 1                 | 最大存储空间(GB) |
| billing_cycle           | enum         | NOT NULL         | 'MONTHLY'         | 计费周期         |
| price_per_class         | int          | NOT NULL         | 0                 | 按课时计费单价   |
| monthly_fee             | int          | NOT NULL         | 0                 | 月费             |
| setup_fee               | int          | NOT NULL         | 0                 | 安装费           |
| status                  | enum         | NOT NULL         | 'TRIAL'           | 租户状态         |
| subscription_expires_at | timestamp    | -                | NULL              | 订阅到期时间     |
| trial_expires_at        | timestamp    | -                | NULL              | 试用期结束时间   |
| total_users             | int          | NOT NULL         | 0                 | 总用户数         |
| total_members           | int          | NOT NULL         | 0                 | 总会员数         |
| total_classes           | int          | NOT NULL         | 0                 | 总课程数         |
| total_revenue           | int          | NOT NULL         | 0                 | 总收入           |
| created_at              | timestamp    | NOT NULL         | CURRENT_TIMESTAMP | 创建时间         |
| updated_at              | timestamp    | -                | NULL              | 更新时间         |
| created_by              | varchar(50)  | -                | NULL              | 创建者           |
| last_login_at           | timestamp    | -                | NULL              | 最后登录时间     |

**外键关系**:

- 无外键（根表）

**枚举定义**:

**plan_type** (套餐类型):

- `TRIAL`: 试用版
- `BASIC`: 基础版
- `PROFESSIONAL`: 专业版
- `ENTERPRISE`: 企业版

**billing_cycle** (计费周期):

- `MONTHLY`: 按月计费
- `YEARLY`: 按年计费

**status** (租户状态):

- `TRIAL`: 试用中
- `ACTIVE`: 正常运营
- `SUSPENDED`: 暂停服务
- `CANCELLED`: 已注销

**⚠️ 设计问题**:

- ~~货币字段使用 DECIMAL 而非 INTEGER~~ ✅
- created_by 应为 INTEGER 外键而非 VARCHAR

### 2. users (用户表)

| 字段名        | 类型         | 约束             | 默认值            | 说明                          |
| ------------- | ------------ | ---------------- | ----------------- | ----------------------------- |
| id            | int          | PK               | AUTO_INCREMENT    | 主键                          |
| tenant_id     | int          | FK               | NULL              | 租户 ID (super_admin 为 null) |
| username      | varchar(50)  | UNIQUE, NOT NULL | -                 | 用户名                        |
| email         | varchar(100) | -                | NULL              | 邮箱                          |
| phone         | varchar(20)  | -                | NULL              | 手机号                        |
| password_hash | varchar(255) | NOT NULL         | -                 | 密码哈希                      |
| real_name     | varchar(50)  | -                | NULL              | 真实姓名                      |
| avatar_url    | varchar(500) | -                | NULL              | 头像 URL                      |
| gender        | enum         | -                | NULL              | 性别                          |
| birthday      | date         | -                | NULL              | 生日                          |
| role          | enum         | NOT NULL         | -                 | 用户角色                      |
| status        | enum         | NOT NULL         | 'ACTIVE'          | 用户状态                      |
| created_at    | timestamp    | NOT NULL         | CURRENT_TIMESTAMP | 创建时间                      |
| updated_at    | timestamp    | -                | NULL              | 更新时间                      |
| created_by    | int          | FK               | NULL              | 创建者                        |

**外键关系**:

- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**唯一约束**:

- `username` (全局唯一)
- `(tenant_id, email)` (租户内邮箱唯一)
- `(tenant_id, phone)` (租户内手机号唯一)

**枚举定义**:

**role** (用户角色):

- `super_admin`: 超级管理员
- `admin`: 租户管理员
- `agent`: 代理人员
- `sale`: 销售人员

**status** (用户状态):

- `active`: 正常
- `inactive`: 停用
- `locked`: 锁定

**gender** (性别):

- `male`: 男
- `female`: 女
- `other`: 其他

### 3. members (会员表)

| 字段名                   | 类型         | 约束         | 默认值            | 说明            |
| ------------------------ | ------------ | ------------ | ----------------- | --------------- |
| id                       | int          | PK           | AUTO_INCREMENT    | 主键            |
| tenant_id                | int          | FK, NOT NULL | -                 | 租户 ID         |
| name                     | varchar(50)  | NOT NULL     | -                 | 会员姓名        |
| gender                   | enum         | -            | NULL              | 性别            |
| birthday                 | date         | -            | NULL              | 生日            |
| phone                    | varchar(20)  | -            | NULL              | 手机号          |
| email                    | varchar(100) | -            | NULL              | 邮箱            |
| avatar_url               | varchar(500) | -            | NULL              | 头像 URL        |
| wechat_openid            | varchar(100) | UNIQUE       | NULL              | 微信 OpenID     |
| wechat_nickname          | varchar(100) | -            | NULL              | 微信昵称        |
| wechat_avatar_url        | varchar(500) | -            | NULL              | 微信头像        |
| status                   | enum         | NOT NULL     | 'ACTIVE'          | 会员状态        |
| member_type              | enum         | NOT NULL     | 'REGULAR'         | 会员类型        |
| sales_id                 | int          | -            | NULL              | 销售人员 ID     |
| agent_id                 | int          | -            | NULL              | 代理人员 ID     |
| address                  | text         | -            | NULL              | 地址            |
| city                     | varchar(50)  | -            | NULL              | 城市            |
| province                 | varchar(50)  | -            | NULL              | 省份            |
| country                  | varchar(50)  | NOT NULL     | 'China'           | 国家            |
| postal_code              | varchar(20)  | -            | NULL              | 邮编            |
| notes                    | text         | -            | NULL              | 备注            |
| tags                     | json         | NOT NULL     | '[]'              | 标签            |
| total_classes            | int          | NOT NULL     | 0                 | 总上课数        |
| completed_classes        | int          | NOT NULL     | 0                 | 完成上课数      |
| cancelled_classes        | int          | NOT NULL     | 0                 | 取消上课数      |
| no_show_classes          | int          | NOT NULL     | 0                 | 缺席上课数      |
| total_spent              | int          | NOT NULL     | 0                 | 总消费金额      |
| avg_rating               | decimal(2,1) | NOT NULL     | 0.0               | 平均评分        |
| rating_count             | int          | NOT NULL     | 0                 | 评价次数        |
| last_class_at            | timestamp    | -            | NULL              | 最后上课时间    |
| last_login_at            | timestamp    | -            | NULL              | 最后登录时间    |
| primary_member_card_id   | int          | -            | NULL              | 主要会员卡 ID ⚠ |
| primary_member_card_name | varchar(100) | -            | NULL              | 主要会员卡名称  |
| created_at               | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 创建时间        |
| updated_at               | timestamp    | -            | NULL              | 更新时间        |
| registered_at            | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 注册时间        |
| created_by               | int          | FK           | NULL              | 创建者          |

**外键关系**:

- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

- `sales_id` → `users.id`
- `agent_id` → `users.id`
- `primary_member_card_id` → `member_cards.id`

**唯一约束**:

- `wechat_openid` (全局唯一)

**枚举定义**:

**status** (会员状态):

- `active`: 正常
- `inactive`: 停用
- `frozen`: 冻结
- `cancelled`: 注销

**member_type** (会员类型):

- `regular`: 普通会员
- `vip`: VIP 会员
- `trial`: 试用会员

### 4. teachers (教师表)

| 字段名               | 类型         | 约束         | 默认值            | 说明               |
| -------------------- | ------------ | ------------ | ----------------- | ------------------ |
| id                   | int          | PK           | AUTO_INCREMENT    | 主键               |
| tenant_id            | int          | FK, NOT NULL | -                 | 租户 ID            |
| name                 | varchar(50)  | NOT NULL     | -                 | 教师姓名           |
| gender               | enum         | -            | NULL              | 性别               |
| avatar               | varchar(500) | -            | NULL              | 头像 URL           |
| phone                | varchar(20)  | -            | NULL              | 手机号             |
| email                | varchar(100) | -            | NULL              | 邮箱               |
| price_per_class      | int          | NOT NULL     | 0                 | 单节课价格（元）✅ |
| teacher_category     | enum         | NOT NULL     | -                 | 教师分类           |
| region               | enum         | NOT NULL     | -                 | 教师区域           |
| wechat_openid        | varchar(100) | UNIQUE       | NULL              | 微信 OpenID        |
| wechat_nickname      | varchar(100) | -            | NULL              | 微信昵称           |
| teaching_experience  | int          | -            | NULL              | 教学经验           |
| education_background | text         | -            | NULL              | 教育背景           |
| certifications       | text         | -            | NULL              | 认证信息           |
| specialties          | text         | -            | NULL              | 专业特长           |
| introduction         | text         | -            | NULL              | 个人介绍           |
| status               | enum         | NOT NULL     | 'ACTIVE'          | 教师状态           |
| show_to_members      | boolean      | NOT NULL     | TRUE              | 是否对会员可见     |
| priority_level       | int          | NOT NULL     | 0                 | 优先级             |
| total_classes        | int          | NOT NULL     | 0                 | 总课程数           |
| completed_classes    | int          | NOT NULL     | 0                 | 完成课程数         |
| cancelled_classes    | int          | NOT NULL     | 0                 | 取消课程数         |
| avg_rating           | decimal(2,1) | NOT NULL     | 0.0               | 平均评分           |
| rating_count         | int          | NOT NULL     | 0                 | 评价次数           |
| last_class_at        | timestamp    | -            | NULL              | 最后上课时间       |
| created_at           | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 创建时间           |
| updated_at           | timestamp    | -            | NULL              | 更新时间           |
| created_by           | int          | FK           | NULL              | 创建者             |

**外键关系**:

- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**唯一约束**:

- `(tenant_id, email)` (租户内邮箱唯一)
- `(tenant_id, phone)` (租户内手机号唯一)
- `wechat_openid` (全局唯一)

**枚举定义**:

**teacher_category** (教师分类):

- `native`: 外教
- `chinese`: 中教
- `assistant`: 助教

**region** (教师区域):

- `north_america`: 北美
- `europe`: 欧洲
- `oceania`: 大洋洲
- `asia`: 亚洲
- `other`: 其他

**status** (教师状态):

- `active`: 正常
- `inactive`: 停用
- `pending`: 待审核

**✅ 设计优点**:

- 正确使用 INTEGER 存储价格
- 完善的外键约束
- 合理的唯一约束设计

### 5. scheduled_classes (已排课表) - 核心表

| 字段名               | 类型         | 约束         | 默认值            | 说明                         |
| -------------------- | ------------ | ------------ | ----------------- | ---------------------------- |
| id                   | int          | PK           | AUTO_INCREMENT    | 主键                         |
| tenant_id            | int          | FK, NOT NULL | -                 | 租户 ID                      |
| teacher_id           | int          | FK, NOT NULL | -                 | 教师 ID                      |
| member_id            | int          | FK           | NULL              | 会员 ID (可为空，未预约状态) |
| class_datetime       | timestamp    | NOT NULL     | -                 | 精确到分钟的上课时间         |
| duration_minutes     | int          | NOT NULL     | 25                | 课程时长（分钟）             |
| class_type           | enum         | NOT NULL     | 'DIRECT'          | 课程类型                     |
| price                | int          | -            | NULL              | 教师单价（元）✅             |
| teacher_name         | varchar(50)  | -            | NULL              | 教师姓名 ✅                  |
| teacher_display_code | varchar(50)  | -            | NULL              | 教师编号 ✅                  |
| member_name          | varchar(50)  | -            | NULL              | 会员姓名                     |
| status               | enum         | NOT NULL     | 'AVAILABLE'       | 课程状态                     |
| is_visible_to_member | boolean      | NOT NULL     | TRUE              | 是否对会员可见               |
| material_name        | varchar(100) | -            | NULL              | 教材名称                     |
| material_id          | int          | -            | NULL              | 预留教材 ID（第二期使用）    |
| operator_name        | varchar(50)  | -            | NULL              | 操作人显示名                 |
| created_at           | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 创建时间                     |
| updated_at           | timestamp    | -            | NULL              | 更新时间                     |
| created_by           | int          | FK           | NULL              | 创建者                       |

**外键关系**:

- `tenant_id` → `tenants.id`
- `teacher_id` → `teachers.id`
- `member_id` → `members.id`
- `created_by` → `users.id`

**唯一约束**:

- `(teacher_id, class_datetime)` (教师时间唯一)

**枚举定义**:

**class_type** (课程类型):

- `direct`: 临时预约
- `fixed`: 固定课位

**status** (课程状态):

- `available`: 可预约
- `booked`: 已预约
- `completed`: 已完成
- `cancelled`: 已取消
- `no_show_member`: 学员缺席
- `no_show_teacher`: 教师缺席

**✅ 设计优点**:

- 正确使用 INTEGER 存储价格
- 包含必需的 teacher_name 和 teacher_display_code 冗余字段
- 适当的唯一约束防止时间冲突

### 6. member_cards (会员卡表)

| 字段名          | 类型        | 约束         | 默认值            | 说明                   |
| --------------- | ----------- | ------------ | ----------------- | ---------------------- |
| id              | int         | PK           | AUTO_INCREMENT    | 主键                   |
| tenant_id       | int         | FK, NOT NULL | -                 | 租户 ID                |
| member_id       | int         | FK, NOT NULL | -                 | 会员 ID                |
| template_id     | int         | FK           | NULL              | 模板 ID                |
| card_type       | enum        | NOT NULL     | -                 | 卡片类型               |
| balance         | int         | NOT NULL     | 0                 | 当前余额（元或次数）✅ |
| status          | enum        | NOT NULL     | 'ACTIVE'          | 卡片状态               |
| card_number     | varchar(50) | UNIQUE       | NULL              | 卡号                   |
| total_recharged | int         | NOT NULL     | 0                 | 总充值金额（元）✅     |
| total_consumed  | int         | NOT NULL     | 0                 | 总消费金额（元）✅     |
| expires_at      | timestamp   | -            | NULL              | 过期时间               |
| last_used_at    | timestamp   | -            | NULL              | 最后使用时间           |
| freeze_reason   | text        | -            | NULL              | 冻结原因               |
| cancel_reason   | text        | -            | NULL              | 注销原因               |
| created_at      | timestamp   | NOT NULL     | CURRENT_TIMESTAMP | 创建时间               |
| updated_at      | timestamp   | -            | NULL              | 更新时间               |
| created_by      | int         | FK           | NULL              | 创建者                 |

**外键关系**:

- `tenant_id` → `tenants.id`
- `member_id` → `members.id`
- `template_id` → `member_card_templates.id`
- `created_by` → `users.id`

**唯一约束**:

- `card_number` (全局唯一)

**枚举定义**:

**card_type** (卡片类型):

- `value`: 储值卡
- `count`: 次数卡
- `period`: 期限卡

**status** (卡片状态):

- `active`: 正常
- `frozen`: 冻结
- `expired`: 过期
- `cancelled`: 注销

**✅ 设计优点**:

- 正确使用 INTEGER 存储所有货币字段
- 完善的外键约束
- 符合业务需求的设计

### 7. member_card_operations (会员卡操作记录表) - 统一操作记录

| 字段名                | 类型         | 约束         | 默认值            | 说明                 |
| --------------------- | ------------ | ------------ | ----------------- | -------------------- |
| id                    | int          | PK           | AUTO_INCREMENT    | 主键                 |
| tenant_id             | int          | FK, NOT NULL | -                 | 租户 ID              |
| member_id             | int          | FK, NOT NULL | -                 | 会员 ID              |
| member_card_id        | int          | FK, NOT NULL | -                 | 会员卡 ID            |
| operation_type        | enum         | NOT NULL     | -                 | 操作类型             |
| operation_description | varchar(200) | NOT NULL     | -                 | 操作描述             |
| amount_change         | int          | -            | NULL              | 余额变化金额（元）✅ |
| balance_before        | int          | -            | NULL              | 操作前余额（元）✅   |
| balance_after         | int          | -            | NULL              | 操作后余额（元）✅   |
| status_before         | enum         | -            | NULL              | 操作前状态           |
| status_after          | enum         | -            | NULL              | 操作后状态           |
| member_card_name      | varchar(100) | -            | NULL              | 会员卡名称           |
| bonus_amount          | int          | -            | NULL              | 赠送金额（元）✅     |
| actual_amount         | int          | -            | NULL              | 实收金额（元）✅     |
| payment_method        | enum         | -            | NULL              | 支付方式             |
| payment_status        | varchar(20)  | -            | NULL              | 支付状态             |
| transaction_id        | varchar(100) | -            | NULL              | 第三方交易 ID        |
| scheduled_class_id    | int          | FK           | NULL              | 关联课程 ID          |
| consumption_type      | enum         | -            | NULL              | 消费类型             |
| operator_id           | int          | FK           | NULL              | 操作人 ID            |
| operator_name         | varchar(50)  | -            | NULL              | 操作人姓名           |
| operator_type         | varchar(20)  | -            | NULL              | 操作人类型           |
| reason                | text         | -            | NULL              | 操作原因             |
| notes                 | text         | -            | NULL              | 备注信息             |
| client_ip             | varchar(45)  | -            | NULL              | 客户端 IP            |
| user_agent            | varchar(500) | -            | NULL              | 用户代理             |
| status                | varchar(20)  | NOT NULL     | 'completed'       | 操作状态             |
| created_at            | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 创建时间             |
| updated_at            | timestamp    | -            | NULL              | 更新时间             |

**外键关系**:

- `tenant_id` → `tenants.id`
- `member_id` → `members.id`
- `member_card_id` → `member_cards.id`
- `scheduled_class_id` → `scheduled_classes.id`
- `operator_id` → `users.id`

**枚举定义**:

**operation_type** (操作类型):

- `recharge`: 充值
- `consume`: 消费
- `refund`: 退款
- `transfer`: 转账
- `adjust`: 调整
- `freeze`: 冻结
- `unfreeze`: 解冻

**payment_method** (支付方式):

- `wechat`: 微信支付
- `alipay`: 支付宝
- `cash`: 现金
- `bank_transfer`: 银行转账
- `manual`: 手动调整

**consumption_type** (消费类型):

- `class_booking`: 课程预约
- `service_fee`: 服务费

**✅ 设计优点**:

- 正确使用 INTEGER 存储所有货币字段
- 统一记录充值和消费操作
- 完整的审计信息

**⚠️ 性能考虑**:

- 表可能变得非常大，需要考虑分区策略

### 8. teacher_fixed_slots (教师固定时间段表)

| 字段名      | 类型      | 约束         | 默认值            | 说明         |
| ----------- | --------- | ------------ | ----------------- | ------------ |
| id          | int       | PK           | AUTO_INCREMENT    | 主键         |
| tenant_id   | int       | FK, NOT NULL | -                 | 租户 ID      |
| teacher_id  | int       | FK, NOT NULL | -                 | 教师 ID      |
| day_of_week | int       | NOT NULL     | -                 | 星期几 (1-7) |
| start_time  | time      | NOT NULL     | -                 | 开始时间     |
| end_time    | time      | NOT NULL     | -                 | 结束时间     |
| is_active   | boolean   | NOT NULL     | TRUE              | 是否启用     |
| created_at  | timestamp | NOT NULL     | CURRENT_TIMESTAMP | 创建时间     |
| updated_at  | timestamp | -            | NULL              | 更新时间     |
| created_by  | int       | FK           | NULL              | 创建者       |

**外键关系**:

- `tenant_id` → `tenants.id`
- `teacher_id` → `teachers.id`
- `created_by` → `users.id`

**唯一约束**:

- `(teacher_id, day_of_week, start_time)` (教师同一天同一时间唯一)

**✅ 设计优点**:

- 使用 TIME 类型存储时间，便于查询
- 合理的唯一约束防止时间冲突

### 9. member_fixed_slot_locks (会员固定课位锁定表)

| 字段名                | 类型      | 约束         | 默认值            | 说明              |
| --------------------- | --------- | ------------ | ----------------- | ----------------- |
| id                    | int       | PK           | AUTO_INCREMENT    | 主键              |
| tenant_id             | int       | FK, NOT NULL | -                 | 租户 ID           |
| member_id             | int       | FK, NOT NULL | -                 | 会员 ID           |
| teacher_fixed_slot_id | int       | FK, NOT NULL | -                 | 教师固定时间段 ID |
| member_card_id        | int       | FK, NOT NULL | -                 | 会员卡 ID         |
| status                | enum      | NOT NULL     | 'ACTIVE'          | 锁定状态          |
| start_date            | date      | NOT NULL     | -                 | 开始日期          |
| end_date              | date      | -            | NULL              | 结束日期          |
| created_at            | timestamp | NOT NULL     | CURRENT_TIMESTAMP | 创建时间          |
| updated_at            | timestamp | -            | NULL              | 更新时间          |
| created_by            | int       | FK           | NULL              | 创建者            |

**外键关系**:

- `tenant_id` → `tenants.id`
- `member_id` → `members.id`
- `teacher_fixed_slot_id` → `teacher_fixed_slots.id`
- `member_card_id` → `member_cards.id`
- `created_by` → `users.id`

**唯一约束**:

- `(teacher_fixed_slot_id, status)` WHERE status='ACTIVE' (同一时间段只能有一个活跃锁定)

**枚举定义**:

**status** (锁定状态):

- `active`: 活跃
- `expired`: 过期
- `cancelled`: 取消

### 10. tags (标签表)

| 字段名      | 类型        | 约束         | 默认值            | 说明     |
| ----------- | ----------- | ------------ | ----------------- | -------- |
| id          | int         | PK           | AUTO_INCREMENT    | 主键     |
| tenant_id   | int         | FK, NOT NULL | -                 | 租户 ID  |
| category_id | int         | FK           | NULL              | 分类 ID  |
| name        | varchar(50) | NOT NULL     | -                 | 标签名称 |
| color       | varchar(7)  | -            | '#1890ff'         | 标签颜色 |
| description | text        | -            | NULL              | 标签描述 |
| is_active   | boolean     | NOT NULL     | TRUE              | 是否启用 |
| sort_order  | int         | NOT NULL     | 0                 | 排序     |
| created_at  | timestamp   | NOT NULL     | CURRENT_TIMESTAMP | 创建时间 |
| updated_at  | timestamp   | -            | NULL              | 更新时间 |
| created_by  | int         | FK           | NULL              | 创建者   |

**外键关系**:

- `tenant_id` → `tenants.id`
- `category_id` → `tag_categories.id`
- `created_by` → `users.id`

**唯一约束**:

- `(tenant_id, category_id, name)` (租户内分类下标签名唯一)

### 11. tag_categories (标签分类表)

| 字段名      | 类型        | 约束         | 默认值            | 说明     |
| ----------- | ----------- | ------------ | ----------------- | -------- |
| id          | int         | PK           | AUTO_INCREMENT    | 主键     |
| tenant_id   | int         | FK, NOT NULL | -                 | 租户 ID  |
| name        | varchar(50) | NOT NULL     | -                 | 分类名称 |
| description | text        | -            | NULL              | 分类描述 |
| is_active   | boolean     | NOT NULL     | TRUE              | 是否启用 |
| sort_order  | int         | NOT NULL     | 0                 | 排序     |
| created_at  | timestamp   | NOT NULL     | CURRENT_TIMESTAMP | 创建时间 |
| updated_at  | timestamp   | -            | NULL              | 更新时间 |
| created_by  | int         | FK           | NULL              | 创建者   |

**外键关系**:

- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**唯一约束**:

- `(tenant_id, name)` (租户内分类名唯一)

### 12. member_card_templates (会员卡模板表)

| 字段名          | 类型         | 约束         | 默认值            | 说明         |
| --------------- | ------------ | ------------ | ----------------- | ------------ |
| id              | int          | PK           | AUTO_INCREMENT    | 主键         |
| tenant_id       | int          | FK, NOT NULL | -                 | 租户 ID      |
| name            | varchar(100) | NOT NULL     | -                 | 模板名称     |
| card_type       | enum         | NOT NULL     | -                 | 卡片类型     |
| default_balance | int          | NOT NULL     | 0                 | 默认余额 ✅  |
| validity_days   | int          | -            | NULL              | 有效期（天） |
| is_transferable | boolean      | NOT NULL     | FALSE             | 是否可转让   |
| is_refundable   | boolean      | NOT NULL     | FALSE             | 是否可退款   |
| description     | text         | -            | NULL              | 模板描述     |
| is_active       | boolean      | NOT NULL     | TRUE              | 是否启用     |
| is_default      | boolean      | NOT NULL     | FALSE             | 是否默认模板 |
| created_at      | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 创建时间     |
| updated_at      | timestamp    | -            | NULL              | 更新时间     |
| created_by      | int          | FK           | NULL              | 创建者       |

**外键关系**:

- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**唯一约束**:

- `(tenant_id, name)` (租户内模板名唯一)

**枚举定义**:

**card_type** (卡片类型):

- `value`: 储值卡
- `count`: 次数卡
- `period`: 期限卡

### 13. course_system_configs (课程系统配置表)

| 字段名       | 类型         | 约束         | 默认值            | 说明     |
| ------------ | ------------ | ------------ | ----------------- | -------- |
| id           | int          | PK           | AUTO_INCREMENT    | 主键     |
| tenant_id    | int          | FK, NOT NULL | -                 | 租户 ID  |
| config_key   | varchar(100) | NOT NULL     | -                 | 配置键   |
| config_value | text         | -            | NULL              | 配置值   |
| config_type  | varchar(20)  | NOT NULL     | 'string'          | 配置类型 |
| description  | text         | -            | NULL              | 配置描述 |
| is_active    | boolean      | NOT NULL     | TRUE              | 是否启用 |
| created_at   | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 创建时间 |
| updated_at   | timestamp    | -            | NULL              | 更新时间 |
| created_by   | int          | FK           | NULL              | 创建者   |

**外键关系**:

- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**唯一约束**:

- `(tenant_id, config_key)` (租户内配置键唯一)

## 系统表（非租户相关）

### 14. user_sessions (用户会话表)

| 字段名           | 类型         | 约束             | 默认值            | 说明         |
| ---------------- | ------------ | ---------------- | ----------------- | ------------ |
| id               | int          | PK               | AUTO_INCREMENT    | 主键         |
| user_id          | int          | FK, NOT NULL     | -                 | 用户 ID      |
| session_token    | varchar(255) | UNIQUE, NOT NULL | -                 | 会话令牌     |
| expires_at       | timestamp    | NOT NULL         | -                 | 过期时间     |
| is_active        | boolean      | NOT NULL         | TRUE              | 是否活跃     |
| ip_address       | varchar(45)  | -                | NULL              | IP 地址      |
| user_agent       | varchar(500) | -                | NULL              | 用户代理     |
| created_at       | timestamp    | NOT NULL         | CURRENT_TIMESTAMP | 创建时间     |
| last_accessed_at | timestamp    | -                | NULL              | 最后访问时间 |

**外键关系**:

- `user_id` → `users.id`

**唯一约束**:

- `session_token` (全局唯一)

### 15. teacher_tags (教师标签关联表)

| 字段名     | 类型      | 约束         | 默认值            | 说明     |
| ---------- | --------- | ------------ | ----------------- | -------- |
| id         | int       | PK           | AUTO_INCREMENT    | 主键     |
| teacher_id | int       | FK, NOT NULL | -                 | 教师 ID  |
| tag_id     | int       | FK, NOT NULL | -                 | 标签 ID  |
| created_at | timestamp | NOT NULL     | CURRENT_TIMESTAMP | 创建时间 |
| created_by | int       | FK           | NULL              | 创建者   |

**外键关系**:

- `teacher_id` → `teachers.id`
- `tag_id` → `tags.id`
- `created_by` → `users.id`

**唯一约束**:

- `(teacher_id, tag_id)` (教师标签关联唯一)

## 全局系统表

### 16-21. 系统管理表

**系统管理相关表**（简化描述）:

- `system_admins` - 系统管理员表
- `system_configs` - 系统配置表
- `system_audit_logs` - 系统审计日志表
- `tenant_plan_templates` - 租户计划模板表
- `tenant_usage_stats` - 租户使用统计表
- `tenant_usage_monthly` - 租户月度使用统计表

这些表主要用于系统级别的管理和监控，结构相对简单，包含基本的配置、日志和统计功能。

## 设计问题汇总

### 🟡 中优先级问题

1. **分区策略**:
   - 大表缺少分区设计
   - `member_card_operations`等表需要按时间分区

## 总结

当前数据库设计整体架构良好，具有以下特点：

**✅ 设计优势**:

- 完善的多租户架构和 RLS 策略
- 模块化的表结构设计
- 大部分表有正确的外键约束
- 教师和课程相关表设计合理

**⚠️ 需要改进**:

- 性能优化空间较大

通过系统性地解决这些问题，可以显著提升数据库的一致性、完整性和性能。
