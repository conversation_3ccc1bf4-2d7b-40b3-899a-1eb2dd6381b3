# 未来数据库设计文档（第二阶段和第三阶段）

## 概述

本文档描述了 FastAPI + SQLModel + PostgreSQL 多租户英语辅导系统第二阶段和第三阶段的计划数据库设计。基于当前数据库设计分析，确保与现有架构的一致性和多租户模式的兼容性。

## 第二阶段：固定课排课和预约系统

### 2.1 固定课排课系统表

#### 2.1.1 排课任务记录表 (fixed_schedule_tasks)

**用途**: 记录固定课排课任务的执行情况

```sql
CREATE TABLE fixed_schedule_tasks (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 任务信息
    task_name VARCHAR(100) NOT NULL,              -- 任务名称

    -- 排课参数
    teacher_ids JSON,                             -- 参与排课的教师ID列表
    from_date DATE NOT NULL,                      -- 开始排课日期（周一）
    schedule_weeks INTEGER DEFAULT 4,            -- 排课周数
    interrupt_on_conflict BOOLEAN DEFAULT TRUE,   -- 遇到冲突是否终止
    skip_insufficient_balance BOOLEAN DEFAULT TRUE, -- 是否跳过余额不足
    remove_insufficient_locks BOOLEAN DEFAULT TRUE, -- 是否移除余额不足的锁定

    -- 执行状态
    status VARCHAR(20) DEFAULT 'pending',         -- pending, running, completed, failed

    -- 执行结果统计
    total_teachers INTEGER DEFAULT 0,            -- 总教师数
    success_teachers INTEGER DEFAULT 0,          -- 成功教师数
    failed_teachers INTEGER DEFAULT 0,           -- 失败教师数
    total_classes INTEGER DEFAULT 0,             -- 总生成课程数
    total_amount INTEGER DEFAULT 0,              -- 总扣费金额（元）

    -- 时间信息
    started_at TIMESTAMP,                         -- 开始执行时间
    completed_at TIMESTAMP,                      -- 完成时间

    -- 备注和错误信息
    remark TEXT,                                  -- 备注
    error_message TEXT,                           -- 错误信息

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    created_by INTEGER REFERENCES users(id),

    -- 索引
    INDEX idx_fixed_schedule_tasks_tenant (tenant_id),
    INDEX idx_fixed_schedule_tasks_status (tenant_id, status),
    INDEX idx_fixed_schedule_tasks_date (tenant_id, from_date),
    INDEX idx_fixed_schedule_tasks_created (tenant_id, created_at)
);
```

#### 2.1.2 排课详细日志表 (fixed_schedule_task_logs)

**用途**: 记录排课过程中的详细操作日志

```sql
CREATE TABLE fixed_schedule_task_logs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    task_id INTEGER NOT NULL REFERENCES fixed_schedule_tasks(id),

    -- 日志信息
    log_level VARCHAR(10) NOT NULL,              -- INFO, WARN, ERROR
    message TEXT NOT NULL,                   -- 日志消息

    -- 关联信息
    teacher_id INTEGER REFERENCES teachers(id),  -- 相关教师ID
    member_id INTEGER REFERENCES members(id),    -- 相关会员ID

    -- 操作详情
    operation_type VARCHAR(50),                  -- 操作类型
    operation_data JSON,                         -- 操作相关数据

    -- 时间信息
    log_time TIMESTAMP DEFAULT NOW(),

    -- 索引
    INDEX idx_fixed_schedule_tasks_logs_task (task_id),
    INDEX idx_fixed_schedule_tasks_logs_level (tenant_id, log_level),
    INDEX idx_fixed_schedule_tasks_logs_teacher (teacher_id),
    INDEX idx_fixed_schedule_tasks_logs_time (tenant_id, log_time)
);
```

#### 2.1.3 课程操作记录表 (scheduled_class_operations)

**用途**: 记录课程的所有操作历史（预约、取消、完成等）

```sql
CREATE TABLE scheduled_class_operations (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 关联信息
    scheduled_class_id INTEGER NOT NULL REFERENCES scheduled_classes(id),
    member_id INTEGER REFERENCES members(id),
    teacher_id INTEGER NOT NULL REFERENCES teachers(id),

    -- 操作信息
    operation_type VARCHAR(20) NOT NULL,         -- create_slot, book, cancel, reschedule, complete, no_show_member, no_show_teacher, delete

    -- 状态变更
    old_status VARCHAR(20),                      -- 操作前状态
    new_status VARCHAR(20),                      -- 操作后状态

    -- 课程信息
    class_datetime TIMESTAMP,                   -- 课程时间
    class_type VARCHAR(20),                     -- 课程类型

    -- 会员卡信息
    member_card_id INTEGER REFERENCES member_cards(id),
    member_card_name VARCHAR(100),
    deducted_amount INTEGER,                    -- 扣除金额（元）
    deducted_count DECIMAL(4,1),               -- 扣除次数

    -- 教材信息
    material_id INTEGER,                        -- 预留教材ID
    material_name VARCHAR(100),                 -- 教材名称

    -- 操作详情
    operation_reason TEXT,                      -- 操作原因
    remark TEXT,                               -- 备注

    -- 操作人信息
    operated_by INTEGER REFERENCES users(id),  -- 操作人ID（NULL表示会员自己操作）
    operator_name VARCHAR(50),                 -- 操作人姓名
    operator_type VARCHAR(20),                 -- member, teacher, admin

    -- 时间信息
    operation_time TIMESTAMP DEFAULT NOW(),

    -- 索引
    INDEX idx_class_ops_class (scheduled_class_id),
    INDEX idx_class_ops_member (member_id, operation_time),
    INDEX idx_class_ops_teacher (teacher_id, operation_time),
    INDEX idx_class_ops_type (tenant_id, operation_type),
    INDEX idx_class_ops_time (tenant_id, operation_time)
);
```

### 2.2 高级统计和报表表

#### 2.2.1 教师统计表 (teacher_statistics)

**用途**: 独立存储教师统计信息，避免主表频繁更新

```sql
CREATE TABLE teacher_statistics (
    teacher_id INTEGER PRIMARY KEY REFERENCES teachers(id),
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 课程统计
    total_classes INTEGER DEFAULT 0,            -- 总课程数
    completed_classes INTEGER DEFAULT 0,        -- 完成课程数
    cancelled_classes INTEGER DEFAULT 0,        -- 取消课程数
    no_show_classes INTEGER DEFAULT 0,          -- 缺席课程数

    -- 收入统计
    total_earnings INTEGER DEFAULT 0,           -- 总收入（元）
    current_month_earnings INTEGER DEFAULT 0,   -- 当月收入（元）

    -- 评价统计
    avg_rating DECIMAL(3,2) DEFAULT 0.00,      -- 平均评分
    rating_count INTEGER DEFAULT 0,            -- 评价次数

    -- 时间信息
    last_class_at TIMESTAMP,                   -- 最后上课时间
    last_updated_at TIMESTAMP DEFAULT NOW(),   -- 最后更新时间

    -- 索引
    INDEX idx_teacher_stats_tenant (tenant_id),
    INDEX idx_teacher_stats_earnings (tenant_id, total_earnings),
    INDEX idx_teacher_stats_rating (tenant_id, avg_rating)
);
```

#### 2.2.2 会员统计表 (member_statistics)

**用途**: 独立存储会员统计信息，避免主表频繁更新

```sql
CREATE TABLE member_statistics (
    member_id INTEGER PRIMARY KEY REFERENCES members(id),
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 课程统计
    total_classes INTEGER DEFAULT 0,            -- 总上课数
    completed_classes INTEGER DEFAULT 0,        -- 完成上课数
    cancelled_classes INTEGER DEFAULT 0,        -- 取消上课数
    no_show_classes INTEGER DEFAULT 0,          -- 缺席上课数

    -- 消费统计
    total_spent INTEGER DEFAULT 0,              -- 总消费金额（元）
    current_month_spent INTEGER DEFAULT 0,      -- 当月消费金额（元）
    total_recharged INTEGER DEFAULT 0,          -- 总充值金额（元）

    -- 评价统计
    avg_rating DECIMAL(3,2) DEFAULT 0.00,      -- 平均评分
    rating_count INTEGER DEFAULT 0,            -- 评价次数

    -- 时间信息
    last_class_at TIMESTAMP,                   -- 最后上课时间
    last_login_at TIMESTAMP,                   -- 最后登录时间
    last_updated_at TIMESTAMP DEFAULT NOW(),   -- 最后更新时间

    -- 索引
    INDEX idx_member_stats_tenant (tenant_id),
    INDEX idx_member_stats_spent (tenant_id, total_spent),
    INDEX idx_member_stats_rating (tenant_id, avg_rating)
);
```

## 第三阶段：完善功能和运营支持

### 3.1 支付系统表

#### 3.1.1 支付记录表 (payment_records)

**用途**: 记录所有支付交易

```sql
CREATE TABLE payment_records (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    member_id INTEGER NOT NULL REFERENCES members(id),
    member_card_id INTEGER REFERENCES member_cards(id),

    -- 支付信息
    payment_method VARCHAR(20) NOT NULL,        -- wechat, alipay, manual, bank_transfer
    payment_amount INTEGER NOT NULL,            -- 支付金额（元）
    actual_amount INTEGER NOT NULL,             -- 实际到账金额（元）
    bonus_amount INTEGER DEFAULT 0,             -- 赠送金额（元）

    -- 第三方支付信息
    transaction_id VARCHAR(100),                -- 第三方交易ID
    payment_status VARCHAR(20) DEFAULT 'pending', -- pending, completed, failed, refunded
    payment_time TIMESTAMP,                     -- 支付完成时间

    -- 订单信息
    order_no VARCHAR(50) UNIQUE NOT NULL,       -- 内部订单号
    order_title VARCHAR(200),                   -- 订单标题
    order_description TEXT,                     -- 订单描述

    -- 回调信息
    callback_data JSON,                         -- 支付回调数据
    callback_time TIMESTAMP,                    -- 回调时间

    -- 退款信息
    refund_amount INTEGER DEFAULT 0,            -- 退款金额（元）
    refund_reason TEXT,                         -- 退款原因
    refund_time TIMESTAMP,                      -- 退款时间

    -- 操作人信息
    operated_by INTEGER REFERENCES users(id),   -- 操作人ID
    operator_name VARCHAR(50),                  -- 操作人姓名

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,

    -- 索引
    INDEX idx_payments_tenant (tenant_id),
    INDEX idx_payments_member (member_id, created_at),
    INDEX idx_payments_status (tenant_id, payment_status),
    INDEX idx_payments_method (tenant_id, payment_method),
    INDEX idx_payments_transaction (transaction_id),
    INDEX idx_payments_order (order_no)
);
```

### 3.2 消息通知系统表

#### 3.2.1 消息模板表 (message_templates)

**用途**: 管理各种消息通知模板

```sql
CREATE TABLE message_templates (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id),   -- NULL表示系统模板

    -- 模板信息
    template_code VARCHAR(50) NOT NULL,         -- 模板代码
    template_name VARCHAR(100) NOT NULL,        -- 模板名称
    template_type VARCHAR(20) NOT NULL,         -- sms, wechat, email, system

    -- 模板内容
    title VARCHAR(200),                         -- 消息标题
    content TEXT NOT NULL,                      -- 消息内容（支持变量替换）
    variables JSON,                             -- 可用变量列表

    -- 发送配置
    is_active BOOLEAN DEFAULT TRUE,             -- 是否启用
    send_immediately BOOLEAN DEFAULT TRUE,      -- 是否立即发送

    -- 第三方配置
    third_party_template_id VARCHAR(100),       -- 第三方模板ID
    third_party_config JSON,                   -- 第三方配置

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    created_by INTEGER REFERENCES users(id),

    -- 约束和索引
    UNIQUE (tenant_id, template_code),
    INDEX idx_templates_tenant (tenant_id),
    INDEX idx_templates_type (tenant_id, template_type),
    INDEX idx_templates_active (tenant_id, is_active)
);
```

#### 3.2.2 消息发送记录表 (message_records)

**用途**: 记录所有消息发送历史

```sql
CREATE TABLE message_records (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 接收者信息
    recipient_type VARCHAR(20) NOT NULL,        -- member, teacher, admin
    recipient_id INTEGER NOT NULL,              -- 接收者ID
    recipient_name VARCHAR(50),                 -- 接收者姓名
    recipient_contact VARCHAR(100),             -- 接收者联系方式

    -- 消息信息
    template_id INTEGER REFERENCES message_templates(id),
    template_code VARCHAR(50),                  -- 模板代码
    message_type VARCHAR(20) NOT NULL,          -- sms, wechat, email, system

    -- 消息内容
    title VARCHAR(200),                         -- 消息标题
    content TEXT NOT NULL,                      -- 消息内容
    variables JSON,                             -- 变量值

    -- 发送状态
    send_status VARCHAR(20) DEFAULT 'pending',  -- pending, sent, failed, delivered, read
    send_time TIMESTAMP,                        -- 发送时间
    delivered_time TIMESTAMP,                  -- 送达时间
    read_time TIMESTAMP,                       -- 阅读时间

    -- 第三方信息
    third_party_id VARCHAR(100),               -- 第三方消息ID
    third_party_response JSON,                 -- 第三方响应

    -- 错误信息
    error_code VARCHAR(50),                     -- 错误代码
    error_message TEXT,                        -- 错误信息
    retry_count INTEGER DEFAULT 0,             -- 重试次数

    -- 关联业务
    business_type VARCHAR(50),                  -- 业务类型
    business_id INTEGER,                       -- 业务ID

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,

    -- 索引
    INDEX idx_messages_tenant (tenant_id),
    INDEX idx_messages_recipient (recipient_type, recipient_id),
    INDEX idx_messages_status (tenant_id, send_status),
    INDEX idx_messages_type (tenant_id, message_type),
    INDEX idx_messages_template (template_id),
    INDEX idx_messages_business (business_type, business_id),
    INDEX idx_messages_time (tenant_id, created_at)
);
```

### 3.3 文件管理系统表

#### 3.3.1 文件存储表 (file_storage)

**用途**: 管理系统中的所有文件

```sql
CREATE TABLE file_storage (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id),   -- NULL表示系统文件

    -- 文件信息
    file_name VARCHAR(255) NOT NULL,            -- 原始文件名
    file_path VARCHAR(500) NOT NULL,            -- 存储路径
    file_size INTEGER NOT NULL,                 -- 文件大小（字节）
    file_type VARCHAR(100),                     -- 文件类型
    mime_type VARCHAR(100),                     -- MIME类型

    -- 文件分类
    category VARCHAR(50),                       -- 文件分类
    business_type VARCHAR(50),                  -- 业务类型
    business_id INTEGER,                        -- 业务ID

    -- 存储信息
    storage_type VARCHAR(20) DEFAULT 'local',   -- local, oss, cos, s3
    storage_config JSON,                        -- 存储配置

    -- 访问控制
    is_public BOOLEAN DEFAULT FALSE,            -- 是否公开访问
    access_url VARCHAR(500),                    -- 访问URL

    -- 状态信息
    status VARCHAR(20) DEFAULT 'active',        -- active, deleted, archived

    -- 审计字段
    uploaded_by INTEGER REFERENCES users(id),   -- 上传者
    uploaded_at TIMESTAMP DEFAULT NOW(),
    deleted_at TIMESTAMP,
    deleted_by INTEGER REFERENCES users(id),

    -- 索引
    INDEX idx_files_tenant (tenant_id),
    INDEX idx_files_business (business_type, business_id),
    INDEX idx_files_category (tenant_id, category),
    INDEX idx_files_uploader (uploaded_by),
    INDEX idx_files_status (tenant_id, status)
);
```

### 3.4 教材管理系统表

#### 3.4.1 教材分类表 (material_categories)

**用途**: 管理教材分类

```sql
CREATE TABLE material_categories (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 分类信息
    name VARCHAR(100) NOT NULL,                 -- 分类名称
    description TEXT,                           -- 分类描述

    -- 层级结构
    parent_id INTEGER REFERENCES material_categories(id),
    level INTEGER DEFAULT 1,                   -- 层级
    sort_order INTEGER DEFAULT 0,              -- 排序

    -- 状态
    is_active BOOLEAN DEFAULT TRUE,             -- 是否启用

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    created_by INTEGER REFERENCES users(id),

    -- 约束和索引
    UNIQUE (tenant_id, parent_id, name),
    INDEX idx_material_cats_tenant (tenant_id),
    INDEX idx_material_cats_parent (parent_id),
    INDEX idx_material_cats_active (tenant_id, is_active)
);
```

#### 3.4.2 教材表 (materials)

**用途**: 管理教材信息

```sql
CREATE TABLE materials (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    category_id INTEGER REFERENCES material_categories(id),

    -- 教材信息
    name VARCHAR(200) NOT NULL,                 -- 教材名称
    code VARCHAR(50),                           -- 教材编码
    description TEXT,                           -- 教材描述

    -- 教材属性
    level VARCHAR(50),                          -- 适用级别
    age_range VARCHAR(50),                      -- 适用年龄
    duration_minutes INTEGER,                   -- 建议课时长度

    -- 文件信息
    cover_image_id INTEGER REFERENCES file_storage(id),
    content_file_id INTEGER REFERENCES file_storage(id),

    -- 使用统计
    usage_count INTEGER DEFAULT 0,             -- 使用次数

    -- 状态
    is_active BOOLEAN DEFAULT TRUE,             -- 是否启用

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    created_by INTEGER REFERENCES users(id),

    -- 约束和索引
    UNIQUE (tenant_id, code),
    INDEX idx_materials_tenant (tenant_id),
    INDEX idx_materials_category (category_id),
    INDEX idx_materials_level (tenant_id, level),
    INDEX idx_materials_active (tenant_id, is_active),
    INDEX idx_materials_usage (tenant_id, usage_count)
);
```

### 3.5 评价系统表

#### 3.5.1 课程评价表 (class_ratings)

**用途**: 记录课程评价

```sql
CREATE TABLE class_ratings (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 关联信息
    scheduled_class_id INTEGER NOT NULL REFERENCES scheduled_classes(id),
    member_id INTEGER NOT NULL REFERENCES members(id),
    teacher_id INTEGER NOT NULL REFERENCES teachers(id),

    -- 评价信息
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5), -- 1-5星评价
    comment TEXT,                               -- 评价内容

    -- 详细评分
    teaching_quality INTEGER CHECK (teaching_quality >= 1 AND teaching_quality <= 5),
    interaction_quality INTEGER CHECK (interaction_quality >= 1 AND interaction_quality <= 5),
    material_quality INTEGER CHECK (material_quality >= 1 AND material_quality <= 5),

    -- 标签
    tags JSON,                                  -- 评价标签

    -- 状态
    is_anonymous BOOLEAN DEFAULT FALSE,         -- 是否匿名
    is_visible BOOLEAN DEFAULT TRUE,            -- 是否可见

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,

    -- 约束和索引
    UNIQUE (scheduled_class_id, member_id),
    INDEX idx_ratings_tenant (tenant_id),
    INDEX idx_ratings_teacher (teacher_id, created_at),
    INDEX idx_ratings_member (member_id, created_at),
    INDEX idx_ratings_rating (tenant_id, rating),
    INDEX idx_ratings_visible (tenant_id, is_visible)
);
```

## 数据库设计原则和约束

### 4.1 设计原则

#### 4.1.1 一致性原则

- **货币字段**: 统一使用`INTEGER`类型存储（元单位）
- **时间字段**: 统一使用`TIMESTAMP`和`TIME`类型
- **审计字段**: 所有业务表包含`created_at`, `updated_at`, `created_by`
- **多租户**: 所有业务表包含`tenant_id`字段

#### 4.1.2 性能原则

- **索引设计**: 为常用查询字段添加合适索引
- **分区策略**: 大表考虑按时间分区
- **统计分离**: 统计数据独立存储，避免频繁更新主表

#### 4.1.3 扩展性原则

- **预留字段**: 关键表预留扩展字段
- **JSON 字段**: 灵活配置使用 JSON 存储
- **软删除**: 重要数据支持软删除

### 4.2 RLS 策略扩展

所有新增表都需要配置 RLS 策略：

```sql
-- 新增表的RLS策略模板
CREATE POLICY {table_name}_tenant_isolation_v2 ON {table_name}
FOR ALL TO PUBLIC
USING (
    CASE
        -- 全局模式：可以看到所有数据
        WHEN current_setting('app.current_tenant_id', true) IS NULL OR
             current_setting('app.current_tenant_id', true) = '' THEN true
        -- 租户模式：只能看到本租户数据
        ELSE tenant_id = current_setting('app.current_tenant_id', true)::int
    END
);
```

### 4.3 性能优化建议

#### 4.3.1 分区策略

```sql
-- 大表分区示例（按月分区）
CREATE TABLE fixed_schedule_task_logs (
    -- 字段定义...
) PARTITION BY RANGE (log_time);

-- 创建分区
CREATE TABLE fixed_schedule_tasks_logs_2024_01 PARTITION OF fixed_schedule_task_logs
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

#### 4.3.2 索引优化

```sql
-- 复合索引优化查询
CREATE INDEX idx_complex_query ON table_name (tenant_id, status, created_at);

-- 部分索引减少存储
CREATE INDEX idx_active_records ON table_name (tenant_id, id) WHERE is_active = true;
```

## 总结

第二阶段和第三阶段的数据库设计遵循以下原则：

1. **与现有设计保持一致**: 使用相同的命名规范、字段类型和约束模式
2. **解决现有问题**: 统一货币字段类型，完善外键约束
3. **支持业务扩展**: 为固定排课、支付、通知等功能提供完整的数据支持
4. **性能考虑**: 通过统计表分离、分区策略等优化大数据量场景
5. **多租户兼容**: 所有新表都支持 RLS 策略和租户隔离

这些设计将为系统的后续发展提供坚实的数据基础。
