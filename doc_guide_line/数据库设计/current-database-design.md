# 当前数据库设计文档

## 概述

本文档提供了 FastAPI + SQLModel + PostgreSQL 多租户英语辅导系统当前数据库设计的全面分析。该系统为租户 CMS 后台和会员 H5/小程序前端提供服务，为外教机构提供完整的管理解决方案。

### 系统架构特点

- **多租户架构**: 基于行级安全(RLS)的租户隔离
- **预期规模**: 2000 并发用户，2000 租户，24/7 运行
- **技术栈**: FastAPI + SQLModel + PostgreSQL
- **设计模式**: 按功能/领域组织的模块化架构（垂直分层）

## 数据库表概览

### 核心系统表（共 21 张表）

#### 1. 全局管理模块（7 张表）

- `tenants` - 租户/机构管理
- `tenant_plan_templates` - 租户订阅计划模板
- `system_configs` - 系统配置设置
- `system_admins` - 系统管理员账户
- `system_audit_logs` - 系统操作审计日志
- `tenant_usage_stats` - 租户每日使用统计
- `tenant_usage_monthly` - 租户月度使用汇总

#### 2. 用户认证模块（2 张表）

- `users` - 用户账户（super_admin, admin, agent, sale）
- `user_sessions` - 用户会话管理

#### 3. 会员管理模块（4 张表）

- `members` - 会员/学生账户
- `member_cards` - 会员卡实例
- `member_card_templates` - 会员卡模板
- `member_card_operations` - 统一操作记录（替代独立的充值/消费表）

#### 4. 教师管理模块（3 张表）

- `teachers` - 教师档案
- `teacher_tags` - 教师-标签关联
- `teacher_fixed_slots` - 教师固定时间段

#### 5. 标签管理模块（2 张表）

- `tag_categories` - 标签分类
- `tags` - 标签

#### 6. 课程调度模块（3 张表）

- `course_system_configs` - 课程系统配置
- `scheduled_classes` - 已排课记录（核心表）
- `member_fixed_slot_locks` - 会员固定课位锁定

### RLS（行级安全）配置

所有租户相关表都实现了 RLS，采用以下模式：

- **全局模式**: 无租户上下文 = 查看所有数据（超级管理员）
- **租户模式**: 有上下文 = 仅查看租户数据
- **超级管理员**: 可在任何上下文中操作

#### 启用 RLS 的表：

```sql
-- 直接tenant_id列
users, members, tag_categories, tags, teachers, teacher_fixed_slots,
course_system_configs, scheduled_classes, member_fixed_slot_locks,
member_card_templates, member_cards, recharge_records, consumption_records

-- 间接租户隔离（通过关系）
user_sessions (通过users), teacher_tags (通过teachers)
```

## 详细表分析

### 1. 租户表 (tenants)

**表结构**:

```sql
CREATE TABLE tenants (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,                    -- 机构名称
    code VARCHAR(50) UNIQUE NOT NULL,              -- 机构代码
    display_name VARCHAR(100),                     -- 显示名称
    description TEXT,                              -- 机构描述

    -- 域名和访问
    domain VARCHAR(100) UNIQUE,                    -- 自定义域名
    subdomain VARCHAR(50) UNIQUE,                  -- 子域名
    logo_url VARCHAR(500),                         -- 机构logo
    favicon_url VARCHAR(500),                      -- 网站图标

    -- 联系信息
    contact_name VARCHAR(50),                      -- 联系人姓名
    contact_phone VARCHAR(20),                     -- 联系电话
    contact_email VARCHAR(100),                    -- 联系邮箱
    address TEXT,                                  -- 机构地址

    -- 服务配置
    plan_type ENUM DEFAULT 'TRIAL',                -- 套餐类型
    max_teachers INTEGER DEFAULT 5,               -- 最大教师数量
    max_members INTEGER DEFAULT 50,               -- 最大会员数量
    max_storage_gb INTEGER DEFAULT 1,             -- 最大存储空间(GB)

    -- 计费相关
    billing_cycle ENUM DEFAULT 'MONTHLY',          -- 计费周期
    price_per_class INTEGER DEFAULT 0,   -- 按课时计费单价
    monthly_fee INTEGER DEFAULT 0,       -- 月费
    setup_fee INTEGER DEFAULT 0,         -- 安装费

    -- 状态管理
    status ENUM DEFAULT 'TRIAL',                   -- 租户状态
    subscription_expires_at TIMESTAMP,            -- 订阅到期时间
    trial_expires_at TIMESTAMP,                   -- 试用期结束时间

    -- 统计信息（反规范化以提高性能）
    total_users INTEGER DEFAULT 0,                -- 总用户数
    total_members INTEGER DEFAULT 0,              -- 总会员数
    total_classes INTEGER DEFAULT 0,              -- 总课程数
    total_revenue int DEFAULT 0,     -- 总收入

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    created_by VARCHAR(50),                        -- 创建者
    last_login_at TIMESTAMP                        -- 最后登录时间
);
```

**识别的问题**:

- ❌ `created_by`应为`INTEGER`外键引用 users，而非`VARCHAR(50)`

  暂不处理，后期会改为 system_admins 表里的 id，而非 users 里的

- ⚠️ 大量统计字段可能在频繁更新时造成性能问题

  拆出单独的表 tenant_statistics

### 2. 用户表 (users)

**表结构**:

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id),     -- super_admin为NULL

    -- 登录信息
    username VARCHAR(50) UNIQUE NOT NULL,         -- 用户名
    email VARCHAR(100),                           -- 邮箱
    phone VARCHAR(20),                            -- 手机号
    password_hash VARCHAR(255) NOT NULL,          -- 密码哈希

    -- 基础信息
    real_name VARCHAR(50),                        -- 真实姓名
    avatar_url VARCHAR(500),                      -- 头像URL
    gender ENUM,                                  -- 性别
    birthday DATE,                                -- 生日

    -- 角色和状态
    role ENUM NOT NULL,                           -- super_admin, admin, agent, sale
    status ENUM DEFAULT 'ACTIVE',                 -- 用户状态

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    created_by INTEGER REFERENCES users(id),

    -- 约束
    CONSTRAINT uq_username UNIQUE (username),
    CONSTRAINT uq_tenant_email UNIQUE (tenant_id, email),
    CONSTRAINT uq_tenant_phone UNIQUE (tenant_id, phone)
);
```

**识别的问题**:

- ✅ 正确的外键约束
- ✅ 适当的多租户唯一约束
- ✅ RLS 兼容设计

### 3. 会员表 (members)

**表结构**:

```sql
CREATE TABLE members (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 基础信息
    name VARCHAR(50) NOT NULL,                    -- 会员姓名
    gender ENUM,                                  -- 性别
    birthday DATE,                                -- 生日
    phone VARCHAR(20),                            -- 手机号
    email VARCHAR(100),                           -- 邮箱
    avatar_url VARCHAR(500),                      -- 头像URL

    -- 微信集成
    wechat_openid VARCHAR(100) UNIQUE,            -- 微信OpenID
    wechat_nickname VARCHAR(100),                 -- 微信昵称
    wechat_avatar_url VARCHAR(500),               -- 微信头像

    -- 状态和类型
    status ENUM DEFAULT 'ACTIVE',                 -- 会员状态
    member_type ENUM DEFAULT 'REGULAR',           -- 会员类型

    -- 归属关系
    sales_id INTEGER,                             -- 销售人员ID（缺少FK约束）
    agent_id INTEGER,                             -- 代理人员ID（缺少FK约束）

    -- 地址信息
    address TEXT,                                 -- 地址
    city VARCHAR(50),                             -- 城市
    province VARCHAR(50),                         -- 省份
    country VARCHAR(50) DEFAULT 'China',          -- 国家
    postal_code VARCHAR(20),                      -- 邮编

    -- 备注和标签
    notes TEXT,                                   -- 备注
    tags JSON DEFAULT '[]',                       -- 标签

    -- 统计信息（反规范化）
    total_classes INTEGER DEFAULT 0,             -- 总上课数
    completed_classes INTEGER DEFAULT 0,         -- 完成上课数
    cancelled_classes INTEGER DEFAULT 0,         -- 取消上课数
    no_show_classes INTEGER DEFAULT 0,           -- 缺席上课数
    total_spent int  DEFAULT 0,      -- 总消费金额（应为INTEGER）

    -- 评价信息
    avg_rating DECIMAL(2,1) DEFAULT 0.0,         -- 平均评分
    rating_count INTEGER DEFAULT 0,              -- 评价次数

    -- 时间信息
    last_class_at TIMESTAMP,                     -- 最后上课时间
    last_login_at TIMESTAMP,                     -- 最后登录时间

    -- 会员卡信息（冗余字段）
    primary_member_card_id INTEGER,              -- 主要会员卡ID（缺少FK约束）
    primary_member_card_name VARCHAR(100),       -- 主要会员卡名称

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    registered_at TIMESTAMP DEFAULT NOW(),       -- 注册时间
    created_by INTEGER REFERENCES users(id)
);
```

**识别的问题**:

- ❌ `sales_id`和`agent_id`缺少外键约束
- ❌ `primary_member_card_id`缺少外键约束
- ⚠️ 大量反规范化统计字段可能导致更新性能问题

  拆出单独的表 member_statistics

### 4. 教师表 (teachers)

**表结构**:

```sql
CREATE TABLE teachers (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 基础信息
    name VARCHAR(50) NOT NULL,                    -- 教师姓名
    gender ENUM,                                  -- 性别
    avatar VARCHAR(500),                          -- 头像URL
    phone VARCHAR(20),                            -- 手机号
    email VARCHAR(100),                           -- 邮箱

    -- 教师属性
    price_per_class INTEGER DEFAULT 0,           -- ✅ 单节课价格（整数，单位：元）
    teacher_category ENUM NOT NULL,              -- 教师分类
    region ENUM NOT NULL,                        -- 教师区域

    -- 微信集成
    wechat_openid VARCHAR(100) UNIQUE,           -- 微信OpenID
    wechat_nickname VARCHAR(100),                -- 微信昵称

    -- 专业信息
    teaching_experience INTEGER,                 -- 教学经验
    education_background TEXT,                   -- 教育背景
    certifications TEXT,                         -- 认证信息
    specialties TEXT,                            -- 专业特长
    introduction TEXT,                           -- 个人介绍

    -- 状态和可见性
    status ENUM DEFAULT 'ACTIVE',                -- 教师状态
    show_to_members BOOLEAN DEFAULT TRUE,        -- 是否对会员可见
    priority_level INTEGER DEFAULT 0,           -- 优先级

    -- 统计信息（反规范化）
    total_classes INTEGER DEFAULT 0,            -- 总课程数
    completed_classes INTEGER DEFAULT 0,        -- 完成课程数
    cancelled_classes INTEGER DEFAULT 0,        -- 取消课程数
    avg_rating DECIMAL(2,1) DEFAULT 0.0,       -- 平均评分
    rating_count INTEGER DEFAULT 0,             -- 评价次数

    -- 时间信息
    last_class_at TIMESTAMP,                    -- 最后上课时间

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    created_by INTEGER REFERENCES users(id),

    -- 约束
    CONSTRAINT uq_tenant_teacher_email UNIQUE (tenant_id, email),
    CONSTRAINT uq_tenant_teacher_phone UNIQUE (tenant_id, phone),
    CONSTRAINT uq_teacher_wechat_openid UNIQUE (wechat_openid)
);
```

**识别的问题**:

- ✅ 正确使用`INTEGER`表示`price_per_class`
- ✅ 适当的唯一约束
- ✅ 良好的索引策略
- ⚠️ 大量反规范化统计字段可能导致更新性能问题

  拆出单独的表 teacher_statistics

### 5. 会员卡表 (member_cards)

**表结构**:

```sql
CREATE TABLE member_cards (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    member_id INTEGER NOT NULL REFERENCES members(id),
    template_id INTEGER REFERENCES member_card_templates(id),

    -- 卡片信息
    card_type ENUM NOT NULL,                     -- 卡片类型
    balance INTEGER DEFAULT 0,                   -- ✅ 当前余额（元或次数）
    status ENUM DEFAULT 'ACTIVE',                -- 卡片状态

    -- 扩展字段
    card_number VARCHAR(50) UNIQUE,              -- 卡号
    total_recharged INTEGER DEFAULT 0,           -- ✅ 总充值金额（元）
    total_consumed INTEGER DEFAULT 0,            -- ✅ 总消费金额（元）
    expires_at TIMESTAMP,                        -- 过期时间

    -- 业务扩展字段
    last_used_at TIMESTAMP,                      -- 最后使用时间
    freeze_reason TEXT,                          -- 冻结原因
    cancel_reason TEXT,                          -- 注销原因

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);
```

**识别的问题**:

- ✅ 正确使用`INTEGER`表示货币字段
- ✅ 适当的外键约束
- ✅ 符合业务需求的设计

### 6. 已排课表 (scheduled_classes) - 核心表

**表结构**:

```sql
CREATE TABLE scheduled_classes (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 基础信息
    teacher_id INTEGER NOT NULL REFERENCES teachers(id),
    member_id INTEGER REFERENCES members(id),    -- 可为空，未预约状态

    -- 时间信息
    class_datetime TIMESTAMP NOT NULL,           -- 精确到分钟的上课时间
    duration_minutes INTEGER DEFAULT 25,        -- 课程时长（分钟）

    -- 课程信息
    class_type ENUM DEFAULT 'DIRECT',            -- 课程类型
    price INTEGER,                               -- ✅ 教师单价（整数，单位：元）

    -- 冗余字段（便于查询）
    teacher_name VARCHAR(50),                    -- ✅ 教师姓名
    teacher_display_code VARCHAR(50),                  -- ✅ 教师编号
    member_name VARCHAR(50),                     -- 会员姓名

    -- 状态管理
    status ENUM DEFAULT 'AVAILABLE',             -- 课程状态
    is_visible_to_member BOOLEAN DEFAULT TRUE,   -- 是否对会员可见

    -- 教材信息
    material_name VARCHAR(100),                  -- 教材名称
    material_id INTEGER,                         -- 预留教材ID（第二期使用）

    -- 操作信息
    operator_name VARCHAR(50),                   -- 操作人显示名

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP,
    created_by INTEGER REFERENCES users(id),

    -- 约束
    CONSTRAINT uq_teacher_class_datetime UNIQUE (teacher_id, class_datetime)
);
```

**识别的问题**:

- ✅ 正确使用`INTEGER`表示价格
- ✅ 包含必需的 teacher_name 和 teacher_display_code 冗余字段
- ✅ 适当的唯一约束防止时间冲突
- ✅ 良好的索引设计

### 7. 会员卡操作记录表 (member_card_operations) - 统一操作记录

**表结构**:

```sql
CREATE TABLE member_card_operations (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    member_id INTEGER NOT NULL REFERENCES members(id),
    member_card_id INTEGER NOT NULL REFERENCES member_cards(id),

    -- 操作基础信息
    operation_type ENUM NOT NULL,                -- 操作类型
    operation_description VARCHAR(200) NOT NULL, -- 操作描述

    -- 余额变动信息
    amount_change INTEGER,                       -- ✅ 余额变化金额（元，正数增加，负数减少）
    balance_before INTEGER,                      -- ✅ 操作前余额（元）
    balance_after INTEGER,                       -- ✅ 操作后余额（元）

    -- 状态变更信息
    status_before ENUM,                          -- 操作前状态
    status_after ENUM,                           -- 操作后状态

    -- 会员卡信息（冗余字段）
    member_card_name VARCHAR(100),               -- 会员卡名称

    -- 充值相关字段
    bonus_amount INTEGER,                        -- ✅ 赠送金额（元）
    actual_amount INTEGER,                       -- ✅ 实收金额（元）
    payment_method ENUM,                         -- 支付方式
    payment_status VARCHAR(20),                  -- 支付状态
    transaction_id VARCHAR(100),                 -- 第三方交易ID

    -- 消费相关字段
    scheduled_class_id INTEGER REFERENCES scheduled_classes(id), -- 关联课程ID
    consumption_type ENUM,                       -- 消费类型

    -- 操作人信息
    operator_id INTEGER REFERENCES users(id),   -- 操作人ID
    operator_name VARCHAR(50),                   -- 操作人姓名
    operator_type VARCHAR(20),                   -- 操作人类型

    -- 操作原因和备注
    reason TEXT,                                 -- 操作原因
    notes TEXT,                                  -- 备注信息

    -- 客户端信息
    client_ip VARCHAR(45),                       -- 客户端IP
    user_agent VARCHAR(500),                     -- 用户代理

    -- 操作状态
    status VARCHAR(20) DEFAULT 'completed',      -- 操作状态

    -- 审计字段
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP
);
```

**识别的问题**:

- ✅ 正确使用`INTEGER`表示所有货币字段
- ✅ 统一记录充值和消费操作
- ✅ 完整的审计信息
- ⚠️ 表可能变得非常大，需要考虑分区策略

## 关键设计问题总结

### 1. ~~货币字段不一致性~~ ✅ 已完成

- **影响**: 精度问题，业务逻辑不一致
- **建议**: 统一使用`INTEGER`（元单位）

### 2. 缺少外键约束

- **问题**: 多个字段引用其他表但无 FK 约束
- **示例**: `members.sales_id`, `members.agent_id`, `members.primary_member_card_id`
- **影响**: 数据完整性风险，孤立记录
- **建议**: 添加缺失的 FK 约束 【批准】

### 3. 操作记录表设计不一致

- **当前**: 使用统一的`member_card_operations`表
- **RLS 配置**: 引用独立的`recharge_records`和`consumption_records`表
- **问题**: 实现与配置不一致
- **建议**: 明确使用统一表还是独立表

  【批准】使用统一的 member_card_operations，因为且目前我们没有表 recharge_records 和 consumption_records，而是使用了统一的 member_card_operations

### 4. 大表性能担忧

- **问题**: 会员/教师表中大量反规范化统计字段
- **影响**: 100 万+记录时更新性能下降
- **建议**: 考虑独立统计表或缓存策略

### 5. RLS 实现缺口

- **问题**: RLS 配置中的某些表不存在独立模型
- **影响**: RLS 策略可能失败
- **建议**: 使 RLS 配置与实际表结构保持一致

## 索引设计分析

### 优秀的索引设计示例

#### 教师表索引（teachers）

```sql
-- 基础索引
CREATE INDEX idx_tenant_teachers ON teachers(tenant_id, id);
CREATE INDEX idx_teacher_status ON teachers(tenant_id, status);
CREATE INDEX idx_teacher_category ON teachers(tenant_id, teacher_category);
CREATE INDEX idx_teacher_region ON teachers(tenant_id, region);
CREATE INDEX idx_teacher_show_members ON teachers(tenant_id, show_to_members);
CREATE INDEX idx_teacher_priority ON teachers(tenant_id, priority_level);
CREATE INDEX idx_teacher_price ON teachers(tenant_id, price_per_class);

-- 复合索引 - 优化多维度筛选查询
CREATE INDEX idx_teacher_active_show ON teachers(tenant_id, status, show_to_members);
CREATE INDEX idx_teacher_category_region ON teachers(tenant_id, teacher_category, region);
CREATE INDEX idx_teacher_price_priority ON teachers(tenant_id, price_per_class, priority_level);

-- 排序优化索引
CREATE INDEX idx_teacher_priority_desc ON teachers(tenant_id, priority_level, id);
CREATE INDEX idx_teacher_created_desc ON teachers(tenant_id, created_at, id);
```

#### 已排课表索引（scheduled_classes）

```sql
-- 基础索引
CREATE INDEX idx_scheduled_classes_tenant ON scheduled_classes(tenant_id);
CREATE INDEX idx_scheduled_classes_teacher ON scheduled_classes(teacher_id);
CREATE INDEX idx_scheduled_classes_member ON scheduled_classes(member_id);
CREATE INDEX idx_scheduled_classes_datetime ON scheduled_classes(class_datetime);
CREATE INDEX idx_scheduled_classes_status ON scheduled_classes(tenant_id, status);

-- 复合索引 - 优化多维度筛选查询
CREATE INDEX idx_scheduled_classes_teacher_time_status ON scheduled_classes(teacher_id, class_datetime, status);
CREATE INDEX idx_scheduled_classes_member_time_status ON scheduled_classes(member_id, class_datetime, status);
CREATE INDEX idx_scheduled_classes_tenant_datetime ON scheduled_classes(tenant_id, class_datetime);
CREATE INDEX idx_scheduled_classes_visible_datetime ON scheduled_classes(is_visible_to_member, class_datetime);

-- 排序优化索引
CREATE INDEX idx_scheduled_classes_datetime_desc ON scheduled_classes(tenant_id, class_datetime, id);
```

### 索引设计问题

#### 1. 缺少必要索引的表【批准】

- `members`表缺少按状态、类型的复合索引
- `member_cards`表缺少按状态、类型的查询索引
- `member_card_operations`表索引设计需要优化

#### 2. 建议添加的索引

```sql
-- 会员表
CREATE INDEX idx_members_status_type ON members(tenant_id, status, member_type);
CREATE INDEX idx_members_sales_agent ON members(tenant_id, sales_id, agent_id);

-- 会员卡表
CREATE INDEX idx_member_cards_status_type ON member_cards(tenant_id, status, card_type);
CREATE INDEX idx_member_cards_expires ON member_cards(tenant_id, expires_at) WHERE expires_at IS NOT NULL;

-- 操作记录表
CREATE INDEX idx_operations_type_date ON member_card_operations(tenant_id, operation_type, created_at);
CREATE INDEX idx_operations_member_date ON member_card_operations(member_id, created_at);
```

## 多租户设计分析

### RLS 策略实现

#### 当前 RLS 策略模式

```sql
-- 普通表的RLS策略
CREATE POLICY table_tenant_isolation_v2 ON table_name
FOR ALL TO PUBLIC
USING (
    CASE
        -- 全局模式：可以看到所有数据
        WHEN current_setting('app.current_tenant_id', true) IS NULL OR
             current_setting('app.current_tenant_id', true) = '' THEN true
        -- 租户模式：只能看到本租户数据
        ELSE tenant_id = current_setting('app.current_tenant_id', true)::int
    END
);
```

#### 用户表特殊 RLS 策略

```sql
-- 用户表策略（处理super_admin的特殊情况）
CREATE POLICY users_tenant_isolation_v2 ON users
FOR ALL TO PUBLIC
USING (
    CASE
        -- 全局模式：可以看到所有数据
        WHEN current_setting('app.current_tenant_id', true) IS NULL OR
             current_setting('app.current_tenant_id', true) = '' THEN true
        -- 租户模式：只能看到本租户数据，但不能看到super_admin
        ELSE tenant_id = current_setting('app.current_tenant_id', true)::int
    END
);
```

### 多租户设计优势

- ✅ 数据完全隔离，安全性高
- ✅ 支持超级管理员全局访问
- ✅ 租户上下文自动切换
- ✅ 防止跨租户数据泄露

### 多租户设计问题

- ⚠️ RLS 配置与实际表结构不一致
- ⚠️ 某些表（如 recharge_records, consumption_records）在 RLS 中配置但未实现 【批准】删除未实现的
- ⚠️ 超级管理员标识方案需要统一（NULL vs tenant_id=0）【批准】统一为 NULL

## 性能考虑

### 大表性能问题

#### 1. 统计字段更新性能

**问题表**: members, teachers, tenants

- 包含大量反规范化统计字段
- 每次相关操作都需要更新统计
- 在高并发下可能成为性能瓶颈

**建议解决方案**: 【批准】

```sql
-- 创建独立统计表
CREATE TABLE member_statistics (
    member_id INTEGER PRIMARY KEY REFERENCES members(id),
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    total_classes INTEGER DEFAULT 0,
    completed_classes INTEGER DEFAULT 0,
    cancelled_classes INTEGER DEFAULT 0,
    no_show_classes INTEGER DEFAULT 0,
    total_spent INTEGER DEFAULT 0,
    avg_rating DECIMAL(2,1) DEFAULT 0.0,
    rating_count INTEGER DEFAULT 0,
    last_class_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. 操作记录表分区策略

**问题**: member_card_operations 表预计会非常大
**建议**: 按时间分区 【批准】

```sql
-- 按月分区
CREATE TABLE member_card_operations (
    -- 字段定义...
) PARTITION BY RANGE (created_at);

-- 创建分区
CREATE TABLE member_card_operations_2024_01 PARTITION OF member_card_operations
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

## 数据库设计评估

### 设计优势

#### 1. 架构设计

- ✅ **模块化设计**: 按功能域垂直分层，便于维护
- ✅ **多租户架构**: RLS 实现完整的数据隔离
- ✅ **审计完整**: 所有表都有完整的审计字段
- ✅ **扩展性好**: 预留字段支持未来功能扩展

#### 2. 业务逻辑

- ✅ **货币处理**: 部分表正确使用 INTEGER 存储货币
- ✅ **时间处理**: 使用 TIME 类型存储时间段，便于查询
- ✅ **冗余设计**: 关键显示字段适当冗余，提高查询性能
- ✅ **约束设计**: 多租户唯一约束设计合理

#### 3. 性能优化

- ✅ **索引策略**: 教师表和课程表有良好的索引设计
- ✅ **查询优化**: 复合索引支持多维度查询
- ✅ **排序优化**: 专门的排序索引提高列表查询性能

### 设计问题

#### 1. 高优先级问题（需要立即修复）

- 🔴 **缺少外键约束**: members 表的 sales_id、agent_id 等缺少 FK 约束【批准】
- 🔴 **RLS 配置不一致**: 配置中的表与实际实现不匹配 【批准】

#### 2. 中优先级问题（建议优化）

- 🟡 **性能隐患**: 大量反规范化统计字段可能影响更新性能 【批准】
- 🟡 **索引不足**: 部分表缺少必要的查询索引 【批准】
- 🟡 **分区策略**: 大表缺少分区设计 【批准】

#### 3. 低优先级问题（长期优化）

- 🟢 **命名规范**: 部分字段命名可以更加统一
- 🟢 **文档完善**: 需要更详细的字段说明文档

## 修复建议

### 立即修复项

#### 1. ~~统一货币字段类型~~ 【已修复】

```sql
-- 修改tenants表
ALTER TABLE tenants
ALTER COLUMN price_per_class TYPE INTEGER USING (price_per_class * 100)::INTEGER,
ALTER COLUMN monthly_fee TYPE INTEGER USING (monthly_fee * 100)::INTEGER,
ALTER COLUMN setup_fee TYPE INTEGER USING (setup_fee * 100)::INTEGER,
ALTER COLUMN total_revenue TYPE INTEGER USING (total_revenue * 100)::INTEGER;

-- 修改members表
ALTER TABLE members
ALTER COLUMN total_spent TYPE INTEGER USING (total_spent * 100)::INTEGER;
```

#### 2. ~~添加缺失的外键约束~~ 【已修复】

```sql
-- 添加members表外键约束
ALTER TABLE members
ADD CONSTRAINT fk_members_sales_id FOREIGN KEY (sales_id) REFERENCES users(id),
ADD CONSTRAINT fk_members_agent_id FOREIGN KEY (agent_id) REFERENCES users(id),
ADD CONSTRAINT fk_members_primary_member_card_id FOREIGN KEY (primary_member_card_id) REFERENCES member_cards(id);

-- 修改tenants表created_by字段
ALTER TABLE tenants
ALTER COLUMN created_by TYPE INTEGER USING NULL,
ADD CONSTRAINT fk_tenants_created_by FOREIGN KEY (created_by) REFERENCES users(id);
```

#### 3. ~~统一 RLS 配置~~ 【已修复】

**选择方案 A**: 使用统一操作记录表

- 移除 RLS 配置中的 recharge_records 和 consumption_records
- 统一使用 member_card_operations 表

**选择方案 B**: 创建独立的充值消费表

- 实现 RLS 配置中引用的独立表
- 保持 member_card_operations 作为汇总表

### 性能优化建议

#### 1. 创建独立统计表【批准】

```sql
-- 会员统计表
CREATE TABLE member_statistics (
    member_id INTEGER PRIMARY KEY REFERENCES members(id),
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    total_classes INTEGER DEFAULT 0,
    completed_classes INTEGER DEFAULT 0,
    cancelled_classes INTEGER DEFAULT 0,
    no_show_classes INTEGER DEFAULT 0,
    total_spent INTEGER DEFAULT 0,
    avg_rating DECIMAL(2,1) DEFAULT 0.0,
    rating_count INTEGER DEFAULT 0,
    last_class_at TIMESTAMP,
    last_login_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 教师统计表
CREATE TABLE teacher_statistics (
    teacher_id INTEGER PRIMARY KEY REFERENCES teachers(id),
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    total_classes INTEGER DEFAULT 0,
    completed_classes INTEGER DEFAULT 0,
    cancelled_classes INTEGER DEFAULT 0,
    avg_rating DECIMAL(2,1) DEFAULT 0.0,
    rating_count INTEGER DEFAULT 0,
    last_class_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 2. ~~添加必要索引~~

```sql
-- 会员表索引
CREATE INDEX idx_members_status_type ON members(tenant_id, status, member_type);
CREATE INDEX idx_members_sales_agent ON members(tenant_id, sales_id, agent_id);

-- 会员卡表索引
CREATE INDEX idx_member_cards_status_type ON member_cards(tenant_id, status, card_type);
CREATE INDEX idx_member_cards_expires ON member_cards(tenant_id, expires_at) WHERE expires_at IS NOT NULL;

-- 操作记录表索引
CREATE INDEX idx_operations_type_date ON member_card_operations(tenant_id, operation_type, created_at);
CREATE INDEX idx_operations_member_date ON member_card_operations(member_id, created_at);
```

## 总结

当前数据库设计整体架构良好，多租户设计完善，但存在一些需要修复的问题：

1. **货币字段不一致性**是最重要的问题，需要立即统一
2. **缺失的外键约束**影响数据完整性，需要尽快添加
3. **RLS 配置与实现不一致**需要明确统一方案
4. **性能优化**可以通过独立统计表和分区策略解决

建议按优先级逐步修复这些问题，确保系统的稳定性和可扩展性。
