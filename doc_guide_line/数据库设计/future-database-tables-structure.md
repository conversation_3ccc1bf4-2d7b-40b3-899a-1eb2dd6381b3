# 未来数据库表结构详细定义（第二/三阶段）

## 概述

本文档以列表形式详细描述第二阶段和第三阶段计划新增的数据库表结构，包括字段类型、约束、外键关系和枚举值定义。

## 第二阶段新增表概览

| 表名                           | 图标 | 说明                     | 主要字段数 | 模块       |
| ------------------------------ | ---- | ------------------------ | ---------- | ---------- |
| **fixed_schedule_tasks**       | 📋   | 固定课排课任务记录表     | 20+        | 固定课排课 |
| **fixed_schedule_task_logs**   | 📝   | 固定课排课任务详细日志表 | 10+        | 固定课排课 |
| **scheduled_class_operations** | 🔄   | 课程操作记录表           | 25+        | 课程管理   |
| **teacher_statistics**         | 📊   | 教师统计表               | 15+        | 统计报表   |
| **member_statistics**          | 📈   | 会员统计表               | 20+        | 统计报表   |
| **system_scheduled_jobs**      | ⏰   | 系统任务调度表           | 20+        | 系统管理   |

## 第三阶段新增表概览

| 表名                    | 图标 | 说明           | 主要字段数 | 模块     |
| ----------------------- | ---- | -------------- | ---------- | -------- |
| **payment_records**     | 💰   | 支付记录表     | 25+        | 支付系统 |
| **message_templates**   | 📧   | 消息模板表     | 15+        | 消息通知 |
| **message_records**     | 📨   | 消息发送记录表 | 25+        | 消息通知 |
| **file_storage**        | 📁   | 文件存储表     | 20+        | 文件管理 |
| **material_categories** | 📚   | 教材分类表     | 10+        | 教材管理 |
| **materials**           | 📖   | 教材表         | 15+        | 教材管理 |
| **class_ratings**       | ⭐   | 课程评价表     | 15+        | 评价系统 |

## 第二阶段详细表结构

### 1. fixed_schedule_tasks (排课任务记录表)

| 字段名                    | 类型         | 约束         | 默认值            | 说明                   |
| ------------------------- | ------------ | ------------ | ----------------- | ---------------------- |
| id                        | int          | PK           | AUTO_INCREMENT    | 主键                   |
| tenant_id                 | int          | FK, NOT NULL | -                 | 租户 ID                |
| task_name                 | varchar(100) | NOT NULL     | -                 | 任务名称               |
| teacher_ids               | json         | -            | NULL              | 参与排课的教师 ID 列表 |
| from_date                 | date         | NOT NULL     | -                 | 开始排课日期（周一）   |
| schedule_weeks            | int          | NOT NULL     | 4                 | 排课周数               |
| interrupt_on_conflict     | boolean      | NOT NULL     | TRUE              | 遇到冲突是否终止       |
| skip_insufficient_balance | boolean      | NOT NULL     | TRUE              | 是否跳过余额不足       |
| remove_insufficient_locks | boolean      | NOT NULL     | TRUE              | 是否移除余额不足的锁定 |
| status                    | varchar(20)  | NOT NULL     | 'pending'         | 执行状态               |
| total_teachers            | int          | NOT NULL     | 0                 | 总教师数               |
| success_teachers          | int          | NOT NULL     | 0                 | 成功教师数             |
| failed_teachers           | int          | NOT NULL     | 0                 | 失败教师数             |
| total_classes             | int          | NOT NULL     | 0                 | 总生成课程数           |
| total_amount              | int          | NOT NULL     | 0                 | 总扣费金额（元）✅     |
| started_at                | timestamp    | -            | NULL              | 开始执行时间           |
| completed_at              | timestamp    | -            | NULL              | 完成时间               |
| remark                    | text         | -            | NULL              | 备注                   |
| error_message             | text         | -            | NULL              | 错误信息               |
| created_at                | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 创建时间               |
| updated_at                | timestamp    | -            | NULL              | 更新时间               |
| created_by                | int          | FK           | NULL              | 创建者                 |

**外键关系**:

- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**枚举定义**:

**status** (执行状态):

- `pending`: 待执行
- `running`: 执行中
- `completed`: 已完成
- `failed`: 执行失败

### 2. fixed_schedule_task_logs (排课详细日志表)

| 字段名           | 类型        | 约束         | 默认值            | 说明         |
| ---------------- | ----------- | ------------ | ----------------- | ------------ |
| id               | int         | PK           | AUTO_INCREMENT    | 主键         |
| tenant_id        | int         | FK, NOT NULL | -                 | 租户 ID      |
| schedule_task_id | int         | FK, NOT NULL | -                 | 排课任务 ID  |
| log_level        | varchar(10) | NOT NULL     | -                 | 日志级别     |
| message          | text        | NOT NULL     | -                 | 日志消息     |
| teacher_id       | int         | FK           | NULL              | 相关教师 ID  |
| member_id        | int         | FK           | NULL              | 相关会员 ID  |
| operation_type   | varchar(50) | -            | NULL              | 操作类型     |
| operation_data   | json        | -            | NULL              | 操作相关数据 |
| log_time         | timestamp   | NOT NULL     | CURRENT_TIMESTAMP | 日志时间     |

**外键关系**:

- `tenant_id` → `tenants.id`
- `schedule_task_id` → `schedule_tasks.id`
- `teacher_id` → `teachers.id`
- `member_id` → `members.id`

**枚举定义**:

**log_level** (日志级别):

- `INFO`: 信息
- `WARN`: 警告
- `ERROR`: 错误

### 3. scheduled_class_operations (课程操作记录表)

| 字段名             | 类型         | 约束         | 默认值            | 说明             |
| ------------------ | ------------ | ------------ | ----------------- | ---------------- |
| id                 | int          | PK           | AUTO_INCREMENT    | 主键             |
| tenant_id          | int          | FK, NOT NULL | -                 | 租户 ID          |
| scheduled_class_id | int          | FK, NOT NULL | -                 | 课程 ID          |
| member_id          | int          | FK           | NULL              | 会员 ID          |
| teacher_id         | int          | FK, NOT NULL | -                 | 教师 ID          |
| operation_type     | varchar(20)  | NOT NULL     | -                 | 操作类型         |
| old_status         | varchar(20)  | -            | NULL              | 操作前状态       |
| new_status         | varchar(20)  | -            | NULL              | 操作后状态       |
| class_datetime     | timestamp    | -            | NULL              | 课程时间         |
| class_type         | varchar(20)  | -            | NULL              | 课程类型         |
| member_card_id     | int          | FK           | NULL              | 会员卡 ID        |
| member_card_name   | varchar(100) | -            | NULL              | 会员卡名称       |
| deducted_amount    | int          | -            | NULL              | 扣除金额（元）✅ |
| deducted_count     | int          | -            | NULL              | 扣除次数         |
| material_id        | int          | -            | NULL              | 教材 ID          |
| material_name      | varchar(100) | -            | NULL              | 教材名称         |
| operation_reason   | text         | -            | NULL              | 操作原因         |
| remark             | text         | -            | NULL              | 备注             |
| operated_by        | int          | FK           | NULL              | 操作人 ID        |
| operator_name      | varchar(50)  | -            | NULL              | 操作人姓名       |
| operator_type      | varchar(20)  | -            | NULL              | 操作人类型       |
| operation_time     | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 操作时间         |

**外键关系**:

- `tenant_id` → `tenants.id`
- `scheduled_class_id` → `scheduled_classes.id`
- `member_id` → `members.id`
- `teacher_id` → `teachers.id`
- `member_card_id` → `member_cards.id`
- `operated_by` → `users.id`

**枚举定义**:

**operation_type** (操作类型):

- `create_slot`: 创建课位
- `book`: 预约
- `cancel`: 取消
- `reschedule`: 改期
- `complete`: 完成
- `no_show_member`: 学员缺席
- `no_show_teacher`: 教师缺席
- `delete`: 删除

**operator_type** (操作人类型):

- `member`: 会员
- `teacher`: 教师
- `admin`: 管理员

### 4. teacher_statistics (教师统计表)

| 字段名                 | 类型         | 约束         | 默认值            | 说明             |
| ---------------------- | ------------ | ------------ | ----------------- | ---------------- |
| teacher_id             | int          | PK, FK       | -                 | 教师 ID          |
| tenant_id              | int          | FK, NOT NULL | -                 | 租户 ID          |
| total_classes          | int          | NOT NULL     | 0                 | 总课程数         |
| completed_classes      | int          | NOT NULL     | 0                 | 完成课程数       |
| cancelled_classes      | int          | NOT NULL     | 0                 | 取消课程数       |
| no_show_classes        | int          | NOT NULL     | 0                 | 缺席课程数       |
| total_earnings         | int          | NOT NULL     | 0                 | 总收入（元）✅   |
| current_month_earnings | int          | NOT NULL     | 0                 | 当月收入（元）✅ |
| avg_rating             | decimal(2,1) | NOT NULL     | 0.0               | 平均评分         |
| rating_count           | int          | NOT NULL     | 0                 | 评价次数         |
| last_class_at          | timestamp    | -            | NULL              | 最后上课时间     |
| last_updated_at        | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 最后更新时间     |

**外键关系**:

- `teacher_id` → `teachers.id`
- `tenant_id` → `tenants.id`

### 5. member_statistics (会员统计表)

| 字段名              | 类型         | 约束         | 默认值            | 说明                 |
| ------------------- | ------------ | ------------ | ----------------- | -------------------- |
| member_id           | int          | PK, FK       | -                 | 会员 ID              |
| tenant_id           | int          | FK, NOT NULL | -                 | 租户 ID              |
| total_classes       | int          | NOT NULL     | 0                 | 总上课数             |
| completed_classes   | int          | NOT NULL     | 0                 | 完成上课数           |
| cancelled_classes   | int          | NOT NULL     | 0                 | 取消上课数           |
| no_show_classes     | int          | NOT NULL     | 0                 | 缺席上课数           |
| total_spent         | int          | NOT NULL     | 0                 | 总消费金额（元）✅   |
| current_month_spent | int          | NOT NULL     | 0                 | 当月消费金额（元）✅ |
| total_recharged     | int          | NOT NULL     | 0                 | 总充值金额（元）✅   |
| avg_rating          | decimal(2,1) | NOT NULL     | 0.0               | 平均评分             |
| rating_count        | int          | NOT NULL     | 0                 | 评价次数             |
| last_class_at       | timestamp    | -            | NULL              | 最后上课时间         |
| last_login_at       | timestamp    | -            | NULL              | 最后登录时间         |
| last_updated_at     | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 最后更新时间         |

**外键关系**:

- `member_id` → `members.id`
- `tenant_id` → `tenants.id`

### 6. system_scheduled_jobs (系统任务调度表)

| 字段名             | 类型         | 约束     | 默认值            | 说明                        |
| ------------------ | ------------ | -------- | ----------------- | --------------------------- |
| id                 | int          | PK       | AUTO_INCREMENT    | 主键                        |
| tenant_id          | int          | FK       | NULL              | 租户 ID (NULL 表示全局任务) |
| job_name           | varchar(100) | NOT NULL | -                 | 任务名称                    |
| job_type           | varchar(50)  | NOT NULL | -                 | 任务类型                    |
| job_description    | text         | -        | NULL              | 任务描述                    |
| cron_expression    | varchar(100) | -        | NULL              | Cron 表达式                 |
| next_run_time      | timestamp    | -        | NULL              | 下次执行时间                |
| is_active          | boolean      | NOT NULL | TRUE              | 是否启用                    |
| status             | varchar(20)  | NOT NULL | 'pending'         | 任务状态                    |
| total_runs         | int          | NOT NULL | 0                 | 总执行次数                  |
| success_runs       | int          | NOT NULL | 0                 | 成功次数                    |
| failed_runs        | int          | NOT NULL | 0                 | 失败次数                    |
| last_run_time      | timestamp    | -        | NULL              | 最后执行时间                |
| last_run_status    | varchar(20)  | -        | NULL              | 最后执行状态                |
| last_error_message | text         | -        | NULL              | 最后错误信息                |
| created_at         | timestamp    | NOT NULL | CURRENT_TIMESTAMP | 创建时间                    |
| updated_at         | timestamp    | -        | NULL              | 更新时间                    |
| created_by         | int          | FK       | NULL              | 创建者                      |

**外键关系**:

- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**枚举定义**:

**job_type** (任务类型):

- `auto_schedule`: 自动排课
- `statistics_update`: 统计更新
- `data_cleanup`: 数据清理
- `notification_send`: 通知发送

**status** (任务状态):

- `pending`: 待执行
- `running`: 执行中
- `completed`: 已完成
- `failed`: 执行失败

## 第三阶段详细表结构

### 7. payment_records (支付记录表)

| 字段名            | 类型         | 约束             | 默认值            | 说明                 |
| ----------------- | ------------ | ---------------- | ----------------- | -------------------- |
| id                | int          | PK               | AUTO_INCREMENT    | 主键                 |
| tenant_id         | int          | FK, NOT NULL     | -                 | 租户 ID              |
| member_id         | int          | FK, NOT NULL     | -                 | 会员 ID              |
| member_card_id    | int          | FK               | NULL              | 会员卡 ID            |
| payment_method    | varchar(20)  | NOT NULL         | -                 | 支付方式             |
| payment_amount    | int          | NOT NULL         | -                 | 支付金额（元）✅     |
| actual_amount     | int          | NOT NULL         | -                 | 实际到账金额（元）✅ |
| bonus_amount      | int          | NOT NULL         | 0                 | 赠送金额（元）✅     |
| transaction_id    | varchar(100) | -                | NULL              | 第三方交易 ID        |
| payment_status    | varchar(20)  | NOT NULL         | 'pending'         | 支付状态             |
| payment_time      | timestamp    | -                | NULL              | 支付完成时间         |
| order_no          | varchar(50)  | UNIQUE, NOT NULL | -                 | 内部订单号           |
| order_title       | varchar(200) | -                | NULL              | 订单标题             |
| order_description | text         | -                | NULL              | 订单描述             |
| callback_data     | json         | -                | NULL              | 支付回调数据         |
| callback_time     | timestamp    | -                | NULL              | 回调时间             |
| refund_amount     | int          | NOT NULL         | 0                 | 退款金额（元）✅     |
| refund_reason     | text         | -                | NULL              | 退款原因             |
| refund_time       | timestamp    | -                | NULL              | 退款时间             |
| operated_by       | int          | FK               | NULL              | 操作人 ID            |
| operator_name     | varchar(50)  | -                | NULL              | 操作人姓名           |
| created_at        | timestamp    | NOT NULL         | CURRENT_TIMESTAMP | 创建时间             |
| updated_at        | timestamp    | -                | NULL              | 更新时间             |

**外键关系**:

- `tenant_id` → `tenants.id`
- `member_id` → `members.id`
- `member_card_id` → `member_cards.id`
- `operated_by` → `users.id`

**唯一约束**:

- `order_no` (全局唯一)
- `transaction_id` (第三方交易 ID 唯一)

**枚举定义**:

**payment_method** (支付方式):

- `wechat`: 微信支付
- `alipay`: 支付宝
- `manual`: 手动录入
- `bank_transfer`: 银行转账

**payment_status** (支付状态):

- `pending`: 待支付
- `completed`: 支付完成
- `failed`: 支付失败
- `refunded`: 已退款

### 8. message_templates (消息模板表)

| 字段名                  | 类型         | 约束     | 默认值            | 说明                        |
| ----------------------- | ------------ | -------- | ----------------- | --------------------------- |
| id                      | int          | PK       | AUTO_INCREMENT    | 主键                        |
| tenant_id               | int          | FK       | NULL              | 租户 ID (NULL 表示系统模板) |
| template_code           | varchar(50)  | NOT NULL | -                 | 模板代码                    |
| template_name           | varchar(100) | NOT NULL | -                 | 模板名称                    |
| template_type           | varchar(20)  | NOT NULL | -                 | 模板类型                    |
| title                   | varchar(200) | -        | NULL              | 消息标题                    |
| content                 | text         | NOT NULL | -                 | 消息内容                    |
| variables               | json         | -        | NULL              | 可用变量列表                |
| is_active               | boolean      | NOT NULL | TRUE              | 是否启用                    |
| send_immediately        | boolean      | NOT NULL | TRUE              | 是否立即发送                |
| third_party_template_id | varchar(100) | -        | NULL              | 第三方模板 ID               |
| third_party_config      | json         | -        | NULL              | 第三方配置                  |
| created_at              | timestamp    | NOT NULL | CURRENT_TIMESTAMP | 创建时间                    |
| updated_at              | timestamp    | -        | NULL              | 更新时间                    |
| created_by              | int          | FK       | NULL              | 创建者                      |

**外键关系**:

- `tenant_id` → `tenants.id`
- `created_by` → `users.id`

**唯一约束**:

- `(tenant_id, template_code)` (租户内模板代码唯一)

**枚举定义**:

**template_type** (模板类型):

- `sms`: 短信
- `wechat`: 微信消息
- `email`: 邮件
- `system`: 系统通知

### 9. message_records (消息发送记录表)

| 字段名               | 类型         | 约束         | 默认值            | 说明           |
| -------------------- | ------------ | ------------ | ----------------- | -------------- |
| id                   | int          | PK           | AUTO_INCREMENT    | 主键           |
| tenant_id            | int          | FK, NOT NULL | -                 | 租户 ID        |
| recipient_type       | varchar(20)  | NOT NULL     | -                 | 接收者类型     |
| recipient_id         | int          | NOT NULL     | -                 | 接收者 ID      |
| recipient_name       | varchar(50)  | -            | NULL              | 接收者姓名     |
| recipient_contact    | varchar(100) | -            | NULL              | 接收者联系方式 |
| template_id          | int          | FK           | NULL              | 模板 ID        |
| template_code        | varchar(50)  | -            | NULL              | 模板代码       |
| message_type         | varchar(20)  | NOT NULL     | -                 | 消息类型       |
| title                | varchar(200) | -            | NULL              | 消息标题       |
| content              | text         | NOT NULL     | -                 | 消息内容       |
| variables            | json         | -            | NULL              | 变量值         |
| send_status          | varchar(20)  | NOT NULL     | 'pending'         | 发送状态       |
| send_time            | timestamp    | -            | NULL              | 发送时间       |
| delivered_time       | timestamp    | -            | NULL              | 送达时间       |
| read_time            | timestamp    | -            | NULL              | 阅读时间       |
| third_party_id       | varchar(100) | -            | NULL              | 第三方消息 ID  |
| third_party_response | json         | -            | NULL              | 第三方响应     |
| error_code           | varchar(50)  | -            | NULL              | 错误代码       |
| error_message        | text         | -            | NULL              | 错误信息       |
| retry_count          | int          | NOT NULL     | 0                 | 重试次数       |
| business_type        | varchar(50)  | -            | NULL              | 业务类型       |
| business_id          | int          | -            | NULL              | 业务 ID        |
| created_at           | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 创建时间       |
| updated_at           | timestamp    | -            | NULL              | 更新时间       |

**外键关系**:

- `tenant_id` → `tenants.id`
- `template_id` → `message_templates.id`

**枚举定义**:

**recipient_type** (接收者类型):

- `member`: 会员
- `teacher`: 教师
- `admin`: 管理员

**message_type** (消息类型):

- `sms`: 短信
- `wechat`: 微信消息
- `email`: 邮件
- `system`: 系统通知

**send_status** (发送状态):

- `pending`: 待发送
- `sent`: 已发送
- `failed`: 发送失败
- `delivered`: 已送达
- `read`: 已阅读

### 10. file_storage (文件存储表)

| 字段名         | 类型         | 约束     | 默认值            | 说明                        |
| -------------- | ------------ | -------- | ----------------- | --------------------------- |
| id             | int          | PK       | AUTO_INCREMENT    | 主键                        |
| tenant_id      | int          | FK       | NULL              | 租户 ID (NULL 表示系统文件) |
| file_name      | varchar(255) | NOT NULL | -                 | 原始文件名                  |
| file_path      | varchar(500) | NOT NULL | -                 | 存储路径                    |
| file_size      | int          | NOT NULL | -                 | 文件大小（字节）            |
| file_type      | varchar(100) | -        | NULL              | 文件类型                    |
| mime_type      | varchar(100) | -        | NULL              | MIME 类型                   |
| category       | varchar(50)  | -        | NULL              | 文件分类                    |
| business_type  | varchar(50)  | -        | NULL              | 业务类型                    |
| business_id    | int          | -        | NULL              | 业务 ID                     |
| storage_type   | varchar(20)  | NOT NULL | 'local'           | 存储类型                    |
| storage_config | json         | -        | NULL              | 存储配置                    |
| is_public      | boolean      | NOT NULL | FALSE             | 是否公开访问                |
| access_url     | varchar(500) | -        | NULL              | 访问 URL                    |
| status         | varchar(20)  | NOT NULL | 'active'          | 状态                        |
| uploaded_by    | int          | FK       | NULL              | 上传者                      |
| uploaded_at    | timestamp    | NOT NULL | CURRENT_TIMESTAMP | 上传时间                    |
| deleted_at     | timestamp    | -        | NULL              | 删除时间                    |
| deleted_by     | int          | FK       | NULL              | 删除者                      |

**外键关系**:

- `tenant_id` → `tenants.id`
- `uploaded_by` → `users.id`
- `deleted_by` → `users.id`

**枚举定义**:

**storage_type** (存储类型):

- `local`: 本地存储
- `oss`: 阿里云 OSS
- `cos`: 腾讯云 COS
- `s3`: AWS S3

**status** (文件状态):

- `active`: 正常
- `deleted`: 已删除
- `archived`: 已归档

### 11. material_categories (教材分类表)

| 字段名      | 类型         | 约束         | 默认值            | 说明      |
| ----------- | ------------ | ------------ | ----------------- | --------- |
| id          | int          | PK           | AUTO_INCREMENT    | 主键      |
| tenant_id   | int          | FK, NOT NULL | -                 | 租户 ID   |
| name        | varchar(100) | NOT NULL     | -                 | 分类名称  |
| description | text         | -            | NULL              | 分类描述  |
| parent_id   | int          | FK           | NULL              | 父分类 ID |
| level       | int          | NOT NULL     | 1                 | 层级      |
| sort_order  | int          | NOT NULL     | 0                 | 排序      |
| is_active   | boolean      | NOT NULL     | TRUE              | 是否启用  |
| created_at  | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 创建时间  |
| updated_at  | timestamp    | -            | NULL              | 更新时间  |
| created_by  | int          | FK           | NULL              | 创建者    |

**外键关系**:

- `tenant_id` → `tenants.id`
- `parent_id` → `material_categories.id`
- `created_by` → `users.id`

**唯一约束**:

- `(tenant_id, parent_id, name)` (租户内同级分类名唯一)

### 12. materials (教材表)

| 字段名           | 类型         | 约束         | 默认值            | 说明         |
| ---------------- | ------------ | ------------ | ----------------- | ------------ |
| id               | int          | PK           | AUTO_INCREMENT    | 主键         |
| tenant_id        | int          | FK, NOT NULL | -                 | 租户 ID      |
| category_id      | int          | FK           | NULL              | 分类 ID      |
| name             | varchar(200) | NOT NULL     | -                 | 教材名称     |
| code             | varchar(50)  | -            | NULL              | 教材编码     |
| description      | text         | -            | NULL              | 教材描述     |
| level            | varchar(50)  | -            | NULL              | 适用级别     |
| age_range        | varchar(50)  | -            | NULL              | 适用年龄     |
| duration_minutes | int          | -            | NULL              | 建议课时长度 |
| cover_image_id   | int          | FK           | NULL              | 封面图片 ID  |
| content_file_id  | int          | FK           | NULL              | 内容文件 ID  |
| usage_count      | int          | NOT NULL     | 0                 | 使用次数     |
| is_active        | boolean      | NOT NULL     | TRUE              | 是否启用     |
| created_at       | timestamp    | NOT NULL     | CURRENT_TIMESTAMP | 创建时间     |
| updated_at       | timestamp    | -            | NULL              | 更新时间     |
| created_by       | int          | FK           | NULL              | 创建者       |

**外键关系**:

- `tenant_id` → `tenants.id`
- `category_id` → `material_categories.id`
- `cover_image_id` → `file_storage.id`
- `content_file_id` → `file_storage.id`
- `created_by` → `users.id`

**唯一约束**:

- `(tenant_id, code)` (租户内教材编码唯一)

### 13. class_ratings (课程评价表)

| 字段名              | 类型      | 约束         | 默认值            | 说明               |
| ------------------- | --------- | ------------ | ----------------- | ------------------ |
| id                  | int       | PK           | AUTO_INCREMENT    | 主键               |
| tenant_id           | int       | FK, NOT NULL | -                 | 租户 ID            |
| scheduled_class_id  | int       | FK, NOT NULL | -                 | 课程 ID            |
| member_id           | int       | FK, NOT NULL | -                 | 会员 ID            |
| teacher_id          | int       | FK, NOT NULL | -                 | 教师 ID            |
| rating              | int       | NOT NULL     | -                 | 评分 (1-5 星)      |
| comment             | text      | -            | NULL              | 评价内容           |
| teaching_quality    | int       | -            | NULL              | 教学质量评分 (1-5) |
| interaction_quality | int       | -            | NULL              | 互动质量评分 (1-5) |
| material_quality    | int       | -            | NULL              | 教材质量评分 (1-5) |
| tags                | json      | -            | NULL              | 评价标签           |
| is_anonymous        | boolean   | NOT NULL     | FALSE             | 是否匿名           |
| is_visible          | boolean   | NOT NULL     | TRUE              | 是否可见           |
| created_at          | timestamp | NOT NULL     | CURRENT_TIMESTAMP | 创建时间           |
| updated_at          | timestamp | -            | NULL              | 更新时间           |

**外键关系**:

- `tenant_id` → `tenants.id`
- `scheduled_class_id` → `scheduled_classes.id`
- `member_id` → `members.id`
- `teacher_id` → `teachers.id`

**唯一约束**:

- `(scheduled_class_id, member_id)` (每个课程每个会员只能评价一次)

**约束检查**:

- `rating BETWEEN 1 AND 5`
- `teaching_quality BETWEEN 1 AND 5`
- `interaction_quality BETWEEN 1 AND 5`
- `material_quality BETWEEN 1 AND 5`

## 设计原则和约束

### 一致性原则

#### 1. 货币字段统一

- **✅ 所有货币字段使用 INTEGER 类型**（元单位）
- 包括：payment_amount, actual_amount, bonus_amount, refund_amount, total_earnings, total_spent 等

#### 2. 时间字段统一

- **创建时间**: `created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`
- **更新时间**: `updated_at TIMESTAMP NULL`
- **业务时间**: 根据具体业务需求使用 TIMESTAMP

#### 3. 审计字段统一

- **创建者**: `created_by INTEGER REFERENCES users(id)`
- **操作者**: `operated_by INTEGER REFERENCES users(id)`
- **上传者**: `uploaded_by INTEGER REFERENCES users(id)`

#### 4. 多租户支持

- **所有业务表包含**: `tenant_id INTEGER REFERENCES tenants(id)`
- **系统级表可为 NULL**: 表示全局数据

### 性能优化设计

#### 1. 索引策略

```sql
-- 基础索引
CREATE INDEX idx_{table}_tenant ON {table}(tenant_id);
CREATE INDEX idx_{table}_created ON {table}(tenant_id, created_at);

-- 复合索引
CREATE INDEX idx_{table}_status_type ON {table}(tenant_id, status, type);
CREATE INDEX idx_{table}_business ON {table}(business_type, business_id);
```

#### 2. 分区策略

```sql
-- 大表按月分区
CREATE TABLE {table}_partitioned (
    LIKE {table} INCLUDING ALL
) PARTITION BY RANGE (created_at);
```

#### 3. 统计数据分离

- 独立的统计表避免主表频繁更新
- 定时任务更新统计数据
- 缓存热点统计数据

### RLS 策略扩展

所有新增表都配置 RLS 策略：
直接更新现有工具类配置即可

```
app/db/rls_v2.py
setup_rls_policies_v2
```

## 总结

### 设计优势

1. **一致性**: 统一的字段类型、命名规范和约束模式
2. **完整性**: 完善的外键约束和数据验证
3. **性能**: 合理的索引设计和分区策略
4. **扩展性**: 灵活的 JSON 字段和预留扩展空间
5. **多租户**: 完整的 RLS 支持和租户隔离

### 与现有设计的改进

1. **解决货币字段不一致问题**: 统一使用 INTEGER 类型
2. **完善外键约束**: 所有引用关系都有 FK 约束
3. **性能优化**: 通过统计表分离减少主表更新压力
4. **功能扩展**: 支持支付、通知、文件管理等完整业务功能

### 实施建议

1. **分阶段实施**: 按功能模块逐步创建表
2. **数据迁移**: 制定详细的数据迁移计划
3. **性能测试**: 在大数据量下验证设计效果
4. **监控优化**: 建立完善的性能监控体系

这些设计将为系统的长期发展提供坚实的数据基础，支持 2000 并发用户和 2000 租户的业务需求。
