# 需求文档冲突标记 - 待决策

本文档标记了需求文档与实际实现之间的冲突，需要您的决策来确定是更新需求文档还是调整实现。

## 🔴 高优先级冲突（影响核心业务逻辑）

### 1. 会员卡操作记录表设计冲突

**需求文档描述** (`tasks/prd-ks-english-admin-backend-complete.md` 第486行):

```
会员(members) 1:N 消费记录(consumption_records)
```

**实际实现**:
- 使用统一的 `member_card_operations` 表记录所有操作（充值、消费、退款等）
- 没有独立的 `consumption_records` 表
- 通过 `operation_type` 字段区分操作类型

**冲突分析**:

- 需求文档假设有独立的消费记录表
- 实际实现采用了更简化的统一操作记录表设计
- 两种设计各有优劣

**决策选项**:

1. **保持当前实现** - 更新需求文档，说明使用统一操作记录表
2. **按需求实现** - 创建独立的 `consumption_records` 表，保持 `member_card_operations` 作为汇总表
3. **混合方案** - 保持当前实现，但在需求文档中明确说明设计变更原因

**建议**: 选择选项1，因为统一表设计更简洁，且已经实现并测试通过

---

## 🟡 中优先级冲突（影响文档一致性）

### 2. 数据库表关系描述不完整

**需求文档描述**:

- 缺少对 `member_card_templates` 表的关系描述
- 缺少对 `tag_categories` 和 `tags` 表关系的描述

**实际实现**:

- `member_cards` 表通过 `template_id` 关联 `member_card_templates`
- `tags` 表通过 `category_id` 关联 `tag_categories`

**决策选项**:

1. 更新需求文档，补充完整的表关系图
2. 保持需求文档简化，在技术文档中详细描述

**建议**: 选择选项1，确保需求文档的完整性

---

## 🟢 低优先级冲突（不影响功能）

### 3. 字段命名约定差异

**需求文档描述**:
- 使用 `class_datetime` 字段名

**实际实现**:
- 部分表使用 `scheduled_at` 或类似命名

**决策选项**:
1. 统一使用需求文档中的命名
2. 保持当前实现的命名约定
3. 制定统一的命名规范文档

**建议**: 选择选项3，制定命名规范文档

---

## 📋 决策记录模板

请在下方记录您的决策：

### 冲突1：会员卡操作记录表设计
- [x] 选项1：保持当前实现，更新需求文档
- [ ] 选项2：按需求实现独立表
- [ ] 选项3：混合方案
- **决策**: ______1_________
- **理由**: _______________

### 冲突2：数据库表关系描述
- [ ] 选项1：更新需求文档补充关系
- [x] 选项2：保持简化描述
- **决策**: _____2_____
- **理由**: _______________

### 冲突3：字段命名约定
- [ ] 选项1：统一使用需求文档命名
- [ ] 选项2：保持当前命名
- [ ] 选项3：制定命名规范
- **决策**: _______________
- **理由**: _______________

---

## 📝 后续行动

根据您的决策，需要执行以下行动：

1. **如果选择更新需求文档**：
   - 修改 `tasks/prd-ks-english-admin-backend-complete.md`
   - 更新相关的数据库设计文档
   - 确保所有文档一致性

2. **如果选择调整实现**：
   - 修改相关的数据库模型
   - 更新业务逻辑代码
   - 执行数据库迁移（如需要）
   - 更新测试用例

3. **如果选择混合方案**：
   - 在需求文档中添加设计变更说明
   - 创建技术决策记录文档
   - 确保团队理解变更原因

---

**创建时间**: 2025-07-12  
**状态**: 待决策  
**负责人**: 项目负责人
