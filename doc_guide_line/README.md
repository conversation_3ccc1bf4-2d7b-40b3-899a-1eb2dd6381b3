# 项目文档指导中心

本目录包含KS English Admin Backend项目的核心开发指导文档。

## 📚 文档导航

### 🎯 核心开发指南
- [API设计指南](./Api设计/API_DESIGN_GUIDE.md) - 统一API响应格式、异常处理规范
- [路由设计指南](./Api设计/ROUTE_DESIGN_GUIDE.md) - 路由结构、URL命名规范
- [路由重构总结](./路由重构总结.md) - 路由架构调整记录

### 🗄️ 数据库设计
- [当前数据库设计](./数据库设计/current-database-design.md) - 完整的数据库设计文档
- [数据库表结构](./数据库设计/current-database-tables-structure.md) - 详细表结构说明
- [数据库问题分析](./数据库设计/database-design-issues-and-recommendations.md) - 问题识别与修复建议
- [未来数据库设计](./数据库设计/future-database-design.md) - 第二、三阶段规划
- [索引相关](./数据库设计/索引相关.md) - 索引设计指南

### 🧪 测试相关
- [快速测试指南](./测试相关/QUICK_TEST_GUIDE.md) - 日常测试命令和技巧
- [完整测试指南](./测试相关/TESTING_AND_LOGGING_COMPLETE_GUIDE.md) - 测试架构和日志系统
- [测试用例规范](./测试相关/TEST_CASE_GUIDELINES_V1.md) - 测试编写标准
- [测试评审指南](./测试相关/TEST_REVIEW_GUIDE.md) - 测试代码评审标准
- [路由日志使用](./测试相关/route-logging-usage.md) - 路由日志调试方法
- [测试架构分析](./测试相关/testing-architecture-analysis.md) - 测试架构深度分析
- [测试改进使用](./测试相关/testing-improvements-usage.md) - 测试改进方案使用指南
- [测试快速参考](./测试相关/testing-quick-reference.md) - 测试命令快速参考

### ⚠️ 冲突与决策
- [需求文档冲突标记](./REQUIREMENTS_CONFLICTS_REVIEW.md) - 需求与实现冲突待决策

## 🔗 相关文档

### 扩展技术文档 (`../docs/`)
- [模块实现一致性分析](../docs/模块实现一致性分析报告.md)
- [RLS问题解决方案](../docs/rls-problem-solution-summary.md)
- [基础服务重构指南](../docs/base-service-refactoring-guide.md)
- [各模块开发指南](../docs/) - teachers, members, courses等模块指南

### 项目总结文档 (根目录)
- [项目总结](../PROJECT_SUMMARY.md) - 项目整体概述
- [架构审计日志](../ARCHITECTURE_AUDIT_LOG.md) - 架构变更记录
- [审计报告](../AUDIT_REPORT.md) - 项目审计结果
- [Claude使用指南](../CLAUDE.md) - AI助手使用说明

### 需求文档 (`../tasks/`)
- [完整产品需求](../tasks/prd-ks-english-admin-backend-complete.md)
- [核心课程模块需求](../tasks/prd-核心课程模块-v1.md)
- [数据库设计任务](../tasks/database-design/) - 详细设计文档

## 📋 文档使用指南

### 新开发者入门
1. 先阅读 [项目总结](../PROJECT_SUMMARY.md) 了解项目概况
2. 学习 [API设计指南](./Api设计/API_DESIGN_GUIDE.md) 掌握开发规范
3. 参考 [快速测试指南](./测试相关/QUICK_TEST_GUIDE.md) 设置开发环境
4. 查看相关模块的开发指南开始具体开发

### 日常开发参考
- **API开发**: 参考API设计指南和路由设计指南
- **数据库修改**: 查看数据库设计文档和问题分析
- **测试编写**: 使用测试用例规范和快速测试指南
- **问题调试**: 参考路由日志使用和完整测试指南

### 架构决策
- **需求冲突**: 查看需求文档冲突标记
- **设计变更**: 参考架构审计日志和模块一致性分析
- **性能优化**: 查看数据库问题分析和索引相关文档

## 🔄 文档维护

### 更新原则
1. **代码优先**: 代码实现是真理，文档应与代码保持同步
2. **及时更新**: 重大变更后及时更新相关文档
3. **版本控制**: 重要变更在git中记录，便于追踪

### 文档分类
- **指导性文档**: 长期稳定，如设计规范、开发指南
- **描述性文档**: 随实现变化，如数据库设计、API文档
- **决策性文档**: 记录重要决策，如冲突标记、架构审计

---

**最后更新**: 2025-07-12  
**维护者**: 项目团队  
**版本**: v1.0
