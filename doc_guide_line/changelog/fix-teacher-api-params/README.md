# 教师API参数优化

## 概述

本次优化统一了教师API的参数传递方式，将原来的多个独立Query参数改为使用TeacherQuery模型，与其他模块的API保持一致。

## 问题分析

### 原有问题

**教师API（问题方式）**：
```python
def get_teachers(
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    name: Optional[str] = Query(default=None, description="教师姓名搜索"),
    teacher_category: Optional[TeacherCategory] = Query(default=None, description="教师分类"),
    region: Optional[TeacherRegion] = Query(default=None, description="教师区域"),
    status: Optional[TeacherStatus] = Query(default=None, description="教师状态"),
    show_to_members: Optional[bool] = Query(default=None, description="是否对会员端展示"),
    min_price: Optional[int] = Query(default=None, ge=0, description="最低价格"),
    max_price: Optional[int] = Query(default=None, ge=0, description="最高价格"),
    tag_ids_str: Optional[str] = Query(default=None, alias="tag_ids", description="标签ID列表，逗号分隔，如：1,2,3"),
    sort_by: str = Query(default="created_at", description="排序字段"),
    sort_order: str = Query(default="desc", regex="^(asc|desc)$", description="排序方向：asc/desc"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
```

**会员卡API（推荐方式）**：
```python
def get_member_cards(
    query: MemberCardQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
```

### 问题分析

1. **不一致性**: 教师API使用多个独立参数，而其他模块使用Query模型
2. **维护困难**: 参数变更需要修改多处代码
3. **代码冗余**: 函数签名过长，可读性差
4. **类型安全**: 缺乏统一的参数验证和类型检查

## 解决方案

### 1. 修改API接口

**优化后的教师API**：
```python
def get_teachers(
    query: TeacherQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
```

### 2. 增强TeacherQuery模型

**处理tag_ids参数的特殊需求**：
```python
class TeacherQuery(SQLModel):
    """教师查询参数模型"""
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    name: Optional[str] = Field(default=None, description="教师姓名搜索")
    teacher_category: Optional[TeacherCategory] = Field(default=None, description="教师分类")
    region: Optional[TeacherRegion] = Field(default=None, description="教师区域")
    status: Optional[TeacherStatus] = Field(default=None, description="教师状态")
    show_to_members: Optional[bool] = Field(default=None, description="是否对会员端展示")
    min_price: Optional[int] = Field(default=None, ge=0, description="最低价格")
    max_price: Optional[int] = Field(default=None, ge=0, description="最高价格")
    tag_ids: Optional[str] = Field(default=None, alias="tag_ids", description="标签ID列表，逗号分隔，如：1,2,3")
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", description="排序方向")
    
    @property
    def tag_ids_list(self) -> Optional[List[int]]:
        """解析tag_ids字符串为整数列表"""
        if not self.tag_ids:
            return None
        try:
            return [int(tag_id.strip()) for tag_id in self.tag_ids.split(',') if tag_id.strip()]
        except ValueError:
            raise ValueError('标签ID必须是有效的整数，用逗号分隔')
    
    def validate_tag_ids_format(self) -> List[int]:
        """验证并返回标签ID列表"""
        if not self.tag_ids:
            return []
        try:
            tag_id_list = [int(tag_id.strip()) for tag_id in self.tag_ids.split(',') if tag_id.strip()]
            if any(tag_id <= 0 for tag_id in tag_id_list):
                raise ValueError('标签ID必须是正整数')
            return tag_id_list
        except ValueError as e:
            if '标签ID必须是正整数' in str(e):
                raise e
            raise ValueError('标签ID格式不正确，请使用逗号分隔的整数，如：1,2,3')
```

### 3. 更新Service层

**修改标签筛选逻辑**：
```python
# 原来的方式
if query_params.tag_ids:
    tag_subquery = select(TeacherTag.teacher_id).where(
        TeacherTag.tag_id.in_(query_params.tag_ids)
    )

# 优化后的方式
tag_ids_list = query_params.validate_tag_ids_format()
if tag_ids_list:
    tag_subquery = select(TeacherTag.teacher_id).where(
        TeacherTag.tag_id.in_(tag_ids_list)
    )
```

## 优化效果

### 1. 代码简洁性

- **函数签名**: 从16行减少到4行
- **参数管理**: 统一在TeacherQuery模型中管理
- **类型安全**: 利用Pydantic的验证功能

### 2. 一致性

- **API风格**: 与会员卡、会员等其他模块保持一致
- **参数传递**: 统一使用Query模型的方式
- **文档生成**: FastAPI自动生成完整的参数文档

### 3. 维护性

- **参数变更**: 只需修改TeacherQuery模型
- **验证逻辑**: 集中在模型中，便于维护
- **错误处理**: 统一的参数验证和错误提示

### 4. 扩展性

- **新增参数**: 在TeacherQuery中添加即可
- **复杂验证**: 可以添加更多的验证器
- **业务逻辑**: 可以在模型中添加业务相关的方法

## API使用示例

### 基础查询
```
GET /api/v1/admin/teachers/?page=1&size=20
```

### 带筛选条件
```
GET /api/v1/admin/teachers/?name=张三&teacher_category=chinese&status=active
```

### 带标签筛选
```
GET /api/v1/admin/teachers/?tag_ids=1,2,3&sort_by=created_at&sort_order=desc
```

### 价格范围筛选
```
GET /api/v1/admin/teachers/?min_price=100&max_price=500
```

## 测试验证

### 运行结果

- ✅ 所有现有测试用例通过（34个）
- ✅ 参数验证功能正常
- ✅ 标签筛选功能正常
- ✅ API文档生成正确

### 测试命令

```bash
# 运行所有教师API测试
python -m pytest tests/integration/api/v1/admin/test_teachers.py -v

# 测试特定功能
python -m pytest tests/integration/api/v1/admin/test_teachers.py::TestAdminTeacherAPI::test_get_teachers_success -v
```

## 相关文件

### 修改的文件

- `app/api/v1/admin/teachers.py` - API接口定义
- `app/features/teachers/schemas.py` - TeacherQuery模型增强
- `app/features/teachers/service.py` - Service层标签处理逻辑

### 测试文件

- `tests/integration/api/v1/admin/test_teachers.py` - API集成测试

## 总结

本次优化成功统一了教师API的参数传递方式，提升了代码的一致性、可维护性和扩展性。同时保持了向后兼容性，所有现有功能都正常工作。

这种统一的API设计模式可以作为其他模块的参考，有助于提升整个项目的代码质量和开发效率。
