# 路由设计指南

## 📋 目录

- [路由结构设计](#路由结构设计)
- [URL 命名规范](#url命名规范)
- [路由实现模式](#路由实现模式)
- [权限控制](#权限控制)
- [开发实践](#开发实践)

## 🏗️ 路由结构设计

### 总体架构

```
/api/v1/
├── admin/          # 管理端API (机构管理员)
├── member/         # 会员端API (学员用户)
├── teacher/        # 教师端API (未来扩展)
├── public/         # 公共API
```

### 目录结构

```
app/api/v1/
├── admin/
│   ├── __init__.py
│   ├── tenants.py          # 租户管理
│   ├── users.py            # 用户管理
│   ├── members.py          # 会员管理
│   ├── teachers.py         # 教师管理
│   ├── courses.py          # 课程管理
│   ├── member_cards.py     # 会员卡管理
│   └── tags.py             # 标签管理
├── member/
│   ├── __init__.py
│   ├── profile.py          # 个人信息
│   ├── courses.py          # 课程预约
│   ├── cards.py            # 会员卡查询
│   └── records.py          # 记录查询
├── public/
│   ├── __init__.py
│   ├── auth.py             # 认证相关
│   └── info.py             # 公开信息
└── api.py                  # 主路由注册
```

## 🔗 URL 命名规范

### 路径结构

```
/api/v1/{role}/{resource}/{action}
```

### 管理端 API 示例（仅部分）

```bash
# 会员管理
GET    /api/v1/admin/members              # 获取会员列表
POST   /api/v1/admin/members              # 创建会员
GET    /api/v1/admin/members/{id}         # 获取会员详情
PUT    /api/v1/admin/members/{id}         # 更新会员信息

# 会员卡管理
GET    /api/v1/admin/member-cards/templates    # 获取卡模板列表
POST   /api/v1/admin/member-cards/templates    # 创建卡模板
GET    /api/v1/admin/member-cards/cards        # 获取会员卡列表
POST   /api/v1/admin/member-cards/cards/{id}/recharge  # 会员卡充值

# 课程管理
GET    /api/v1/admin/courses/classes      # 获取课程列表
POST   /api/v1/admin/courses/classes      # 创建课程
POST   /api/v1/admin/courses/classes/{id}/assign  # 分配课程
```

### 会员端 API 示例

```bash
# 个人信息
GET    /api/v1/member/profile/me          # 获取我的信息
PUT    /api/v1/member/profile/me          # 更新我的信息

# 课程预约
GET    /api/v1/member/courses/available   # 获取可预约课程
GET    /api/v1/member/courses/my          # 获取我的课程
POST   /api/v1/member/courses/{id}/book   # 预约课程

# 会员卡查询
GET    /api/v1/member/cards/my            # 获取我的会员卡
GET    /api/v1/member/cards/my/primary    # 获取主要会员卡
```

### 公共 API 示例

```bash
# 认证相关
POST   /api/v1/auth/admin/login                  # 管理员登录
POST   /api/v1/auth/member/login                # 会员登录

# 公开信息
GET    /api/v1/info/health                # 健康检查
GET    /api/v1/info/version               # 版本信息
```

## 🔐 权限控制

### 依赖注入模式

```python
# 管理端权限
from app.core.dependencies import get_user_context, UserContext

@router.get("/members")
def get_members(user_context: UserContext = Depends(get_user_context)):
    # 自动验证管理员权限
    pass

# 会员端权限
from app.core.dependencies import get_member_context, MemberContext

@router.get("/my")
def get_my_profile(member_context: MemberContext = Depends(get_member_context)):
    # 自动验证会员权限
    pass
```

### 权限验证流程

1. **路由级别**：通过依赖注入验证角色
2. **服务级别**：通过 RLS 策略控制数据访问
3. **业务级别**：在服务层实现细粒度权限控制

## 💻 开发实践

### 1. 路由注册

在 `app/api/v1/api.py` 中统一注册：

```python
def create_api_router() -> APIRouter:
    api_router = APIRouter()

    # 管理端路由组
    api_router.include_router(tenants.router, prefix="/admin/tenants", tags=["管理端-租户管理"])
    api_router.include_router(members.router, prefix="/admin/members", tags=["管理端-会员管理"])

    # 会员端路由组
    api_router.include_router(profile.router, prefix="/member/profile", tags=["会员端-个人中心"])
    api_router.include_router(courses.router, prefix="/member/courses", tags=["会员端-课程预约"])

    return api_router
```

### 2. 标签规范

- **管理端**：`["管理端-{功能模块}"]`
- **会员端**：`["会员端-{功能模块}"]`
- **公共 API**：`["{功能描述}"]`

### 3. 响应格式

统一使用 `app.api.common.responses` 中的响应模型：

```python
from app.api.common.responses import DataResponse, PageResponse, success_response, page_response

@router.get("/members", response_model=PageResponse[MemberList])
def get_members():
    members, total = service.get_members(query)
    return page_response(members, total, query.page, query.size, "获取会员列表成功")
```

### 4. 错误处理

使用统一异常处理：

```python
from app.api.common.exceptions import BusinessException

# 在服务层抛出异常
if not member:
    raise BusinessException("会员不存在", "MEMBER_NOT_FOUND")
```

## 📚 相关文档

- [API 设计指南](./API_DESIGN_GUIDE.md)
- [测试指南](../测试相关/QUICK_TEST_GUIDE.md)

---

**版本**: v1.0  
**更新时间**: 2025-07-08  
**维护者**: 开发团队
