{"openapi": "3.1.0", "info": {"title": "课程预订系统API", "description": "KS English Admin Backend API - 多租户SaaS后端，提供课程预约与管理能力。\n\n\n## 🎯 系统概述\n\n这是一个现代化的课程预订系统后端API，基于FastAPI + SQLModel + PostgreSQL构建。\n系统采用多租户架构，支持不同机构独立管理用户和会员数据。\n\n## 🔐 认证机制\n\n系统使用JWT（JSON Web Token）进行身份认证：\n\n1. **获取Token**: 通过 `/auth/login` 接口登录获取access_token\n2. **使用Token**: 在请求头中添加 `Authorization: Bearer <access_token>`\n3. **Token刷新**: 使用refresh_token通过 `/auth/refresh` 接口刷新access_token\n\n## 📊 响应格式\n\n所有API响应都遵循统一的格式：\n\n\n### 成功响应\n\n```json\n{\n  \"success\": true,\n  \"message\": \"操作成功\",\n  \"http_code\": 200,\n  \"business_code\": \"SUCCESS\",\n  \"data\": { ... },\n  \"timestamp\": \"2024-01-01T00:00:00Z\"\n}\n```\n\n### 不分页列表响应\n\n```json\n{\n  \"success\": true,\n  \"message\": \"获取列表成功\",\n  \"http_code\": 200,\n  \"business_code\": \"SUCCESS\",\n  \"data\": [ ... ],\n  \"total\": 10,\n  \"timestamp\": \"2024-01-01T00:00:00Z\"\n}\n```\n\n### 分页列表响应\n```json\n{\n  \"success\": true,\n  \"message\": \"获取数据成功\",\n  \"http_code\": 200,\n  \"business_code\": \"SUCCESS\",\n  \"data\": [...],\n  \"total\": 100,\n  \"page\": 1,\n  \"size\": 20,\n  \"pages\": 5,\n  \"timestamp\": \"2024-01-01T00:00:00Z\"\n}\n```\n\n### 错误响应\n\n```json\n{\n  \"success\": false,\n  \"message\": \"错误描述\",\n  \"http_code\": 400,\n  \"business_code\": \"SPECIFIC_ERROR_CODE\",\n  \"level\": \"error\",\n  \"details\": { ... },\n  \"timestamp\": \"2024-01-01T00:00:00Z\"\n}\n```\n\n## 🏷️ 错误码说明\n\n| 错误码 | 说明 | HTTP状态码 |\n|--------|------|------------|\n| BUSINESS_ERROR | 通用业务错误 | 400 |\n| VALIDATION_ERROR | 数据验证失败 | 422 |\n| AUTHENTICATION_FAILED | 认证失败 | 401 |\n| PERMISSION_DENIED | 权限不足 | 403 |\n\n## 📝 开发指南\n\n1. **分页查询**: 使用 `skip` 和 `limit` 参数进行分页\n2. **数据验证**: 所有输入数据都会进行严格的格式验证\n3. **多租户**: 大部分接口都会自动根据用户的租户ID进行数据隔离\n4. **幂等性**: 支持幂等操作的接口会在文档中特别说明\n\n## 🔧 环境配置\n\n使用PostgreSQL数据库，需要配置相应的环境变量\n\n## 📞 技术支持\n\n如有问题，请联系开发团队或查看项目文档。\n", "version": "1.0.0"}, "paths": {"/": {"get": {"summary": "Root", "description": "根路径", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "健康检查端点", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/admin/tenants/": {"post": {"tags": ["管理端-租户管理"], "summary": "Create Tenant", "description": "创建新租户（仅超级管理员）", "operationId": "create_tenant_api_v1_admin_tenants__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TenantRead_"}}}}}}, "get": {"tags": ["管理端-租户管理"], "summary": "Get Tenants", "description": "获取租户列表（仅超级管理员）", "operationId": "get_tenants_api_v1_admin_tenants__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TenantRead_"}}}}}}}, "/api/v1/admin/tenants/{tenant_id}": {"get": {"tags": ["管理端-租户管理"], "summary": "Get Tenant", "description": "根据ID获取租户详情（仅超级管理员）", "operationId": "get_tenant_api_v1_admin_tenants__tenant_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tenant Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TenantRead_"}}}}}}, "post": {"tags": ["管理端-租户管理"], "summary": "Update Tenant", "description": "更新租户信息（仅超级管理员）", "operationId": "update_tenant_api_v1_admin_tenants__tenant_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tenant Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TenantUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TenantRead_"}}}}}}}, "/api/v1/admin/tenants/code/{code}": {"get": {"tags": ["管理端-租户管理"], "summary": "Get Tenant By Code", "description": "根据代码获取租户详情（仅超级管理员）", "operationId": "get_tenant_by_code_api_v1_admin_tenants_code__code__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "title": "Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TenantRead_"}}}}}}}, "/api/v1/admin/tenants/{tenant_id}/delete": {"post": {"tags": ["管理端-租户管理"], "summary": "Delete Tenant", "description": "删除租户（软删除）（仅超级管理员）", "operationId": "delete_tenant_api_v1_admin_tenants__tenant_id__delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tenant Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/tenants/plans/templates": {"get": {"tags": ["管理端-租户管理"], "summary": "Get Plan Templates", "description": "获取所有套餐模板（仅超级管理员）", "operationId": "get_plan_templates_api_v1_admin_tenants_plans_templates_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TenantPlanTemplate_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/tenants/{tenant_id}/apply-plan/{plan_code}": {"post": {"tags": ["管理端-租户管理"], "summary": "Apply Plan Template", "description": "为租户应用套餐模板（仅超级管理员）", "operationId": "apply_plan_template_api_v1_admin_tenants__tenant_id__apply_plan__plan_code__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tenant Id"}}, {"name": "plan_code", "in": "path", "required": true, "schema": {"type": "string", "title": "Plan Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TenantRead_"}}}}}}}, "/api/v1/admin/tenants/{tenant_id}/activate": {"post": {"tags": ["管理端-租户管理"], "summary": "Activate Tenant", "description": "激活租户（仅超级管理员）", "operationId": "activate_tenant_api_v1_admin_tenants__tenant_id__activate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tenant Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TenantRead_"}}}}}}}, "/api/v1/admin/tenants/{tenant_id}/suspend": {"post": {"tags": ["管理端-租户管理"], "summary": "Suspend <PERSON><PERSON>", "description": "暂停租户（仅超级管理员）", "operationId": "suspend_tenant_api_v1_admin_tenants__tenant_id__suspend_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tenant_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tenant Id"}}, {"name": "reason", "in": "query", "required": false, "schema": {"type": "string", "title": "Reason"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TenantRead_"}}}}}}}, "/api/v1/admin/users/": {"post": {"tags": ["管理端-用户管理"], "summary": "Create User", "description": "创建用户", "operationId": "create_user_api_v1_admin_users__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}, "get": {"tags": ["管理端-用户管理"], "summary": "Get Users", "description": "获取用户列表", "operationId": "get_users_api_v1_admin_users__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "跳过记录数", "default": 0, "title": "<PERSON><PERSON>"}, "description": "跳过记录数"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "返回记录数", "default": 100, "title": "Limit"}, "description": "返回记录数"}, {"name": "role", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/UserRole"}, {"type": "null"}], "description": "按角色筛选", "title": "Role"}, "description": "按角色筛选"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/UserStatus"}, {"type": "null"}], "description": "按状态筛选", "title": "Status"}, "description": "按状态筛选"}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "搜索关键词", "title": "Search"}, "description": "搜索关键词"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}": {"get": {"tags": ["管理端-用户管理"], "summary": "Get User", "description": "获取用户详情", "operationId": "get_user_api_v1_admin_users__user_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}/update": {"post": {"tags": ["管理端-用户管理"], "summary": "Update User", "description": "更新用户信息", "operationId": "update_user_api_v1_admin_users__user_id__update_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}/change-password": {"post": {"tags": ["管理端-用户管理"], "summary": "Change Password", "description": "修改密码", "operationId": "change_password_api_v1_admin_users__user_id__change_password_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordChange"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/users/{user_id}/reset-password": {"post": {"tags": ["管理端-用户管理"], "summary": "Reset Password", "description": "重置密码（管理员操作）", "operationId": "reset_password_api_v1_admin_users__user_id__reset_password_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "new_password", "in": "query", "required": true, "schema": {"type": "string", "title": "New Password"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/users/{user_id}/activate": {"post": {"tags": ["管理端-用户管理"], "summary": "Activate User", "description": "激活用户", "operationId": "activate_user_api_v1_admin_users__user_id__activate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}/deactivate": {"post": {"tags": ["管理端-用户管理"], "summary": "Deactivate User", "description": "停用用户", "operationId": "deactivate_user_api_v1_admin_users__user_id__deactivate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}/lock": {"post": {"tags": ["管理端-用户管理"], "summary": "Lock User", "description": "锁定用户", "operationId": "lock_user_api_v1_admin_users__user_id__lock_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_UserRead_"}}}}}}}, "/api/v1/admin/users/{user_id}/delete": {"post": {"tags": ["管理端-用户管理"], "summary": "Delete User", "description": "删除用户", "operationId": "delete_user_api_v1_admin_users__user_id__delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/users/salesman/list": {"get": {"tags": ["管理端-用户管理"], "summary": "Get Salesmen", "description": "获取销售人员列表（角色为AGENT，状态为ACTIVE的用户）", "operationId": "get_salesmen_api_v1_admin_users_salesman_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_UserRead_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/members/": {"post": {"tags": ["管理端-会员管理"], "summary": "创建会员", "description": "创建新会员账户\n    \n    **可能的错误码：**\n    - `MEMBER_PHONE_EXISTS`: 手机号已存在\n    - `MEMBER_EMAIL_EXISTS`: 邮箱已存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "create_member_api_v1_admin_members__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}}, "get": {"tags": ["管理端-会员管理"], "summary": "获取会员列表", "description": "分页获取会员列表\n    \n    **分页参数：**\n    - `page`: 页码，从1开始（默认1）\n    - `size`: 每页大小，默认20，最大100\n    \n    **筛选参数：**\n    - `member_type`: 会员类型\n    - `member_status`: 会员状态\n    - `agent_id`: 代理ID\n    - `search_keyword`: 搜索关键词", "operationId": "get_members_api_v1_admin_members__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MemberType"}, {"type": "null"}], "title": "Member Type"}}, {"name": "member_status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MemberStatus"}, {"type": "null"}], "title": "Member Status"}}, {"name": "agent_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id"}}, {"name": "search_keyword", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search Keyword"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "default": 20, "title": "Size"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_MemberRead_"}}}}}}}, "/api/v1/admin/members/count": {"get": {"tags": ["管理端-会员管理"], "summary": "Count Members", "description": "统计会员数量", "operationId": "count_members_api_v1_admin_members_count_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MemberType"}, {"type": "null"}], "title": "Member Type"}}, {"name": "member_status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MemberStatus"}, {"type": "null"}], "title": "Member Status"}}, {"name": "agent_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_int_"}}}}}}}, "/api/v1/admin/members/{member_id}": {"get": {"tags": ["管理端-会员管理"], "summary": "Get Member", "description": "获取会员详情", "operationId": "get_member_api_v1_admin_members__member_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}}}, "/api/v1/admin/members/{member_id}/update": {"post": {"tags": ["管理端-会员管理"], "summary": "Update Member", "description": "更新会员信息", "operationId": "update_member_api_v1_admin_members__member_id__update_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}}}, "/api/v1/admin/members/{member_id}/update-status": {"post": {"tags": ["管理端-会员管理"], "summary": "Update Member Status", "description": "更新会员状态", "operationId": "update_member_status_api_v1_admin_members__member_id__update_status_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}, {"name": "status", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/MemberStatus"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}}}, "/api/v1/admin/members/{member_id}/update-stats": {"post": {"tags": ["管理端-会员管理"], "summary": "Update Member Stats", "description": "更新会员统计信息", "operationId": "update_member_stats_api_v1_admin_members__member_id__update_stats_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}, {"name": "class_completed", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class Completed"}}, {"name": "class_cancelled", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class Cancelled"}}, {"name": "class_no_show", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class No Show"}}, {"name": "amount_spent", "in": "query", "required": false, "schema": {"type": "number", "default": 0.0, "title": "Amount Spent"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}}}, "/api/v1/admin/members/{member_id}/delete": {"post": {"tags": ["管理端-会员管理"], "summary": "Delete Member", "description": "删除会员", "operationId": "delete_member_api_v1_admin_members__member_id__delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/members/{member_id}/deactivate": {"post": {"tags": ["管理端-会员管理"], "summary": "Deactivate Member", "description": "停用会员", "operationId": "deactivate_member_api_v1_admin_members__member_id__deactivate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/members/{member_id}/activate": {"post": {"tags": ["管理端-会员管理"], "summary": "Activate Member", "description": "激活会员", "operationId": "activate_member_api_v1_admin_members__member_id__activate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/members/fixed-locks/": {"post": {"tags": ["管理端-会员固定课位"], "summary": "创建会员固定课位锁定", "description": "创建会员固定课位锁定记录\n    \n    **可能的错误码：**\n    - `MEMBER_NOT_FOUND`: 会员不存在\n    - `TEACHER_SLOT_NOT_FOUND`: 教师固定时间段不存在\n    - `SLOT_NOT_AVAILABLE`: 时间段不可用\n    - `SLOT_NOT_VISIBLE`: 时间段对会员不可见\n    - `SLOT_ALREADY_LOCKED`: 时间段已被锁定", "operationId": "create_member_fixed_slot_lock_api_v1_admin_members_fixed_locks__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberFixedSlotLockCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberFixedSlotLockResponse_"}}}}}}, "get": {"tags": ["管理端-会员固定课位"], "summary": "获取会员固定课位锁定列表", "description": "查询会员固定课位锁定记录列表，支持多种筛选条件", "operationId": "get_member_fixed_slot_locks_api_v1_admin_members_fixed_locks__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "会员ID", "title": "Member Id"}, "description": "会员ID"}, {"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "teacher_fixed_slot_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师固定时间段ID", "title": "Teacher Fixed Slot Id"}, "description": "教师固定时间段ID"}, {"name": "weekday", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "maximum": 7, "minimum": 1}, {"type": "null"}], "description": "星期几（1-7）", "title": "Weekday"}, "description": "星期几（1-7）"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "weekday,start_time", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "description": "排序方向", "default": "asc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_MemberFixedSlotLockList_"}}}}}}}, "/api/v1/admin/members/fixed-locks/batch": {"post": {"tags": ["管理端-会员固定课位"], "summary": "批量创建会员固定课位锁定", "description": "批量创建会员固定课位锁定记录\n    \n    **业务规则：**\n    - 跳过不存在或不可用的时间段\n    - 跳过已被锁定的时间段\n    - 返回成功和失败的详情", "operationId": "batch_create_member_fixed_slot_locks_api_v1_admin_members_fixed_locks_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberFixedSlotLockBatchCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_BatchLockOperationResult_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/members/fixed-locks/member/{member_id}": {"get": {"tags": ["管理端-会员固定课位"], "summary": "获取指定会员的固定课位锁定", "description": "获取指定会员的所有固定课位锁定记录", "operationId": "get_member_locks_api_v1_admin_members_fixed_locks_member__member_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_MemberFixedSlotLockList_"}}}}}}}, "/api/v1/admin/members/fixed-locks/teacher/{teacher_id}": {"get": {"tags": ["管理端-会员固定课位"], "summary": "获取指定教师的固定课位锁定情况", "description": "获取指定教师所有时间段的锁定情况", "operationId": "get_teacher_slot_locks_api_v1_admin_members_fixed_locks_teacher__teacher_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_MemberFixedSlotLockList_"}}}}}}}, "/api/v1/admin/members/fixed-locks/available-slots": {"get": {"tags": ["管理端-会员固定课位"], "summary": "获取可锁定的时间段", "description": "查询可以被会员锁定的教师固定时间段", "operationId": "get_available_slots_api_v1_admin_members_fixed_locks_available_slots_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "weekdays", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "星期列表，逗号分隔，如：1,2,3", "title": "Weekdays"}, "description": "星期列表，逗号分隔，如：1,2,3"}, {"name": "only_available", "in": "query", "required": false, "schema": {"type": "boolean", "description": "仅返回可用时间段", "default": true, "title": "Only Available"}, "description": "仅返回可用时间段"}, {"name": "only_visible", "in": "query", "required": false, "schema": {"type": "boolean", "description": "仅返回对会员可见的时间段", "default": true, "title": "Only Visible"}, "description": "仅返回对会员可见的时间段"}, {"name": "exclude_locked", "in": "query", "required": false, "schema": {"type": "boolean", "description": "排除已被锁定的时间段", "default": true, "title": "Exclude Locked"}, "description": "排除已被锁定的时间段"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_AvailableSlotResponse_"}}}}}}}, "/api/v1/admin/members/fixed-locks/conflict-check": {"post": {"tags": ["管理端-会员固定课位"], "summary": "检测锁定冲突", "description": "检测指定时间段是否存在锁定冲突", "operationId": "check_lock_conflict_api_v1_admin_members_fixed_locks_conflict_check_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LockConflictCheck"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_LockConflictResult_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/members/fixed-locks/batch/delete": {"post": {"tags": ["管理端-会员固定课位"], "summary": "批量删除固定课位锁定", "description": "批量删除多个固定课位锁定记录", "operationId": "batch_delete_locks_api_v1_admin_members_fixed_locks_batch_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberFixedSlotLockBatchDelete"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_BatchLockOperationResult_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/members/fixed-locks/{lock_id}/delete": {"post": {"tags": ["管理端-会员固定课位"], "summary": "删除固定课位锁定", "description": "删除指定的固定课位锁定记录", "operationId": "delete_lock_api_v1_admin_members_fixed_locks__lock_id__delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lock_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lock Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/members/fixed-locks/{lock_id}/cancel": {"post": {"tags": ["管理端-会员固定课位"], "summary": "取消固定课位锁定", "description": "取消指定的固定课位锁定（会员取消会删除记录）", "operationId": "cancel_lock_api_v1_admin_members_fixed_locks__lock_id__cancel_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lock_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lock Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/members/fixed-locks/{lock_id}/admin-cancel": {"post": {"tags": ["管理端-会员固定课位"], "summary": "管理员取消固定课位锁定", "description": "管理员取消指定的固定课位锁定，保留记录并标记为已取消", "operationId": "admin_cancel_lock_api_v1_admin_members_fixed_locks__lock_id__admin_cancel_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lock_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lock Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberFixedSlotLockResponse_"}}}}}}}, "/api/v1/admin/members/fixed-locks/{lock_id}": {"get": {"tags": ["管理端-会员固定课位"], "summary": "获取固定课位锁定详情", "description": "根据ID获取固定课位锁定的详细信息", "operationId": "get_member_fixed_slot_lock_api_v1_admin_members_fixed_locks__lock_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lock_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lock Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberFixedSlotLockResponse_"}}}}}}}, "/api/v1/admin/members/fixed-locks/data-consistency/check": {"post": {"tags": ["管理端-会员固定课位"], "summary": "检查冗余字段数据一致性", "description": "检查并修复会员固定课位锁定表中的冗余字段与主表数据的一致性", "operationId": "check_data_consistency_api_v1_admin_members_fixed_locks_data_consistency_check_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "fix_inconsistencies", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否自动修复发现的不一致数据", "default": false, "title": "Fix Inconsistencies"}, "description": "是否自动修复发现的不一致数据"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/members/fixed-locks/data-consistency/validate/{lock_id}": {"get": {"tags": ["管理端-会员固定课位"], "summary": "验证特定锁定记录的数据一致性", "description": "验证指定会员固定课位锁定记录的冗余字段与主表数据的一致性", "operationId": "validate_lock_consistency_api_v1_admin_members_fixed_locks_data_consistency_validate__lock_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lock_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lock Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/teachers/statistics": {"get": {"tags": ["管理端-教师管理"], "summary": "获取教师统计信息", "description": "获取教师数量统计和分布信息", "operationId": "get_teacher_statistics_api_v1_admin_teachers_statistics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/priority": {"get": {"tags": ["管理端-教师管理"], "summary": "按优先级获取教师", "description": "按优先级排序获取激活状态的教师列表", "operationId": "get_teachers_by_priority_api_v1_admin_teachers_priority_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "description": "返回数量限制", "default": 10, "title": "Limit"}, "description": "返回数量限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TeacherList_"}}}}}}}, "/api/v1/admin/teachers/available": {"get": {"tags": ["管理端-教师管理"], "summary": "获取对会员可见的教师", "description": "获取激活且对会员端展示的教师列表", "operationId": "get_available_teachers_for_members_api_v1_admin_teachers_available_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "region", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherRegion"}, {"type": "null"}], "description": "教师区域筛选", "title": "Region"}, "description": "教师区域筛选"}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherCategory"}, {"type": "null"}], "description": "教师分类筛选", "title": "Category"}, "description": "教师分类筛选"}, {"name": "max_price", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "description": "最高价格筛选", "title": "Max Price"}, "description": "最高价格筛选"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TeacherList_"}}}}}}}, "/api/v1/admin/teachers/search": {"get": {"tags": ["管理端-教师管理"], "summary": "搜索教师", "description": "根据关键词搜索教师", "operationId": "search_teachers_api_v1_admin_teachers_search_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "keyword", "in": "query", "required": true, "schema": {"type": "string", "description": "搜索关键词", "title": "Keyword"}, "description": "搜索关键词"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "返回数量限制", "default": 20, "title": "Limit"}, "description": "返回数量限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TeacherList_"}}}}}}}, "/api/v1/admin/teachers/tags/batch": {"post": {"tags": ["管理端-教师管理"], "summary": "批量管理教师标签", "description": "批量为多个教师分配或移除标签", "operationId": "batch_manage_teacher_tags_api_v1_admin_teachers_tags_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherTagBatch"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/": {"post": {"tags": ["管理端-教师管理"], "summary": "创建教师", "description": "创建新教师\n    \n    **可能的错误码：**\n    - `TEACHER_EMAIL_EXISTS`: 邮箱已存在\n    - `TEACHER_PHONE_EXISTS`: 手机号已存在\n    - `TEACHER_WECHAT_BOUND`: 微信已绑定其他教师\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "create_teacher_api_v1_admin_teachers__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}, "get": {"tags": ["管理端-教师管理"], "summary": "获取教师列表", "description": "获取教师列表，支持分页、筛选和排序", "operationId": "get_teachers_api_v1_admin_teachers__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "default": 20, "title": "Size"}}, {"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}}, {"name": "teacher_category", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherCategory"}, {"type": "null"}], "title": "Teacher Category"}}, {"name": "region", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherRegion"}, {"type": "null"}], "title": "Region"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "show_to_members", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Show To Members"}}, {"name": "min_price", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>"}}, {"name": "max_price", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Max Price"}}, {"name": "tag_ids", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tag Ids"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "default": "created_at", "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "default": "desc", "title": "Sort Order"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_TeacherList_"}}}}}}}, "/api/v1/admin/teachers/all": {"get": {"tags": ["管理端-教师管理"], "summary": "获取所有教师列表", "description": "获取所有教师列表，不分页，包含标签信息", "operationId": "get_all_teachers_api_v1_admin_teachers_all_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherStatus"}, {"type": "null"}], "description": "教师状态筛选", "title": "Status"}, "description": "教师状态筛选"}, {"name": "show_to_members", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "是否对会员端展示", "title": "Show To Members"}, "description": "是否对会员端展示"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TeacherList_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}": {"get": {"tags": ["管理端-教师管理"], "summary": "获取教师详情", "description": "根据ID获取教师详情，包含关联的标签信息", "operationId": "get_teacher_api_v1_admin_teachers__teacher_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherDetail_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/update": {"post": {"tags": ["管理端-教师管理"], "summary": "更新教师信息", "description": "更新教师基本信息", "operationId": "update_teacher_api_v1_admin_teachers__teacher_id__update_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/delete": {"post": {"tags": ["管理端-教师管理"], "summary": "删除教师", "description": "删除指定教师", "operationId": "delete_teacher_api_v1_admin_teachers__teacher_id__delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/status": {"post": {"tags": ["管理端-教师管理"], "summary": "更新教师状态", "description": "更新教师状态（激活/停用/暂停）", "operationId": "update_teacher_status_api_v1_admin_teachers__teacher_id__status_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherStatusUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/update-stats": {"post": {"tags": ["管理端-教师管理"], "summary": "Update Teacher Stats", "description": "更新教师统计信息", "operationId": "update_teacher_stats_api_v1_admin_teachers__teacher_id__update_stats_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}, {"name": "class_completed", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class Completed"}}, {"name": "class_cancelled", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class Cancelled"}}, {"name": "class_no_show", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Class No Show"}}, {"name": "earnings_added", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Earnings Added"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/activate": {"post": {"tags": ["管理端-教师管理"], "summary": "激活教师", "description": "激活指定教师", "operationId": "activate_teacher_api_v1_admin_teachers__teacher_id__activate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/deactivate": {"post": {"tags": ["管理端-教师管理"], "summary": "停用教师", "description": "停用指定教师", "operationId": "deactivate_teacher_api_v1_admin_teachers__teacher_id__deactivate_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherRead_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/tags": {"post": {"tags": ["管理端-教师管理"], "summary": "为教师分配标签", "description": "为指定教师分配一个或多个标签", "operationId": "assign_tags_to_teacher_api_v1_admin_teachers__teacher_id__tags_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherTagAssign"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}, "get": {"tags": ["管理端-教师管理"], "summary": "获取教师标签列表", "description": "获取指定教师的所有标签", "operationId": "get_teacher_tags_api_v1_admin_teachers__teacher_id__tags_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_dict_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/tags/remove": {"post": {"tags": ["管理端-教师管理"], "summary": "移除教师标签", "description": "移除教师的指定标签", "operationId": "remove_tags_from_teacher_api_v1_admin_teachers__teacher_id__tags_remove_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}, {"name": "tag_ids", "in": "query", "required": true, "schema": {"type": "string", "description": "要移除的标签ID列表，逗号分隔", "title": "Tag Ids"}, "description": "要移除的标签ID列表，逗号分隔"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/avatar": {"post": {"tags": ["管理端-教师管理"], "summary": "上传教师头像", "description": "上传教师头像图片（基础版本，返回文件路径）", "operationId": "upload_teacher_avatar_api_v1_admin_teachers__teacher_id__avatar_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_teacher_avatar_api_v1_admin_teachers__teacher_id__avatar_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/teachers/{teacher_id}/avatar/delete": {"post": {"tags": ["管理端-教师管理"], "summary": "删除教师头像", "description": "删除教师头像", "operationId": "delete_teacher_avatar_api_v1_admin_teachers__teacher_id__avatar_delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/check-conflict": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "检测时间段冲突", "description": "检测指定时间段是否与现有时间段冲突\n\n    **功能说明：**\n    - 用于在创建或更新时间段前预检测冲突\n    - 支持排除指定ID（用于更新时检测）\n    - 返回true表示有冲突，false表示无冲突", "operationId": "check_time_conflict_api_v1_admin_teachers_fixed_slots_check_conflict_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TimeSlotConflictCheck"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_bool_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/fixed-slots/available-times": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "获取教师可用时间段", "description": "获取教师的可用时间段列表\n\n    **功能说明：**\n    - 支持按可用性和可见性筛选\n    - 支持按星期和时间范围筛选\n    - 按星期和时间排序返回", "operationId": "get_available_times_api_v1_admin_teachers_fixed_slots_available_times_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableTimesQuery"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TeacherFixedSlotList_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/fixed-slots/statistics": {"get": {"tags": ["管理端-教师固定时间段"], "summary": "获取固定时间段统计信息", "description": "获取教师固定时间段的统计信息，包括总数、可用数、按星期分布等", "operationId": "get_slot_statistics_api_v1_admin_teachers_fixed_slots_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID，不指定则统计所有教师", "title": "Teacher Id"}, "description": "教师ID，不指定则统计所有教师"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherFixedSlotStats_"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/teachers/batch-query": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "批量查询多个教师的固定时间表", "description": "批量查询多个教师的固定时间表\n\n    **功能说明：**\n    - 支持同时查询多个教师的时间安排\n    - 可以按星期、时间范围等条件筛选\n    - 返回按教师分组的时间表数据\n\n    **返回格式：**\n    ```json\n    {\n      \"teacher_1\": {\n        \"teacher_info\": {...},\n        \"slots\": [...]\n      },\n      \"teacher_2\": {\n        \"teacher_info\": {...},\n        \"slots\": [...]\n      }\n    }\n    ```", "operationId": "batch_query_teacher_schedules_api_v1_admin_teachers_fixed_slots_teachers_batch_query_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchQueryRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/fixed-slots/time-slots/by-time-range": {"get": {"tags": ["管理端-教师固定时间段"], "summary": "按时间范围查询所有教师的可用时间段", "description": "按时间范围查询所有教师的可用时间段\n\n    **功能说明：**\n    - 查询指定时间范围内所有教师的可用时间段\n    - 支持按教师分类、区域等条件筛选\n    - 按时间排序返回，便于排课使用", "operationId": "get_slots_by_time_range_api_v1_admin_teachers_fixed_slots_time_slots_by_time_range_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_time_from", "in": "query", "required": true, "schema": {"type": "string", "description": "开始时间范围-起始（HH:MM格式）", "title": "Start Time From"}, "description": "开始时间范围-起始（HH:MM格式）"}, {"name": "start_time_to", "in": "query", "required": true, "schema": {"type": "string", "description": "开始时间范围-结束（HH:MM格式）", "title": "Start Time To"}, "description": "开始时间范围-结束（HH:MM格式）"}, {"name": "weekdays", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "星期列表，逗号分隔（1-7）", "title": "Weekdays"}, "description": "星期列表，逗号分隔（1-7）"}, {"name": "teacher_category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "教师分类筛选", "title": "Teacher Category"}, "description": "教师分类筛选"}, {"name": "teacher_region", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "教师区域筛选", "title": "Teacher Region"}, "description": "教师区域筛选"}, {"name": "only_available", "in": "query", "required": false, "schema": {"type": "boolean", "description": "仅返回可用时间段", "default": true, "title": "Only Available"}, "description": "仅返回可用时间段"}, {"name": "only_visible", "in": "query", "required": false, "schema": {"type": "boolean", "description": "仅返回对会员可见的时间段", "default": true, "title": "Only Visible"}, "description": "仅返回对会员可见的时间段"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "description": "返回数量限制", "default": 100, "title": "Limit"}, "description": "返回数量限制"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_dict_"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/time-slots/conflicts": {"get": {"tags": ["管理端-教师固定时间段"], "summary": "查询时间段冲突情况", "description": "查询系统中的时间段冲突情况\n\n    **功能说明：**\n    - 检测同一教师的时间段重叠情况\n    - 返回冲突的时间段详情\n    - 用于数据清理和问题排查", "operationId": "get_time_slot_conflicts_api_v1_admin_teachers_fixed_slots_time_slots_conflicts_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID，不指定则检查所有教师", "title": "Teacher Id"}, "description": "教师ID，不指定则检查所有教师"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_dict_"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/availability/batch-update": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "批量更新时间段可用性", "description": "批量更新指定时间段的可用性设置\n\n    **功能说明：**\n    - 支持批量设置时间段的可用性\n    - 支持批量设置时间段对会员的可见性\n    - 可以同时更新多个时间段的状态\n\n    **返回信息：**\n    - 更新成功的时间段数量\n    - 更新失败的时间段数量和原因", "operationId": "batch_update_availability_api_v1_admin_teachers_fixed_slots_availability_batch_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailabilityUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/fixed-slots/availability/teacher-batch-update": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "按教师批量更新时间段可用性", "description": "按教师和条件批量更新时间段可用性\n\n    **功能说明：**\n    - 支持按教师ID批量更新时间段\n    - 可以按星期、时间范围等条件筛选\n    - 适用于教师请假、调整可用时间等场景\n\n    **使用场景：**\n    - 教师请假：将某个时间段设为不可用\n    - 教师调整：批量修改可见性设置\n    - 管理员操作：批量调整教师时间段状态", "operationId": "batch_update_teacher_availability_api_v1_admin_teachers_fixed_slots_availability_teacher_batch_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherAvailabilityUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/fixed-slots/availability/statistics": {"get": {"tags": ["管理端-教师固定时间段"], "summary": "获取时间段可用性统计", "description": "获取时间段可用性的统计信息\n\n    **功能说明：**\n    - 统计各种状态的时间段数量\n    - 按教师、星期等维度分组统计\n    - 提供可用性分析数据\n\n    **统计维度：**\n    - 总体统计：总数、可用数、可见数\n    - 按教师统计：每个教师的时间段分布\n    - 按星期统计：每个星期的时间段分布\n    - 按时间段统计：不同时间段的分布", "operationId": "get_availability_statistics_api_v1_admin_teachers_fixed_slots_availability_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID，不指定则统计所有教师", "title": "Teacher Id"}, "description": "教师ID，不指定则统计所有教师"}, {"name": "include_teacher_breakdown", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否包含按教师的详细统计", "default": false, "title": "Include Teacher Breakdown"}, "description": "是否包含按教师的详细统计"}, {"name": "include_weekday_breakdown", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否包含按星期的详细统计", "default": true, "title": "Include Weekday Breakdown"}, "description": "是否包含按星期的详细统计"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/batch/create": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "批量创建时间段", "description": "批量创建教师固定时间段\n\n    **功能说明：**\n    - 支持一次性创建多个时间段\n    - 自动检测时间冲突\n    - 返回创建成功和失败的详细信息\n\n    **请求格式：**\n    ```json\n    {\n      \"teacher_id\": 1,\n      \"slots\": [\n        {\n          \"weekday\": 1,\n          \"start_time\": \"08:30\",\n          \"duration_minutes\": 25,\n          \"is_available\": true,\n          \"is_visible_to_members\": true\n        }\n      ],\n      \"created_by\": 1\n    }\n    ```", "operationId": "batch_create_slots_api_v1_admin_teachers_fixed_slots_batch_create_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchCreateSlotsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/fixed-slots/batch/update": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "批量更新时间段", "description": "批量更新教师固定时间段\n\n    **功能说明：**\n    - 支持一次性更新多个时间段\n    - 支持部分字段更新\n    - 自动检测时间冲突\n\n    **请求格式：**\n    ```json\n    {\n      \"updates\": [\n        {\n          \"id\": 1,\n          \"is_available\": false\n        },\n        {\n          \"id\": 2,\n          \"duration_minutes\": 30,\n          \"is_visible_to_members\": false\n        }\n      ]\n    }\n    ```", "operationId": "batch_update_slots_api_v1_admin_teachers_fixed_slots_batch_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchUpdateSlotsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/fixed-slots/batch/delete": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "批量删除时间段", "description": "批量删除教师固定时间段\n\n    **功能说明：**\n    - 支持一次性删除多个时间段\n    - 返回删除成功和失败的详细信息\n    - 不存在的时间段会被跳过", "operationId": "batch_delete_slots_api_v1_admin_teachers_fixed_slots_batch_delete_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchDeleteSlotsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/fixed-slots/batch/copy": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "复制教师时间段", "description": "复制一个教师的时间段到另一个教师\n\n    **功能说明：**\n    - 支持复制教师的所有时间段或指定星期的时间段\n    - 可选择是否覆盖目标教师已存在的时间段\n    - 自动跳过冲突的时间段\n\n    **使用场景：**\n    - 新教师入职，复制相似教师的时间安排\n    - 教师调整，快速设置新的时间安排\n    - 批量设置，提高操作效率", "operationId": "copy_teacher_slots_api_v1_admin_teachers_fixed_slots_batch_copy_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CopySlotsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/fixed-slots/batch/clear": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "清空教师时间段", "description": "清空教师的固定时间段\n\n    **功能说明：**\n    - 支持清空教师的所有时间段或指定星期的时间段\n    - 需要确认操作以防误删\n    - 返回清空的时间段数量\n\n    **注意：**\n    - 此操作不可逆，请谨慎使用\n    - 建议在清空前先备份数据", "operationId": "clear_teacher_slots_api_v1_admin_teachers_fixed_slots_batch_clear_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClearSlotsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/teachers/fixed-slots/": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "创建教师固定时间段", "description": "为教师创建固定时间段\n\n    **功能说明：**\n    - 教师可以设置每周固定的上课时间段\n    - 系统会自动检测时间冲突\n    - 支持设置可用性和对会员的可见性\n\n    **可能的错误码：**\n    - `TIME_SLOT_CONFLICT`: 时间段冲突\n    - `TEACHER_NOT_FOUND`: 教师不存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "create_fixed_slot_api_v1_admin_teachers_fixed_slots__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherFixedSlotCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherFixedSlotResponse_"}}}}}}, "get": {"tags": ["管理端-教师固定时间段"], "summary": "获取教师固定时间段列表", "description": "获取教师固定时间段列表，支持分页、筛选和排序", "operationId": "get_fixed_slots_api_v1_admin_teachers_fixed_slots__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "weekday", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/Weekday"}, {"type": "null"}], "description": "星期几", "title": "Weekday"}, "description": "星期几"}, {"name": "is_available", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "是否可用", "title": "Is Available"}, "description": "是否可用"}, {"name": "is_visible_to_members", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "是否对会员可见", "title": "Is Visible To Members"}, "description": "是否对会员可见"}, {"name": "start_time_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "开始时间范围-起始（HH:MM格式）", "title": "Start Time From"}, "description": "开始时间范围-起始（HH:MM格式）"}, {"name": "start_time_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "开始时间范围-结束（HH:MM格式）", "title": "Start Time To"}, "description": "开始时间范围-结束（HH:MM格式）"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "weekday,start_time", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "description": "排序方向：asc/desc", "default": "asc", "title": "Sort Order"}, "description": "排序方向：asc/desc"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_TeacherFixedSlotList_"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/teachers/{teacher_id}/weekly-schedule": {"get": {"tags": ["管理端-教师固定时间段"], "summary": "获取教师周时间安排", "description": "获取指定教师的一周时间安排\n\n    **返回格式：**\n    ```json\n    {\n      \"星期一\": [时间段列表],\n      \"星期二\": [时间段列表],\n      ...\n    }\n    ```", "operationId": "get_teacher_weekly_schedule_api_v1_admin_teachers_fixed_slots_teachers__teacher_id__weekly_schedule_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/{slot_id}/toggle-availability": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "切换时间段可用性状态", "description": "切换指定时间段的可用性状态\n\n    **功能说明：**\n    - 如果当前可用，则设为不可用\n    - 如果当前不可用，则设为可用\n    - 返回更新后的时间段信息", "operationId": "toggle_slot_availability_api_v1_admin_teachers_fixed_slots__slot_id__toggle_availability_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "slot_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Slot Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherFixedSlotResponse_"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/{slot_id}/toggle-visibility": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "切换时间段对会员可见性", "description": "切换指定时间段对会员的可见性\n\n    **功能说明：**\n    - 如果当前对会员可见，则设为不可见\n    - 如果当前对会员不可见，则设为可见\n    - 返回更新后的时间段信息", "operationId": "toggle_slot_visibility_api_v1_admin_teachers_fixed_slots__slot_id__toggle_visibility_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "slot_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Slot Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherFixedSlotResponse_"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/{slot_id}": {"get": {"tags": ["管理端-教师固定时间段"], "summary": "获取固定时间段详情", "description": "根据ID获取固定时间段详情", "operationId": "get_fixed_slot_api_v1_admin_teachers_fixed_slots__slot_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "slot_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Slot Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherFixedSlotResponse_"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/{slot_id}/update": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "更新固定时间段", "description": "更新教师固定时间段信息\n\n    **功能说明：**\n    - 支持部分字段更新\n    - 更新时间相关字段时会自动检测冲突\n    - 更新后会自动刷新更新时间\n\n    **可能的错误码：**\n    - `TIME_SLOT_CONFLICT`: 时间段冲突\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "update_fixed_slot_api_v1_admin_teachers_fixed_slots__slot_id__update_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "slot_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Slot Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherFixedSlotUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherFixedSlotResponse_"}}}}}}}, "/api/v1/admin/teachers/fixed-slots/{slot_id}/delete": {"post": {"tags": ["管理端-教师固定时间段"], "summary": "删除固定时间段", "description": "删除指定的固定时间段", "operationId": "delete_fixed_slot_api_v1_admin_teachers_fixed_slots__slot_id__delete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "slot_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Slot Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/tags/categories": {"post": {"tags": ["管理端-标签管理"], "summary": "创建标签分类", "description": "创建新的标签分类\n    \n    **可能的错误码：**\n    - `TAG_CATEGORY_NAME_EXISTS`: 分类名称已存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "create_tag_category_api_v1_admin_tags_categories_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCategoryCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagCategoryRead_"}}}}}}, "get": {"tags": ["管理端-标签管理"], "summary": "获取标签分类列表", "description": "获取标签分类列表，支持搜索", "operationId": "get_tag_categories_api_v1_admin_tags_categories_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "分类名称模糊查询", "title": "Name"}, "description": "分类名称模糊查询"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TagCategoryList_"}}}}}}}, "/api/v1/admin/tags/categories/{category_id}": {"get": {"tags": ["管理端-标签管理"], "summary": "获取标签分类详情", "description": "根据ID获取标签分类详情", "operationId": "get_tag_category_api_v1_admin_tags_categories__category_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagCategoryRead_"}}}}}}, "post": {"tags": ["管理端-标签管理"], "summary": "更新标签分类", "description": "更新标签分类信息\n    \n    **可能的错误码：**\n    - `TAG_CATEGORY_NAME_EXISTS`: 分类名称已存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "update_tag_category_api_v1_admin_tags_categories__category_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCategoryUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagCategoryRead_"}}}}}}, "delete": {"tags": ["管理端-标签管理"], "summary": "删除标签分类", "description": "删除标签分类\n    \n    **可能的错误码：**\n    - `TAG_CATEGORY_HAS_TAGS`: 分类下还有标签，无法删除", "operationId": "delete_tag_category_api_v1_admin_tags_categories__category_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/tags/": {"post": {"tags": ["管理端-标签管理"], "summary": "创建标签", "description": "创建新标签\n    \n    **可能的错误码：**\n    - `TAG_TAG_NAME_EXISTS`: 标签名称在该分类下已存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "create_tag_api_v1_admin_tags__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagRead_"}}}}}}, "get": {"tags": ["管理端-标签管理"], "summary": "获取标签列表", "description": "获取标签列表，支持搜索和筛选", "operationId": "get_tags_api_v1_admin_tags__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "标签名称模糊查询", "title": "Name"}, "description": "标签名称模糊查询"}, {"name": "category_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "标签分类ID", "title": "Category Id"}, "description": "标签分类ID"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TagStatus"}, {"type": "null"}], "description": "标签状态", "title": "Status"}, "description": "标签状态"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TagRead_"}}}}}}}, "/api/v1/admin/tags/with-teacher-count": {"get": {"tags": ["管理端-标签管理"], "summary": "获取带教师数量的标签列表", "description": "获取标签列表，包含每个标签被多少教师使用的统计信息", "operationId": "get_tags_with_teacher_count_api_v1_admin_tags_with_teacher_count_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "标签名称模糊查询", "title": "Name"}, "description": "标签名称模糊查询"}, {"name": "category_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "标签分类ID", "title": "Category Id"}, "description": "标签分类ID"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TagStatus"}, {"type": "null"}], "description": "标签状态", "title": "Status"}, "description": "标签状态"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TagWithTeacherCount_"}}}}}}}, "/api/v1/admin/tags/batch": {"post": {"tags": ["管理端-标签管理"], "summary": "批量创建标签", "description": "批量创建标签\n\n    **注意：**\n    - 如果某个标签名称已存在，会跳过该标签继续处理其他标签\n    - 返回成功创建的标签列表\n\n    **可能的错误码：**\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "batch_create_tags_api_v1_admin_tags_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagBatchCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_TagRead__"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/tags/batch/update": {"post": {"tags": ["管理端-标签管理"], "summary": "批量更新标签", "description": "批量更新标签\n\n    **可能的错误码：**\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "batch_update_tags_api_v1_admin_tags_batch_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagBatchUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_TagRead__"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/tags/active": {"get": {"tags": ["管理端-标签管理"], "summary": "获取所有激活状态的标签", "description": "获取所有激活状态的标签，按分类排序", "operationId": "get_active_tags_api_v1_admin_tags_active_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TagList_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/tags/{tag_id}": {"get": {"tags": ["管理端-标签管理"], "summary": "获取标签详情", "description": "根据ID获取标签详情", "operationId": "get_tag_api_v1_admin_tags__tag_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tag_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tag Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagRead_"}}}}}}, "post": {"tags": ["管理端-标签管理"], "summary": "更新标签", "description": "更新标签信息\n\n    **可能的错误码：**\n    - `TAG_TAG_NAME_EXISTS`: 标签名称在该分类下已存在\n    - `VALIDATION_ERROR`: 数据验证失败", "operationId": "update_tag_api_v1_admin_tags__tag_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tag_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tag Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TagRead_"}}}}}}, "delete": {"tags": ["管理端-标签管理"], "summary": "删除标签", "description": "删除标签\n\n    **可能的错误码：**\n    - `TAG_TAG_IN_USE`: 标签正在使用中，无法删除", "operationId": "delete_tag_api_v1_admin_tags__tag_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "tag_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Tag Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}}}, "/api/v1/admin/tags/categories/{category_id}/tags": {"get": {"tags": ["管理端-标签管理"], "summary": "获取指定分类下的所有标签", "description": "获取指定分类下的所有标签，不分页", "operationId": "get_tags_by_category_api_v1_admin_tags_categories__category_id__tags_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Category Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TagStatus"}, {"type": "null"}], "description": "标签状态筛选", "title": "Status"}, "description": "标签状态筛选"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_TagList_"}}}}}}}, "/api/v1/admin/courses/classes/search": {"get": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "搜索课程", "description": "按多维度条件搜索课程列表", "operationId": "search_classes_api_v1_admin_courses_classes_search_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0}, {"type": "null"}], "description": "会员ID", "title": "Member Id"}, "description": "会员ID"}, {"name": "class_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "课程类型", "title": "Class Type"}, "description": "课程类型"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "课程状态", "title": "Status"}, "description": "课程状态"}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "开始日期", "title": "Date From"}, "description": "开始日期"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "结束日期", "title": "Date To"}, "description": "结束日期"}, {"name": "time_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "description": "时间段开始", "title": "Time From"}, "description": "时间段开始"}, {"name": "time_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "description": "时间段结束", "title": "Time To"}, "description": "时间段结束"}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "description": "搜索关键词", "title": "Search"}, "description": "搜索关键词"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "class_datetime", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "description": "排序方向", "default": "asc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_ScheduledClassList_"}}}}}}}, "/api/v1/admin/courses/classes/available": {"get": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "获取可预约课程", "description": "会员端获取可预约的课程列表", "operationId": "get_available_classes_api_v1_admin_courses_classes_available_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "teacher_category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "教师分类", "title": "Teacher Category"}, "description": "教师分类"}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "开始日期", "title": "Date From"}, "description": "开始日期"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "结束日期", "title": "Date To"}, "description": "结束日期"}, {"name": "time_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "description": "时间段开始", "title": "Time From"}, "description": "时间段开始"}, {"name": "time_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "description": "时间段结束", "title": "Time To"}, "description": "时间段结束"}, {"name": "max_price", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "description": "最高价格", "title": "Max Price"}, "description": "最高价格"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "class_datetime", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "description": "排序方向", "default": "asc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_ScheduledClassList_"}}}}}}}, "/api/v1/admin/courses/classes/teacher/{teacher_id}": {"get": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "获取教师课程", "description": "获取指定教师的课程列表", "operationId": "get_teacher_classes_api_v1_admin_courses_classes_teacher__teacher_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "开始日期", "title": "Date From"}, "description": "开始日期"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "结束日期", "title": "Date To"}, "description": "结束日期"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "课程状态", "title": "Status"}, "description": "课程状态"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_ScheduledClassList__"}}}}}}}, "/api/v1/admin/courses/classes/member/{member_id}": {"get": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "获取会员课程", "description": "获取指定会员的课程列表", "operationId": "get_member_classes_api_v1_admin_courses_classes_member__member_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "开始日期", "title": "Date From"}, "description": "开始日期"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "结束日期", "title": "Date To"}, "description": "结束日期"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "课程状态", "title": "Status"}, "description": "课程状态"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_ScheduledClassList__"}}}}}}}, "/api/v1/admin/courses/classes/book/{class_id}": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "预约课程", "description": "会员预约指定课程（支持会员卡自动扣费）", "operationId": "book_class_api_v1_admin_courses_classes_book__class_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Class Id"}}, {"name": "member_id", "in": "query", "required": true, "schema": {"type": "integer", "exclusiveMinimum": 0, "description": "会员ID", "title": "Member Id"}, "description": "会员ID"}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/ClassBookingData"}, {"type": "null"}], "title": "Booking Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_ScheduledClassRead_"}}}}}}}, "/api/v1/admin/courses/classes/cancel/{class_id}": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "取消课程预约", "description": "取消指定课程的预约", "operationId": "cancel_booking_api_v1_admin_courses_classes_cancel__class_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Class Id"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/ClassCancellation"}, {"type": "null"}], "title": "Cancellation Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_ScheduledClassRead_"}}}}}}}, "/api/v1/admin/courses/classes/batch/book": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "批量预约课程", "description": "给同一个会员批量预约多个课程（支持会员卡自动扣费）", "operationId": "batch_book_classes_api_v1_admin_courses_classes_batch_book_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "query", "required": true, "schema": {"type": "integer", "exclusiveMinimum": 0, "description": "会员ID", "title": "Member Id"}, "description": "会员ID"}, {"name": "class_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "integer"}, "description": "要预约的课程ID列表", "title": "Class Ids"}, "description": "要预约的课程ID列表"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_BatchBookResult_"}}}}}}}, "/api/v1/admin/courses/classes/batch/book-atomic": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "批量预约课程（原子事务）", "description": "给同一个会员批量预约多个课程，全部成功或全部失败（原子事务）", "operationId": "batch_book_classes_atomic_api_v1_admin_courses_classes_batch_book_atomic_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "query", "required": true, "schema": {"type": "integer", "exclusiveMinimum": 0, "description": "会员ID", "title": "Member Id"}, "description": "会员ID"}, {"name": "class_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "integer"}, "description": "要预约的课程ID列表", "title": "Class Ids"}, "description": "要预约的课程ID列表"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_BatchBookResult_"}}}}}}}, "/api/v1/admin/courses/classes/batch/cancel": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "批量取消课程预约", "description": "批量取消多个课程的预约", "operationId": "batch_cancel_bookings_api_v1_admin_courses_classes_batch_cancel_post", "requestBody": {"content": {"application/json": {"schema": {"items": {"type": "integer"}, "type": "array", "title": "Class Ids"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_BatchCancelResult_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/classes/check-conflict": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "检查时间冲突", "description": "检查指定时间是否存在冲突", "operationId": "check_time_conflict_api_v1_admin_courses_classes_check_conflict_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConflictCheckRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_ConflictCheckResponse_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/classes/status/{class_id}": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "更新课程状态", "description": "更新指定课程的状态", "operationId": "update_class_status_api_v1_admin_courses_classes_status__class_id__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Class Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassStatusUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_ScheduledClassRead_"}}}}}}}, "/api/v1/admin/courses/classes/batch/status": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "批量更新课程状态", "description": "批量更新多个课程的状态", "operationId": "batch_update_status_api_v1_admin_courses_classes_batch_status_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchClassStatusUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_ScheduledClassRead__"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/classes/batch/delete": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "批量删除课程", "description": "批量删除多个课程（软删除）", "operationId": "batch_delete_classes_api_v1_admin_courses_classes_batch_delete_post", "requestBody": {"content": {"application/json": {"schema": {"items": {"type": "integer"}, "type": "array", "title": "Class Ids"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageResponse"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/classes/stats/count": {"get": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "课程统计", "description": "获取课程数量统计", "operationId": "get_class_statistics_api_v1_admin_courses_classes_stats_count_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0}, {"type": "null"}], "description": "会员ID", "title": "Member Id"}, "description": "会员ID"}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "开始日期", "title": "Date From"}, "description": "开始日期"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "结束日期", "title": "Date To"}, "description": "结束日期"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/courses/classes/temp/create": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "创建临时课程", "description": "管理员创建临时课程（直接约课）", "operationId": "create_temp_class_api_v1_admin_courses_classes_temp_create_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminClassCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_ScheduledClassRead_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/classes/teacher/create": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "教师创建课程", "description": "教师开放可预约课节", "operationId": "create_teacher_class_api_v1_admin_courses_classes_teacher_create_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "query", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherClassCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_ScheduledClassRead_"}}}}}}}, "/api/v1/admin/courses/classes/batch/teacher/create": {"post": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "教师批量创建课程", "description": "教师批量开放可预约课节", "operationId": "batch_create_teacher_classes_api_v1_admin_courses_classes_batch_teacher_create_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchTeacherClassCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_ScheduledClassRead__"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/classes/{class_id}": {"get": {"tags": ["管理端-课程管理", "管理端-课程管理"], "summary": "获取课程详情", "description": "根据课程ID获取详细信息", "operationId": "get_class_detail_api_v1_admin_courses_classes__class_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Class Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_ScheduledClassRead_"}}}}}}}, "/api/v1/admin/courses/config/config": {"get": {"tags": ["管理端-课程配置"], "summary": "获取课程系统配置", "description": "获取当前租户的课程系统配置信息，如果不存在则创建默认配置", "operationId": "get_course_config_api_v1_admin_courses_config_config_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_CourseSystemConfigDetail_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/config/config/update": {"post": {"tags": ["管理端-课程配置"], "summary": "更新课程系统配置", "description": "更新当前租户的课程系统配置信息", "operationId": "update_course_config_api_v1_admin_courses_config_config_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseSystemConfigUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_CourseSystemConfigDetail_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/config/config/reset": {"post": {"tags": ["管理端-课程配置"], "summary": "重置课程系统配置", "description": "将当前租户的课程系统配置重置为默认值", "operationId": "reset_course_config_api_v1_admin_courses_config_config_reset_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_CourseSystemConfigDetail_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/config/config/field": {"post": {"tags": ["管理端-课程配置"], "summary": "更新单个配置字段", "description": "更新课程系统配置的单个字段值", "operationId": "update_config_field_api_v1_admin_courses_config_config_field_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFieldRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_CourseSystemConfigDetail_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/config/config/field/{field_name}": {"get": {"tags": ["管理端-课程配置"], "summary": "获取单个配置字段值", "description": "获取课程系统配置的单个字段值", "operationId": "get_config_field_api_v1_admin_courses_config_config_field__field_name__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "field_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Field Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/courses/config/config/booking-time-range": {"get": {"tags": ["管理端-课程配置"], "summary": "获取预约时间范围", "description": "获取课程系统的预约操作时间范围配置", "operationId": "get_booking_time_range_api_v1_admin_courses_config_config_booking_time_range_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/config/config/teacher-permissions": {"get": {"tags": ["管理端-课程配置"], "summary": "获取教师权限配置", "description": "获取教师相关的权限配置信息", "operationId": "get_teacher_permissions_api_v1_admin_courses_config_config_teacher_permissions_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/config/config/schedule-config": {"get": {"tags": ["管理端-课程配置"], "summary": "获取排课配置", "description": "获取自动排课相关的配置信息", "operationId": "get_schedule_config_api_v1_admin_courses_config_config_schedule_config_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/courses/config/config/validate-consistency": {"post": {"tags": ["管理端-课程配置"], "summary": "验证配置一致性", "description": "验证当前租户的课程系统配置是否一致", "operationId": "validate_config_consistency_api_v1_admin_courses_config_config_validate_consistency_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/member-cards/templates": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Templates", "description": "获取会员卡模板列表", "operationId": "get_card_templates_api_v1_admin_member_cards_templates_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_MemberCardTemplateList__"}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["管理端-会员卡管理"], "summary": "Create Card Template", "description": "创建会员卡模板", "operationId": "create_card_template_api_v1_admin_member_cards_templates_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberCardTemplateCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardTemplateRead_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/member-cards/templates/{template_id}": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Template", "description": "获取会员卡模板详情", "operationId": "get_card_template_api_v1_admin_member_cards_templates__template_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardTemplateRead_"}}}}}}, "put": {"tags": ["管理端-会员卡管理"], "summary": "Update Card Template", "description": "更新会员卡模板", "operationId": "update_card_template_api_v1_admin_member_cards_templates__template_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberCardTemplateUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardTemplateRead_"}}}}}}, "delete": {"tags": ["管理端-会员卡管理"], "summary": "Delete Card Template", "description": "删除会员卡模板", "operationId": "delete_card_template_api_v1_admin_member_cards_templates__template_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/member-cards/templates/{template_id}/toggle-status": {"patch": {"tags": ["管理端-会员卡管理"], "summary": "Toggle Template Status", "description": "切换会员卡模板激活状态", "operationId": "toggle_template_status_api_v1_admin_member_cards_templates__template_id__toggle_status_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardTemplateRead_"}}}}}}}, "/api/v1/admin/member-cards/cards": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Member Cards", "description": "获取会员卡列表", "operationId": "get_member_cards_api_v1_admin_member_cards_cards_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Member Id"}}, {"name": "card_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/CardType"}, {"type": "null"}], "title": "Card Type"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/CardStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "template_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Template Id"}}, {"name": "search_keyword", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search Keyword"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "default": 20, "title": "Size"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "created_at", "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "desc", "title": "Sort Order"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_MemberCardList_"}}}}}}, "post": {"tags": ["管理端-会员卡管理"], "summary": "Create Member Card", "description": "创建会员卡", "operationId": "create_member_card_api_v1_admin_member_cards_cards_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberCardCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Member Card", "description": "获取会员卡详情", "operationId": "get_member_card_api_v1_admin_member_cards_cards__card_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}, "put": {"tags": ["管理端-会员卡管理"], "summary": "Update Member Card", "description": "更新会员卡", "operationId": "update_member_card_api_v1_admin_member_cards_cards__card_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberCardUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/freeze": {"patch": {"tags": ["管理端-会员卡管理"], "summary": "Freeze Member Card", "description": "冻结会员卡", "operationId": "freeze_member_card_api_v1_admin_member_cards_cards__card_id__freeze_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}, {"name": "reason", "in": "query", "required": false, "schema": {"type": "string", "default": "管理员冻结", "title": "Reason"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/unfreeze": {"patch": {"tags": ["管理端-会员卡管理"], "summary": "Unfreeze Member Card", "description": "解冻会员卡", "operationId": "unfreeze_member_card_api_v1_admin_member_cards_cards__card_id__unfreeze_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/cancel": {"patch": {"tags": ["管理端-会员卡管理"], "summary": "Cancel Member Card", "description": "注销会员卡", "operationId": "cancel_member_card_api_v1_admin_member_cards_cards__card_id__cancel_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}, {"name": "reason", "in": "query", "required": false, "schema": {"type": "string", "default": "管理员注销", "title": "Reason"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}}}, "/api/v1/admin/member-cards/members/{member_id}/cards": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Member Cards By Member", "description": "获取指定会员的所有卡片", "operationId": "get_member_cards_by_member_api_v1_admin_member_cards_members__member_id__cards_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_MemberCardRead__"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/recharge": {"post": {"tags": ["管理端-会员卡管理"], "summary": "Recharge Member Card", "description": "会员卡充值", "operationId": "recharge_member_card_api_v1_admin_member_cards_cards__card_id__recharge_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RechargeRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_RechargeResponse_"}}}}}}}, "/api/v1/admin/member-cards/recharge/batch": {"post": {"tags": ["管理端-会员卡管理"], "summary": "Batch Recharge Member Cards", "description": "批量充值会员卡", "operationId": "batch_recharge_member_cards_api_v1_admin_member_cards_recharge_batch_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchRechargeRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_BatchRechargeResponse_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/member-cards/cards/{card_id}/recharge-history": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Recharge History", "description": "获取会员卡充值历史", "operationId": "get_card_recharge_history_api_v1_admin_member_cards_cards__card_id__recharge_history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_MemberCardOperationRead__"}}}}}}}, "/api/v1/admin/member-cards/members/{member_id}/recharge-statistics": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Member Recharge Statistics", "description": "获取会员充值统计", "operationId": "get_member_recharge_statistics_api_v1_admin_member_cards_members__member_id__recharge_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/member-cards/recharge/daily-statistics": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Daily Recharge Statistics", "description": "获取每日充值统计", "operationId": "get_daily_recharge_statistics_api_v1_admin_member_cards_recharge_daily_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": true, "schema": {"type": "string", "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": true, "schema": {"type": "string", "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/deduct": {"post": {"tags": ["管理端-会员卡管理"], "summary": "Deduct Member Card", "description": "会员卡扣费", "operationId": "deduct_member_card_api_v1_admin_member_cards_cards__card_id__deduct_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeductionRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_DeductionResponse_"}}}}}}}, "/api/v1/admin/member-cards/operations": {"get": {"tags": ["管理端-会员卡管理"], "summary": "获取会员卡操作记录", "description": "分页获取会员卡操作记录列表，支持按会员ID、卡ID和操作类型筛选\n    \n    **必填参数：**\n    - `member_id`: 会员ID\n    \n    **可选参数：**\n    - `member_card_id`: 会员卡ID\n    - `operation_types`: 操作类型列表，支持多选\n    \n    **分页参数：**\n    - `page`: 页码，从1开始（默认1）\n    - `size`: 每页大小，默认20，最大100", "operationId": "get_member_card_operations_api_v1_admin_member_cards_operations_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Member Id"}}, {"name": "member_card_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Member Card Id"}}, {"name": "operation_types", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}, "title": "Operation Types"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "default": 20, "title": "Size"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_MemberCardOperationRead_"}}}}}}}, "/api/v1/admin/member-cards/operations/{operation_id}": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Operation Detail", "description": "获取操作记录详情", "operationId": "get_card_operation_detail_api_v1_admin_member_cards_operations__operation_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "operation_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Operation Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardOperationRead_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/consumption-history": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Consumption History", "description": "获取会员卡消费历史", "operationId": "get_card_consumption_history_api_v1_admin_member_cards_cards__card_id__consumption_history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_MemberCardOperationRead__"}}}}}}}, "/api/v1/admin/member-cards/members/{member_id}/consumption-statistics": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Member Consumption Statistics", "description": "获取会员消费统计", "operationId": "get_member_consumption_statistics_api_v1_admin_member_cards_members__member_id__consumption_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/member-cards/consumption/daily-statistics": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Daily Consumption Statistics", "description": "获取每日消费统计", "operationId": "get_daily_consumption_statistics_api_v1_admin_member_cards_consumption_daily_statistics_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": true, "schema": {"type": "string", "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": true, "schema": {"type": "string", "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/member-cards/cards/{card_id}/operation-summary": {"get": {"tags": ["管理端-会员卡管理"], "summary": "Get Card Operation Summary", "description": "获取会员卡操作汇总统计", "operationId": "get_card_operation_summary_api_v1_admin_member_cards_cards__card_id__operation_summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "card_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Card Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/operation-logs/class-operations": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Class Operation Logs", "description": "获取课程操作记录列表", "operationId": "get_class_operation_logs_api_v1_admin_operation_logs_class_operations_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "scheduled_class_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "课程ID", "title": "Scheduled Class Id"}, "description": "课程ID"}, {"name": "operation_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClassOperationType"}, {"type": "null"}], "description": "操作类型", "title": "Operation Type"}, "description": "操作类型"}, {"name": "operation_status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/OperationStatus"}, {"type": "null"}], "description": "操作状态", "title": "Operation Status"}, "description": "操作状态"}, {"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "会员ID", "title": "Member Id"}, "description": "会员ID"}, {"name": "operator_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "操作人ID", "title": "Operator Id"}, "description": "操作人ID"}, {"name": "operator_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "操作人类型", "title": "Operator Type"}, "description": "操作人类型"}, {"name": "class_datetime_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "课程时间范围-起始", "title": "Class Datetime From"}, "description": "课程时间范围-起始"}, {"name": "class_datetime_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "课程时间范围-结束", "title": "Class Datetime To"}, "description": "课程时间范围-结束"}, {"name": "created_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "创建时间范围-起始", "title": "Created From"}, "description": "创建时间范围-起始"}, {"name": "created_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "创建时间范围-结束", "title": "Created To"}, "description": "创建时间范围-结束"}, {"name": "operation_description", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "操作描述（模糊搜索）", "title": "Operation Description"}, "description": "操作描述（模糊搜索）"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "created_at", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(asc|desc)$", "description": "排序方向", "default": "desc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_ScheduledClassOperationLogResponse_"}}}}}}}, "/api/v1/admin/operation-logs/class-operations/{log_id}": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Class Operation Log", "description": "获取课程操作记录详情", "operationId": "get_class_operation_log_api_v1_admin_operation_logs_class_operations__log_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "log_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Log Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_ScheduledClassOperationLogResponse_"}}}}}}}, "/api/v1/admin/operation-logs/class-operations/by-class/{class_id}": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Class Operation Logs By Class", "description": "获取指定课程的操作记录", "operationId": "get_class_operation_logs_by_class_api_v1_admin_operation_logs_class_operations_by_class__class_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Class Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "description": "限制数量", "default": 50, "title": "Limit"}, "description": "限制数量"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_ScheduledClassOperationLogResponse__"}}}}}}}, "/api/v1/admin/operation-logs/teacher-slot-operations": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Teacher Slot Operation Logs", "description": "获取教师固定时间段操作记录列表", "operationId": "get_teacher_slot_operation_logs_api_v1_admin_operation_logs_teacher_slot_operations_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_fixed_slot_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师固定时间段ID", "title": "Teacher Fixed Slot Id"}, "description": "教师固定时间段ID"}, {"name": "operation_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TeacherSlotOperationType"}, {"type": "null"}], "description": "操作类型", "title": "Operation Type"}, "description": "操作类型"}, {"name": "operation_status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/OperationStatus"}, {"type": "null"}], "description": "操作状态", "title": "Operation Status"}, "description": "操作状态"}, {"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "operator_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "操作人ID", "title": "Operator Id"}, "description": "操作人ID"}, {"name": "operator_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "操作人类型", "title": "Operator Type"}, "description": "操作人类型"}, {"name": "weekday", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "maximum": 7, "minimum": 1}, {"type": "null"}], "description": "星期几", "title": "Weekday"}, "description": "星期几"}, {"name": "created_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "创建时间范围-起始", "title": "Created From"}, "description": "创建时间范围-起始"}, {"name": "created_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "创建时间范围-结束", "title": "Created To"}, "description": "创建时间范围-结束"}, {"name": "operation_description", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "操作描述（模糊搜索）", "title": "Operation Description"}, "description": "操作描述（模糊搜索）"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "created_at", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(asc|desc)$", "description": "排序方向", "default": "desc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_TeacherFixedSlotOperationLogResponse_"}}}}}}}, "/api/v1/admin/operation-logs/teacher-slot-operations/{log_id}": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Teacher Slot Operation Log", "description": "获取教师固定时间段操作记录详情", "operationId": "get_teacher_slot_operation_log_api_v1_admin_operation_logs_teacher_slot_operations__log_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "log_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Log Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_TeacherFixedSlotOperationLogResponse_"}}}}}}}, "/api/v1/admin/operation-logs/teacher-slot-operations/by-teacher/{teacher_id}": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Teacher Slot Operation Logs By Teacher", "description": "获取指定教师的时间段操作记录", "operationId": "get_teacher_slot_operation_logs_by_teacher_api_v1_admin_operation_logs_teacher_slot_operations_by_teacher__teacher_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Teacher Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "description": "限制数量", "default": 50, "title": "Limit"}, "description": "限制数量"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_TeacherFixedSlotOperationLogResponse__"}}}}}}}, "/api/v1/admin/operation-logs/member-lock-operations": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Member Lock Operation Logs", "description": "获取会员固定位锁定操作记录列表", "operationId": "get_member_lock_operation_logs_api_v1_admin_operation_logs_member_lock_operations_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_fixed_slot_lock_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "会员固定位锁定ID", "title": "Member Fixed Slot Lock Id"}, "description": "会员固定位锁定ID"}, {"name": "operation_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MemberLockOperationType"}, {"type": "null"}], "description": "操作类型", "title": "Operation Type"}, "description": "操作类型"}, {"name": "operation_status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/OperationStatus"}, {"type": "null"}], "description": "操作状态", "title": "Operation Status"}, "description": "操作状态"}, {"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "会员ID", "title": "Member Id"}, "description": "会员ID"}, {"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "operator_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "操作人ID", "title": "Operator Id"}, "description": "操作人ID"}, {"name": "operator_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "操作人类型", "title": "Operator Type"}, "description": "操作人类型"}, {"name": "weekday", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "maximum": 7, "minimum": 1}, {"type": "null"}], "description": "星期几", "title": "Weekday"}, "description": "星期几"}, {"name": "created_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "创建时间范围-起始", "title": "Created From"}, "description": "创建时间范围-起始"}, {"name": "created_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "创建时间范围-结束", "title": "Created To"}, "description": "创建时间范围-结束"}, {"name": "operation_description", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "操作描述（模糊搜索）", "title": "Operation Description"}, "description": "操作描述（模糊搜索）"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "created_at", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(asc|desc)$", "description": "排序方向", "default": "desc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_MemberFixedLockOperationLogResponse_"}}}}}}}, "/api/v1/admin/operation-logs/member-lock-operations/{log_id}": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Member Lock Operation Log", "description": "获取会员固定位锁定操作记录详情", "operationId": "get_member_lock_operation_log_api_v1_admin_operation_logs_member_lock_operations__log_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "log_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Log Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberFixedLockOperationLogResponse_"}}}}}}}, "/api/v1/admin/operation-logs/member-lock-operations/by-member/{member_id}": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Member Lock Operation Logs By Member", "description": "获取指定会员的锁定操作记录", "operationId": "get_member_lock_operation_logs_by_member_api_v1_admin_operation_logs_member_lock_operations_by_member__member_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "member_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Member Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "description": "限制数量", "default": 50, "title": "Limit"}, "description": "限制数量"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_MemberFixedLockOperationLogResponse__"}}}}}}}, "/api/v1/admin/operation-logs/statistics/class-operations": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Class Operation Statistics", "description": "获取课程操作统计", "operationId": "get_class_operation_statistics_api_v1_admin_operation_logs_statistics_class_operations_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始时间", "title": "Start Date"}, "description": "开始时间"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束时间", "title": "End Date"}, "description": "结束时间"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/operation-logs/statistics/teacher-slot-operations": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Teacher Slot Operation Statistics", "description": "获取教师时间段操作统计", "operationId": "get_teacher_slot_operation_statistics_api_v1_admin_operation_logs_statistics_teacher_slot_operations_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始时间", "title": "Start Date"}, "description": "开始时间"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束时间", "title": "End Date"}, "description": "结束时间"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/operation-logs/statistics/member-lock-operations": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Member Lock Operation Statistics", "description": "获取会员锁定操作统计", "operationId": "get_member_lock_operation_statistics_api_v1_admin_operation_logs_statistics_member_lock_operations_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始时间", "title": "Start Date"}, "description": "开始时间"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束时间", "title": "End Date"}, "description": "结束时间"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/operation-logs/statistics/comprehensive": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Comprehensive Statistics", "description": "获取综合操作统计", "operationId": "get_comprehensive_statistics_api_v1_admin_operation_logs_statistics_comprehensive_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始时间", "title": "Start Date"}, "description": "开始时间"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束时间", "title": "End Date"}, "description": "结束时间"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/operation-logs/statistics/daily-trends": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Daily Operation Trends", "description": "获取每日操作趋势", "operationId": "get_daily_operation_trends_api_v1_admin_operation_logs_statistics_daily_trends_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time", "description": "开始时间", "title": "Start Date"}, "description": "开始时间"}, {"name": "end_date", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time", "description": "结束时间", "title": "End Date"}, "description": "结束时间"}, {"name": "operation_type", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(all|class|teacher_slot|member_lock)$", "description": "操作类型", "default": "all", "title": "Operation Type"}, "description": "操作类型"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/operation-logs/statistics/operators": {"get": {"tags": ["管理端-操作记录", "操作记录管理"], "summary": "Get Operator Statistics", "description": "获取操作人统计", "operationId": "get_operator_statistics_api_v1_admin_operation_logs_statistics_operators_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "开始时间", "title": "Start Date"}, "description": "开始时间"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "结束时间", "title": "End Date"}, "description": "结束时间"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/fixed-schedule/tasks": {"post": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Create Schedule Task", "description": "创建固定课排课任务", "operationId": "create_schedule_task_api_v1_admin_fixed_schedule_tasks_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FixedScheduleTaskCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_FixedScheduleTaskResponse_"}}}}}}, "get": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Get Schedule Tasks", "description": "获取固定课排课任务列表", "operationId": "get_schedule_tasks_api_v1_admin_fixed_schedule_tasks_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "任务名称（模糊搜索）", "title": "Task Name"}, "description": "任务名称（模糊搜索）"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ScheduleTaskStatus"}, {"type": "null"}], "description": "任务状态", "title": "Status"}, "description": "任务状态"}, {"name": "start_date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "开始日期范围-起始", "title": "Start Date From"}, "description": "开始日期范围-起始"}, {"name": "start_date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "开始日期范围-结束", "title": "Start Date To"}, "description": "开始日期范围-结束"}, {"name": "created_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "创建人ID", "title": "Created By"}, "description": "创建人ID"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "created_at", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(asc|desc)$", "description": "排序方向", "default": "desc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_FixedScheduleTaskResponse_"}}}}}}}, "/api/v1/admin/fixed-schedule/tasks/{task_id}/execute": {"post": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Execute Schedule Task", "description": "执行固定课排课任务", "operationId": "execute_schedule_task_api_v1_admin_fixed_schedule_tasks__task_id__execute_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}, {"name": "force_restart", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否强制重新开始", "default": false, "title": "Force Restart"}, "description": "是否强制重新开始"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/fixed-schedule/tasks/create-and-execute": {"post": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Create And Execute Schedule Task", "description": "创建并立即执行固定课排课任务", "operationId": "create_and_execute_schedule_task_api_v1_admin_fixed_schedule_tasks_create_and_execute_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FixedScheduleTaskCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/fixed-schedule/tasks/{task_id}/retry": {"post": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Retry Failed Task", "description": "重试失败的排课任务", "operationId": "retry_failed_task_api_v1_admin_fixed_schedule_tasks__task_id__retry_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/fixed-schedule/tasks/{task_id}/cancel": {"post": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Cancel Running Task", "description": "取消正在执行的排课任务", "operationId": "cancel_running_task_api_v1_admin_fixed_schedule_tasks__task_id__cancel_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/fixed-schedule/tasks/{task_id}/status": {"get": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Get Task Execution Status", "description": "获取任务执行状态", "operationId": "get_task_execution_status_api_v1_admin_fixed_schedule_tasks__task_id__status_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/fixed-schedule/tasks/statistics": {"get": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Get Task Statistics", "description": "获取任务统计信息", "operationId": "get_task_statistics_api_v1_admin_fixed_schedule_tasks_statistics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_ScheduleTaskStatsResponse_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/admin/fixed-schedule/tasks/{task_id}": {"get": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Get Schedule Task", "description": "获取固定课排课任务详情", "operationId": "get_schedule_task_api_v1_admin_fixed_schedule_tasks__task_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_FixedScheduleTaskResponse_"}}}}}}, "put": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Update Schedule Task", "description": "更新固定课排课任务", "operationId": "update_schedule_task_api_v1_admin_fixed_schedule_tasks__task_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FixedScheduleTaskUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_FixedScheduleTaskResponse_"}}}}}}, "delete": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Delete Schedule Task", "description": "删除固定课排课任务", "operationId": "delete_schedule_task_api_v1_admin_fixed_schedule_tasks__task_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/admin/fixed-schedule/tasks/{task_id}/logs": {"get": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Get Task Logs", "description": "获取指定任务的日志列表", "operationId": "get_task_logs_api_v1_admin_fixed_schedule_tasks__task_id__logs_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Task Id"}}, {"name": "log_level", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "日志级别", "title": "Log Level"}, "description": "日志级别"}, {"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "会员ID", "title": "Member Id"}, "description": "会员ID"}, {"name": "operation_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "操作类型", "title": "Operation Type"}, "description": "操作类型"}, {"name": "created_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "创建时间范围-起始", "title": "Created From"}, "description": "创建时间范围-起始"}, {"name": "created_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "创建时间范围-结束", "title": "Created To"}, "description": "创建时间范围-结束"}, {"name": "message", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "日志消息（模糊搜索）", "title": "Message"}, "description": "日志消息（模糊搜索）"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "created_at", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(asc|desc)$", "description": "排序方向", "default": "desc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_FixedScheduleTaskLogResponse_"}}}}}}}, "/api/v1/admin/fixed-schedule/logs": {"get": {"tags": ["管理端-固定课排课", "固定课排课管理"], "summary": "Get All Logs", "description": "获取所有排课日志列表", "operationId": "get_all_logs_api_v1_admin_fixed_schedule_logs_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "task_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "任务ID", "title": "Task Id"}, "description": "任务ID"}, {"name": "log_level", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "日志级别", "title": "Log Level"}, "description": "日志级别"}, {"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "member_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "会员ID", "title": "Member Id"}, "description": "会员ID"}, {"name": "operation_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "操作类型", "title": "Operation Type"}, "description": "操作类型"}, {"name": "created_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "创建时间范围-起始", "title": "Created From"}, "description": "创建时间范围-起始"}, {"name": "created_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "创建时间范围-结束", "title": "Created To"}, "description": "创建时间范围-结束"}, {"name": "message", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "日志消息（模糊搜索）", "title": "Message"}, "description": "日志消息（模糊搜索）"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "排序字段", "default": "created_at", "title": "Sort By"}, "description": "排序字段"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "pattern": "^(asc|desc)$", "description": "排序方向", "default": "desc", "title": "Sort Order"}, "description": "排序方向"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_FixedScheduleTaskLogResponse_"}}}}}}}, "/api/v1/member/profile/me": {"get": {"tags": ["会员端-个人中心"], "summary": "Get My Profile", "description": "获取我的个人信息", "operationId": "get_my_profile_api_v1_member_profile_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberRead_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/member/courses/available": {"get": {"tags": ["会员端-课程预约"], "summary": "Get Available Courses", "description": "获取可预约课程列表", "operationId": "get_available_courses_api_v1_member_courses_available_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "开始日期", "title": "Date From"}, "description": "开始日期"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "结束日期", "title": "Date To"}, "description": "结束日期"}, {"name": "max_price", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "description": "最高价格", "title": "Max Price"}, "description": "最高价格"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_ScheduledClassList_"}}}}}}}, "/api/v1/member/courses/my": {"get": {"tags": ["会员端-课程预约"], "summary": "Get My Courses", "description": "获取我的课程列表", "operationId": "get_my_courses_api_v1_member_courses_my_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "开始日期", "title": "Date From"}, "description": "开始日期"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "结束日期", "title": "Date To"}, "description": "结束日期"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "课程状态", "title": "Status"}, "description": "课程状态"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_ScheduledClassList_"}}}}}}}, "/api/v1/member/courses/{class_id}/book": {"post": {"tags": ["会员端-课程预约"], "summary": "Book Course", "description": "预约课程 - 会员自主预约（支持会员卡自动扣费）", "operationId": "book_course_api_v1_member_courses__class_id__book_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Class Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberClassBooking"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_ScheduledClassRead_"}}}}}}}, "/api/v1/member/fixed-courses/my-schedule": {"get": {"tags": ["会员端-固定课程"], "summary": "获取我的固定课程安排", "description": "获取当前会员的固定课程安排\n    \n    **功能说明：**\n    - 显示会员锁定的所有固定时间段\n    - 包含教师信息和上课时间\n    - 按星期和时间排序\n    - 使用优化查询，性能更佳", "operationId": "get_my_fixed_schedule_api_v1_member_fixed_courses_my_schedule_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_MemberFixedScheduleItem_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/member/fixed-courses/available-slots": {"get": {"tags": ["会员端-固定课程"], "summary": "获取可锁定的时间段", "description": "获取可以锁定的教师固定时间段\n    \n    **筛选条件：**\n    - 支持按教师筛选\n    - 支持按星期筛选\n    - 只显示可用且对会员可见的时间段\n    - 排除已被锁定的时间段", "operationId": "get_available_slots_api_v1_member_fixed_courses_available_slots_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "teacher_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "教师ID", "title": "Teacher Id"}, "description": "教师ID"}, {"name": "weekdays", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "星期列表，逗号分隔，如：1,2,3", "title": "Weekdays"}, "description": "星期列表，逗号分隔，如：1,2,3"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_AvailableSlotResponse_"}}}}}}}, "/api/v1/member/fixed-courses/lock-request": {"post": {"tags": ["会员端-固定课程"], "summary": "申请锁定固定时间段", "description": "会员申请锁定指定的固定时间段\n    \n    **业务规则：**\n    - 只能锁定可用且对会员可见的时间段\n    - 不能锁定已被其他会员锁定的时间段\n    - 锁定后会自动同步教师时间段的占用状态", "operationId": "request_slot_lock_api_v1_member_fixed_courses_lock_request_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberFixedSlotLockCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberFixedSlotLockResponse_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/member/fixed-courses/my-locks": {"get": {"tags": ["会员端-固定课程"], "summary": "获取我的锁定记录", "description": "获取当前会员的所有锁定记录\n    \n    **包含信息：**\n    - 锁定的时间段信息\n    - 锁定状态和时间\n    - 教师信息", "operationId": "get_my_locks_api_v1_member_fixed_courses_my_locks_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "锁定状态筛选", "title": "Status"}, "description": "锁定状态筛选"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ListResponse_MemberFixedSlotLockResponse_"}}}}}}}, "/api/v1/member/fixed-courses/locks/{lock_id}": {"delete": {"tags": ["会员端-固定课程"], "summary": "取消锁定", "description": "会员取消自己的固定时间段锁定\n    \n    **注意：**\n    - 只能取消自己的锁定\n    - 取消后会清空教师时间段的占用状态\n    - 取消操作会直接删除锁定记录", "operationId": "cancel_my_lock_api_v1_member_fixed_courses_locks__lock_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lock_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lock Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_str_"}}}}}}}, "/api/v1/member/cards/my": {"get": {"tags": ["会员端-会员卡"], "summary": "Get My Cards", "description": "获取我的会员卡列表", "operationId": "get_my_cards_api_v1_member_cards_my_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_List_MemberCardRead__"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/member/cards/my/primary": {"get": {"tags": ["会员端-会员卡"], "summary": "Get My Primary Card", "description": "获取我的主要会员卡", "operationId": "get_my_primary_card_api_v1_member_cards_my_primary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberCardRead_"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/v1/member/records/operations": {"get": {"tags": ["会员端-记录查询"], "summary": "Get My Card Operations", "description": "获取我的会员卡操作记录", "operationId": "get_my_card_operations_api_v1_member_records_operations_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "页码", "default": 1, "title": "Page"}, "description": "页码"}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "每页数量", "default": 20, "title": "Size"}, "description": "每页数量"}, {"name": "operation_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "操作类型", "title": "Operation Type"}, "description": "操作类型"}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "开始日期", "title": "Date From"}, "description": "开始日期"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "结束日期", "title": "Date To"}, "description": "结束日期"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageResponse_MemberCardOperationRead_"}}}}}}}, "/api/v1/auth/admin/login": {"post": {"tags": ["认证"], "summary": "<PERSON><PERSON>", "description": "CMS管理员登录", "operationId": "admin_login_api_v1_auth_admin_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminLoginRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_AdminLoginResponse_"}}}}}}}, "/api/v1/auth/member/login": {"post": {"tags": ["认证"], "summary": "Member <PERSON><PERSON>", "description": "会员登录（手机号+验证码）", "operationId": "member_login_api_v1_auth_member_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MemberLogin"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_MemberLoginResponse_"}}}}}}}, "/api/v1/auth/member/send-code": {"post": {"tags": ["认证"], "summary": "Send Verification Code", "description": "发送验证码", "operationId": "send_verification_code_api_v1_auth_member_send_code_post", "parameters": [{"name": "phone", "in": "query", "required": true, "schema": {"type": "string", "title": "Phone"}}, {"name": "tenant_code", "in": "query", "required": true, "schema": {"type": "string", "title": "Tenant Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataResponse_dict_"}}}}}}}, "/api/v1/info/health": {"get": {"tags": ["公开信息"], "summary": "Health Check", "description": "健康检查", "operationId": "health_check_api_v1_info_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/info/version": {"get": {"tags": ["公开信息"], "summary": "Get Version", "description": "获取API版本信息", "operationId": "get_version_api_v1_info_version_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AdminClassCreate": {"properties": {"teacher_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Teacher Id", "description": "教师ID"}, "member_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Member Id", "description": "会员ID（可选）"}, "class_datetime": {"type": "string", "format": "date-time", "title": "Class Datetime", "description": "精确到分钟的上课时间"}, "duration_minutes": {"type": "integer", "maximum": 120.0, "minimum": 5.0, "title": "Duration Minutes", "description": "课程时长（分钟）", "default": 25}, "class_type": {"$ref": "#/components/schemas/ClassType", "description": "课程类型", "default": "direct"}, "price": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Price", "description": "教师单价（整数，单位：元）"}, "member_card_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Member Card Id", "description": "会员卡ID"}, "member_card_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Member Card Name", "description": "会员卡名称"}, "booking_remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Booking Remark", "description": "预约备注"}, "member_no_cancel": {"type": "boolean", "title": "Member No Cancel", "description": "会员是否可取消", "default": false}, "material_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Material Id", "description": "教材ID"}, "material_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Material Name", "description": "教材名称"}, "is_visible_to_member": {"type": "boolean", "title": "Is Visible To Member", "description": "是否对会员可见", "default": true}, "operator_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Operator Name", "description": "操作人显示名"}}, "type": "object", "required": ["teacher_id", "class_datetime"], "title": "AdminClassCreate", "description": "管理员版课程创建请求模型 - 管理员全权限操作"}, "AdminLoginRequest": {"properties": {"username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username", "examples": ["demo_admin"]}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email", "examples": ["<EMAIL>"]}, "password": {"type": "string", "title": "Password", "examples": ["demo123456"]}, "tenant_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tenant Code", "examples": ["demo_tenant"]}}, "type": "object", "required": ["password"], "title": "AdminLoginRequest", "description": "管理员登录请求\n支持两种模式：\n1) 超级管理员：username + password\n2) 租户管理员：email + password + tenant_code"}, "AdminLoginResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "expires": {"type": "string", "format": "date-time", "title": "Expires"}, "token_type": {"type": "string", "title": "Token Type"}, "user": {"$ref": "#/components/schemas/UserRead"}, "tenant": {"anyOf": [{"$ref": "#/components/schemas/TenantRead"}, {"type": "null"}]}}, "type": "object", "required": ["access_token", "expires", "token_type", "user"], "title": "AdminLoginResponse", "description": "管理员登录响应"}, "AvailabilityUpdateRequest": {"properties": {"slot_ids": {"items": {"type": "integer"}, "type": "array", "title": "Slot Ids", "description": "时间段ID列表"}, "is_available": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Available", "description": "是否可用"}, "is_visible_to_members": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Visible To Members", "description": "是否对会员可见"}}, "type": "object", "required": ["slot_ids"], "title": "AvailabilityUpdateRequest", "description": "时间段可用性更新请求模型"}, "AvailableSlotResponse": {"properties": {"teacher_fixed_slot_id": {"type": "integer", "title": "Teacher Fixed Slot Id", "description": "教师固定时间段ID"}, "teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "teacher_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Teacher Name", "description": "教师姓名"}, "weekday": {"type": "integer", "title": "Weekday", "description": "星期几（1-7）"}, "weekday_name": {"type": "string", "title": "Weekday Name", "description": "星期名称"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "开始时间"}, "duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration Minutes", "description": "课程时长（分钟）"}, "time_slot_display": {"type": "string", "title": "Time Slot Display", "description": "时间段显示"}, "is_available": {"type": "boolean", "title": "Is Available", "description": "是否可用"}, "is_visible_to_members": {"type": "boolean", "title": "Is Visible To Members", "description": "是否对会员可见"}}, "type": "object", "required": ["teacher_fixed_slot_id", "teacher_id", "weekday", "weekday_name", "start_time", "time_slot_display", "is_available", "is_visible_to_members"], "title": "AvailableSlotResponse", "description": "可锁定时间段响应模型"}, "AvailableTimesQuery": {"properties": {"teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "weekdays": {"anyOf": [{"items": {"$ref": "#/components/schemas/Weekday"}, "type": "array"}, {"type": "null"}], "title": "Weekdays", "description": "星期列表"}, "start_time_from": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Start Time From", "description": "开始时间范围-起始"}, "start_time_to": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Start Time To", "description": "开始时间范围-结束"}, "only_available": {"type": "boolean", "title": "Only Available", "description": "仅返回可用时间", "default": true}, "only_visible": {"type": "boolean", "title": "Only Visible", "description": "仅返回对会员可见的时间", "default": true}}, "type": "object", "required": ["teacher_id"], "title": "AvailableTimesQuery", "description": "可用时间查询参数"}, "BalanceInsufficientAction": {"type": "string", "enum": ["skip", "remove_lock", "notify_only", "terminate"], "title": "BalanceInsufficientAction", "description": "余额不足处理动作枚举"}, "BatchBookResult": {"properties": {"success_count": {"type": "integer", "title": "Success Count", "description": "成功预约数量"}, "failed_count": {"type": "integer", "title": "Failed Count", "description": "失败数量"}, "booked_classes": {"items": {"$ref": "#/components/schemas/ScheduledClassRead"}, "type": "array", "title": "Booked Classes", "description": "成功预约的课程", "default": []}, "failed_bookings": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Failed Bookings", "description": "失败的预约记录", "default": []}}, "type": "object", "required": ["success_count", "failed_count"], "title": "BatchBookResult", "description": "批量预约结果"}, "BatchCancelResult": {"properties": {"success_count": {"type": "integer", "title": "Success Count", "description": "成功取消数量"}, "failed_count": {"type": "integer", "title": "Failed Count", "description": "失败数量"}, "cancelled_classes": {"items": {"$ref": "#/components/schemas/ScheduledClassRead"}, "type": "array", "title": "Cancelled Classes", "description": "成功取消的课程", "default": []}, "failed_cancellations": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Failed Cancellations", "description": "失败的取消记录", "default": []}}, "type": "object", "required": ["success_count", "failed_count"], "title": "BatchCancelResult", "description": "批量取消预约结果"}, "BatchClassStatusUpdate": {"properties": {"class_ids": {"items": {"type": "integer"}, "type": "array", "title": "Class Ids", "description": "课程ID列表"}, "new_status": {"$ref": "#/components/schemas/ClassStatus", "description": "新状态"}, "operator_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Operator Name", "description": "操作人显示名"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "状态更新备注"}}, "type": "object", "required": ["class_ids", "new_status"], "title": "BatchClassStatusUpdate", "description": "批量课程状态更新请求模型"}, "BatchCreateSlotsRequest": {"properties": {"teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "slots": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Slots", "description": "时间段列表"}, "created_by": {"type": "integer", "title": "Created By", "description": "创建者ID"}}, "type": "object", "required": ["teacher_id", "slots", "created_by"], "title": "BatchCreateSlotsRequest", "description": "批量创建时间段请求模型"}, "BatchDeleteSlotsRequest": {"properties": {"slot_ids": {"items": {"type": "integer"}, "type": "array", "title": "Slot Ids", "description": "要删除的时间段ID列表"}}, "type": "object", "required": ["slot_ids"], "title": "BatchDeleteSlotsRequest", "description": "批量删除时间段请求模型"}, "BatchLockOperationResult": {"properties": {"total_count": {"type": "integer", "title": "Total Count", "description": "总操作数"}, "success_count": {"type": "integer", "title": "Success Count", "description": "成功数"}, "failed_count": {"type": "integer", "title": "Failed Count", "description": "失败数"}, "success_ids": {"items": {"type": "integer"}, "type": "array", "title": "Success Ids", "description": "成功的ID列表"}, "failed_items": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Failed Items", "description": "失败的项目详情"}, "message": {"type": "string", "title": "Message", "description": "操作结果消息"}}, "type": "object", "required": ["total_count", "success_count", "failed_count", "success_ids", "failed_items", "message"], "title": "BatchLockOperationResult", "description": "批量锁定操作结果"}, "BatchQueryRequest": {"properties": {"teacher_ids": {"items": {"type": "integer"}, "type": "array", "title": "Teacher Ids", "description": "教师ID列表"}, "weekdays": {"anyOf": [{"items": {"$ref": "#/components/schemas/Weekday"}, "type": "array"}, {"type": "null"}], "title": "Weekdays", "description": "星期列表"}, "start_time_from": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Time From", "description": "开始时间范围-起始（HH:MM格式）"}, "start_time_to": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Time To", "description": "开始时间范围-结束（HH:MM格式）"}, "only_available": {"type": "boolean", "title": "Only Available", "description": "仅返回可用时间段", "default": true}, "only_visible": {"type": "boolean", "title": "Only Visible", "description": "仅返回对会员可见的时间段", "default": true}}, "type": "object", "required": ["teacher_ids"], "title": "BatchQueryRequest", "description": "批量查询教师时间表请求模型"}, "BatchRechargeRequest": {"properties": {"recharge_items": {"items": {"$ref": "#/components/schemas/RechargeRequest"}, "type": "array", "title": "Recharge Items", "description": "充值项目列表"}}, "type": "object", "required": ["recharge_items"], "title": "BatchRechargeRequest", "description": "批量充值请求模型"}, "BatchRechargeResponse": {"properties": {"success_count": {"type": "integer", "title": "Success Count", "description": "成功数量"}, "failed_count": {"type": "integer", "title": "Failed Count", "description": "失败数量"}, "success_items": {"items": {"$ref": "#/components/schemas/RechargeResponse"}, "type": "array", "title": "Success Items", "description": "成功项目列表"}, "failed_items": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Failed Items", "description": "失败项目列表（包含错误信息）"}}, "type": "object", "required": ["success_count", "failed_count", "success_items", "failed_items"], "title": "BatchRechargeResponse", "description": "批量充值响应模型"}, "BatchTeacherClassCreate": {"properties": {"teacher_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Teacher Id", "description": "教师ID"}, "class_datetimes": {"items": {"type": "string", "format": "date-time"}, "type": "array", "title": "Class Datetimes", "description": "课程时间列表"}, "duration_minutes": {"type": "integer", "maximum": 120.0, "minimum": 5.0, "title": "Duration Minutes", "description": "课程时长（分钟）", "default": 25}, "price": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Price", "description": "教师单价（整数，单位：元）"}, "material_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Material Name", "description": "教材名称"}, "is_visible_to_member": {"type": "boolean", "title": "Is Visible To Member", "description": "是否对会员可见", "default": true}}, "type": "object", "required": ["teacher_id", "class_datetimes"], "title": "BatchTeacherClassCreate", "description": "批量创建教师课程请求模型"}, "BatchUpdateSlotsRequest": {"properties": {"updates": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Updates", "description": "更新列表，每个元素包含id和要更新的字段"}}, "type": "object", "required": ["updates"], "title": "BatchUpdateSlotsRequest", "description": "批量更新时间段请求模型"}, "BillingCycle": {"type": "string", "enum": ["monthly", "quarterly", "yearly"], "title": "BillingCycle", "description": "计费周期枚举"}, "Body_upload_teacher_avatar_api_v1_admin_teachers__teacher_id__avatar_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File", "description": "头像图片文件"}}, "type": "object", "required": ["file"], "title": "Body_upload_teacher_avatar_api_v1_admin_teachers__teacher_id__avatar_post"}, "CardStatus": {"type": "string", "enum": ["active", "frozen", "expired", "cancelled"], "title": "CardStatus", "description": "卡片状态枚举"}, "CardType": {"type": "string", "enum": ["times_limited", "times_unlimited", "value_limited", "value_unlimited"], "title": "CardType", "description": "卡片类型枚举"}, "ClassBookingData": {"properties": {"member_card_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Member Card Id", "description": "会员卡ID"}, "member_card_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Member Card Name", "description": "会员卡名称"}, "material_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Material Name", "description": "教材名称"}, "booking_remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Booking Remark", "description": "预约备注"}}, "type": "object", "title": "ClassBookingData", "description": "课程预约数据模型 - 用于预约接口的请求体"}, "ClassCancellation": {"properties": {"cancellation_reason": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Cancellation Reason", "description": "取消原因"}, "operator_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Operator Name", "description": "操作人显示名"}}, "type": "object", "title": "ClassCancellation", "description": "课程取消请求模型"}, "ClassOperationType": {"type": "string", "enum": ["create", "update", "book", "cancel_booking", "cancel_class", "delete"], "title": "ClassOperationType", "description": "课程操作类型枚举"}, "ClassStatus": {"type": "string", "enum": ["available", "booked", "teacher_no_show", "member_no_show"], "title": "ClassStatus", "description": "课程状态枚举"}, "ClassStatusUpdate": {"properties": {"status": {"$ref": "#/components/schemas/ClassStatus", "description": "课程状态"}, "operator_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Operator Name", "description": "操作人显示名"}, "remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Remark", "description": "状态更新备注"}}, "type": "object", "required": ["status"], "title": "ClassStatusUpdate", "description": "课程状态更新请求模型"}, "ClassType": {"type": "string", "enum": ["fixed", "direct"], "title": "ClassType", "description": "课程类型枚举"}, "ClearSlotsRequest": {"properties": {"teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "weekdays": {"anyOf": [{"items": {"$ref": "#/components/schemas/Weekday"}, "type": "array"}, {"type": "null"}], "title": "Weekdays", "description": "要清空的星期列表，不指定则清空所有"}, "confirm": {"type": "boolean", "title": "Confirm", "description": "确认清空操作"}}, "type": "object", "required": ["teacher_id", "confirm"], "title": "ClearSlotsRequest", "description": "清空时间段请求模型"}, "ConflictCheckRequest": {"properties": {"teacher_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Teacher Id", "description": "教师ID"}, "member_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Member Id", "description": "会员ID"}, "class_datetime": {"type": "string", "format": "date-time", "title": "Class Datetime", "description": "上课时间"}, "duration_minutes": {"type": "integer", "maximum": 120.0, "minimum": 5.0, "title": "Duration Minutes", "description": "课程时长（分钟）", "default": 25}, "exclude_class_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Exclude Class Id", "description": "排除的课程ID（用于更新时检测）"}}, "type": "object", "required": ["teacher_id", "class_datetime"], "title": "ConflictCheckRequest", "description": "时间冲突检测请求模型"}, "ConflictCheckResponse": {"properties": {"has_conflict": {"type": "boolean", "title": "Has Conflict", "description": "是否存在冲突"}, "teacher_conflict": {"type": "boolean", "title": "Teacher Conflict", "description": "教师时间冲突", "default": false}, "member_conflict": {"type": "boolean", "title": "Member Conflict", "description": "会员时间冲突", "default": false}, "conflict_details": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Conflict Details", "description": "冲突详情"}}, "type": "object", "required": ["has_conflict"], "title": "ConflictCheckResponse", "description": "时间冲突检测响应模型"}, "CopySlotsRequest": {"properties": {"source_teacher_id": {"type": "integer", "title": "Source Teacher Id", "description": "源教师ID"}, "target_teacher_id": {"type": "integer", "title": "Target Teacher Id", "description": "目标教师ID"}, "weekdays": {"anyOf": [{"items": {"$ref": "#/components/schemas/Weekday"}, "type": "array"}, {"type": "null"}], "title": "Weekdays", "description": "要复制的星期列表，不指定则复制所有"}, "overwrite_existing": {"type": "boolean", "title": "Overwrite Existing", "description": "是否覆盖已存在的时间段", "default": false}, "created_by": {"type": "integer", "title": "Created By", "description": "创建者ID"}}, "type": "object", "required": ["source_teacher_id", "target_teacher_id", "created_by"], "title": "CopySlotsRequest", "description": "复制时间段请求模型"}, "CourseSystemConfigDetail": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "default_slot_duration_minutes": {"type": "integer", "maximum": 120.0, "minimum": 5.0, "title": "Default Slot Duration Minutes", "description": "默认课节时长(分钟)", "default": 25}, "default_slot_interval_minutes": {"type": "integer", "maximum": 30.0, "minimum": 0.0, "title": "Default Slot Interval Minutes", "description": "默认课节间隔(分钟)", "default": 5}, "direct_booking_enabled": {"type": "boolean", "title": "Direct Booking Enabled", "description": "是否启用直接约课", "default": true}, "max_advance_days": {"anyOf": [{"type": "integer", "maximum": 365.0, "minimum": 1.0}, {"type": "null"}], "title": "Max Advance Days", "description": "会员最多可预约x天后的课程", "default": 30}, "booking_deadline_hours": {"anyOf": [{"type": "integer", "maximum": 72.0, "minimum": 0.0}, {"type": "null"}], "title": "Booking Deadline Hours", "description": "预约截止时间：上课前x小时", "default": 2}, "cancel_deadline_hours": {"anyOf": [{"type": "integer", "maximum": 72.0, "minimum": 0.0}, {"type": "null"}], "title": "Cancel Deadline Hours", "description": "取消截止时间：上课前x小时", "default": 2}, "booking_time_from": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Booking Time From", "description": "预约操作时间限制：从"}, "booking_time_to": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Booking Time To", "description": "预约操作时间限制：到"}, "require_material": {"type": "boolean", "title": "Require Material", "description": "会员预约时是否必须选教材", "default": true}, "teacher_can_add_slots": {"type": "boolean", "title": "Teacher Can Add Slots", "description": "教师是否可自主增加课时", "default": true}, "teacher_can_delete_empty_slots": {"type": "boolean", "title": "Teacher Can Delete Empty Slots", "description": "教师是否可删除空课时", "default": true}, "teacher_can_cancel_booking": {"type": "boolean", "title": "Teacher Can Cancel Booking", "description": "教师是否可取消学生预约", "default": false}, "teacher_need_confirm": {"type": "boolean", "title": "Teacher Need Confirm", "description": "学生预约后是否需要教师确认", "default": false}, "fixed_booking_enabled": {"type": "boolean", "title": "Fixed Booking Enabled", "description": "是否启用固定课表约课", "default": true}, "auto_schedule_enabled": {"type": "boolean", "title": "Auto Schedule Enabled", "description": "是否启用自动排课", "default": true}, "auto_schedule_day": {"type": "integer", "maximum": 28.0, "minimum": 1.0, "title": "Auto Schedule Day", "description": "自动排课日期(每月几号)", "default": 22}, "auto_schedule_time": {"type": "string", "format": "time", "title": "Auto Schedule Time", "description": "自动排课时间", "default": "14:00:00"}, "default_schedule_weeks": {"type": "integer", "maximum": 12.0, "minimum": 1.0, "title": "Default Schedule Weeks", "description": "默认排课周数", "default": 4}, "interrupt_on_conflict": {"type": "boolean", "title": "Interrupt On Conflict", "description": "遇到重复时间是否终止", "default": true}, "skip_insufficient_balance": {"type": "boolean", "title": "Skip Insufficient Balance", "description": "是否跳过余额不足的学生", "default": true}, "remove_insufficient_locks": {"type": "boolean", "title": "Remove Insufficient Locks", "description": "是否移除余额不足学生的固定位", "default": true}, "id": {"type": "integer", "title": "Id", "description": "配置ID"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By", "description": "创建者ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "更新时间"}, "booking_time_range": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Booking Time Range", "description": "约课时间范围（格式化显示）"}, "auto_schedule_time_display": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Auto Schedule Time Display", "description": "自动排课时间（格式化显示）"}, "validation_summary": {"additionalProperties": true, "type": "object", "title": "Validation Summary", "description": "配置验证摘要"}, "usage_statistics": {"additionalProperties": true, "type": "object", "title": "Usage Statistics", "description": "配置使用统计"}}, "type": "object", "required": ["tenant_id", "id", "created_by", "created_at", "updated_at"], "title": "CourseSystemConfigDetail", "description": "课程系统配置详情响应模型"}, "CourseSystemConfigUpdate": {"properties": {"default_slot_duration_minutes": {"anyOf": [{"type": "integer", "maximum": 120.0, "minimum": 5.0}, {"type": "null"}], "title": "Default Slot Duration Minutes", "description": "默认课节时长(分钟)"}, "default_slot_interval_minutes": {"anyOf": [{"type": "integer", "maximum": 30.0, "minimum": 0.0}, {"type": "null"}], "title": "Default Slot Interval Minutes", "description": "默认课节间隔(分钟)"}, "direct_booking_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Direct Booking Enabled", "description": "是否启用直接约课"}, "max_advance_days": {"anyOf": [{"type": "integer", "maximum": 365.0, "minimum": 1.0}, {"type": "null"}], "title": "Max Advance Days", "description": "最大提前预约天数"}, "booking_deadline_hours": {"anyOf": [{"type": "integer", "maximum": 72.0, "minimum": 0.0}, {"type": "null"}], "title": "Booking Deadline Hours", "description": "约课截止时间(小时)"}, "cancel_deadline_hours": {"anyOf": [{"type": "integer", "maximum": 72.0, "minimum": 0.0}, {"type": "null"}], "title": "Cancel Deadline Hours", "description": "取消约课截止时间(小时)"}, "booking_time_from": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Booking Time From", "description": "约课时间范围-开始时间"}, "booking_time_to": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Booking Time To", "description": "约课时间范围-结束时间"}, "require_material": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Require Material", "description": "是否必须选择教材"}, "teacher_can_add_slots": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Teacher Can Add Slots", "description": "教师是否可以添加时间段"}, "teacher_can_delete_empty_slots": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Teacher Can Delete Empty Slots", "description": "教师是否可以删除空时间段"}, "teacher_can_cancel_booking": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Teacher Can Cancel Booking", "description": "教师是否可以取消预约"}, "teacher_need_confirm": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Teacher Need Confirm", "description": "教师是否需要确认预约"}, "fixed_booking_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Fixed Booking Enabled", "description": "是否启用固定课表"}, "auto_schedule_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Auto Schedule Enabled", "description": "是否启用自动排课"}, "auto_schedule_day": {"anyOf": [{"type": "integer", "maximum": 7.0, "minimum": 1.0}, {"type": "null"}], "title": "Auto Schedule Day", "description": "自动排课提前天数"}, "auto_schedule_time": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Auto Schedule Time", "description": "自动排课时间"}, "default_schedule_weeks": {"anyOf": [{"type": "integer", "maximum": 52.0, "minimum": 1.0}, {"type": "null"}], "title": "Default Schedule Weeks", "description": "默认排课周数"}}, "type": "object", "title": "CourseSystemConfigUpdate", "description": "更新课程系统配置请求模型"}, "DataResponse_AdminLoginResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/AdminLoginResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[AdminLoginResponse]"}, "DataResponse_BatchBookResult_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/BatchBookResult"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[BatchBookResult]"}, "DataResponse_BatchCancelResult_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/BatchCancelResult"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[BatchCancelResult]"}, "DataResponse_BatchLockOperationResult_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/BatchLockOperationResult"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[BatchLockOperationResult]"}, "DataResponse_BatchRechargeResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/BatchRechargeResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[BatchRechargeResponse]"}, "DataResponse_ConflictCheckResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/ConflictCheckResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[ConflictCheckResponse]"}, "DataResponse_CourseSystemConfigDetail_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/CourseSystemConfigDetail"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[CourseSystemConfigDetail]"}, "DataResponse_DeductionResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/DeductionResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[DeductionResponse]"}, "DataResponse_FixedScheduleTaskResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/FixedScheduleTaskResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[FixedScheduleTaskResponse]"}, "DataResponse_List_MemberCardOperationRead__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/MemberCardOperationRead"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[app.features.member_cards.schemas.MemberCardOperationRead]]"}, "DataResponse_List_MemberCardRead__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/MemberCardRead"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[app.features.member_cards.schemas.MemberCardRead]]"}, "DataResponse_List_MemberCardTemplateList__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/MemberCardTemplateList"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[app.features.member_cards.schemas.MemberCardTemplateList]]"}, "DataResponse_List_MemberFixedLockOperationLogResponse__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/MemberFixedLockOperationLogResponse"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[app.features.courses.operations.schemas.MemberFixedLockOperationLogResponse]]"}, "DataResponse_List_ScheduledClassList__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/ScheduledClassList"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[app.features.courses.scheduled_classes_schemas.ScheduledClassList]]"}, "DataResponse_List_ScheduledClassOperationLogResponse__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/ScheduledClassOperationLogResponse"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[app.features.courses.operations.schemas.ScheduledClassOperationLogResponse]]"}, "DataResponse_List_ScheduledClassRead__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/ScheduledClassRead"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[app.features.courses.scheduled_classes_schemas.ScheduledClassRead]]"}, "DataResponse_List_TagRead__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/TagRead"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[app.features.tags.schemas.TagRead]]"}, "DataResponse_List_TeacherFixedSlotOperationLogResponse__": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/TeacherFixedSlotOperationLogResponse"}, "type": "array"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[List[app.features.courses.operations.schemas.TeacherFixedSlotOperationLogResponse]]"}, "DataResponse_LockConflictResult_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/LockConflictResult"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[LockConflictResult]"}, "DataResponse_MemberCardOperationRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberCardOperationRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberCardOperationRead]"}, "DataResponse_MemberCardRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberCardRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberCardRead]"}, "DataResponse_MemberCardTemplateRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberCardTemplateRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberCardTemplateRead]"}, "DataResponse_MemberFixedLockOperationLogResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberFixedLockOperationLogResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberFixedLockOperationLogResponse]"}, "DataResponse_MemberFixedSlotLockResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberFixedSlotLockResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberFixedSlotLockResponse]"}, "DataResponse_MemberLoginResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberLoginResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberLoginResponse]"}, "DataResponse_MemberRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MemberRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[MemberRead]"}, "DataResponse_RechargeResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/RechargeResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[RechargeResponse]"}, "DataResponse_ScheduleTaskStatsResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/ScheduleTaskStatsResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[ScheduleTaskStatsResponse]"}, "DataResponse_ScheduledClassOperationLogResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/ScheduledClassOperationLogResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[ScheduledClassOperationLogResponse]"}, "DataResponse_ScheduledClassRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/ScheduledClassRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[ScheduledClassRead]"}, "DataResponse_TagCategoryRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TagCategoryRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TagCategoryRead]"}, "DataResponse_TagRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TagRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TagRead]"}, "DataResponse_TeacherDetail_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TeacherDetail"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TeacherDetail]"}, "DataResponse_TeacherFixedSlotOperationLogResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TeacherFixedSlotOperationLogResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TeacherFixedSlotOperationLogResponse]"}, "DataResponse_TeacherFixedSlotResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TeacherFixedSlotResponse"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TeacherFixedSlotResponse]"}, "DataResponse_TeacherFixedSlotStats_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TeacherFixedSlotStats"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TeacherFixedSlotStats]"}, "DataResponse_TeacherRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TeacherRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TeacherRead]"}, "DataResponse_TenantRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/TenantRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[TenantRead]"}, "DataResponse_UserRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"$ref": "#/components/schemas/UserRead"}, {"type": "null"}], "description": "响应数据"}}, "type": "object", "title": "DataResponse[UserRead]"}, "DataResponse_bool_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[bool]"}, "DataResponse_dict_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[dict]"}, "DataResponse_int_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[int]"}, "DataResponse_str_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Data", "description": "响应数据"}}, "type": "object", "title": "DataResponse[str]"}, "DeductionRequest": {"properties": {"member_card_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Member Card Id", "description": "会员卡ID"}, "amount": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Amount", "description": "扣费金额（元）"}, "reduce_validity_days": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Reduce Validity Days", "description": "减少有效期（天）", "default": 0}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "备注信息"}}, "type": "object", "required": ["member_card_id", "amount"], "title": "DeductionRequest", "description": "扣费请求模型"}, "DeductionResponse": {"properties": {"operation_id": {"type": "integer", "title": "Operation Id", "description": "操作记录ID"}, "member_card_id": {"type": "integer", "title": "Member Card Id", "description": "会员卡ID"}, "amount": {"type": "integer", "title": "Amount", "description": "扣费金额（元）"}, "balance_before": {"type": "integer", "title": "Balance Before", "description": "扣费前余额（元）"}, "balance_after": {"type": "integer", "title": "Balance After", "description": "扣费后余额（元）"}, "reduce_validity_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Reduce Validity Days", "description": "减少的有效期天数"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "新的过期时间"}, "transaction_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Transaction Id", "description": "交易ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "扣费时间"}}, "type": "object", "required": ["operation_id", "member_card_id", "amount", "balance_before", "balance_after", "transaction_id", "created_at"], "title": "DeductionResponse", "description": "扣费响应模型"}, "ErrorResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": false}, "message": {"type": "string", "title": "Message", "default": "操作失败"}, "http_code": {"type": "integer", "title": "Http Code", "default": 400}, "business_code": {"type": "string", "title": "Business Code", "default": "BUSINESS_ERROR"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "level": {"type": "string", "title": "Level", "default": "error"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details"}}, "type": "object", "title": "ErrorResponse", "description": "错误响应模型"}, "FixedScheduleTaskCreate": {"properties": {"task_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Task Name", "description": "任务名称"}, "start_date": {"type": "string", "format": "date", "title": "Start Date", "description": "开始排课日期（必须为周一）"}, "weeks_count": {"type": "integer", "maximum": 12.0, "minimum": 1.0, "title": "Weeks Count", "description": "排课周数", "default": 4}, "teacher_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Teacher Ids", "description": "参与排课的教师ID列表，null表示全部教师"}, "teacher_priority_rule": {"$ref": "#/components/schemas/TeacherPriorityRule", "description": "教师优先级规则", "default": "region_first"}, "balance_insufficient_action": {"$ref": "#/components/schemas/BalanceInsufficientAction", "description": "余额不足时的处理动作", "default": "skip"}, "interrupt_on_conflict": {"type": "boolean", "title": "Interrupt On Conflict", "description": "遇到冲突是否终止任务", "default": true}, "remark": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Remark", "description": "备注"}}, "type": "object", "required": ["task_name", "start_date"], "title": "FixedScheduleTaskCreate", "description": "创建固定课排课任务请求"}, "FixedScheduleTaskLogResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "tenant_id": {"type": "integer", "title": "Tenant Id"}, "task_id": {"type": "integer", "title": "Task Id"}, "log_level": {"$ref": "#/components/schemas/LogLevel"}, "message": {"type": "string", "title": "Message"}, "teacher_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Teacher Id"}, "member_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Member Id"}, "class_datetime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Class Datetime"}, "operation_type": {"anyOf": [{"$ref": "#/components/schemas/OperationType"}, {"type": "null"}]}, "operation_data": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Operation Data"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "tenant_id", "task_id", "log_level", "message", "teacher_id", "member_id", "class_datetime", "operation_type", "operation_data", "created_at"], "title": "FixedScheduleTaskLogResponse", "description": "排课任务日志响应"}, "FixedScheduleTaskResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "tenant_id": {"type": "integer", "title": "Tenant Id"}, "task_name": {"type": "string", "title": "Task Name"}, "start_date": {"type": "string", "format": "date-time", "title": "Start Date"}, "weeks_count": {"type": "integer", "title": "Weeks Count"}, "teacher_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "title": "Teacher Ids"}, "teacher_priority_rule": {"$ref": "#/components/schemas/TeacherPriorityRule"}, "balance_insufficient_action": {"$ref": "#/components/schemas/BalanceInsufficientAction"}, "interrupt_on_conflict": {"type": "boolean", "title": "Interrupt On Conflict"}, "status": {"$ref": "#/components/schemas/ScheduleTaskStatus"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}, "total_teachers": {"type": "integer", "title": "Total Teachers"}, "successful_teachers": {"type": "integer", "title": "Successful Teachers"}, "failed_teachers": {"type": "integer", "title": "Failed Teachers"}, "total_classes": {"type": "integer", "title": "Total Classes"}, "total_amount": {"type": "integer", "title": "Total Amount"}, "error_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Error Message"}, "remark": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Remark"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["id", "tenant_id", "task_name", "start_date", "weeks_count", "teacher_ids", "teacher_priority_rule", "balance_insufficient_action", "interrupt_on_conflict", "status", "started_at", "completed_at", "total_teachers", "successful_teachers", "failed_teachers", "total_classes", "total_amount", "error_message", "remark", "created_at", "updated_at", "created_by"], "title": "FixedScheduleTaskResponse", "description": "固定课排课任务响应"}, "FixedScheduleTaskUpdate": {"properties": {"task_name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Task Name", "description": "任务名称"}, "remark": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Remark", "description": "备注"}}, "type": "object", "title": "FixedScheduleTaskUpdate", "description": "更新固定课排课任务请求"}, "Gender": {"type": "string", "enum": ["male", "female", "other"], "title": "Gender", "description": "性别枚举"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ListResponse_AvailableSlotResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/AvailableSlotResponse"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[AvailableSlotResponse]"}, "ListResponse_MemberFixedScheduleItem_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberFixedScheduleItem"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[MemberFixedScheduleItem]"}, "ListResponse_MemberFixedSlotLockList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberFixedSlotLockList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[MemberFixedSlotLockList]"}, "ListResponse_MemberFixedSlotLockResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberFixedSlotLockResponse"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[MemberFixedSlotLockResponse]"}, "ListResponse_TagCategoryList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TagCategoryList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TagCategoryList]"}, "ListResponse_TagList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TagList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TagList]"}, "ListResponse_TagRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TagRead"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TagRead]"}, "ListResponse_TagWithTeacherCount_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TagWithTeacherCount"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TagWithTeacherCount]"}, "ListResponse_TeacherFixedSlotList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TeacherFixedSlotList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TeacherFixedSlotList]"}, "ListResponse_TeacherList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TeacherList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TeacherList]"}, "ListResponse_TenantPlanTemplate_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TenantPlanTemplate"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TenantPlanTemplate]"}, "ListResponse_TenantRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TenantRead"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[TenantRead]"}, "ListResponse_UserRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/UserRead"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[UserRead]"}, "ListResponse_dict_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}}, "type": "object", "title": "ListResponse[dict]"}, "LockConflictCheck": {"properties": {"member_id": {"type": "integer", "title": "Member Id", "description": "会员ID"}, "teacher_fixed_slot_id": {"type": "integer", "title": "Teacher Fixed Slot Id", "description": "教师固定时间段ID"}, "exclude_lock_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Exclude Lock Id", "description": "排除的锁定记录ID（用于更新时检测）"}}, "type": "object", "required": ["member_id", "teacher_fixed_slot_id"], "title": "LockConflictCheck", "description": "锁定冲突检测参数"}, "LockConflictResult": {"properties": {"has_conflict": {"type": "boolean", "title": "Has Conflict", "description": "是否存在冲突"}, "conflict_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Conflict Type", "description": "冲突类型"}, "conflict_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Conflict Message", "description": "冲突详情"}, "conflicting_lock_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Conflicting Lock Id", "description": "冲突的锁定记录ID"}}, "type": "object", "required": ["has_conflict", "conflict_type", "conflict_message", "conflicting_lock_id"], "title": "LockConflictResult", "description": "锁定冲突检测结果"}, "LogLevel": {"type": "string", "enum": ["info", "warn", "error"], "title": "LogLevel", "description": "日志级别枚举"}, "MemberBasicRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "姓名"}, "phone": {"type": "string", "maxLength": 20, "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday", "description": "生日"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar Url", "description": "头像URL"}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "wechat_nickname": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "微信昵称"}, "wechat_avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Wechat Avatar", "description": "微信头像"}, "member_type": {"$ref": "#/components/schemas/MemberType", "description": "会员类型", "default": "trial"}, "member_status": {"$ref": "#/components/schemas/MemberStatus", "description": "会员状态", "default": "active"}, "source_channel": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Source Channel", "description": "来源渠道"}, "agent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id", "description": "代理人员ID"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "地址"}, "city": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "City", "description": "城市"}, "province": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Province", "description": "省份"}, "country": {"type": "string", "maxLength": 50, "title": "Country", "description": "国家", "default": "China"}, "postal_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Postal Code", "description": "邮编"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "标签"}, "primary_member_card_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Primary Member Card Id", "description": "主要会员卡ID"}, "primary_member_card_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Primary Member Card Name", "description": "主要会员卡名称"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "registered_at": {"type": "string", "format": "date-time", "title": "Registered At"}}, "type": "object", "required": ["tenant_id", "name", "phone", "id", "created_at", "registered_at"], "title": "MemberBasicRead", "description": "会员基础响应模型（不包含统计信息）"}, "MemberCardCreate": {"properties": {"member_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Member Id", "description": "会员ID"}, "template_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Template Id", "description": "模板ID"}}, "type": "object", "required": ["member_id", "template_id"], "title": "MemberCardCreate", "description": "创建会员卡请求模型"}, "MemberCardList": {"properties": {"id": {"type": "integer", "title": "Id"}, "member_id": {"type": "integer", "title": "Member Id"}, "name": {"type": "string", "title": "Name"}, "card_type": {"$ref": "#/components/schemas/CardType"}, "balance": {"type": "integer", "title": "Balance"}, "status": {"$ref": "#/components/schemas/CardStatus"}, "card_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Card Number"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At"}, "last_used_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "member_id", "name", "card_type", "balance", "status", "created_at"], "title": "MemberCardList", "description": "会员卡列表项模型"}, "MemberCardOperationRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "member_id": {"type": "integer", "title": "Member Id", "description": "会员ID"}, "member_card_id": {"type": "integer", "title": "Member Card Id", "description": "会员卡ID"}, "operation_type": {"$ref": "#/components/schemas/MemberCardOperationType", "description": "操作类型"}, "operation_description": {"type": "string", "maxLength": 200, "title": "Operation Description", "description": "操作描述"}, "amount_change": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Amount Change", "description": "余额变化金额（元，正数表示增加，负数表示减少）"}, "balance_before": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Balance Before", "description": "操作前余额（元）"}, "balance_after": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Balance After", "description": "操作后余额（元）"}, "status_before": {"anyOf": [{"$ref": "#/components/schemas/CardStatus"}, {"type": "null"}], "description": "操作前状态"}, "status_after": {"anyOf": [{"$ref": "#/components/schemas/CardStatus"}, {"type": "null"}], "description": "操作后状态"}, "member_card_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Member Card Name", "description": "会员卡名称"}, "bonus_amount": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Bonus Amount", "description": "赠送金额（元）"}, "actual_amount": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Actual Amount", "description": "实收金额（元）"}, "payment_method": {"anyOf": [{"$ref": "#/components/schemas/PaymentMethod"}, {"type": "null"}], "description": "支付方式"}, "payment_status": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Payment Status", "description": "支付状态"}, "transaction_id": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Transaction Id", "description": "第三方交易ID"}, "scheduled_class_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Scheduled Class Id", "description": "关联课程ID"}, "operator_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Operator Id", "description": "操作人ID（为空表示会员自己操作）"}, "operator_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Operator Name", "description": "操作人姓名"}, "operator_type": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Operator Type", "description": "操作人类型（member/admin/system）"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "操作原因"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注信息"}, "client_ip": {"anyOf": [{"type": "string", "maxLength": 45}, {"type": "null"}], "title": "Client Ip", "description": "客户端IP"}, "user_agent": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "User Agent", "description": "用户代理"}, "status": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Status", "description": "操作状态", "default": "completed"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["tenant_id", "member_id", "member_card_id", "operation_type", "operation_description", "id", "created_at"], "title": "MemberCardOperationRead", "description": "会员卡操作记录响应模型"}, "MemberCardOperationType": {"type": "string", "enum": ["create_card", "update_card_info", "freeze_card", "unfreeze_card", "cancel_card", "recharge", "initial_binding", "direct_booking", "fixed_schedule_booking", "admin_booking", "manual_deduction", "member_cancel_booking", "admin_cancel_booking", "refund", "other"], "title": "MemberCardOperationType", "description": "会员卡操作类型枚举 - 统一记录所有会员卡相关操作"}, "MemberCardRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "member_id": {"type": "integer", "title": "Member Id", "description": "会员ID"}, "template_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Template Id", "description": "模板ID"}, "name": {"type": "string", "maxLength": 100, "title": "Name", "description": "卡片名称"}, "card_type": {"$ref": "#/components/schemas/CardType", "description": "卡片类型"}, "balance": {"type": "integer", "minimum": 0.0, "title": "Balance", "description": "当前余额（元或次数）", "default": 0}, "status": {"$ref": "#/components/schemas/CardStatus", "description": "卡片状态", "default": "active"}, "card_number": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Card Number", "description": "卡号"}, "total_recharged": {"type": "integer", "minimum": 0.0, "title": "Total Recharged", "description": "总充值金额（元）", "default": 0}, "total_consumed": {"type": "integer", "minimum": 0.0, "title": "Total Consumed", "description": "总消费金额（元）", "default": 0}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "过期时间"}, "last_used_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used At", "description": "最后使用时间"}, "freeze_reason": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Freeze Reason", "description": "冻结原因"}, "cancel_reason": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Cancel Reason", "description": "注销原因"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["tenant_id", "member_id", "name", "card_type", "id", "created_at"], "title": "MemberCardRead", "description": "会员卡响应模型"}, "MemberCardSummary": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "card_type": {"$ref": "#/components/schemas/CardType"}, "balance": {"type": "integer", "title": "Balance"}, "status": {"$ref": "#/components/schemas/CardStatus"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At"}, "last_used_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Used At"}}, "type": "object", "required": ["id", "name", "card_type", "balance", "status"], "title": "MemberCardSummary", "description": "会员卡摘要信息模型（用于会员详情页面）"}, "MemberCardTemplateCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "卡片模板名称"}, "card_type": {"$ref": "#/components/schemas/CardType", "description": "卡片类型"}, "sale_price": {"type": "integer", "minimum": 0.0, "title": "Sale Price", "description": "售卖价格（元）"}, "available_balance": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Available Balance", "description": "可用余额（元或次数）"}, "validity_days": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Validity Days", "description": "有效期(天)"}, "is_agent_exclusive": {"type": "boolean", "title": "Is Agent Exclusive", "description": "是否代理专售", "default": false}, "allow_repeat_purchase": {"type": "boolean", "title": "Allow Repeat Purchase", "description": "是否允许重复购买", "default": true}, "allow_renewal": {"type": "boolean", "title": "Allow <PERSON>", "description": "是否支持线上续费", "default": true}, "description": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Description", "description": "模板描述"}}, "type": "object", "required": ["name", "card_type", "sale_price"], "title": "MemberCardTemplateCreate", "description": "创建会员卡模板请求模型"}, "MemberCardTemplateList": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "card_type": {"$ref": "#/components/schemas/CardType"}, "sale_price": {"type": "integer", "title": "Sale Price"}, "available_balance": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Available Balance"}, "validity_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Validity Days"}, "is_agent_exclusive": {"type": "boolean", "title": "Is Agent Exclusive"}, "is_active": {"type": "boolean", "title": "Is Active"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "name", "card_type", "sale_price", "is_agent_exclusive", "is_active", "created_at"], "title": "MemberCardTemplateList", "description": "会员卡模板列表项模型"}, "MemberCardTemplateRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 100, "title": "Name", "description": "卡片模板名称"}, "card_type": {"$ref": "#/components/schemas/CardType", "description": "卡片类型"}, "sale_price": {"type": "integer", "minimum": 0.0, "title": "Sale Price", "description": "售卖价格（元）"}, "available_balance": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Available Balance", "description": "可用余额（元或次数）"}, "validity_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Validity Days", "description": "有效期(天)"}, "is_agent_exclusive": {"type": "boolean", "title": "Is Agent Exclusive", "description": "是否代理专售", "default": false}, "allow_repeat_purchase": {"type": "boolean", "title": "Allow Repeat Purchase", "description": "是否允许重复购买", "default": true}, "allow_renewal": {"type": "boolean", "title": "Allow <PERSON>", "description": "是否支持续费", "default": true}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "模板描述"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否启用", "default": true}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["tenant_id", "name", "card_type", "sale_price", "id", "created_at"], "title": "MemberCardTemplateRead", "description": "会员卡模板响应模型"}, "MemberCardTemplateUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "卡片模板名称"}, "card_type": {"anyOf": [{"$ref": "#/components/schemas/CardType"}, {"type": "null"}], "description": "卡片类型（不允许修改）"}, "sale_price": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Sale Price", "description": "售卖价格（元）"}, "available_balance": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Available Balance", "description": "可用余额（元或次数）"}, "validity_days": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Validity Days", "description": "有效期(天)"}, "is_agent_exclusive": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Agent Exclusive", "description": "是否代理专售"}, "allow_repeat_purchase": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Allow Repeat Purchase", "description": "是否允许重复购买"}, "allow_renewal": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Allow <PERSON>", "description": "是否支持线上续费"}, "description": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Description", "description": "模板描述"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "是否启用"}}, "type": "object", "title": "MemberCardTemplateUpdate", "description": "更新会员卡模板请求模型"}, "MemberCardUpdate": {"properties": {"status": {"anyOf": [{"$ref": "#/components/schemas/CardStatus"}, {"type": "null"}], "description": "卡片状态"}, "balance": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Balance", "description": "余额（元或次数）"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "过期时间"}, "freeze_reason": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Freeze Reason", "description": "冻结原因"}, "cancel_reason": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Cancel Reason", "description": "注销原因"}}, "type": "object", "title": "MemberCardUpdate", "description": "更新会员卡请求模型"}, "MemberClassBooking": {"properties": {"class_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Class Id", "description": "课程ID"}, "member_card_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Member Card Id", "description": "会员卡ID"}, "member_card_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Member Card Name", "description": "会员卡名称"}, "material_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Material Name", "description": "教材名称"}, "booking_remark": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Booking Remark", "description": "预约备注"}}, "type": "object", "required": ["class_id"], "title": "MemberClassBooking", "description": "会员版课程预约请求模型 - 会员直接约课"}, "MemberCreate": {"properties": {"name": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Name"}, "phone": {"type": "string", "maxLength": 20, "minLength": 11, "title": "Phone"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}]}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday"}, "member_type": {"$ref": "#/components/schemas/MemberType", "default": "trial"}, "source_channel": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source Channel"}, "agent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["name", "phone"], "title": "MemberCreate", "description": "创建会员请求模型"}, "MemberFixedLockOperationLogResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "tenant_id": {"type": "integer", "title": "Tenant Id"}, "member_fixed_slot_lock_id": {"type": "integer", "title": "Member Fixed Slot Lock Id"}, "operation_type": {"$ref": "#/components/schemas/MemberLockOperationType"}, "operation_status": {"$ref": "#/components/schemas/OperationStatus"}, "operation_description": {"type": "string", "title": "Operation Description"}, "operator_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Operator Id"}, "operator_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Operator Name"}, "operator_type": {"type": "string", "title": "Operator Type"}, "member_id": {"type": "integer", "title": "Member Id"}, "member_name": {"type": "string", "title": "Member Name"}, "teacher_id": {"type": "integer", "title": "Teacher Id"}, "teacher_name": {"type": "string", "title": "Teacher Name"}, "weekday": {"type": "integer", "title": "Weekday"}, "start_time": {"type": "string", "title": "Start Time"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "tenant_id", "member_fixed_slot_lock_id", "operation_type", "operation_status", "operation_description", "operator_id", "operator_name", "operator_type", "member_id", "member_name", "teacher_id", "teacher_name", "weekday", "start_time", "reason", "created_at"], "title": "MemberFixedLockOperationLogResponse", "description": "会员固定位锁定操作记录响应"}, "MemberFixedScheduleItem": {"properties": {"id": {"type": "integer", "title": "Id", "description": "时间段ID"}, "teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "weekday": {"$ref": "#/components/schemas/Weekday", "description": "星期几"}, "weekday_name": {"type": "string", "title": "Weekday Name", "description": "星期名称"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "开始时间"}, "duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration Minutes", "description": "课节时长(分钟)"}, "lock_status": {"type": "string", "title": "Lock Status", "description": "锁定状态"}, "locked_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Locked At", "description": "锁定时间"}}, "type": "object", "required": ["id", "teacher_id", "weekday", "weekday_name", "start_time", "duration_minutes", "lock_status", "locked_at"], "title": "MemberFixedScheduleItem", "description": "会员固定课程安排项"}, "MemberFixedSlotLockBatchCreate": {"properties": {"member_id": {"type": "integer", "title": "Member Id", "description": "会员ID"}, "teacher_fixed_slot_ids": {"items": {"type": "integer"}, "type": "array", "title": "Teacher Fixed Slot Ids", "description": "教师固定时间段ID列表"}}, "type": "object", "required": ["member_id", "teacher_fixed_slot_ids"], "title": "MemberFixedSlotLockBatchCreate", "description": "批量创建会员固定课位锁定请求模型"}, "MemberFixedSlotLockBatchDelete": {"properties": {"lock_ids": {"items": {"type": "integer"}, "type": "array", "title": "Lock Ids", "description": "要删除的锁定记录ID列表"}, "reason": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Reason", "description": "删除原因"}}, "type": "object", "required": ["lock_ids"], "title": "MemberFixedSlotLockBatchDelete", "description": "批量删除会员固定课位锁定请求模型"}, "MemberFixedSlotLockCreate": {"properties": {"member_id": {"type": "integer", "title": "Member Id", "description": "会员ID"}, "teacher_fixed_slot_id": {"type": "integer", "title": "Teacher Fixed Slot Id", "description": "教师固定时间段ID"}, "created_by": {"type": "integer", "title": "Created By", "description": "创建者ID"}}, "type": "object", "required": ["member_id", "teacher_fixed_slot_id", "created_by"], "title": "MemberFixedSlotLockCreate", "description": "创建会员固定课位锁定请求模型"}, "MemberFixedSlotLockList": {"properties": {"id": {"type": "integer", "title": "Id", "description": "主键ID"}, "member_id": {"type": "integer", "title": "Member Id", "description": "会员ID"}, "member_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Member Name", "description": "会员姓名"}, "member_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Member Phone", "description": "会员手机号"}, "teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "teacher_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Teacher Name", "description": "教师姓名"}, "weekday": {"type": "integer", "title": "Weekday", "description": "星期几"}, "weekday_name": {"type": "string", "title": "Weekday Name", "description": "星期名称"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "开始时间"}, "time_slot_display": {"type": "string", "title": "Time Slot Display", "description": "时间段显示"}, "locked_at": {"type": "string", "format": "date-time", "title": "Locked At", "description": "锁定时间"}}, "type": "object", "required": ["id", "member_id", "teacher_id", "weekday", "weekday_name", "start_time", "time_slot_display", "locked_at"], "title": "MemberFixedSlotLockList", "description": "会员固定课位锁定列表响应模型"}, "MemberFixedSlotLockResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "主键ID"}, "member_id": {"type": "integer", "title": "Member Id", "description": "会员ID"}, "member_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Member Name", "description": "会员姓名"}, "teacher_fixed_slot_id": {"type": "integer", "title": "Teacher Fixed Slot Id", "description": "教师固定时间段ID"}, "teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "teacher_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Teacher Name", "description": "教师姓名"}, "weekday": {"type": "integer", "title": "Weekday", "description": "星期几（1-7）"}, "weekday_name": {"type": "string", "title": "Weekday Name", "description": "星期名称"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "开始时间"}, "time_slot_display": {"type": "string", "title": "Time Slot Display", "description": "时间段显示"}, "locked_at": {"type": "string", "format": "date-time", "title": "Locked At", "description": "锁定时间"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By", "description": "创建者ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}}, "type": "object", "required": ["id", "member_id", "teacher_fixed_slot_id", "teacher_id", "weekday", "weekday_name", "start_time", "time_slot_display", "locked_at", "created_by", "created_at", "updated_at"], "title": "MemberFixedSlotLockResponse", "description": "会员固定课位锁定响应模型"}, "MemberLockOperationType": {"type": "string", "enum": ["create", "delete_by_member", "delete_by_admin"], "title": "MemberLockOperationType", "description": "会员固定位锁定操作类型枚举"}, "MemberLogin": {"properties": {"phone": {"type": "string", "maxLength": 20, "minLength": 11, "title": "Phone"}, "verification_code": {"type": "string", "maxLength": 6, "minLength": 4, "title": "Verification Code"}, "tenant_code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Tenant Code"}}, "type": "object", "required": ["phone", "verification_code", "tenant_code"], "title": "Member<PERSON><PERSON><PERSON>", "description": "会员登录请求模型"}, "MemberLoginResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type"}, "member": {"$ref": "#/components/schemas/MemberBasicRead"}, "tenant": {"$ref": "#/components/schemas/TenantRead"}}, "type": "object", "required": ["access_token", "token_type", "member", "tenant"], "title": "MemberLoginResponse", "description": "会员登录响应"}, "MemberRead": {"properties": {"total_classes": {"type": "integer", "title": "Total Classes", "description": "总上课数", "default": 0}, "completed_classes": {"type": "integer", "title": "Completed Classes", "description": "完成上课数", "default": 0}, "cancelled_classes": {"type": "integer", "title": "Cancelled Classes", "description": "取消上课数", "default": 0}, "no_show_classes": {"type": "integer", "title": "No Show Classes", "description": "缺席上课数", "default": 0}, "total_spent": {"type": "integer", "title": "Total Spent", "description": "总消费金额（元）", "default": 0}, "current_month_spent": {"type": "integer", "title": "Current Month Spent", "description": "当月消费金额（元）", "default": 0}, "total_recharged": {"type": "integer", "title": "Total Recharged", "description": "总充值金额（元）", "default": 0}, "avg_rating": {"type": "string", "title": "Avg <PERSON>ing", "description": "平均评分", "default": "0.0"}, "rating_count": {"type": "integer", "title": "Rating Count", "description": "评价次数", "default": 0}, "last_class_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Class At", "description": "最后上课时间"}, "last_login_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login At", "description": "最后登录时间"}, "tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "姓名"}, "phone": {"type": "string", "maxLength": 20, "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday", "description": "生日"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar Url", "description": "头像URL"}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "wechat_nickname": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "微信昵称"}, "wechat_avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Wechat Avatar", "description": "微信头像"}, "member_type": {"$ref": "#/components/schemas/MemberType", "description": "会员类型", "default": "trial"}, "member_status": {"$ref": "#/components/schemas/MemberStatus", "description": "会员状态", "default": "active"}, "source_channel": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Source Channel", "description": "来源渠道"}, "agent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id", "description": "代理人员ID"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "地址"}, "city": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "City", "description": "城市"}, "province": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Province", "description": "省份"}, "country": {"type": "string", "maxLength": 50, "title": "Country", "description": "国家", "default": "China"}, "postal_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Postal Code", "description": "邮编"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "标签"}, "primary_member_card_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Primary Member Card Id", "description": "主要会员卡ID"}, "primary_member_card_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Primary Member Card Name", "description": "主要会员卡名称"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "registered_at": {"type": "string", "format": "date-time", "title": "Registered At"}, "cards": {"anyOf": [{"items": {"$ref": "#/components/schemas/MemberCardSummary"}, "type": "array"}, {"type": "null"}], "title": "Cards", "description": "会员卡列表"}}, "type": "object", "required": ["tenant_id", "name", "phone", "id", "created_at", "registered_at"], "title": "MemberRead", "description": "会员响应模型（包含统计信息）"}, "MemberStatus": {"type": "string", "enum": ["active", "silent", "frozen", "cancelled"], "title": "MemberStatus", "description": "会员状态枚举"}, "MemberType": {"type": "string", "enum": ["trial", "formal", "vip"], "title": "MemberType", "description": "会员类型枚举"}, "MemberUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}]}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday"}, "avatar_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar Url"}, "wechat_openid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Wechat Openid"}, "wechat_unionid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Wechat <PERSON>id"}, "wechat_nickname": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "wechat_avatar": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Wechat Avatar"}, "member_type": {"anyOf": [{"$ref": "#/components/schemas/MemberType"}, {"type": "null"}]}, "member_status": {"anyOf": [{"$ref": "#/components/schemas/MemberStatus"}, {"type": "null"}]}, "source_channel": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source Channel"}, "agent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Agent Id"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City"}, "province": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Province"}, "country": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Country"}, "postal_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Postal Code"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Tags"}}, "type": "object", "title": "MemberUpdate", "description": "更新会员请求模型"}, "MessageResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}}, "type": "object", "title": "MessageResponse", "description": "消息响应模型（无数据）"}, "OperationStatus": {"type": "string", "enum": ["success", "failed", "pending"], "title": "OperationStatus", "description": "操作状态枚举"}, "OperationType": {"type": "string", "enum": ["task_start", "task_complete", "teacher_process", "member_process", "class_create", "balance_check", "balance_deduct", "conflict_detect", "error_handle"], "title": "OperationType", "description": "操作类型枚举"}, "PageResponse_FixedScheduleTaskLogResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/FixedScheduleTaskLogResponse"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[FixedScheduleTaskLogResponse]"}, "PageResponse_FixedScheduleTaskResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/FixedScheduleTaskResponse"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[FixedScheduleTaskResponse]"}, "PageResponse_MemberCardList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberCardList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[MemberCardList]"}, "PageResponse_MemberCardOperationRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberCardOperationRead"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[MemberCardOperationRead]"}, "PageResponse_MemberFixedLockOperationLogResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberFixedLockOperationLogResponse"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[MemberFixedLockOperationLogResponse]"}, "PageResponse_MemberFixedSlotLockList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberFixedSlotLockList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[MemberFixedSlotLockList]"}, "PageResponse_MemberRead_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/MemberRead"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[MemberRead]"}, "PageResponse_ScheduledClassList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/ScheduledClassList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[ScheduledClassList]"}, "PageResponse_ScheduledClassOperationLogResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/ScheduledClassOperationLogResponse"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[ScheduledClassOperationLogResponse]"}, "PageResponse_TeacherFixedSlotList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TeacherFixedSlotList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[TeacherFixedSlotList]"}, "PageResponse_TeacherFixedSlotOperationLogResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TeacherFixedSlotOperationLogResponse"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[TeacherFixedSlotOperationLogResponse]"}, "PageResponse_TeacherList_": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "请求是否成功", "default": true}, "message": {"type": "string", "title": "Message", "description": "响应消息", "default": "操作成功"}, "http_code": {"type": "integer", "title": "Http Code", "description": "HTTP状态码", "default": 200}, "business_code": {"type": "string", "title": "Business Code", "description": "业务状态码", "default": "SUCCESS"}, "timestamp": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timestamp"}, "data": {"items": {"$ref": "#/components/schemas/TeacherList"}, "type": "array", "title": "Data", "description": "数据列表", "default": []}, "total": {"type": "integer", "title": "Total", "description": "总记录数", "default": 0}, "page": {"type": "integer", "title": "Page", "description": "当前页码", "default": 1}, "size": {"type": "integer", "title": "Size", "description": "每页记录数", "default": 20}, "pages": {"type": "integer", "title": "Pages", "description": "总页数", "default": 0}}, "type": "object", "title": "PageResponse[TeacherList]"}, "PasswordChange": {"properties": {"old_password": {"type": "string", "title": "Old Password"}, "new_password": {"type": "string", "minLength": 6, "title": "New Password"}}, "type": "object", "required": ["old_password", "new_password"], "title": "PasswordChange", "description": "修改密码请求模型"}, "PaymentMethod": {"type": "string", "enum": ["wechat", "alipay", "manual", "bank_transfer"], "title": "PaymentMethod", "description": "支付方式枚举"}, "PlanType": {"type": "string", "enum": ["trial", "basic", "standard", "premium", "enterprise"], "title": "PlanType", "description": "套餐类型枚举"}, "RechargeRequest": {"properties": {"member_card_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Member Card Id", "description": "会员卡ID"}, "amount": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Amount", "description": "充值金额（元）"}, "bonus_amount": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Bonus Amount", "description": "赠送金额（元）", "default": 0}, "actual_amount": {"type": "integer", "minimum": 0.0, "title": "Actual Amount", "description": "实收金额（元）", "default": 0}, "payment_method": {"$ref": "#/components/schemas/PaymentMethod", "description": "支付方式"}, "extend_validity_days": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Extend Validity Days", "description": "延长有效期（天）", "default": 0}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "备注信息"}}, "type": "object", "required": ["member_card_id", "amount", "payment_method"], "title": "RechargeRequest", "description": "充值请求模型"}, "RechargeResponse": {"properties": {"operation_id": {"type": "integer", "title": "Operation Id", "description": "操作记录ID"}, "member_card_id": {"type": "integer", "title": "Member Card Id", "description": "会员卡ID"}, "amount": {"type": "integer", "title": "Amount", "description": "充值金额（元）"}, "bonus_amount": {"type": "integer", "title": "Bonus Amount", "description": "赠送金额（元）"}, "total_amount": {"type": "integer", "title": "Total Amount", "description": "总到账金额（元）"}, "balance_before": {"type": "integer", "title": "Balance Before", "description": "充值前余额（元）"}, "balance_after": {"type": "integer", "title": "Balance After", "description": "充值后余额（元）"}, "payment_method": {"$ref": "#/components/schemas/PaymentMethod", "description": "支付方式"}, "transaction_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Transaction Id", "description": "交易ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "充值时间"}, "extend_validity_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Extend Validity Days", "description": "延长的有效期天数"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "新的过期时间"}}, "type": "object", "required": ["operation_id", "member_card_id", "amount", "bonus_amount", "total_amount", "balance_before", "balance_after", "payment_method", "transaction_id", "created_at"], "title": "RechargeResponse", "description": "充值响应模型"}, "ScheduleTaskStatsResponse": {"properties": {"total_tasks": {"type": "integer", "title": "Total Tasks", "description": "总任务数"}, "pending_tasks": {"type": "integer", "title": "Pending Tasks", "description": "待执行任务数"}, "running_tasks": {"type": "integer", "title": "Running Tasks", "description": "执行中任务数"}, "completed_tasks": {"type": "integer", "title": "Completed Tasks", "description": "已完成任务数"}, "failed_tasks": {"type": "integer", "title": "Failed Tasks", "description": "失败任务数"}, "total_classes_generated": {"type": "integer", "title": "Total Classes Generated", "description": "总生成课程数"}, "total_amount_deducted": {"type": "integer", "title": "Total Amount Deducted", "description": "总扣费金额（元）"}}, "type": "object", "required": ["total_tasks", "pending_tasks", "running_tasks", "completed_tasks", "failed_tasks", "total_classes_generated", "total_amount_deducted"], "title": "ScheduleTaskStatsResponse", "description": "排课任务统计响应"}, "ScheduleTaskStatus": {"type": "string", "enum": ["pending", "running", "completed", "failed"], "title": "ScheduleTaskStatus", "description": "排课任务状态枚举"}, "ScheduledClassList": {"properties": {"id": {"type": "integer", "title": "Id"}, "teacher_id": {"type": "integer", "title": "Teacher Id"}, "teacher_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Teacher Name"}, "teacher_display_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Teacher Display Code"}, "member_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Member Id"}, "member_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Member Name"}, "class_datetime": {"type": "string", "format": "date-time", "title": "Class Datetime"}, "class_datetime_display": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Class Datetime Display"}, "duration_minutes": {"type": "integer", "title": "Duration Minutes"}, "class_type": {"$ref": "#/components/schemas/ClassType"}, "class_type_display": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Class Type Display"}, "status": {"$ref": "#/components/schemas/ClassStatus"}, "status_display": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status Display"}, "price": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Price"}, "is_visible_to_member": {"type": "boolean", "title": "Is Visible To Member"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "teacher_id", "class_datetime", "duration_minutes", "class_type", "status", "is_visible_to_member", "created_at"], "title": "ScheduledClassList", "description": "已排课表列表响应模型"}, "ScheduledClassOperationLogResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "tenant_id": {"type": "integer", "title": "Tenant Id"}, "scheduled_class_id": {"type": "integer", "title": "Scheduled Class Id"}, "operation_type": {"$ref": "#/components/schemas/ClassOperationType"}, "operation_status": {"$ref": "#/components/schemas/OperationStatus"}, "operation_description": {"type": "string", "title": "Operation Description"}, "operator_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Operator Id"}, "operator_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Operator Name"}, "operator_type": {"type": "string", "title": "Operator Type"}, "teacher_id": {"type": "integer", "title": "Teacher Id"}, "teacher_name": {"type": "string", "title": "Teacher Name"}, "member_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Member Id"}, "member_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Member Name"}, "class_datetime": {"type": "string", "format": "date-time", "title": "Class Datetime"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "tenant_id", "scheduled_class_id", "operation_type", "operation_status", "operation_description", "operator_id", "operator_name", "operator_type", "teacher_id", "teacher_name", "member_id", "member_name", "class_datetime", "reason", "created_at"], "title": "ScheduledClassOperationLogResponse", "description": "课程操作记录响应"}, "ScheduledClassRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "member_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Member Id", "description": "会员ID（可为空，未预约状态）"}, "class_datetime": {"type": "string", "format": "date-time", "title": "Class Datetime", "description": "精确到分钟的上课时间"}, "duration_minutes": {"type": "integer", "maximum": 120.0, "minimum": 5.0, "title": "Duration Minutes", "description": "课程时长（分钟）", "default": 25}, "class_type": {"$ref": "#/components/schemas/ClassType", "description": "课程类型", "default": "direct"}, "price": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Price", "description": "教师单价（整数，单位：元）"}, "member_card_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Member Card Id", "description": "会员卡ID"}, "member_card_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Member Card Name", "description": "会员卡名称（冗余字段，便于显示）"}, "booking_remark": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Booking Remark", "description": "预约备注"}, "member_no_cancel": {"type": "boolean", "title": "Member No Cancel", "description": "会员是否可取消", "default": false}, "material_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Material Id", "description": "预留教材ID（第二期使用）"}, "material_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Material Name", "description": "教材名称（第一期主要使用此字段）"}, "status": {"$ref": "#/components/schemas/ClassStatus", "description": "课程状态", "default": "available"}, "is_deleted": {"type": "boolean", "title": "Is Deleted", "description": "是否删除", "default": false}, "is_visible_to_member": {"type": "boolean", "title": "Is Visible To Member", "description": "是否对会员可见", "default": true}, "operator_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Operator Name", "description": "操作人显示名"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}, "teacher_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Teacher Name", "description": "教师姓名"}, "teacher_display_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Teacher Display Code", "description": "教师显示编码，方便会员识别"}, "member_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Member Name", "description": "会员姓名"}, "member_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Member Phone", "description": "会员手机号"}, "class_datetime_display": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Class Datetime Display", "description": "上课时间显示"}, "status_display": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status Display", "description": "状态显示名称"}, "class_type_display": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Class Type Display", "description": "课程类型显示名称"}}, "type": "object", "required": ["tenant_id", "teacher_id", "class_datetime", "id", "created_at"], "title": "ScheduledClassRead", "description": "已排课表响应模型"}, "TagBatchCreate": {"properties": {"category_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Category Id", "description": "标签分类ID"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "标签名称列表"}}, "type": "object", "required": ["category_id", "tags"], "title": "TagBatchCreate", "description": "批量创建标签请求模型"}, "TagBatchUpdate": {"properties": {"tag_ids": {"items": {"type": "integer"}, "type": "array", "title": "Tag Ids", "description": "标签ID列表"}, "status": {"anyOf": [{"$ref": "#/components/schemas/TagStatus"}, {"type": "null"}], "description": "标签状态"}, "category_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Category Id", "description": "标签分类ID"}}, "type": "object", "required": ["tag_ids"], "title": "TagBatchUpdate", "description": "批量更新标签请求模型"}, "TagCategoryCreate": {"properties": {"name": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Name", "description": "分类名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "分类描述"}, "sort_order": {"type": "integer", "title": "Sort Order", "description": "排序顺序", "default": 0}}, "type": "object", "required": ["name"], "title": "TagCategoryCreate", "description": "创建标签分类请求模型"}, "TagCategoryList": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "sort_order": {"type": "integer", "title": "Sort Order"}, "tag_count": {"type": "integer", "title": "Tag Count", "description": "该分类下的标签数量", "default": 0}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["id", "name", "sort_order", "created_at"], "title": "TagCategoryList", "description": "标签分类列表响应模型"}, "TagCategoryRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "分类名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "分类描述"}, "sort_order": {"type": "integer", "title": "Sort Order", "description": "排序顺序", "default": 0}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["tenant_id", "name", "id", "created_at"], "title": "TagCategoryRead", "description": "标签分类响应模型"}, "TagCategoryUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "分类名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "分类描述"}, "sort_order": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Sort Order", "description": "排序顺序"}}, "type": "object", "title": "TagCategoryUpdate", "description": "更新标签分类请求模型"}, "TagCreate": {"properties": {"name": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Name", "description": "标签名称"}, "category_id": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Category Id", "description": "标签分类ID"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "标签描述"}, "status": {"$ref": "#/components/schemas/TagStatus", "description": "标签状态", "default": "active"}}, "type": "object", "required": ["name", "category_id"], "title": "TagCreate", "description": "创建标签请求模型"}, "TagList": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "status": {"$ref": "#/components/schemas/TagStatus"}, "category_id": {"type": "integer", "title": "Category Id"}, "category_name": {"type": "string", "title": "Category Name", "description": "分类名称"}}, "type": "object", "required": ["id", "name", "status", "category_id", "category_name"], "title": "TagList", "description": "标签列表响应模型"}, "TagRead": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "category_id": {"type": "integer", "title": "Category Id", "description": "标签分类ID"}, "category_name": {"type": "string", "maxLength": 50, "title": "Category Name", "description": "标签分类名称（冗余字段，用于优化查询）"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "标签名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "标签描述"}, "status": {"$ref": "#/components/schemas/TagStatus", "description": "标签状态", "default": "active"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["tenant_id", "category_id", "category_name", "name", "id", "created_at"], "title": "TagRead", "description": "标签响应模型"}, "TagStatus": {"type": "string", "enum": ["active", "inactive"], "title": "TagStatus", "description": "标签状态枚举"}, "TagUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "标签名称"}, "category_id": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Category Id", "description": "标签分类ID"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "标签描述"}, "status": {"anyOf": [{"$ref": "#/components/schemas/TagStatus"}, {"type": "null"}], "description": "标签状态"}}, "type": "object", "title": "TagUpdate", "description": "更新标签请求模型"}, "TagWithTeacherCount": {"properties": {"tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "category_id": {"type": "integer", "title": "Category Id", "description": "标签分类ID"}, "category_name": {"type": "string", "maxLength": 50, "title": "Category Name", "description": "标签分类名称（冗余字段，用于优化查询）"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "标签名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Description", "description": "标签描述"}, "status": {"$ref": "#/components/schemas/TagStatus", "description": "标签状态", "default": "active"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}, "teacher_count": {"type": "integer", "title": "Teacher Count", "description": "使用该标签的教师数量", "default": 0}}, "type": "object", "required": ["tenant_id", "category_id", "category_name", "name", "id", "created_at"], "title": "TagWithTeacherCount", "description": "带教师数量的标签响应模型"}, "TeacherAvailabilityUpdateRequest": {"properties": {"teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "weekdays": {"anyOf": [{"items": {"$ref": "#/components/schemas/Weekday"}, "type": "array"}, {"type": "null"}], "title": "Weekdays", "description": "星期列表，不指定则更新所有星期"}, "start_time_from": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Time From", "description": "开始时间范围-起始（HH:MM格式）"}, "start_time_to": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Time To", "description": "开始时间范围-结束（HH:MM格式）"}, "is_available": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Available", "description": "是否可用"}, "is_visible_to_members": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Visible To Members", "description": "是否对会员可见"}}, "type": "object", "required": ["teacher_id"], "title": "TeacherAvailabilityUpdateRequest", "description": "教师时间段可用性批量更新请求模型"}, "TeacherCategory": {"type": "string", "enum": ["european", "south_african", "filipino", "chinese", "other"], "title": "TeacherCategory", "description": "教师分类枚举"}, "TeacherClassCreate": {"properties": {"class_datetime": {"type": "string", "format": "date-time", "title": "Class Datetime", "description": "精确到分钟的上课时间"}, "duration_minutes": {"type": "integer", "maximum": 120.0, "minimum": 5.0, "title": "Duration Minutes", "description": "课程时长（分钟）", "default": 25}, "price": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Price", "description": "教师单价（整数，单位：元）"}, "material_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Material Name", "description": "教材名称"}, "is_visible_to_member": {"type": "boolean", "title": "Is Visible To Member", "description": "是否对会员可见", "default": true}}, "type": "object", "required": ["class_datetime"], "title": "TeacherClassCreate", "description": "教师版课程创建请求模型 - 教师开放可预约课节"}, "TeacherCreate": {"properties": {"name": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Name", "description": "教师姓名"}, "display_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Display Code", "description": "教师显示编号（给会员看的标识）"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar", "description": "头像URL"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "price_per_class": {"type": "integer", "title": "Price Per Class", "description": "单节课价格（整数，单位：元）", "default": 0}, "teacher_category": {"$ref": "#/components/schemas/TeacherCategory", "description": "教师分类"}, "region": {"$ref": "#/components/schemas/TeacherRegion", "description": "教师区域"}, "wechat_bound": {"type": "boolean", "title": "Wechat Bound", "description": "是否绑定微信", "default": false}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "show_to_members": {"type": "boolean", "title": "Show To Members", "description": "是否对会员端展示", "default": true}, "introduction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Introduction", "description": "教师介绍"}, "teaching_experience": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Teaching Experience", "description": "教学经验(年)"}, "specialties": {"items": {"type": "string"}, "type": "array", "title": "Specialties", "description": "专业特长"}, "certifications": {"items": {"type": "string"}, "type": "array", "title": "Certifications", "description": "资质证书"}, "priority_level": {"type": "integer", "title": "Priority Level", "description": "排课优先级", "default": 0}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "tag_ids": {"items": {"type": "integer"}, "type": "array", "title": "Tag Ids", "description": "标签ID列表"}}, "type": "object", "required": ["name", "teacher_category", "region"], "title": "TeacherCreate", "description": "创建教师请求模型"}, "TeacherDetail": {"properties": {"total_classes": {"type": "integer", "title": "Total Classes", "description": "总上课数", "default": 0}, "completed_classes": {"type": "integer", "title": "Completed Classes", "description": "完成上课数", "default": 0}, "cancelled_classes": {"type": "integer", "title": "Cancelled Classes", "description": "取消上课数", "default": 0}, "no_show_classes": {"type": "integer", "title": "No Show Classes", "description": "缺席上课数", "default": 0}, "total_earnings": {"type": "integer", "title": "Total Earnings", "description": "总收入（元）", "default": 0}, "current_month_earnings": {"type": "integer", "title": "Current Month Earnings", "description": "当月收入（元）", "default": 0}, "avg_rating": {"type": "string", "title": "Avg <PERSON>ing", "description": "平均评分", "default": "0.0"}, "rating_count": {"type": "integer", "title": "Rating Count", "description": "评价次数", "default": 0}, "last_class_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Class At", "description": "最后上课时间"}, "tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "教师姓名"}, "display_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Display Code", "description": "教师显示编号（给会员看的标识）"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar", "description": "头像URL"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "price_per_class": {"type": "integer", "title": "Price Per Class", "description": "单节课价格（整数，单位：元）", "default": 0}, "teacher_category": {"$ref": "#/components/schemas/TeacherCategory", "description": "教师分类"}, "region": {"$ref": "#/components/schemas/TeacherRegion", "description": "教师区域"}, "wechat_bound": {"type": "boolean", "title": "Wechat Bound", "description": "是否绑定微信", "default": false}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "show_to_members": {"type": "boolean", "title": "Show To Members", "description": "是否对会员端展示", "default": true}, "status": {"$ref": "#/components/schemas/TeacherStatus", "description": "教师状态", "default": "pending"}, "introduction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Introduction", "description": "教师介绍"}, "teaching_experience": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Teaching Experience", "description": "教学经验(年)"}, "specialties": {"items": {"type": "string"}, "type": "array", "title": "Specialties", "description": "专业特长"}, "certifications": {"items": {"type": "string"}, "type": "array", "title": "Certifications", "description": "资质证书"}, "priority_level": {"type": "integer", "title": "Priority Level", "description": "排课优先级(数字越大优先级越高)", "default": 0}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}, "tags": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Tags", "description": "关联的标签列表"}}, "type": "object", "required": ["tenant_id", "name", "teacher_category", "region", "id", "created_at"], "title": "TeacherDetail", "description": "教师详情响应模型"}, "TeacherFixedSlotCreate": {"properties": {"teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "weekday": {"$ref": "#/components/schemas/Weekday", "description": "星期几（1-7，1为星期一）"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "开始时间（HH:MM格式）"}, "duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration Minutes", "description": "课节时长(分钟)，NULL时使用系统默认值", "default": 25}, "is_available": {"type": "boolean", "title": "Is Available", "description": "是否开放给会员锁定", "default": true}, "is_visible_to_members": {"type": "boolean", "title": "Is Visible To Members", "description": "是否对会员可见", "default": true}, "created_by": {"type": "integer", "title": "Created By", "description": "创建者ID"}}, "type": "object", "required": ["teacher_id", "weekday", "start_time", "created_by"], "title": "TeacherFixedSlotCreate", "description": "创建教师固定时间段请求模型"}, "TeacherFixedSlotList": {"properties": {"id": {"type": "integer", "title": "Id", "description": "主键ID"}, "teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "weekday": {"$ref": "#/components/schemas/Weekday", "description": "星期几"}, "weekday_name": {"type": "string", "title": "Weekday Name", "description": "星期名称"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "开始时间"}, "duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration Minutes", "description": "课节时长(分钟)"}, "is_available": {"type": "boolean", "title": "Is Available", "description": "是否可用"}, "is_visible_to_members": {"type": "boolean", "title": "Is Visible To Members", "description": "是否对会员可见"}}, "type": "object", "required": ["id", "teacher_id", "weekday", "weekday_name", "start_time", "duration_minutes", "is_available", "is_visible_to_members"], "title": "TeacherFixedSlotList", "description": "教师固定时间段列表响应模型"}, "TeacherFixedSlotOperationLogResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "tenant_id": {"type": "integer", "title": "Tenant Id"}, "teacher_fixed_slot_id": {"type": "integer", "title": "Teacher Fixed Slot Id"}, "operation_type": {"$ref": "#/components/schemas/TeacherSlotOperationType"}, "operation_status": {"$ref": "#/components/schemas/OperationStatus"}, "operation_description": {"type": "string", "title": "Operation Description"}, "operator_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Operator Id"}, "operator_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Operator Name"}, "operator_type": {"type": "string", "title": "Operator Type"}, "teacher_id": {"type": "integer", "title": "Teacher Id"}, "teacher_name": {"type": "string", "title": "Teacher Name"}, "weekday": {"type": "integer", "title": "Weekday"}, "start_time": {"type": "string", "title": "Start Time"}, "duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration Minutes"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "tenant_id", "teacher_fixed_slot_id", "operation_type", "operation_status", "operation_description", "operator_id", "operator_name", "operator_type", "teacher_id", "teacher_name", "weekday", "start_time", "duration_minutes", "reason", "created_at"], "title": "TeacherFixedSlotOperationLogResponse", "description": "教师固定时间段操作记录响应"}, "TeacherFixedSlotResponse": {"properties": {"id": {"type": "integer", "title": "Id", "description": "主键ID"}, "teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "weekday": {"$ref": "#/components/schemas/Weekday", "description": "星期几（1-7，1为星期一）"}, "weekday_name": {"type": "string", "title": "Weekday Name", "description": "星期名称"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "开始时间（HH:MM格式）"}, "duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration Minutes", "description": "课节时长(分钟)"}, "is_available": {"type": "boolean", "title": "Is Available", "description": "是否开放给会员锁定"}, "is_visible_to_members": {"type": "boolean", "title": "Is Visible To Members", "description": "是否对会员可见"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By", "description": "创建者ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}}, "type": "object", "required": ["id", "teacher_id", "weekday", "weekday_name", "start_time", "duration_minutes", "is_available", "is_visible_to_members", "created_by", "created_at", "updated_at"], "title": "TeacherFixedSlotResponse", "description": "教师固定时间段响应模型"}, "TeacherFixedSlotStats": {"properties": {"teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "total_slots": {"type": "integer", "title": "Total Slots", "description": "总时间段数"}, "available_slots": {"type": "integer", "title": "Available Slots", "description": "可用时间段数"}, "visible_slots": {"type": "integer", "title": "Visible Slots", "description": "对会员可见的时间段数"}, "weekday_distribution": {"additionalProperties": true, "type": "object", "title": "Weekday Distribution", "description": "按星期分布统计"}}, "type": "object", "required": ["teacher_id", "total_slots", "available_slots", "visible_slots", "weekday_distribution"], "title": "TeacherFixedSlotStats", "description": "教师固定时间段统计信息"}, "TeacherFixedSlotUpdate": {"properties": {"weekday": {"anyOf": [{"$ref": "#/components/schemas/Weekday"}, {"type": "null"}], "description": "星期几（1-7，1为星期一）"}, "start_time": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Start Time", "description": "开始时间（HH:MM格式）"}, "duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration Minutes", "description": "课节时长(分钟)"}, "is_available": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Available", "description": "是否开放给会员锁定"}, "is_visible_to_members": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Visible To Members", "description": "是否对会员可见"}}, "type": "object", "title": "TeacherFixedSlotUpdate", "description": "更新教师固定时间段请求模型"}, "TeacherList": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "display_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Display Code"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}]}, "avatar": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar"}, "teacher_category": {"$ref": "#/components/schemas/TeacherCategory"}, "region": {"$ref": "#/components/schemas/TeacherRegion"}, "price_per_class": {"type": "integer", "title": "Price Per Class"}, "status": {"$ref": "#/components/schemas/TeacherStatus"}, "show_to_members": {"type": "boolean", "title": "Show To Members"}, "priority_level": {"type": "integer", "title": "Priority Level"}, "teaching_experience": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Teaching Experience"}, "tag_count": {"type": "integer", "title": "Tag Count", "description": "标签数量", "default": 0}, "tags": {"anyOf": [{"items": {"$ref": "#/components/schemas/TagList"}, "type": "array"}, {"type": "null"}], "title": "Tags", "description": "教师标签列表"}}, "type": "object", "required": ["id", "name", "teacher_category", "region", "price_per_class", "status", "show_to_members", "priority_level"], "title": "TeacherList", "description": "教师列表响应模型"}, "TeacherPriorityRule": {"type": "string", "enum": ["region_first", "id_desc", "random", "price_desc", "number_desc", "rating_desc"], "title": "TeacherPriorityRule", "description": "教师优先级规则枚举"}, "TeacherRead": {"properties": {"total_classes": {"type": "integer", "title": "Total Classes", "description": "总上课数", "default": 0}, "completed_classes": {"type": "integer", "title": "Completed Classes", "description": "完成上课数", "default": 0}, "cancelled_classes": {"type": "integer", "title": "Cancelled Classes", "description": "取消上课数", "default": 0}, "no_show_classes": {"type": "integer", "title": "No Show Classes", "description": "缺席上课数", "default": 0}, "total_earnings": {"type": "integer", "title": "Total Earnings", "description": "总收入（元）", "default": 0}, "current_month_earnings": {"type": "integer", "title": "Current Month Earnings", "description": "当月收入（元）", "default": 0}, "avg_rating": {"type": "string", "title": "Avg <PERSON>ing", "description": "平均评分", "default": "0.0"}, "rating_count": {"type": "integer", "title": "Rating Count", "description": "评价次数", "default": 0}, "last_class_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Class At", "description": "最后上课时间"}, "tenant_id": {"type": "integer", "title": "Tenant Id", "description": "租户ID"}, "name": {"type": "string", "maxLength": 50, "title": "Name", "description": "教师姓名"}, "display_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Display Code", "description": "教师显示编号（给会员看的标识）"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar", "description": "头像URL"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "price_per_class": {"type": "integer", "title": "Price Per Class", "description": "单节课价格（整数，单位：元）", "default": 0}, "teacher_category": {"$ref": "#/components/schemas/TeacherCategory", "description": "教师分类"}, "region": {"$ref": "#/components/schemas/TeacherRegion", "description": "教师区域"}, "wechat_bound": {"type": "boolean", "title": "Wechat Bound", "description": "是否绑定微信", "default": false}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "show_to_members": {"type": "boolean", "title": "Show To Members", "description": "是否对会员端展示", "default": true}, "status": {"$ref": "#/components/schemas/TeacherStatus", "description": "教师状态", "default": "pending"}, "introduction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Introduction", "description": "教师介绍"}, "teaching_experience": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Teaching Experience", "description": "教学经验(年)"}, "specialties": {"items": {"type": "string"}, "type": "array", "title": "Specialties", "description": "专业特长"}, "certifications": {"items": {"type": "string"}, "type": "array", "title": "Certifications", "description": "资质证书"}, "priority_level": {"type": "integer", "title": "Priority Level", "description": "排课优先级(数字越大优先级越高)", "default": 0}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "created_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Created By"}}, "type": "object", "required": ["tenant_id", "name", "teacher_category", "region", "id", "created_at"], "title": "TeacherRead", "description": "教师响应模型（包含统计信息）"}, "TeacherRegion": {"type": "string", "enum": ["europe", "north_america", "south_africa", "philippines", "china", "other"], "title": "TeacherRegion", "description": "教师区域枚举"}, "TeacherSlotOperationType": {"type": "string", "enum": ["create", "delete", "set_visible", "set_invisible"], "title": "TeacherSlotOperationType", "description": "教师固定时间段操作类型枚举"}, "TeacherStatus": {"type": "string", "enum": ["active", "inactive", "pending", "suspended"], "title": "TeacherStatus", "description": "教师状态枚举"}, "TeacherStatusUpdate": {"properties": {"status": {"$ref": "#/components/schemas/TeacherStatus", "description": "新状态"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "状态变更原因"}}, "type": "object", "required": ["status"], "title": "TeacherStatusUpdate", "description": "教师状态更新模型"}, "TeacherTagAssign": {"properties": {"tag_ids": {"items": {"type": "integer"}, "type": "array", "title": "Tag Ids", "description": "标签ID列表"}}, "type": "object", "required": ["tag_ids"], "title": "TeacherTagAssign", "description": "教师标签分配模型"}, "TeacherTagBatch": {"properties": {"teacher_ids": {"items": {"type": "integer"}, "type": "array", "title": "Teacher Ids", "description": "教师ID列表"}, "tag_ids": {"items": {"type": "integer"}, "type": "array", "title": "Tag Ids", "description": "标签ID列表"}, "operation": {"type": "string", "title": "Operation", "description": "操作类型：add-添加，remove-移除"}}, "type": "object", "required": ["teacher_ids", "tag_ids", "operation"], "title": "TeacherTagBatch", "description": "教师标签批量操作模型"}, "TeacherUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "教师姓名"}, "display_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Display Code", "description": "教师显示编号（给会员看的标识）"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "avatar": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar", "description": "头像URL"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "手机号"}, "email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "price_per_class": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Price Per Class", "description": "单节课价格"}, "teacher_category": {"anyOf": [{"$ref": "#/components/schemas/TeacherCategory"}, {"type": "null"}], "description": "教师分类"}, "region": {"anyOf": [{"$ref": "#/components/schemas/TeacherRegion"}, {"type": "null"}], "description": "教师区域"}, "wechat_bound": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Wechat Bound", "description": "是否绑定微信"}, "wechat_openid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat Openid", "description": "微信OpenID"}, "wechat_unionid": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Wechat <PERSON>id", "description": "微信UnionID"}, "show_to_members": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Show To Members", "description": "是否对会员端展示"}, "introduction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Introduction", "description": "教师介绍"}, "teaching_experience": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Teaching Experience", "description": "教学经验(年)"}, "specialties": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Specialties", "description": "专业特长"}, "certifications": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Certifications", "description": "资质证书"}, "priority_level": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Priority Level", "description": "排课优先级"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}}, "type": "object", "title": "TeacherUpdate", "description": "更新教师请求模型"}, "TenantCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "机构名称"}, "code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Code", "description": "机构代码"}, "display_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Display Name", "description": "显示名称"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "机构描述"}, "domain": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Domain", "description": "自定义域名"}, "subdomain": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Subdomain", "description": "子域名"}, "logo_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Logo Url", "description": "机构logo"}, "favicon_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Favicon <PERSON>", "description": "网站图标"}, "contact_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Contact Name", "description": "联系人姓名"}, "contact_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Contact Phone", "description": "联系电话"}, "contact_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Contact Email", "description": "联系邮箱"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "机构地址"}, "plan_type": {"$ref": "#/components/schemas/PlanType", "description": "套餐类型", "default": "trial"}, "max_teachers": {"type": "integer", "title": "Max Teachers", "description": "最大教师数量", "default": 5}, "max_members": {"type": "integer", "title": "Max Members", "description": "最大会员数量", "default": 50}, "max_storage_gb": {"type": "integer", "title": "Max Storage Gb", "description": "最大存储空间(GB)", "default": 1}, "billing_cycle": {"$ref": "#/components/schemas/BillingCycle", "description": "计费周期", "default": "monthly"}, "price_per_class": {"type": "integer", "title": "Price Per Class", "description": "按课时计费单价", "default": 0}, "monthly_fee": {"type": "integer", "title": "Monthly Fee", "description": "月费", "default": 0}, "setup_fee": {"type": "integer", "title": "<PERSON>up <PERSON>e", "description": "安装费", "default": 0}, "status": {"$ref": "#/components/schemas/TenantStatus", "description": "租户状态", "default": "trial"}, "subscription_expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Subscription Expires At", "description": "订阅到期时间"}}, "type": "object", "required": ["name", "code"], "title": "TenantCreate", "description": "创建租户请求模型"}, "TenantPlanTemplate": {"properties": {"plan_code": {"type": "string", "maxLength": 20, "title": "Plan Code", "description": "套餐代码"}, "plan_name": {"type": "string", "maxLength": 50, "title": "Plan Name", "description": "套餐名称"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "套餐描述"}, "max_teachers": {"type": "integer", "title": "Max Teachers", "description": "最大教师数"}, "max_members": {"type": "integer", "title": "Max Members", "description": "最大会员数"}, "max_storage_gb": {"type": "integer", "title": "Max Storage Gb", "description": "最大存储空间(GB)"}, "monthly_price": {"type": "integer", "title": "Monthly Price", "description": "月费", "default": 0}, "yearly_price": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Yearly Price", "description": "年费"}, "setup_fee": {"type": "integer", "title": "<PERSON>up <PERSON>e", "description": "安装费", "default": 0}, "price_per_class": {"type": "integer", "title": "Price Per Class", "description": "按课时计费", "default": 0}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否激活", "default": true}, "sort_order": {"type": "integer", "title": "Sort Order", "description": "排序", "default": 0}, "id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "features": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Features", "description": "功能列表"}}, "type": "object", "required": ["plan_code", "plan_name", "max_teachers", "max_members", "max_storage_gb"], "title": "TenantPlanTemplate", "description": "租户配置模板数据库模型"}, "TenantRead": {"properties": {"name": {"type": "string", "maxLength": 100, "title": "Name", "description": "机构名称"}, "code": {"type": "string", "maxLength": 50, "title": "Code", "description": "机构代码"}, "display_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Display Name", "description": "显示名称"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "机构描述"}, "domain": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Domain", "description": "自定义域名"}, "subdomain": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Subdomain", "description": "子域名"}, "logo_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Logo Url", "description": "机构logo"}, "favicon_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Favicon <PERSON>", "description": "网站图标"}, "contact_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Contact Name", "description": "联系人姓名"}, "contact_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Contact Phone", "description": "联系电话"}, "contact_email": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Contact Email", "description": "联系邮箱"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "机构地址"}, "plan_type": {"$ref": "#/components/schemas/PlanType", "description": "套餐类型", "default": "trial"}, "max_teachers": {"type": "integer", "title": "Max Teachers", "description": "最大教师数量", "default": 5}, "max_members": {"type": "integer", "title": "Max Members", "description": "最大会员数量", "default": 50}, "max_storage_gb": {"type": "integer", "title": "Max Storage Gb", "description": "最大存储空间(GB)", "default": 1}, "billing_cycle": {"$ref": "#/components/schemas/BillingCycle", "description": "计费周期", "default": "monthly"}, "price_per_class": {"type": "integer", "title": "Price Per Class", "description": "按课时计费单价", "default": 0}, "monthly_fee": {"type": "integer", "title": "Monthly Fee", "description": "月费", "default": 0}, "setup_fee": {"type": "integer", "title": "<PERSON>up <PERSON>e", "description": "安装费", "default": 0}, "status": {"$ref": "#/components/schemas/TenantStatus", "description": "租户状态", "default": "trial"}, "subscription_expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Subscription Expires At", "description": "订阅到期时间"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "database_schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Database Schema"}, "api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Key"}, "trial_expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Trial Expires At"}}, "type": "object", "required": ["name", "code", "id", "created_at"], "title": "TenantRead", "description": "租户响应模型"}, "TenantStatus": {"type": "string", "enum": ["trial", "active", "suspended", "terminated", "expired"], "title": "TenantStatus", "description": "租户状态枚举"}, "TenantUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "display_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Display Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "contact_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Contact Name"}, "contact_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Contact Phone"}, "contact_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Contact Email"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address"}, "plan_type": {"anyOf": [{"$ref": "#/components/schemas/PlanType"}, {"type": "null"}]}, "status": {"anyOf": [{"$ref": "#/components/schemas/TenantStatus"}, {"type": "null"}]}}, "type": "object", "title": "TenantUpdate", "description": "更新租户请求模型"}, "TimeSlotConflictCheck": {"properties": {"teacher_id": {"type": "integer", "title": "Teacher Id", "description": "教师ID"}, "weekday": {"$ref": "#/components/schemas/Weekday", "description": "星期几"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "开始时间"}, "duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration Minutes", "description": "课程时长", "default": 25}, "exclude_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Exclude Id", "description": "排除的记录ID（用于更新时检测）"}}, "type": "object", "required": ["teacher_id", "weekday", "start_time"], "title": "TimeSlotConflictCheck", "description": "时间段冲突检测参数"}, "UpdateFieldRequest": {"properties": {"field_name": {"type": "string", "title": "Field Name"}, "field_value": {"anyOf": [{"type": "string"}, {"type": "integer"}, {"type": "number"}, {"type": "boolean"}], "title": "Field Value"}}, "type": "object", "required": ["field_name", "field_value"], "title": "UpdateFieldRequest"}, "UserCreate": {"properties": {"username": {"type": "string", "maxLength": 50, "minLength": 3, "title": "Username"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "password": {"type": "string", "minLength": 6, "title": "Password", "description": "密码"}, "real_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Real Name"}, "role": {"$ref": "#/components/schemas/UserRole"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}]}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday"}}, "type": "object", "required": ["username", "password", "role"], "title": "UserCreate", "description": "创建用户请求模型"}, "UserRead": {"properties": {"tenant_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Tenant Id", "description": "租户ID，super_admin为null"}, "username": {"type": "string", "maxLength": 50, "title": "Username", "description": "用户名"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "邮箱"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "手机号"}, "real_name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Real Name", "description": "真实姓名"}, "avatar_url": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Avatar Url", "description": "头像URL"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}], "description": "性别"}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday", "description": "生日"}, "role": {"$ref": "#/components/schemas/UserRole", "description": "用户角色"}, "status": {"$ref": "#/components/schemas/UserStatus", "description": "用户状态", "default": "active"}, "last_login_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login At", "description": "最后登录时间"}, "login_count": {"type": "integer", "title": "Login <PERSON>", "description": "登录次数", "default": 0}, "failed_login_attempts": {"type": "integer", "title": "Failed Login Attempts", "description": "失败登录尝试次数", "default": 0}, "locked_until": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Locked Until", "description": "锁定至"}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}}, "type": "object", "required": ["username", "role", "id", "created_at"], "title": "UserRead", "description": "用户响应模型"}, "UserRole": {"type": "string", "enum": ["super_admin", "admin", "agent"], "title": "UserRole", "description": "用户角色枚举"}, "UserStatus": {"type": "string", "enum": ["active", "inactive", "locked"], "title": "UserStatus", "description": "用户状态枚举"}, "UserUpdate": {"properties": {"email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "real_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Real Name"}, "avatar_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar Url"}, "gender": {"anyOf": [{"$ref": "#/components/schemas/Gender"}, {"type": "null"}]}, "birthday": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birthday"}, "status": {"anyOf": [{"$ref": "#/components/schemas/UserStatus"}, {"type": "null"}]}}, "type": "object", "title": "UserUpdate", "description": "更新用户请求模型"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "Weekday": {"type": "integer", "enum": [1, 2, 3, 4, 5, 6, 7], "title": "Weekday", "description": "星期枚举"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}