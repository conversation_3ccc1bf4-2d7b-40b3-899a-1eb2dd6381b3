# 测试重构与评审指南

## 🔍 测试质量评估

### 代码质量指标

- **覆盖率**: 单元测试 >90%, 集成测试 >80% （暂时不做覆盖率测试）
- **可读性**: 测试意图清晰，命名准确
- **维护性**: 测试易于修改和扩展
- **稳定性**: 测试结果可重复，不依赖外部状态
- **性能**: 单元测试 <30 秒，集成测试 <2 分钟

### 测试架构健康度检查

#### 1. 目录结构评估

```
✅ 良好的结构:
tests/
├── unit/services/          # 按层次分类
├── integration/api/        # 按功能分组
└── fixtures/business/      # 按业务域组织

❌ 需要重构的结构:
tests/
├── test_everything.py      # 单一大文件
├── test_user_stuff.py      # 命名不清晰
└── random_tests/           # 组织混乱
```

#### 2. Fixture 组织评估

```python
# ✅ 良好的Fixture设计
@pytest.fixture(scope="function")  # 适当的作用域
def sample_user(db_session, sample_tenant):
    """创建示例用户 - 清晰的文档"""
    user = User(email=f"test_{uuid4()}@example.com")  # 唯一数据
    db_session.add(user)
    db_session.commit()
    return user

# ❌ 需要重构的Fixture
@pytest.fixture(scope="session")  # 作用域过大，但实际不需要
def user():  # 命名不清晰，无文档
    return User(email="<EMAIL>")  # 可能冲突的数据
```

## 🔧 重构策略

### 1. 测试代码重复消除

#### 重复测试逻辑提取

```python
# 重构前 - 重复的验证逻辑
def test_create_user():
    result = user_service.create_user(...)
    assert result is not None
    assert result.email == "<EMAIL>"
    assert result.status == "active"
    assert result.created_at is not None

def test_update_user():
    result = user_service.update_user(...)
    assert result is not None
    assert result.email == "<EMAIL>"
    assert result.status == "active"
    assert result.created_at is not None

# 重构后 - 提取通用验证
def assert_valid_user(user, expected_email):
    """验证用户对象的通用属性"""
    assert user is not None
    assert user.email == expected_email
    assert user.status == "active"
    assert user.created_at is not None

def test_create_user():
    result = user_service.create_user(...)
    assert_valid_user(result, "<EMAIL>")

def test_update_user():
    result = user_service.update_user(...)
    assert_valid_user(result, "<EMAIL>")
```

#### 测试数据工厂化

```python
# 重构前 - 分散的测试数据创建
def test_create_user():
    user_data = UserCreate(email="<EMAIL>", real_name="测试")

def test_update_user():
    user_data = UserCreate(email="<EMAIL>", real_name="测试2")

# 重构后 - 统一的数据工厂
class UserDataFactory:
    @staticmethod
    def create_user_data(**overrides):
        defaults = {
            "email": f"test_{uuid4()}@example.com",
            "real_name": "测试用户",
            "password": "password123"
        }
        defaults.update(overrides)
        return UserCreate(**defaults)

def test_create_user():
    user_data = UserDataFactory.create_user_data()

def test_update_user():
    user_data = UserDataFactory.create_user_data(real_name="更新用户")
```

### 2. 测试组织优化

#### 测试类重构

```python
# 重构前 - 平铺的测试函数
def test_create_user_success():
    pass

def test_create_user_duplicate_email():
    pass

def test_get_user_success():
    pass

def test_get_user_not_found():
    pass

# 重构后 - 按功能分组
class TestUserCreation:
    """用户创建相关测试"""

    def test_with_valid_data_should_return_user(self):
        pass

    def test_with_duplicate_email_should_raise_error(self):
        pass

class TestUserRetrieval:
    """用户查询相关测试"""

    def test_with_valid_id_should_return_user(self):
        pass

    def test_with_invalid_id_should_return_none(self):
        pass
```

### 3. 性能优化

#### 数据库操作优化

```python
# 重构前 - 每个测试都创建数据
def test_user_operations():
    tenant = create_tenant()  # 数据库操作
    user1 = create_user(tenant)  # 数据库操作
    user2 = create_user(tenant)  # 数据库操作
    # ... 测试逻辑

# 重构后 - 批量创建，事务回滚
@pytest.fixture
def test_data(db_session):
    """批量创建测试数据"""
    tenant = Tenant(...)
    users = [User(...) for _ in range(5)]

    db_session.add(tenant)
    db_session.add_all(users)
    db_session.commit()

    return {"tenant": tenant, "users": users}

def test_user_operations(test_data):
    # 直接使用预创建的数据
    tenant = test_data["tenant"]
    users = test_data["users"]
```

## 📊 测试评审清单

### 代码质量检查

#### 1. 命名规范

- [ ] 测试文件命名: `test_*.py`
- [ ] 测试类命名: `Test*` 且描述清晰
- [ ] 测试方法命名: `test_*` 且遵循模式
- [ ] Fixture 命名: 描述性且一致

#### 2. 测试结构

- [ ] 使用 AAA 模式 (Arrange-Act-Assert)
- [ ] 每个测试只验证一个行为
- [ ] 测试独立，不依赖执行顺序
- [ ] 适当的测试文档和注释

#### 3. 断言质量

- [ ] 断言具体且明确
- [ ] 包含失败时的错误信息
- [ ] 验证正确的属性和值
- [ ] 避免过度断言

### 架构设计检查

#### 1. 测试分层

- [ ] 单元测试专注业务逻辑
- [ ] 集成测试验证模块协作
- [ ] 端到端测试覆盖用户场景
- [ ] 测试类型职责清晰

#### 2. 依赖管理

- [ ] Fixture 依赖关系清晰
- [ ] 避免循环依赖
- [ ] 适当的作用域设置
- [ ] 资源正确清理

#### 3. 数据管理

- [ ] 测试数据隔离
- [ ] 使用工厂模式创建数据
- [ ] 避免硬编码值
- [ ] 事务正确回滚

### 维护性检查

#### 1. 可读性

- [ ] 测试意图清晰
- [ ] 代码简洁明了
- [ ] 适当的抽象层次
- [ ] 一致的编码风格

#### 2. 可维护性

- [ ] 测试易于修改
- [ ] 重复代码已提取
- [ ] 测试数据易于管理
- [ ] 错误信息有用

## 🔄 重构执行计划

### 阶段 1: 评估现状 (1-2 天)

1. **测试覆盖率分析**

   ```bash
   python scripts/test.py unit --cov=app --cov-report=html
   ```

2. **代码质量检查**

   - 运行 linting 工具
   - 检查测试命名一致性
   - 识别重复代码

3. **性能基准测试**
   ```bash
   time python scripts/test.py unit
   time python scripts/test.py api
   ```

### 阶段 2: 架构重构 (3-5 天)

1. **目录结构调整**

   - 按功能重新组织测试文件
   - 拆分大型测试文件
   - 创建合理的 fixture 层次

2. **Fixture 重构**
   - 合并重复的 fixture
   - 优化 fixture 作用域
   - 提取业务数据工厂

### 阶段 3: 代码优化 (2-3 天)

1. **消除重复代码**

   - 提取通用验证逻辑
   - 统一测试数据创建
   - 标准化错误处理

2. **性能优化**
   - 优化数据库操作
   - 并行测试执行
   - 减少不必要的依赖

### 阶段 4: 质量验证 (1 天)

1. **全量测试验证**

   ```bash
   python scripts/test.py all
   ```

2. **性能对比**
   - 重构前后执行时间对比
   - 覆盖率对比
   - 维护成本评估

## 📈 持续改进

### 定期评审 (每月)

- [ ] 测试覆盖率趋势分析
- [ ] 测试执行时间监控
- [ ] 失败测试模式分析
- [ ] 新增测试质量检查

### 工具支持（暂时不做）

```bash
# 覆盖率监控
pytest --cov=app --cov-fail-under=90

# 性能监控
pytest --benchmark-only

# 代码质量
flake8 tests/
black tests/
```

### 团队规范

- 新功能要包含测试（特殊性功能课不测试，需要和我确认）
