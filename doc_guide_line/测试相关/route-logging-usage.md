# 路由日志记录功能使用说明

## 概述

路由日志记录功能允许你在开发和测试过程中查看详细的路由匹配信息，包括请求路径、匹配的路由模式、路由函数名、HTTP 方法和响应状态码。

## 功能特性

- 🔍 显示详细的路由匹配信息
- 📊 记录请求路径、路由模式、函数名等
- 🎛️ 可通过环境变量灵活控制启用/禁用
- 🧪 与测试框架集成，支持测试时启用

## 使用方法

### 1. 通过环境变量控制

#### 启用路由日志记录

```bash
export ENABLE_ROUTE_LOGGING=true
python -m uvicorn app.main:app --reload
```

#### 禁用路由日志记录（默认）

```bash
export ENABLE_ROUTE_LOGGING=false
python -m uvicorn app.main:app --reload
```

### 2. 在测试中使用

#### 方法一：通过测试脚本参数

```bash
# 启用路由日志记录运行测试
python scripts/test_debug.py tests/some_test.py --enable-route-logging

# 同时启用多个调试选项
python scripts/test_debug.py tests/some_test.py --enable-route-logging --keep-test-data
```

#### 方法二：直接使用 pytest 参数

```bash
# 启用路由日志记录
pytest tests/some_test.py --enable-route-logging

# 组合多个参数
pytest tests/some_test.py --enable-route-logging --keep-test-data --debug-rls -v -s
```

### 3. 在代码中动态检查

```python
from app.core.config import settings

if settings.ENABLE_ROUTE_LOGGING:
    print("路由日志记录已启用")
else:
    print("路由日志记录已禁用")
```

## 输出示例

当启用路由日志记录时，每个请求都会输出类似以下的信息：

```
请求路径: /api/v1/users
匹配的路由模式: /api/v1/users
路由函数名: get_users
HTTP方法: GET
响应状态码: 200
--------------------------------------------------
```

## 配置说明

### 环境变量

| 变量名                 | 默认值  | 说明                 |
| ---------------------- | ------- | -------------------- |
| `ENABLE_ROUTE_LOGGING` | `false` | 是否启用路由日志记录 |

### 配置文件

路由日志记录的配置位于 `app/core/config.py` 中：

```python
@property
def ENABLE_ROUTE_LOGGING(self) -> bool:
    """动态读取路由日志记录配置"""
    return config("ENABLE_ROUTE_LOGGING", default=False, cast=bool)
```

## 测试脚本参数

### test_debug.py 新增参数

```bash
python scripts/test_debug.py --help
```

新增的参数：

- `--enable-route-logging`: 启用路由日志记录

### pytest 新增参数

```bash
pytest --help
```

新增的参数：

- `--enable-route-logging`: 启用路由日志记录

## 使用场景

### 1. 开发调试

在开发过程中，当你需要了解请求是如何被路由匹配的时候：

```bash
export ENABLE_ROUTE_LOGGING=true
python -m uvicorn app.main:app --reload
```

### 2. 测试调试

在运行测试时，想要查看测试请求的路由匹配情况：

```bash
python scripts/test_debug.py tests/integration/api/v1/admin/test_users.py --enable-route-logging
```

### 3. 问题排查

当 API 请求出现问题时，启用路由日志记录可以帮助你确认：

- 请求是否正确匹配到预期的路由
- 路由函数是否正确
- 响应状态码是否符合预期

## 性能考虑

- 路由日志记录会增加少量的性能开销
- 默认情况下是禁用的，不会影响生产环境性能
- 仅在需要调试时启用

## 注意事项

1. **生产环境**: 建议在生产环境中保持禁用状态
2. **测试环境**: 可以根据需要灵活启用/禁用
3. **日志量**: 启用后会产生大量日志输出，请注意日志管理
4. **性能**: 虽然开销很小，但在高并发场景下仍需考虑

## 故障排除

### 问题：设置了环境变量但没有生效

确保环境变量设置正确：

```bash
echo $ENABLE_ROUTE_LOGGING
```

### 问题：在测试中看不到路由日志

确保测试脚本传递了正确的参数：

```bash
python scripts/test_debug.py tests/your_test.py --enable-route-logging -v
```

### 问题：日志输出太多

可以结合其他过滤选项使用：

```bash
python scripts/test_debug.py tests/your_test.py --enable-route-logging --no-filter
```

## 扩展功能

如果需要更详细的日志记录，可以在 `app/main.py` 的 `log_route` 中间件中添加更多信息：

```python
# 添加请求头信息
print(f"请求头: {dict(request.headers)}")

# 添加查询参数
print(f"查询参数: {dict(request.query_params)}")

# 添加响应时间
import time
start_time = time.time()
response = await call_next(request)
duration = time.time() - start_time
print(f"响应时间: {duration:.3f}s")
```
