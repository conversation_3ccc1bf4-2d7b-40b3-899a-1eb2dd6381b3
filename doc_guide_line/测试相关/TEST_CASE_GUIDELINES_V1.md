# 测试用例编写规范

## 总体原则

### 1. 测试金字塔原则

- **单元测试 (70%)**：快速、隔离、专注业务逻辑
- **集成测试 (20%)**：验证模块间协作
- **端到端测试 (10%)**：验证完整业务流程

### 2. FIRST 原则

- **Fast**: 测试运行快速
- **Independent**: 测试之间相互独立
- **Repeatable**: 测试结果可重复
- **Self-Validating**: 测试结果明确（通过/失败）
- **Timely**: 测试及时编写

## 命名规范

### 文件命名

```
test_{module_name}.py          # 单元测试
test_{module_name}_api.py      # API集成测试
test_{scenario_name}.py        # 端到端测试
```

### 测试类命名

```python
class Test{ClassName}:         # 对应被测试的类
class Test{FeatureName}:       # 对应特定功能
```

### 测试方法命名

```python
def test_{action}_{condition}_{expected_result}():
    """测试描述"""
    pass

# 示例
def test_create_user_with_valid_data_should_return_user():
def test_create_user_with_duplicate_email_should_raise_error():
def test_get_user_with_invalid_id_should_return_none():
```

## 单元测试规范

### 1. 测试结构 (AAA 模式)

```python
def test_create_user_with_valid_data_should_return_user():
    """测试使用有效数据创建用户应该返回用户对象"""
    # Arrange - 准备测试数据
    tenant_id = "test-tenant"
    user_data = UserCreate(
        email="<EMAIL>",
        real_name="测试用户",
        password="password123"
    )

    # Act - 执行被测试的操作
    result = user_service.create_user(
        db=db_session,
        user_create=user_data,
        tenant_id=tenant_id
    )

    # Assert - 验证结果
    assert result is not None
    assert result.email == user_data.email
    assert result.real_name == user_data.real_name
    assert result.tenant_id == tenant_id
```

### 2. 测试覆盖范围

每个服务类应包含以下测试用例：

```python
class TestUserService:
    """用户服务测试类"""

    # 创建操作测试
    def test_create_user_with_valid_data_should_return_user(self):
    def test_create_user_with_duplicate_email_should_raise_error(self):
    def test_create_user_with_invalid_tenant_should_raise_error(self):

    # 查询操作测试
    def test_get_user_by_id_with_valid_id_should_return_user(self):
    def test_get_user_by_id_with_invalid_id_should_return_none(self):
    def test_get_user_by_email_with_valid_email_should_return_user(self):

    # 更新操作测试
    def test_update_user_with_valid_data_should_return_updated_user(self):
    def test_update_user_with_invalid_id_should_raise_error(self):

    # 删除操作测试
    def test_delete_user_with_valid_id_should_return_true(self):
    def test_delete_user_with_invalid_id_should_raise_error(self):

    # 业务逻辑测试
    def test_verify_password_with_correct_password_should_return_true(self):
    def test_verify_password_with_wrong_password_should_return_false(self):
```

### 3. 数据准备

仅供参考

```python
@pytest.fixture
def sample_user_data():
    """示例用户数据"""
    return UserCreate(
        email="<EMAIL>",
        real_name="测试用户",
        password="password123"
    )

@pytest.fixture
def created_user(db_session, sample_tenant, sample_user_data):
    """已创建的用户"""
    return user_service.create_user(
        db=db_session,
        user_create=sample_user_data,
        tenant_id=sample_tenant.id
    )
```

## API 集成测试规范

### 1. 测试结构

```python
def test_create_user_api_with_valid_data_should_return_201():
    """测试创建用户API应该返回201状态码"""
    # Arrange
    tenant_id = "test-tenant"
    user_data = {
        "email": "<EMAIL>",
        "real_name": "测试用户",
        "password": "password123"
    }

    # Act
    response = client.post(
        "/api/v1/users/",
        json=user_data,
        params={"tenant_id": tenant_id}
    )

    # Assert
    assert response.status_code == 201
    data = response.json()
    assert data["email"] == user_data["email"]
    assert data["real_name"] == user_data["real_name"]
    assert "password" not in data  # 确保密码不被返回
```

### 2. API 测试覆盖范围

每个 API 端点应包含以下测试：

```python
class TestUserAPI:
    """用户API测试类"""

    # 成功场景
    def test_create_user_with_valid_data_should_return_201(self):
    def test_get_user_with_valid_id_should_return_200(self):
    def test_update_user_with_valid_data_should_return_200(self):
    def test_delete_user_with_valid_id_should_return_200(self):

    # 错误场景
    def test_create_user_with_invalid_email_should_return_422(self):
    def test_get_user_with_invalid_id_should_return_404(self):
    def test_update_user_with_invalid_data_should_return_422(self):

    # 边界场景
    def test_create_user_with_duplicate_email_should_return_400(self):
    def test_get_users_with_pagination_should_return_correct_data(self):
```

### 3. 响应验证

```python
def test_get_users_should_return_correct_format():
    """测试获取用户列表应该返回正确格式"""
    response = client.get("/api/v1/users/", params={"tenant_id": "test"})

    assert response.status_code == 200
    data = response.json()

    # 验证响应结构
    assert "items" in data
    assert "total" in data
    assert "page" in data
    assert "size" in data

    # 验证用户对象结构
    if data["items"]:
        user = data["items"][0]
        required_fields = ["id", "email", "real_name", "created_at"]
        for field in required_fields:
            assert field in user
```

## 端到端测试规范

### 1. 业务场景测试

```python
def test_complete_user_lifecycle():
    """测试完整的用户生命周期"""
    tenant_id = "test-tenant"

    # 1. 创建用户
    user_data = {
        "email": "<EMAIL>",
        "real_name": "生命周期测试",
        "password": "password123"
    }

    create_response = client.post(
        "/api/v1/users/",
        json=user_data,
        params={"tenant_id": tenant_id}
    )
    assert create_response.status_code == 201
    user_id = create_response.json()["id"]

    # 2. 获取用户
    get_response = client.get(
        f"/api/v1/users/{user_id}",
        params={"tenant_id": tenant_id}
    )
    assert get_response.status_code == 200

    # 3. 更新用户
    update_data = {"real_name": "更新后的名称"}
    update_response = client.put(
        f"/api/v1/users/{user_id}",
        json=update_data,
        params={"tenant_id": tenant_id}
    )
    assert update_response.status_code == 200

    # 4. 删除用户
    delete_response = client.delete(
        f"/api/v1/users/{user_id}",
        params={"tenant_id": tenant_id}
    )
    assert delete_response.status_code == 200
```

## 错误处理测试

### 1. 异常测试

```python
def test_create_user_with_duplicate_email_should_raise_http_exception():
    """测试创建重复邮箱用户应该抛出HTTP异常"""
    with pytest.raises(HTTPException) as exc_info:
        user_service.create_user(db, duplicate_user_data, tenant_id)

    assert exc_info.value.status_code == 400
    assert "邮箱已存在" in str(exc_info.value.detail)
```

### 2. 验证错误测试

```python
def test_create_user_with_invalid_email_should_return_422():
    """测试使用无效邮箱创建用户应该返回422"""
    invalid_data = {
        "email": "invalid-email",  # 无效邮箱格式
        "real_name": "测试用户",
        "password": "password123"
    }

    response = client.post("/api/v1/users/", json=invalid_data)

    assert response.status_code == 422
    error_detail = response.json()["detail"]
    assert any("email" in str(error).lower() for error in error_detail)
```

## 性能测试规范

### 1. 响应时间测试

```python
import time

def test_get_users_response_time_should_be_acceptable():
    """测试获取用户列表的响应时间应该在可接受范围内"""
    start_time = time.time()

    response = client.get("/api/v1/users/")

    end_time = time.time()
    response_time = end_time - start_time

    assert response.status_code == 200
    assert response_time < 1.0  # 响应时间应小于1秒
```

## 数据隔离和清理

### 1. 使用事务回滚

```python
@pytest.fixture
def db_session():
    """数据库会话fixture，自动回滚事务"""
    connection = engine.connect()
    transaction = connection.begin()
    session = Session(bind=connection)

    yield session

    session.close()
    transaction.rollback()
    connection.close()
```

### 2. 租户隔离

```python
@pytest.fixture
def sample_tenant(db_session):
    """示例租户fixture"""
    tenant = Tenant(
        id="test-tenant",
        name="测试租户",
        subdomain="test"
    )
    db_session.add(tenant)
    db_session.commit()
    return tenant
```



## 测试数据管理

### 1. 使用工厂模式

```python
class UserFactory:
    """用户数据工厂"""

    @staticmethod
    def create_user_data(**kwargs):
        """创建用户数据"""
        default_data = {
            "email": "<EMAIL>",
            "real_name": "测试用户",
            "password": "password123"
        }
        default_data.update(kwargs)
        return UserCreate(**default_data)

    @staticmethod
    def create_multiple_users(count=5, **kwargs):
        """创建多个用户数据"""
        return [
            UserFactory.create_user_data(
                email=f"user{i}@example.com",
                real_name=f"用户{i}",
                **kwargs
            )
            for i in range(count)
        ]
```

## 断言最佳实践

### 1. 具体明确的断言

```python
# 好的断言
assert response.status_code == 201
assert user.email == "<EMAIL>"
assert len(users) == 5

# 避免的断言
assert response.status_code  # 不够具体
assert user  # 不够明确
assert users  # 没有验证具体内容
```

### 2. 自定义断言消息

```python
def test_user_creation():
    user = create_user(user_data)

    assert user is not None, "用户创建失败，返回了None"
    assert user.email == expected_email, f"期望邮箱: {expected_email}, 实际邮箱: {user.email}"
```

## 测试文档

### 1. 测试方法文档

```python
def test_create_user_with_valid_data_should_return_user():
    """
    测试使用有效数据创建用户

    Given: 有效的用户数据和租户ID
    When: 调用create_user方法
    Then: 应该返回创建的用户对象，包含正确的属性
    """
    pass
```

### 2. 测试类文档

```python
class TestUserService:
    """
    用户服务测试类

    测试范围：
    - 用户CRUD操作
    - 密码验证
    - 用户状态管理
    - 错误处理

    依赖：
    - 数据库会话
    - 租户数据
    """
    pass
```

## 持续改进

### 1. 代码覆盖率

- 目标：单元测试覆盖率 > 90%
- 工具：pytest-cov
- 命令：`pytest --cov=app --cov-report=html`

### 2. 测试质量指标

- 测试运行时间 < 30 秒（单元测试）
- 测试通过率 = 100%
- 测试维护成本低
- 测试可读性强

### 3. 定期重构

- 删除重复的测试代码
- 优化测试数据准备
- 更新过时的测试用例
- 改进测试组织结构
