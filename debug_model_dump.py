#!/usr/bin/env python3
"""
调试 model_dump() 行为的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, time
from app.features.teachers.fixed_slots_models import TeacherFixedSlot, Weekday
from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotResponse
from app.features.teachers.fixed_slots_models import get_weekday_name

def test_model_dump():
    """测试 model_dump() 的行为"""

    # 创建一个 TeacherFixedSlot 实例
    slot = TeacherFixedSlot(
        id=1,
        tenant_id=1,
        teacher_id=1,
        weekday=Weekday.MONDAY,
        start_time=time(8, 30),
        duration_minutes=25,
        is_available=True,
        is_visible_to_members=True,
        created_by=1,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )

    # 模拟从数据库加载的对象（可能有不同的内部状态）
    slot_from_db = TeacherFixedSlot.model_validate({
        'id': 1,
        'tenant_id': 1,
        'teacher_id': 1,
        'weekday': Weekday.MONDAY,
        'start_time': time(8, 30),
        'duration_minutes': 25,
        'is_available': True,
        'is_visible_to_members': True,
        'created_by': 1,
        'created_at': datetime.now(),
        'updated_at': datetime.now()
    })
    
    print("=== TeacherFixedSlot 实例 ===")
    print(f"slot.id: {slot.id}")
    print(f"slot.teacher_id: {slot.teacher_id}")
    print(f"slot.weekday: {slot.weekday}")
    print(f"slot.start_time: {slot.start_time}")
    print()

    print("=== 从数据库模拟的 TeacherFixedSlot 实例 ===")
    print(f"slot_from_db.id: {slot_from_db.id}")
    print(f"slot_from_db.teacher_id: {slot_from_db.teacher_id}")
    print(f"slot_from_db.weekday: {slot_from_db.weekday}")
    print(f"slot_from_db.start_time: {slot_from_db.start_time}")
    print()
    
    print("=== model_dump() 输出 ===")
    dump_result = slot.model_dump()
    print(f"type: {type(dump_result)}")
    print(f"keys: {list(dump_result.keys())}")
    print(f"content: {dump_result}")
    print()
    
    print("=== model_dump(exclude_unset=True) 输出 ===")
    dump_exclude_unset = slot.model_dump(exclude_unset=True)
    print(f"content: {dump_exclude_unset}")
    print()
    
    print("=== model_dump(exclude_none=True) 输出 ===")
    dump_exclude_none = slot.model_dump(exclude_none=True)
    print(f"content: {dump_exclude_none}")
    print()
    
    print("=== 尝试构建 TeacherFixedSlotResponse (普通实例) ===")
    try:
        # 尝试使用 **slot.model_dump()
        response_data = TeacherFixedSlotResponse(
            **slot.model_dump(),
            weekday_name=get_weekday_name(slot.weekday)
        )
        print("✅ 成功构建 TeacherFixedSlotResponse")
        print(f"response_data: {response_data}")
    except Exception as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误类型: {type(e)}")

    print()
    print("=== 尝试构建 TeacherFixedSlotResponse (模拟数据库实例) ===")
    try:
        # 尝试使用 **slot_from_db.model_dump()
        response_data = TeacherFixedSlotResponse(
            **slot_from_db.model_dump(),
            weekday_name=get_weekday_name(slot_from_db.weekday)
        )
        print("✅ 成功构建 TeacherFixedSlotResponse")
        print(f"response_data: {response_data}")
    except Exception as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误类型: {type(e)}")

    print()
    print("=== 测试参数传递顺序问题 ===")
    try:
        # 测试参数顺序是否有影响
        dump_data = slot.model_dump()
        weekday_name = get_weekday_name(slot.weekday)

        print(f"dump_data keys: {list(dump_data.keys())}")
        print(f"weekday_name: {weekday_name}")

        # 先传递 weekday_name，再展开 dump_data
        response_data = TeacherFixedSlotResponse(
            weekday_name=weekday_name,
            **dump_data
        )
        print("✅ 成功构建 TeacherFixedSlotResponse (先传weekday_name)")
    except Exception as e:
        print(f"❌ 构建失败: {e}")

    print()
    print("=== 测试关键字参数覆盖问题 ===")
    try:
        # 测试是否存在关键字参数覆盖问题
        dump_data = slot.model_dump()
        print(f"原始dump_data包含tenant_id: {'tenant_id' in dump_data}")

        # 模拟可能的问题：如果weekday_name以某种方式覆盖了整个字典
        response_data = TeacherFixedSlotResponse(
            **dump_data,
            weekday_name=get_weekday_name(slot.weekday)
        )
        print("✅ 成功构建 TeacherFixedSlotResponse (正常顺序)")

        # 测试是否TeacherFixedSlotResponse不接受tenant_id
        print(f"TeacherFixedSlotResponse是否有tenant_id字段: {hasattr(TeacherFixedSlotResponse, 'tenant_id')}")

    except Exception as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误类型: {type(e)}")

    print()
    print("=== 测试去除tenant_id后的构建 ===")
    try:
        # 去除TeacherFixedSlotResponse不需要的字段
        dump_data = slot.model_dump()
        # 移除tenant_id，因为TeacherFixedSlotResponse可能不包含这个字段
        if 'tenant_id' in dump_data:
            del dump_data['tenant_id']

        response_data = TeacherFixedSlotResponse(
            **dump_data,
            weekday_name=get_weekday_name(slot.weekday)
        )
        print("✅ 成功构建 TeacherFixedSlotResponse (去除tenant_id)")

    except Exception as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误类型: {type(e)}")

    print()
    print("=== 重现原始问题：模拟API中的确切语法 ===")
    try:
        # 这是原始API代码中的确切语法
        response_data = TeacherFixedSlotResponse(
            **slot.model_dump(),
            weekday_name=get_weekday_name(slot.weekday)
        )
        print("✅ 成功构建 TeacherFixedSlotResponse (原始语法)")

    except Exception as e:
        print(f"❌ 构建失败 (原始语法): {e}")
        print(f"错误类型: {type(e)}")

    print()
    print("=== 测试空字典+weekday_name的情况 ===")
    try:
        # 模拟如果model_dump()返回空字典的情况
        response_data = TeacherFixedSlotResponse(
            **{},
            weekday_name=get_weekday_name(slot.weekday)
        )
        print("✅ 成功构建 TeacherFixedSlotResponse (空字典)")

    except Exception as e:
        print(f"❌ 构建失败 (空字典): {e}")
        print("这个错误和测试中的错误一样！")

    print()
    print("=== 测试None对象的model_dump ===")
    try:
        # 模拟如果slot是None的情况
        none_slot = None
        response_data = TeacherFixedSlotResponse(
            **none_slot.model_dump(),
            weekday_name="星期一"
        )
        print("✅ 成功构建 TeacherFixedSlotResponse (None对象)")

    except Exception as e:
        print(f"❌ 构建失败 (None对象): {e}")
        print(f"错误类型: {type(e)}")

    print()
    print("=== 尝试只传递 weekday_name ===")
    try:
        # 模拟错误情况
        response_data = TeacherFixedSlotResponse(
            weekday_name=get_weekday_name(slot.weekday)
        )
        print("✅ 成功构建 TeacherFixedSlotResponse")
    except Exception as e:
        print(f"❌ 构建失败: {e}")
        print("这个错误和测试中的错误一样！")

if __name__ == "__main__":
    test_model_dump()
