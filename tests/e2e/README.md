# E2E测试框架

## 📋 概述

这是一个简洁高效的端到端(E2E)测试框架，专门用于验证核心业务流程的完整性。

## 🏗️ 架构设计

### 设计原则
- **简洁性**：直接使用pytest fixtures，无复杂的自定义基类
- **独立性**：每个测试文件完全独立，可单独运行
- **可靠性**：基于成熟的pytest测试框架和现有fixtures
- **专注性**：专注于核心业务流程验证

### 目录结构
```
tests/e2e/
├── __init__.py
├── README.md                           # 本文档
├── run_core_business_tests.py          # 综合测试运行脚本
└── scenarios/                          # 测试场景目录
    ├── __init__.py
    ├── test_direct_booking_flow.py     # 直接约课流程测试
    ├── test_fixed_slot_flow.py         # 固定位管理流程测试
    └── test_fixed_schedule_flow.py     # 固定课排课流程测试
```

## 🧪 测试场景

### 1. 直接约课流程测试 (`test_direct_booking_flow.py`)
验证完整的直接约课业务流程：
- 教师创建 → 会员创建 → 会员卡充值
- 教师开放课表 → 会员预约课程
- 扣费验证 → 状态更新验证

### 2. 固定位管理流程测试 (`test_fixed_slot_flow.py`)
验证固定位管理的核心功能：
- 教师开放固定位 → 会员锁定固定位
- 状态管理 → 数据一致性验证

### 3. 固定课排课流程测试 (`test_fixed_schedule_flow.py`)
验证固定课排课系统的完整功能：
- 完整排课流程：任务创建 → 执行排课 → 结果验证
- 异常处理：余额不足 → 时间冲突 → 重试机制
- 统计和日志：任务统计 → 日志查询 → 数据一致性

## 🚀 运行测试

### 运行单个测试
```bash
# 直接约课流程测试
python -m pytest tests/e2e/scenarios/test_direct_booking_flow.py -v -s

# 固定位管理流程测试
python -m pytest tests/e2e/scenarios/test_fixed_slot_flow.py -v -s

# 固定课排课流程测试
python -m pytest tests/e2e/scenarios/test_fixed_schedule_flow.py -v -s
```

### 运行所有核心业务流程测试
```bash
# 使用综合测试脚本
python tests/e2e/run_core_business_tests.py

# 或使用pytest直接运行
python -m pytest tests/e2e/scenarios/ -v -s -m e2e
```

## 🔧 技术实现

### Fixtures依赖
测试直接使用项目中现有的pytest fixtures：
- `client`: FastAPI测试客户端
- `created_tenant`: 测试租户
- `created_admin_user`: 测试管理员用户
- `admin_token`: 管理员认证令牌

### 数据库管理
- 每个测试使用独立的测试数据库
- 自动创建和清理测试数据
- 支持多租户RLS隔离

### 认证机制
- 使用标准的JWT认证
- 基于现有的认证API接口
- 无自定义认证逻辑

## ✅ 验证内容

### 业务流程完整性
- 端到端业务场景验证
- 多个API接口的集成验证
- 数据流转的正确性验证

### 数据一致性
- 数据库状态验证
- 业务状态同步验证
- 金额计算准确性验证

### 多租户支持
- RLS隔离验证
- 租户上下文正确性
- 数据安全性验证

## 📊 测试报告

运行综合测试脚本会生成详细的测试报告：
- 每个测试的执行状态
- 详细的执行时间统计
- 失败测试的错误信息
- 整体测试覆盖情况

## 🔍 故障排除

### 常见问题
1. **数据库连接问题**：检查测试数据库配置
2. **认证失败**：验证JWT令牌生成逻辑
3. **API路径错误**：确认API路由配置正确

### 调试技巧
- 使用 `-s` 参数查看详细输出
- 检查测试日志中的HTTP请求响应
- 验证数据库RLS设置

## 🎯 最佳实践

1. **保持简洁**：避免过度复杂的测试逻辑
2. **独立运行**：确保每个测试可以独立执行
3. **清晰命名**：使用描述性的测试方法名
4. **适度验证**：专注于核心业务逻辑验证
5. **及时更新**：随业务变化及时更新测试

## 📝 维护指南

### 添加新测试
1. 在 `scenarios/` 目录下创建新的测试文件
2. 使用 `@pytest.mark.e2e` 标记
3. 直接使用现有的pytest fixtures
4. 更新 `run_core_business_tests.py` 中的测试列表

### 修改现有测试
1. 保持测试的独立性
2. 确保修改后测试仍能通过
3. 更新相关文档

这个E2E测试框架为系统的核心业务功能提供了可靠的质量保障。
