#!/usr/bin/env python3
"""
核心业务流程E2E测试运行脚本

运行所有核心业务场景的端到端测试，包括：
1. 直接约课流程测试
2. 固定位管理流程测试
3. 重构后的手动测试场景

使用方法：
    python tests/e2e/run_core_business_tests.py
    
或者使用pytest直接运行：
    python -m pytest tests/e2e/scenarios/ -v -s -m e2e
"""

import subprocess
import sys
import time
from pathlib import Path


def run_test_suite(test_path: str, description: str) -> bool:
    """运行测试套件"""
    print(f"\n{'='*60}")
    print(f"🚀 开始运行: {description}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            test_path, 
            "-v", "-s", 
            "--tb=short"
        ], capture_output=False, text=True)
        
        duration = time.time() - start_time
        
        if result.returncode == 0:
            print(f"\n✅ {description} - 测试通过 (耗时: {duration:.2f}秒)")
            return True
        else:
            print(f"\n❌ {description} - 测试失败 (耗时: {duration:.2f}秒)")
            return False
            
    except Exception as e:
        duration = time.time() - start_time
        print(f"\n💥 {description} - 运行异常: {e} (耗时: {duration:.2f}秒)")
        return False


def main():
    """主函数"""
    print("🎯 核心业务流程E2E测试套件")
    print("=" * 60)
    
    # 检查当前工作目录
    current_dir = Path.cwd()
    if not (current_dir / "tests" / "e2e").exists():
        print("❌ 错误: 请在项目根目录下运行此脚本")
        sys.exit(1)
    
    # 测试套件配置
    test_suites = [
        # 8.1.1 直接约课流程测试
        {
            "path": "tests/e2e/scenarios/test_direct_booking_flow.py::TestDirectBookingFlow::test_teacher_open_schedule_member_booking_flow",
            "description": "教师直接开放课表 → 会员预约 → 扣费完成"
        },
        {
            "path": "tests/e2e/scenarios/test_direct_booking_flow.py::TestDirectBookingFlow::test_admin_create_course_member_booking_flow",
            "description": "管理员生成课程 → 会员预约 → 扣费完成"
        },
        # 8.1.2 固定位管理流程测试
        {
            "path": "tests/e2e/scenarios/test_fixed_slot_flow.py::TestFixedSlotFlow::test_teacher_open_fixed_slot_member_lock_flow",
            "description": "教师开放固定位 → 会员锁定固定位"
        },
        {
            "path": "tests/e2e/scenarios/test_fixed_slot_flow.py::TestFixedSlotFlow::test_teacher_open_multiple_fixed_slots_member_batch_lock_flow",
            "description": "教师开放多个固定位 → 会员批量锁定固定位"
        },
        {
            "path": "tests/e2e/scenarios/test_fixed_slot_flow.py::TestFixedSlotFlow::test_teacher_close_fixed_slot_lock_invalid_flow",
            "description": "教师关闭固定位 → 会员锁定失效处理"
        },
        {
            "path": "tests/e2e/scenarios/test_fixed_slot_flow.py::TestFixedSlotFlow::test_member_cancel_lock_another_member_lock_flow",
            "description": "会员1取消固定位锁定 → 位置释放 → 会员2锁定该固定位"
        },
        # 8.1.3 固定课排课流程测试
        {
            "path": "tests/e2e/scenarios/test_fixed_schedule_flow.py::TestFixedScheduleFlow::test_complete_fixed_schedule_flow",
            "description": "完整固定课排课流程：创建任务 → 执行排课 → 验证结果"
        },
        {
            "path": "tests/e2e/scenarios/test_fixed_schedule_flow.py::TestFixedScheduleFlow::test_fixed_schedule_with_insufficient_balance",
            "description": "固定课排课余额不足处理：余额不足 → 跳过处理"
        },
        {
            "path": "tests/e2e/scenarios/test_fixed_schedule_flow.py::TestFixedScheduleFlow::test_fixed_schedule_statistics_and_logs",
            "description": "固定课排课统计和日志：任务统计 → 日志查询"
        },
        {
            "path": "tests/e2e/scenarios/test_fixed_schedule_flow.py::TestFixedScheduleFlow::test_large_scale_fixed_schedule_flow",
            "description": "大规模固定课排课流程：10个教师 → 20个会员 → 数百节课程排课"
        }
    ]
    
    # 运行测试
    total_tests = len(test_suites)
    passed_tests = 0
    failed_tests = []
    
    overall_start_time = time.time()
    
    for i, suite in enumerate(test_suites, 1):
        print(f"\n📋 进度: {i}/{total_tests}")
        
        if run_test_suite(suite["path"], suite["description"]):
            passed_tests += 1
        else:
            failed_tests.append(suite["description"])
    
    # 总结报告
    overall_duration = time.time() - overall_start_time
    
    print(f"\n{'='*60}")
    print("📊 E2E测试总结报告")
    print(f"{'='*60}")
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {len(failed_tests)}")
    print(f"总耗时: {overall_duration:.2f}秒")
    
    if failed_tests:
        print(f"\n❌ 失败的测试:")
        for test in failed_tests:
            print(f"  - {test}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有核心业务流程E2E测试通过！")
        print("✅ 系统核心功能验证完成")
        sys.exit(0)
    else:
        print(f"\n⚠️  部分测试失败，请检查上述失败的测试")
        sys.exit(1)


if __name__ == "__main__":
    main()
