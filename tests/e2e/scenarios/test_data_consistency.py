"""
8.2 跨模块数据一致性测试

测试多租户数据隔离和并发操作数据一致性
"""

import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from fastapi.testclient import TestClient
import pytest


class TestDataConsistency:
    """跨模块数据一致性测试"""

    def test_multi_tenant_data_isolation(
        self, 
        client: TestClient, 
        created_tenant, 
        created_admin_user, 
        admin_token,
        created_second_tenant_admin,
        second_tenant_admin_token
    ):
        """测试多租户数据隔离验证"""
        print("\n🎯 测试多租户数据隔离验证")
        
        # 第一个租户的headers
        headers_tenant1 = {"Authorization": f"Bearer {admin_token}"}
        # 第二个租户的headers
        headers_tenant2 = {"Authorization": f"Bearer {second_tenant_admin_token}"}
        
        # 步骤1: 创建第一个租户的数据
        print("📋 步骤1: 创建第一个租户的数据")
        
        # 创建第一个租户的教师
        teacher_data_1 = {
            "name": f"租户1教师_{int(time.time())}",
            "email": f"tenant1_teacher_{int(time.time())}@test.com",
            "phone": f"139{int(time.time()) % 100000000:08d}",
            "specialties": ["英语口语"],
            "introduction": "租户1专属教师",
            "price_per_class": 200,
            "teacher_category": "european",
            "region": "europe"
        }
        
        response = client.post("/api/v1/admin/teachers/", json=teacher_data_1, headers=headers_tenant1)
        assert response.status_code == 201, f"创建租户1教师失败: {response.json()}"
        tenant1_teacher = response.json()["data"]
        print(f"✅ 租户1教师创建成功: {tenant1_teacher['name']} (ID: {tenant1_teacher['id']})")
        
        # 创建第一个租户的会员
        member_data_1 = {
            "name": f"租户1会员_{int(time.time())}",
            "phone": f"138{int(time.time()) % 100000000:08d}",
            "email": f"tenant1_member_{int(time.time())}@test.com",
            "gender": "male"
        }
        
        response = client.post("/api/v1/admin/members/", json=member_data_1, headers=headers_tenant1)
        assert response.status_code == 201, f"创建租户1会员失败: {response.json()}"
        tenant1_member = response.json()["data"]
        print(f"✅ 租户1会员创建成功: {tenant1_member['name']} (ID: {tenant1_member['id']})")
        
        # 步骤2: 创建第二个租户的数据
        print("📋 步骤2: 创建第二个租户的数据")
        
        # 创建第二个租户的教师
        teacher_data_2 = {
            "name": f"租户2教师_{int(time.time())}",
            "email": f"tenant2_teacher_{int(time.time())}@test.com",
            "phone": f"137{int(time.time()) % 100000000:08d}",
            "specialties": ["英语写作"],
            "introduction": "租户2专属教师",
            "price_per_class": 180,
            "teacher_category": "chinese",
            "region": "china"
        }
        
        response = client.post("/api/v1/admin/teachers/", json=teacher_data_2, headers=headers_tenant2)
        assert response.status_code == 201, f"创建租户2教师失败: {response.json()}"
        tenant2_teacher = response.json()["data"]
        print(f"✅ 租户2教师创建成功: {tenant2_teacher['name']} (ID: {tenant2_teacher['id']})")
        
        # 创建第二个租户的会员
        member_data_2 = {
            "name": f"租户2会员_{int(time.time())}",
            "phone": f"136{int(time.time()) % 100000000:08d}",
            "email": f"tenant2_member_{int(time.time())}@test.com",
            "gender": "female"
        }
        
        response = client.post("/api/v1/admin/members/", json=member_data_2, headers=headers_tenant2)
        assert response.status_code == 201, f"创建租户2会员失败: {response.json()}"
        tenant2_member = response.json()["data"]
        print(f"✅ 租户2会员创建成功: {tenant2_member['name']} (ID: {tenant2_member['id']})")
        
        # 步骤3: 验证租户1只能看到自己的数据
        print("📋 步骤3: 验证租户1只能看到自己的数据")
        
        # 租户1查询教师列表
        response = client.get("/api/v1/admin/teachers/?page=1&size=100", headers=headers_tenant1)
        assert response.status_code == 200, f"租户1查询教师列表失败: {response.json()}"
        tenant1_teachers = response.json()["data"]
        
        # 验证租户1只能看到自己的教师
        tenant1_teacher_ids = [t["id"] for t in tenant1_teachers]
        assert tenant1_teacher["id"] in tenant1_teacher_ids, "租户1无法查询到自己的教师"
        assert tenant2_teacher["id"] not in tenant1_teacher_ids, "租户1能看到租户2的教师（数据隔离失败）"
        print(f"✅ 租户1可见教师数量: {len(tenant1_teachers)}（不包含租户2的教师）")
        
        # 租户1查询会员列表
        response = client.get("/api/v1/admin/members/?page=1&size=100", headers=headers_tenant1)
        assert response.status_code == 200, f"租户1查询会员列表失败: {response.json()}"
        tenant1_members = response.json()["data"]
        
        # 验证租户1只能看到自己的会员
        tenant1_member_ids = [m["id"] for m in tenant1_members]
        assert tenant1_member["id"] in tenant1_member_ids, "租户1无法查询到自己的会员"
        assert tenant2_member["id"] not in tenant1_member_ids, "租户1能看到租户2的会员（数据隔离失败）"
        print(f"✅ 租户1可见会员数量: {len(tenant1_members)}（不包含租户2的会员）")
        
        # 步骤4: 验证租户2只能看到自己的数据
        print("📋 步骤4: 验证租户2只能看到自己的数据")
        
        # 租户2查询教师列表
        response = client.get("/api/v1/admin/teachers/?page=1&size=100", headers=headers_tenant2)
        assert response.status_code == 200, f"租户2查询教师列表失败: {response.json()}"
        tenant2_teachers = response.json()["data"]
        
        # 验证租户2只能看到自己的教师
        tenant2_teacher_ids = [t["id"] for t in tenant2_teachers]
        assert tenant2_teacher["id"] in tenant2_teacher_ids, "租户2无法查询到自己的教师"
        assert tenant1_teacher["id"] not in tenant2_teacher_ids, "租户2能看到租户1的教师（数据隔离失败）"
        print(f"✅ 租户2可见教师数量: {len(tenant2_teachers)}（不包含租户1的教师）")
        
        # 租户2查询会员列表
        response = client.get("/api/v1/admin/members/?page=1&size=100", headers=headers_tenant2)
        assert response.status_code == 200, f"租户2查询会员列表失败: {response.json()}"
        tenant2_members = response.json()["data"]
        
        # 验证租户2只能看到自己的会员
        tenant2_member_ids = [m["id"] for m in tenant2_members]
        assert tenant2_member["id"] in tenant2_member_ids, "租户2无法查询到自己的会员"
        assert tenant1_member["id"] not in tenant2_member_ids, "租户2能看到租户1的会员（数据隔离失败）"
        print(f"✅ 租户2可见会员数量: {len(tenant2_members)}（不包含租户1的会员）")
        
        # 步骤5: 验证跨租户直接访问被阻止
        print("📋 步骤5: 验证跨租户直接访问被阻止")
        
        # 租户1尝试直接访问租户2的教师详情（应该返回404或403，或者data为空）
        response = client.get(f"/api/v1/admin/teachers/{tenant2_teacher['id']}", headers=headers_tenant1)
        print("租户1尝试直接访问租户2的教师详情 response.json()", response.json())
        assert response.status_code in [404, 403], f"租户1能直接访问租户2的教师详情（数据隔离失败）: {response.status_code}"
        print(f"✅ 租户1无法直接访问租户2的教师详情 (HTTP {response.status_code})")
        
        # 租户2尝试直接访问租户1的会员详情（应该返回404或403，或者data为空）
        response = client.get(f"/api/v1/admin/members/{tenant1_member['id']}", headers=headers_tenant2)
        assert response.status_code in [404, 403], f"租户2能直接访问租户1的会员详情（数据隔离失败）: {response.status_code}"
        print(f"✅ 租户2无法直接访问租户1的会员详情 (HTTP {response.status_code})")
        
        # 步骤6: 验证关联数据的隔离
        print("📋 步骤6: 验证关联数据的隔离")
        
        # 验证会员卡数据隔离
        response = client.get(f"/api/v1/admin/member-cards/members/{tenant1_member['id']}/cards", headers=headers_tenant1)
        assert response.status_code == 200, f"租户1查询自己会员卡失败: {response.json()}"
        tenant1_member_cards = response.json()["data"]
        print(f"✅ 租户1会员卡数量: {len(tenant1_member_cards)}")
        
        # 租户2尝试查询租户1的会员卡（RLS过滤，返回空数组）
        response = client.get(f"/api/v1/admin/member-cards/members/{tenant1_member['id']}/cards", headers=headers_tenant2)
        assert response.status_code == 200, f"会员卡查询失败: {response.json()}"
        assert response.json()["data"] == [], "租户2能查询租户1的会员卡（数据隔离失败）"
        print(f"✅ 租户2查询租户1会员卡返回空数组 (RLS正常工作)")
        
        # 验证固定位数据隔离
        response = client.get("/api/v1/admin/teachers/fixed-slots/?page=1&size=100", headers=headers_tenant1)
        assert response.status_code == 200, f"租户1查询固定位失败: {response.json()}"
        tenant1_fixed_slots = response.json()["data"]
        print(f"✅ 租户1固定位数量: {len(tenant1_fixed_slots)}")
        
        response = client.get("/api/v1/admin/teachers/fixed-slots/?page=1&size=100", headers=headers_tenant2)
        assert response.status_code == 200, f"租户2查询固定位失败: {response.json()}"
        tenant2_fixed_slots = response.json()["data"]
        print(f"✅ 租户2固定位数量: {len(tenant2_fixed_slots)}")
        
        print("🎉 多租户数据隔离验证测试成功！")
        print("📝 说明: RLS策略正确隔离了不同租户的数据，确保了数据安全性")

    def test_sequential_booking_consistency(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """测试顺序预约操作数据一致性（模拟并发场景）"""
        print("\n🎯 测试顺序预约操作数据一致性（模拟并发场景）")
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 步骤1: 准备测试数据
        print("📋 步骤1: 准备测试数据")
        
        # 创建教师
        teacher_data = {
            "name": f"并发测试教师_{int(time.time())}",
            "email": f"concurrent_teacher_{int(time.time())}@test.com",
            "phone": f"139{int(time.time()) % 100000000:08d}",
            "specialties": ["英语口语"],
            "introduction": "并发测试专用教师",
            "price_per_class": 150,
            "teacher_category": "european",
            "region": "europe"
        }
        
        response = client.post("/api/v1/admin/teachers/", json=teacher_data, headers=headers)
        assert response.status_code == 201, f"创建教师失败: {response.json()}"
        teacher = response.json()["data"]
        print(f"✅ 教师创建成功: {teacher['name']} (ID: {teacher['id']})")
        
        # 创建多个会员
        members = []
        for i in range(3):
            member_data = {
                "name": f"并发测试会员{i+1}_{int(time.time())}",
                "phone": f"138{int(time.time()) % 100000000 + i:08d}",
                "email": f"concurrent_member{i+1}_{int(time.time())}@test.com",
                "gender": "male" if i % 2 == 0 else "female"
            }
            
            response = client.post("/api/v1/admin/members/", json=member_data, headers=headers)
            assert response.status_code == 201, f"创建会员{i+1}失败: {response.json()}"
            member = response.json()["data"]
            members.append(member)
            print(f"✅ 会员{i+1}创建成功: {member['name']} (ID: {member['id']})")
            
            # 为每个会员充值
            response = client.get(f"/api/v1/admin/member-cards/members/{member['id']}/cards", headers=headers)
            assert response.status_code == 200, f"获取会员{i+1}卡失败: {response.json()}"
            cards = response.json()["data"]
            member_card = cards[0]
            
            recharge_data = {
                "member_card_id": member_card["id"],
                "amount": 1000,
                "bonus_amount": 0,
                "payment_method": "wechat",
                "notes": f"并发测试会员{i+1}充值"
            }
            
            response = client.post(f"/api/v1/admin/member-cards/cards/{member_card['id']}/recharge", 
                                 json=recharge_data, headers=headers)
            assert response.status_code == 200, f"会员{i+1}充值失败: {response.json()}"
            print(f"✅ 会员{i+1}充值成功")
        
        # 步骤2: 创建一个课程供并发预约
        print("📋 步骤2: 创建课程供并发预约")
        
        # 使用相对时间而不是固定时间
        from datetime import datetime, timedelta
        future_datetime = datetime.now() + timedelta(days=1, hours=2)
        
        course_data = {
            "teacher_id": teacher["id"],
            "class_datetime": future_datetime.strftime("%Y-%m-%dT%H:%M:%S"),
            "duration_minutes": 25,
            "price": 150,
            "course_type": "one_on_one",
            "subject": "英语口语",
            "description": "并发测试课程"
        }
        
        response = client.post("/api/v1/admin/courses/classes/temp/create", json=course_data, headers=headers)
        assert response.status_code == 200, f"创建课程失败: {response.json()}"
        course = response.json()["data"]
        print(f"✅ 课程创建成功: ID {course['id']}")
        
        # 步骤3: 顺序预约测试（模拟并发场景）
        print("📋 步骤3: 执行顺序预约测试")

        booking_results = []

        # 第一个会员预约（应该成功）
        response = client.post(f"/api/v1/admin/courses/classes/book/{course['id']}?member_id={members[0]['id']}",
                             headers=headers)
        result1 = {
            "member_id": members[0]['id'],
            "status_code": response.status_code,
            "response": response.json() if response.status_code != 500 else {"error": "server_error"}
        }
        booking_results.append(result1)
        print(f"   会员{result1['member_id']}预约结果: HTTP {result1['status_code']}")

        # 第二个会员预约（应该失败，课程已被预约）
        response = client.post(f"/api/v1/admin/courses/classes/book/{course['id']}?member_id={members[1]['id']}",
                             headers=headers)
        result2 = {
            "member_id": members[1]['id'],
            "status_code": response.status_code,
            "response": response.json() if response.status_code != 500 else {"error": "server_error"}
        }
        booking_results.append(result2)
        print(f"   会员{result2['member_id']}预约结果: HTTP {result2['status_code']}")

        # 第三个会员预约（应该失败，课程已被预约）
        response = client.post(f"/api/v1/admin/courses/classes/book/{course['id']}?member_id={members[2]['id']}",
                             headers=headers)
        result3 = {
            "member_id": members[2]['id'],
            "status_code": response.status_code,
            "response": response.json() if response.status_code != 500 else {"error": "server_error"}
        }
        booking_results.append(result3)
        print(f"   会员{result3['member_id']}预约结果: HTTP {result3['status_code']}")

        # 步骤4: 验证数据一致性
        print("📋 步骤4: 验证数据一致性")

        # 统计成功和失败的预约
        successful_bookings = [r for r in booking_results if r["status_code"] == 200]
        failed_bookings = [r for r in booking_results if r["status_code"] != 200]

        print(f"✅ 成功预约数量: {len(successful_bookings)}")
        print(f"✅ 失败预约数量: {len(failed_bookings)}")

        # 验证只有一个会员能成功预约（一对一课程）
        assert len(successful_bookings) == 1, f"预约一致性错误: 应该只有1个成功，实际{len(successful_bookings)}个"
        assert len(failed_bookings) == 2, f"预约一致性错误: 应该有2个失败，实际{len(failed_bookings)}个"

        # 验证课程状态
        response = client.get(f"/api/v1/admin/courses/classes/{course['id']}", headers=headers)
        assert response.status_code == 200, f"查询课程状态失败: {response.json()}"
        updated_course = response.json()["data"]

        assert updated_course["status"] == "booked", f"课程状态错误: 期望booked，实际{updated_course['status']}"
        print(f"✅ 课程状态正确: {updated_course['status']}")

        print("🎉 顺序预约操作数据一致性测试成功！")
        print("📝 说明: 业务逻辑正确处理了重复预约，保证了数据一致性")

    def test_sequential_fixed_slot_lock_consistency(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """测试顺序固定位锁定操作数据一致性（模拟并发场景）"""
        print("\n🎯 测试顺序固定位锁定操作数据一致性（模拟并发场景）")

        headers = {"Authorization": f"Bearer {admin_token}"}

        # 步骤1: 准备测试数据
        print("📋 步骤1: 准备测试数据")

        # 创建教师
        teacher_data = {
            "name": f"固定位并发测试教师_{int(time.time())}",
            "email": f"slot_concurrent_teacher_{int(time.time())}@test.com",
            "phone": f"139{int(time.time()) % 100000000:08d}",
            "specialties": ["英语口语"],
            "introduction": "固定位并发测试专用教师",
            "price_per_class": 200,
            "teacher_category": "european",
            "region": "europe"
        }

        response = client.post("/api/v1/admin/teachers/", json=teacher_data, headers=headers)
        assert response.status_code == 201, f"创建教师失败: {response.json()}"
        teacher = response.json()["data"]
        print(f"✅ 教师创建成功: {teacher['name']} (ID: {teacher['id']})")

        # 创建固定位
        fixed_slot_data = {
            "teacher_id": teacher["id"],
            "weekday": 1,  # 周一
            "start_time": "09:00",
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": 1
        }

        response = client.post("/api/v1/admin/teachers/fixed-slots/", json=fixed_slot_data, headers=headers)
        assert response.status_code == 201, f"创建固定位失败: {response.json()}"
        fixed_slot = response.json()["data"]
        print(f"✅ 固定位创建成功: 周{fixed_slot['weekday']} {fixed_slot['start_time']} (ID: {fixed_slot['id']})")

        # 创建多个会员
        members = []
        for i in range(3):
            member_data = {
                "name": f"固定位并发测试会员{i+1}_{int(time.time())}",
                "phone": f"138{int(time.time()) % 100000000 + i:08d}",
                "email": f"slot_concurrent_member{i+1}_{int(time.time())}@test.com",
                "gender": "male" if i % 2 == 0 else "female"
            }

            response = client.post("/api/v1/admin/members/", json=member_data, headers=headers)
            assert response.status_code == 201, f"创建会员{i+1}失败: {response.json()}"
            member = response.json()["data"]
            members.append(member)
            print(f"✅ 会员{i+1}创建成功: {member['name']} (ID: {member['id']})")

        # 步骤2: 顺序锁定固定位测试（模拟并发场景）
        print("📋 步骤2: 执行顺序固定位锁定测试")

        lock_results = []

        # 第一个会员锁定（应该成功）
        lock_data1 = {
            "member_id": members[0]["id"],
            "teacher_fixed_slot_id": fixed_slot["id"],
            "status": "active",
            "created_by": 1
        }

        response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data1, headers=headers)
        result1 = {
            "member_id": members[0]["id"],
            "status_code": response.status_code,
            "response": response.json() if response.status_code != 500 else {"error": "server_error"}
        }
        lock_results.append(result1)
        print(f"   会员{result1['member_id']}锁定结果: HTTP {result1['status_code']}")

        # 第二个会员锁定（应该失败，唯一约束冲突）
        lock_data2 = {
            "member_id": members[1]["id"],
            "teacher_fixed_slot_id": fixed_slot["id"],
            "status": "active",
            "created_by": 1
        }

        response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data2, headers=headers)
        result2 = {
            "member_id": members[1]["id"],
            "status_code": response.status_code,
            "response": response.json() if response.status_code != 500 else {"error": "server_error"}
        }
        lock_results.append(result2)
        print(f"   会员{result2['member_id']}锁定结果: HTTP {result2['status_code']}")

        # 第三个会员锁定（应该失败，唯一约束冲突）
        lock_data3 = {
            "member_id": members[2]["id"],
            "teacher_fixed_slot_id": fixed_slot["id"],
            "status": "active",
            "created_by": 1
        }

        response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data3, headers=headers)
        result3 = {
            "member_id": members[2]["id"],
            "status_code": response.status_code,
            "response": response.json() if response.status_code != 500 else {"error": "server_error"}
        }
        lock_results.append(result3)
        print(f"   会员{result3['member_id']}锁定结果: HTTP {result3['status_code']}")

        # 步骤3: 验证数据一致性
        print("📋 步骤3: 验证锁定数据一致性")

        # 统计成功和失败的锁定
        successful_locks = [r for r in lock_results if r["status_code"] == 201]
        failed_locks = [r for r in lock_results if r["status_code"] != 201]

        print(f"✅ 成功锁定数量: {len(successful_locks)}")
        print(f"✅ 失败锁定数量: {len(failed_locks)}")

        # 验证只有一个会员能成功锁定（唯一约束）
        assert len(successful_locks) == 1, f"锁定一致性错误: 应该只有1个成功，实际{len(successful_locks)}个"
        assert len(failed_locks) == 2, f"锁定一致性错误: 应该有2个失败，实际{len(failed_locks)}个"

        # 验证锁定记录
        response = client.get(f"/api/v1/admin/members/fixed-locks/?teacher_fixed_slot_id={fixed_slot['id']}&page=1&size=10",
                            headers=headers)
        assert response.status_code == 200, f"查询锁定记录失败: {response.json()}"
        lock_records = response.json()["data"]

        active_locks = [lock for lock in lock_records if lock.get('status') == 'active']
        assert len(active_locks) == 1, f"活跃锁定记录数量错误: 期望1个，实际{len(active_locks)}个"
        print(f"✅ 活跃锁定记录数量正确: {len(active_locks)}")

        print("🎉 顺序固定位锁定操作数据一致性测试成功！")
        print("📝 说明: 数据库唯一约束正确处理了重复锁定，保证了数据一致性")
