"""
固定位管理流程端到端测试

测试场景：
1. 教师开放固定位 → 会员锁定固定位
2. 教师关闭固定位 → 会员锁定失效处理  
3. 会员取消固定位锁定 → 位置释放
"""

import pytest
import time
from fastapi.testclient import TestClient


@pytest.mark.e2e
class TestFixedSlotFlow:
    """固定位管理流程端到端测试"""
    
    def test_teacher_open_fixed_slot_member_lock_flow(
        self, 
        client: TestClient, 
        created_tenant, 
        created_admin_user, 
        admin_token
    ):
        """测试教师开放固定位 → 会员锁定固定位的完整流程"""
        print("\n🎯 测试教师开放固定位 → 会员锁定固定位流程")
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 步骤1: 创建教师
        print("📋 步骤1: 创建教师")
        teacher_data = {
            "name": f"E2E固定位教师_{int(time.time())}",
            "email": f"fixed_teacher_{int(time.time())}@test.com",
            "phone": f"139{int(time.time()) % 100000000:08d}",
            "specialties": ["英语口语", "商务英语"],
            "introduction": "专业固定位教师",
            "price_per_class": 200,
            "teacher_category": "european",
            "region": "europe"
        }
        
        response = client.post("/api/v1/admin/teachers/", json=teacher_data, headers=headers)
        assert response.status_code == 201, f"创建教师失败: {response.json()}"
        teacher = response.json()["data"]
        print(f"✅ 教师创建成功: {teacher['name']} (ID: {teacher['id']})")
        
        # 步骤2: 创建会员
        print("📋 步骤2: 创建会员")
        member_data = {
            "name": f"E2E固定位会员_{int(time.time())}",
            "phone": f"138{int(time.time()) % 100000000:08d}",
            "email": f"fixed_member_{int(time.time())}@test.com",
            "gender": "male"
        }
        
        response = client.post("/api/v1/admin/members/", json=member_data, headers=headers)
        assert response.status_code == 201, f"创建会员失败: {response.json()}"
        member = response.json()["data"]
        print(f"✅ 会员创建成功: {member['name']} (ID: {member['id']})")
        
        # 步骤3: 获取会员的默认会员卡
        print("📋 步骤3: 获取会员卡")
        response = client.get(f"/api/v1/admin/member-cards/members/{member['id']}/cards", headers=headers)
        assert response.status_code == 200, f"获取会员卡失败: {response.json()}"
        member_cards = response.json()["data"]
        assert len(member_cards) > 0, "会员没有会员卡"
        member_card = member_cards[0]
        print(f"✅ 获取会员卡: {member_card['card_number']} (余额: {member_card['balance']})")
        
        # 步骤4: 为会员卡充值
        print("📋 步骤4: 为会员卡充值")
        recharge_data = {
            "member_card_id": member_card["id"],
            "amount": 2000,
            "bonus_amount": 0,
            "payment_method": "wechat",
            "notes": "E2E固定位测试充值"
        }
        
        response = client.post(f"/api/v1/admin/member-cards/cards/{member_card['id']}/recharge", 
                             json=recharge_data, headers=headers)
        assert response.status_code == 200, f"充值失败: {response.json()}"
        recharge_result = response.json()["data"]
        print(f"✅ 充值成功，余额: {recharge_result['balance_after']}")
        
        # 步骤5: 教师开放固定位
        print("📋 步骤5: 教师开放固定位")
        fixed_slot_data = {
            "teacher_id": teacher["id"],
            "weekday": 1,  # 周一
            "start_time": "14:00",
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": 1
        }
        
        response = client.post("/api/v1/admin/teachers/fixed-slots/", json=fixed_slot_data, headers=headers)
        assert response.status_code == 201, f"创建固定位失败: {response.json()}"
        fixed_slot = response.json()["data"]
        print(f"✅ 固定位创建成功: 周{fixed_slot['weekday']} {fixed_slot['start_time']} 时长{fixed_slot['duration_minutes']}分钟 (ID: {fixed_slot['id']})")
        
        # 步骤6: 会员锁定固定位
        print("📋 步骤6: 会员锁定固定位")
        lock_data = {
            "member_id": member["id"],
            "teacher_fixed_slot_id": fixed_slot["id"],
            "status": "active",
            "created_by": 1
        }

        response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=headers)
        assert response.status_code == 201, f"锁定固定位失败: {response.json()}"
        slot_lock = response.json()["data"]
        print(f"✅ 固定位锁定成功: 状态{slot_lock['status']} (ID: {slot_lock['id']})")

        # 步骤7: 验证固定位状态
        print("📋 步骤7: 验证固定位状态")
        response = client.get(f"/api/v1/admin/teachers/fixed-slots/{fixed_slot['id']}", headers=headers)
        assert response.status_code == 200, f"获取固定位详情失败: {response.json()}"
        updated_slot = response.json()["data"]
        print(f"✅ 固定位状态: {updated_slot.get('is_available', 'unknown')}")
        print(f"   是否可见: {updated_slot.get('is_visible_to_members', 'unknown')}")

        # 步骤8: 验证会员卡余额（注意：固定位锁定可能不会立即扣费）
        print("📋 步骤8: 验证会员卡余额")
        response = client.get(f"/api/v1/admin/member-cards/cards/{member_card['id']}", headers=headers)
        assert response.status_code == 200, f"获取会员卡详情失败: {response.json()}"
        updated_card = response.json()["data"]
        print(f"✅ 会员卡余额: {updated_card['balance']}")
        print(f"   注意: 固定位锁定可能不会立即扣费，而是在实际排课时扣费")

        # 步骤9: 查看锁定记录
        print("📋 步骤9: 查看锁定记录")
        response = client.get(f"/api/v1/admin/members/fixed-locks/?member_id={member['id']}&page=1&size=10",
                            headers=headers)
        assert response.status_code == 200, f"获取锁定记录失败: {response.json()}"
        lock_records = response.json()["data"]
        assert len(lock_records) >= 1, "未找到锁定记录"

        latest_lock = lock_records[0]
        print(f"✅ 锁定记录: 教师固定位ID {latest_lock.get('teacher_fixed_slot_id', 0)}")
        print(f"   锁定状态: {latest_lock.get('status', 'unknown')}")
        
        print("🎉 教师开放固定位 → 会员锁定固定位流程测试成功！")

    def test_teacher_open_multiple_fixed_slots_member_batch_lock_flow(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """测试教师开放多个固定位 → 会员批量锁定固定位的完整流程"""
        print("\n🎯 测试教师开放多个固定位 → 会员批量锁定固定位流程")

        headers = {"Authorization": f"Bearer {admin_token}"}

        # 步骤1: 创建教师
        print("📋 步骤1: 创建教师")
        teacher_data = {
            "name": f"E2E批量固定位教师_{int(time.time())}",
            "email": f"batch_teacher_{int(time.time())}@test.com",
            "phone": f"139{int(time.time()) % 100000000:08d}",
            "specialties": ["英语口语", "商务英语"],
            "introduction": "专业批量固定位教师",
            "price_per_class": 220,
            "teacher_category": "european",
            "region": "europe"
        }

        response = client.post("/api/v1/admin/teachers/", json=teacher_data, headers=headers)
        assert response.status_code == 201, f"创建教师失败: {response.json()}"
        teacher = response.json()["data"]
        print(f"✅ 教师创建成功: {teacher['name']} (ID: {teacher['id']})")

        # 步骤2: 创建会员
        print("📋 步骤2: 创建会员")
        member_data = {
            "name": f"E2E批量固定位会员_{int(time.time())}",
            "phone": f"138{int(time.time()) % 100000000:08d}",
            "email": f"batch_member_{int(time.time())}@test.com",
            "gender": "male"
        }

        response = client.post("/api/v1/admin/members/", json=member_data, headers=headers)
        assert response.status_code == 201, f"创建会员失败: {response.json()}"
        member = response.json()["data"]
        print(f"✅ 会员创建成功: {member['name']} (ID: {member['id']})")

        # 步骤3: 获取会员卡并充值
        print("📋 步骤3: 获取会员卡并充值")
        response = client.get(f"/api/v1/admin/member-cards/members/{member['id']}/cards", headers=headers)
        assert response.status_code == 200, f"获取会员卡失败: {response.json()}"
        member_cards = response.json()["data"]
        assert len(member_cards) > 0, "会员没有会员卡"
        member_card = member_cards[0]

        # 充值足够的金额
        recharge_data = {
            "member_card_id": member_card["id"],
            "amount": 5000,
            "bonus_amount": 0,
            "payment_method": "wechat",
            "notes": "E2E批量固定位测试充值"
        }

        response = client.post(f"/api/v1/admin/member-cards/cards/{member_card['id']}/recharge",
                             json=recharge_data, headers=headers)
        assert response.status_code == 200, f"充值失败: {response.json()}"
        recharge_result = response.json()["data"]
        print(f"✅ 充值成功，余额: {recharge_result['balance_after']}")

        # 步骤4: 教师开放多个固定位
        print("📋 步骤4: 教师开放多个固定位")
        fixed_slots = []
        slot_configs = [
            {"weekday": 1, "start_time": "09:00"},  # 周一 9:00
            {"weekday": 3, "start_time": "14:00"},  # 周三 14:00
            {"weekday": 5, "start_time": "16:00"},  # 周五 16:00
        ]

        for i, config in enumerate(slot_configs):
            fixed_slot_data = {
                "teacher_id": teacher["id"],
                "weekday": config["weekday"],
                "start_time": config["start_time"],
                "duration_minutes": 25,
                "is_available": True,
                "is_visible_to_members": True,
                "created_by": 1
            }

            response = client.post("/api/v1/admin/teachers/fixed-slots/", json=fixed_slot_data, headers=headers)
            assert response.status_code == 201, f"创建固定位{i+1}失败: {response.json()}"
            fixed_slot = response.json()["data"]
            fixed_slots.append(fixed_slot)
            print(f"✅ 固定位{i+1}创建成功: 周{fixed_slot['weekday']} {fixed_slot['start_time']} (ID: {fixed_slot['id']})")

        # 步骤5: 会员批量锁定固定位
        print("📋 步骤5: 会员批量锁定固定位")
        slot_locks = []
        for i, fixed_slot in enumerate(fixed_slots):
            lock_data = {
                "member_id": member["id"],
                "teacher_fixed_slot_id": fixed_slot["id"],
                "status": "active",
                "created_by": 1
            }

            response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=headers)
            assert response.status_code == 201, f"锁定固定位{i+1}失败: {response.json()}"
            slot_lock = response.json()["data"]
            slot_locks.append(slot_lock)
            print(f"✅ 固定位{i+1}锁定成功: 状态{slot_lock['status']} (ID: {slot_lock['id']})")

        # 步骤6: 验证所有固定位状态
        print("📋 步骤6: 验证所有固定位状态")
        for i, fixed_slot in enumerate(fixed_slots):
            response = client.get(f"/api/v1/admin/teachers/fixed-slots/{fixed_slot['id']}", headers=headers)
            assert response.status_code == 200, f"获取固定位{i+1}详情失败: {response.json()}"
            updated_slot = response.json()["data"]
            print(f"✅ 固定位{i+1}状态: 可用={updated_slot.get('is_available', 'unknown')}")

        # 步骤7: 查看所有锁定记录
        print("📋 步骤7: 查看所有锁定记录")
        response = client.get(f"/api/v1/admin/members/fixed-locks/?member_id={member['id']}&page=1&size=10",
                            headers=headers)
        assert response.status_code == 200, f"获取锁定记录失败: {response.json()}"
        lock_records = response.json()["data"]
        assert len(lock_records) >= 3, f"锁定记录数量不正确: {len(lock_records)}"
        print(f"✅ 批量锁定记录: 共{len(lock_records)}条")

        print("🎉 教师开放多个固定位 → 会员批量锁定固定位流程测试成功！")

    def test_teacher_close_fixed_slot_lock_invalid_flow(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """测试教师关闭固定位 → 会员锁定失效处理的完整流程"""
        print("\n🎯 测试教师关闭固定位 → 会员锁定失效处理流程")

        headers = {"Authorization": f"Bearer {admin_token}"}

        # 步骤1: 创建教师
        print("📋 步骤1: 创建教师")
        teacher_data = {
            "name": f"E2E关闭固定位教师_{int(time.time())}",
            "email": f"close_teacher_{int(time.time())}@test.com",
            "phone": f"139{int(time.time()) % 100000000:08d}",
            "specialties": ["英语口语"],
            "introduction": "专业关闭固定位教师",
            "price_per_class": 200,
            "teacher_category": "european",
            "region": "europe"
        }

        response = client.post("/api/v1/admin/teachers/", json=teacher_data, headers=headers)
        assert response.status_code == 201, f"创建教师失败: {response.json()}"
        teacher = response.json()["data"]
        print(f"✅ 教师创建成功: {teacher['name']} (ID: {teacher['id']})")

        # 步骤2: 创建会员
        print("📋 步骤2: 创建会员")
        member_data = {
            "name": f"E2E关闭固定位会员_{int(time.time())}",
            "phone": f"138{int(time.time()) % 100000000:08d}",
            "email": f"close_member_{int(time.time())}@test.com",
            "gender": "female"
        }

        response = client.post("/api/v1/admin/members/", json=member_data, headers=headers)
        assert response.status_code == 201, f"创建会员失败: {response.json()}"
        member = response.json()["data"]
        print(f"✅ 会员创建成功: {member['name']} (ID: {member['id']})")

        # 步骤3: 获取会员卡并充值
        print("📋 步骤3: 获取会员卡并充值")
        response = client.get(f"/api/v1/admin/member-cards/members/{member['id']}/cards", headers=headers)
        assert response.status_code == 200, f"获取会员卡失败: {response.json()}"
        member_cards = response.json()["data"]
        member_card = member_cards[0]

        recharge_data = {
            "member_card_id": member_card["id"],
            "amount": 2000,
            "bonus_amount": 0,
            "payment_method": "wechat",
            "notes": "E2E关闭固定位测试充值"
        }

        response = client.post(f"/api/v1/admin/member-cards/cards/{member_card['id']}/recharge",
                             json=recharge_data, headers=headers)
        assert response.status_code == 200, f"充值失败: {response.json()}"
        recharge_result = response.json()["data"]
        print(f"✅ 充值成功，余额: {recharge_result['balance_after']}")

        # 步骤4: 教师开放固定位
        print("📋 步骤4: 教师开放固定位")
        fixed_slot_data = {
            "teacher_id": teacher["id"],
            "weekday": 2,  # 周二
            "start_time": "10:00",
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": 1
        }

        response = client.post("/api/v1/admin/teachers/fixed-slots/", json=fixed_slot_data, headers=headers)
        assert response.status_code == 201, f"创建固定位失败: {response.json()}"
        fixed_slot = response.json()["data"]
        print(f"✅ 固定位创建成功: 周{fixed_slot['weekday']} {fixed_slot['start_time']} (ID: {fixed_slot['id']})")

        # 步骤5: 会员锁定固定位
        print("📋 步骤5: 会员锁定固定位")
        lock_data = {
            "member_id": member["id"],
            "teacher_fixed_slot_id": fixed_slot["id"],
            "status": "active",
            "created_by": 1
        }

        response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=headers)
        assert response.status_code == 201, f"锁定固定位失败: {response.json()}"
        slot_lock = response.json()["data"]
        print(f"✅ 固定位锁定成功: 状态{slot_lock['status']} (ID: {slot_lock['id']})")

        # 步骤6: 教师关闭固定位
        print("📋 步骤6: 教师关闭固定位")
        response = client.post(f"/api/v1/admin/teachers/fixed-slots/{fixed_slot['id']}/toggle-availability",
                              headers=headers)
        assert response.status_code == 200, f"关闭固定位失败: {response.json()}"
        updated_slot = response.json()["data"]
        print(f"✅ 固定位状态更新: 可用={updated_slot.get('is_available', 'unknown')}")

        # 步骤7: 验证锁定状态（应该仍然存在，但固定位不可用）
        print("📋 步骤7: 验证锁定状态")
        response = client.get(f"/api/v1/admin/members/fixed-locks/{slot_lock['id']}", headers=headers)
        assert response.status_code == 200, f"获取锁定详情失败: {response.json()}"
        updated_lock = response.json()["data"]
        print(f"✅ 锁定状态: {updated_lock.get('status', 'unknown')}")
        print(f"   注意: 固定位关闭后，锁定记录保留但固定位不可用")

        print("🎉 教师关闭固定位 → 会员锁定失效处理流程测试成功！")

    def test_member_cancel_lock_another_member_lock_flow(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """测试会员1取消固定位锁定 → 位置释放 → 会员2锁定该固定位的完整流程"""
        print("\n🎯 测试会员1取消固定位锁定 → 位置释放 → 会员2锁定该固定位流程")

        headers = {"Authorization": f"Bearer {admin_token}"}

        # 步骤1: 创建教师
        print("📋 步骤1: 创建教师")
        teacher_data = {
            "name": f"E2E转移固定位教师_{int(time.time())}",
            "email": f"transfer_teacher_{int(time.time())}@test.com",
            "phone": f"139{int(time.time()) % 100000000:08d}",
            "specialties": ["英语口语"],
            "introduction": "专业转移固定位教师",
            "price_per_class": 250,
            "teacher_category": "european",
            "region": "europe"
        }

        response = client.post("/api/v1/admin/teachers/", json=teacher_data, headers=headers)
        assert response.status_code == 201, f"创建教师失败: {response.json()}"
        teacher = response.json()["data"]
        print(f"✅ 教师创建成功: {teacher['name']} (ID: {teacher['id']})")

        # 步骤2: 创建两个会员
        print("📋 步骤2: 创建两个会员")
        members = []
        member_cards = []

        for i in range(2):
            member_data = {
                "name": f"E2E转移固定位会员{i+1}_{int(time.time())}",
                "phone": f"138{int(time.time()) % 100000000 + i:08d}",
                "email": f"transfer_member{i+1}_{int(time.time())}@test.com",
                "gender": "male" if i == 0 else "female"
            }

            response = client.post("/api/v1/admin/members/", json=member_data, headers=headers)
            assert response.status_code == 201, f"创建会员{i+1}失败: {response.json()}"
            member = response.json()["data"]
            members.append(member)
            print(f"✅ 会员{i+1}创建成功: {member['name']} (ID: {member['id']})")

            # 获取会员卡并充值
            response = client.get(f"/api/v1/admin/member-cards/members/{member['id']}/cards", headers=headers)
            assert response.status_code == 200, f"获取会员{i+1}卡失败: {response.json()}"
            cards = response.json()["data"]
            member_card = cards[0]
            member_cards.append(member_card)

            recharge_data = {
                "member_card_id": member_card["id"],
                "amount": 3000,
                "bonus_amount": 0,
                "payment_method": "wechat",
                "notes": f"E2E转移固定位会员{i+1}测试充值"
            }

            response = client.post(f"/api/v1/admin/member-cards/cards/{member_card['id']}/recharge",
                                 json=recharge_data, headers=headers)
            assert response.status_code == 200, f"会员{i+1}充值失败: {response.json()}"
            recharge_result = response.json()["data"]
            print(f"✅ 会员{i+1}充值成功，余额: {recharge_result['balance_after']}")

        # 步骤3: 教师开放固定位
        print("📋 步骤3: 教师开放固定位")
        fixed_slot_data = {
            "teacher_id": teacher["id"],
            "weekday": 4,  # 周四
            "start_time": "15:00",
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": 1
        }

        response = client.post("/api/v1/admin/teachers/fixed-slots/", json=fixed_slot_data, headers=headers)
        assert response.status_code == 201, f"创建固定位失败: {response.json()}"
        fixed_slot = response.json()["data"]
        print(f"✅ 固定位创建成功: 周{fixed_slot['weekday']} {fixed_slot['start_time']} (ID: {fixed_slot['id']})")

        # 步骤4: 会员1锁定固定位
        print("📋 步骤4: 会员1锁定固定位")
        lock_data = {
            "member_id": members[0]["id"],
            "teacher_fixed_slot_id": fixed_slot["id"],
            "status": "active",
            "created_by": 1
        }

        response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=headers)
        assert response.status_code == 201, f"会员1锁定固定位失败: {response.json()}"
        slot_lock1 = response.json()["data"]
        print(f"✅ 会员1固定位锁定成功: 状态{slot_lock1['status']} (ID: {slot_lock1['id']})")

        # 步骤5: 会员1取消固定位锁定
        print("📋 步骤5: 会员1取消固定位锁定")
        response = client.post(f"/api/v1/admin/members/fixed-locks/{slot_lock1['id']}/cancel",
                              headers=headers)
        assert response.status_code == 200, f"会员1取消锁定失败: {response.json()}"
        result = response.json()
        print(f"✅ 会员1锁定取消成功: {result.get('message', 'unknown')}")

        # 步骤6: 验证固定位释放
        print("📋 步骤6: 验证固定位释放")
        response = client.get(f"/api/v1/admin/teachers/fixed-slots/{fixed_slot['id']}", headers=headers)
        assert response.status_code == 200, f"获取固定位详情失败: {response.json()}"
        released_slot = response.json()["data"]
        print(f"✅ 固定位状态: 可用={released_slot.get('is_available', 'unknown')}")

        # 步骤7: 会员2锁定该固定位（现在应该成功，因为会员1的记录已被删除）
        print("📋 步骤7: 会员2锁定该固定位")
        lock_data2 = {
            "member_id": members[1]["id"],
            "teacher_fixed_slot_id": fixed_slot["id"],
            "status": "active",
            "created_by": 1
        }

        response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data2, headers=headers)
        assert response.status_code == 201, f"会员2锁定固定位失败: {response.json()}"
        slot_lock2 = response.json()["data"]
        print(f"✅ 会员2固定位锁定成功: 状态{slot_lock2['status']} (ID: {slot_lock2['id']})")

        # 步骤8: 验证最终状态
        print("📋 步骤8: 验证最终状态")

        # 验证会员1的锁定记录已被删除
        response = client.get(f"/api/v1/admin/members/fixed-locks/?member_id={members[0]['id']}&page=1&size=10",
                            headers=headers)
        assert response.status_code == 200, f"获取会员1锁定记录失败: {response.json()}"
        member1_locks = response.json()["data"]
        print(f"✅ 会员1锁定记录: 总数{len(member1_locks)} (应该为0，因为记录已删除)")

        # 验证会员2的锁定记录存在且为活跃状态
        response = client.get(f"/api/v1/admin/members/fixed-locks/?member_id={members[1]['id']}&page=1&size=10",
                            headers=headers)
        assert response.status_code == 200, f"获取会员2锁定记录失败: {response.json()}"
        member2_locks = response.json()["data"]
        active_locks = [lock for lock in member2_locks if lock.get('status') == 'active']
        print(f"✅ 会员2锁定记录: 总数{len(member2_locks)}, 活跃{len(active_locks)}")

        assert len(active_locks) >= 1, "会员2的活跃锁定记录不存在"

        print("🎉 会员1取消固定位锁定 → 位置释放 → 会员2锁定该固定位流程测试成功！")
        print("📝 说明: 会员取消锁定会删除记录，释放固定位供其他会员锁定")

