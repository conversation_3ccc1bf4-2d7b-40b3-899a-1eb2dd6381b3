"""标签相关测试fixtures"""
import pytest
import uuid
from sqlmodel import Session
from sqlalchemy import text


@pytest.fixture
def sample_tag_category_data():
    """标签分类数据"""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"测试分类_{unique_id}",
        "description": "这是一个测试标签分类",
        "sort_order": 1
    }


@pytest.fixture
def sample_tag_data():
    """标签数据"""
    unique_id = str(uuid.uuid4())[:8]
    return {
        "name": f"测试标签_{unique_id}",
        "description": "这是一个测试标签",
        "status": "active"
    }


@pytest.fixture
def created_tag_category(test_session: Session, sample_tag_category_data, created_admin_user):
    """创建测试标签分类"""
    from app.features.tags.service import get_tag_category_service
    from app.features.tags.schemas import TagCategoryCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    category_service = get_tag_category_service(test_session, created_admin_user["tenant_id"])
    category_create = TagCategoryCreate(**sample_tag_category_data)
    
    category = category_service.create_category(category_create,created_by=created_admin_user["id"])
    test_session.commit()
    
    return {
        "id": category.id,
        "name": category.name,
        "description": category.description,
        "sort_order": category.sort_order,
        "tenant_id": category.tenant_id
    }


@pytest.fixture
def created_tag(test_session: Session, sample_tag_data, created_tag_category, created_admin_user):
    """创建测试标签"""
    from app.features.tags.service import get_tag_service
    from app.features.tags.schemas import TagCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    tag_service = get_tag_service(test_session, created_admin_user['tenant_id'])
    tag_data = {**sample_tag_data, "category_id": created_tag_category["id"]}
    tag_create = TagCreate(**tag_data)
    
    tag = tag_service.create_tag(tag_create,created_by=created_admin_user["id"])
    test_session.commit()
    
    return {
        "id": tag.id,
        "name": tag.name,
        "description": tag.description,
        "status": tag.status,
        "category_id": tag.category_id,
        "tenant_id": tag.tenant_id
    }


@pytest.fixture
def multiple_tag_categories(test_session: Session, created_admin_user):
    """创建多个测试标签分类"""
    from app.features.tags.service import get_tag_category_service
    from app.features.tags.schemas import TagCategoryCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    category_service = get_tag_category_service(test_session, created_admin_user['tenant_id'])
    
    categories = []
    for i in range(3):
        unique_id = str(uuid.uuid4())[:8]
        category_data = {
            "name": f"分类_{i}_{unique_id}",
            "description": f"测试分类{i}",
            "sort_order": i
        }
        category_create = TagCategoryCreate(**category_data)
        category = category_service.create_category(category_create, created_by=created_admin_user['id'])
        categories.append({
            "id": category.id,
            "name": category.name,
            "description": category.description,
            "sort_order": category.sort_order,
            "tenant_id": category.tenant_id
        })
    
    test_session.commit()
    return categories


@pytest.fixture
def multiple_tags(test_session: Session, created_tag_category, created_admin_user):
    """创建多个测试标签"""
    from app.features.tags.service import get_tag_service
    from app.features.tags.schemas import TagCreate
    from app.features.tags.models import TagStatus
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    tag_service = get_tag_service(test_session, created_admin_user['tenant_id'])
    
    tags = []
    statuses = [TagStatus.ACTIVE, TagStatus.INACTIVE, TagStatus.ACTIVE]
    
    for i in range(3):
        unique_id = str(uuid.uuid4())[:8]
        tag_data = {
            "name": f"标签_{i}_{unique_id}",
            "description": f"测试标签{i}",
            "category_id": created_tag_category["id"],
            "status": statuses[i]
        }
        tag_create = TagCreate(**tag_data)
        tag = tag_service.create_tag(tag_create,created_by=created_admin_user['id'])
        tags.append({
            "id": tag.id,
            "name": tag.name,
            "description": tag.description,
            "status": tag.status,
            "category_id": tag.category_id,
            "tenant_id": tag.tenant_id
        })
    
    test_session.commit()
    return tags


@pytest.fixture
def batch_tag_names():
    """批量标签名称数据"""
    unique_id = str(uuid.uuid4())[:8]
    return [
        f"批量标签1_{unique_id}",
        f"批量标签2_{unique_id}",
        f"批量标签3_{unique_id}"
    ]
