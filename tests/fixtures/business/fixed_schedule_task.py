"""固定课排课任务相关的测试fixture"""

import pytest
from datetime import datetime, date, timedelta
from fastapi.testclient import TestClient

from app.features.courses.scheduling.models import (
    FixedScheduleTask, ScheduleTaskStatus, TeacherPriorityRule, BalanceInsufficientAction
)


@pytest.fixture
def next_monday():
    """获取下一个周一的日期"""
    today = date.today()
    days_ahead = 0 - today.weekday()  # 0 = Monday
    if days_ahead <= 0:  # Target day already happened this week
        days_ahead += 7
    return today + timedelta(days_ahead)


@pytest.fixture
def created_schedule_task(client: TestClient, admin_token, created_teacher, next_monday):
    """创建一个测试用的排课任务"""
    task_data = {
        "task_name": "测试排课任务",
        "start_date": next_monday.isoformat(),
        "weeks_count": 4,
        "teacher_ids": [created_teacher["id"]],
        "teacher_priority_rule": "region_first",
        "balance_insufficient_action": "skip",
        "interrupt_on_conflict": False,
        "remark": "测试用排课任务"
    }

    response = client.post(
        "/api/v1/admin/fixed-schedule/tasks",
        json=task_data,
        headers={"Authorization": f"Bearer {admin_token}"}
    )

    assert response.status_code == 201
    data = response.json()
    assert data["success"] is True

    return data["data"]


@pytest.fixture
def sample_schedule_task_data(next_monday, created_teacher):
    """示例排课任务数据"""
    return {
        "task_name": "示例排课任务",
        "start_date": next_monday.isoformat(),
        "weeks_count": 2,
        "teacher_ids": [created_teacher["id"]],
        "teacher_priority_rule": "region_first",
        "balance_insufficient_action": "skip",
        "interrupt_on_conflict": False,
        "remark": "示例任务备注"
    }


@pytest.fixture
def multiple_schedule_tasks(client: TestClient, admin_token, created_teacher, next_monday):
    """创建多个测试用的排课任务"""
    tasks = []

    # 计算不同的周一日期
    monday1 = next_monday
    monday2 = monday1 + timedelta(weeks=1)
    monday3 = monday1 + timedelta(weeks=2)

    task_data_list = [
        {
            "task_name": "任务1",
            "start_date": monday1.isoformat(),
            "weeks_count": 2,
            "teacher_ids": [created_teacher["id"]],
            "teacher_priority_rule": "region_first",
            "balance_insufficient_action": "skip",
            "interrupt_on_conflict": False
        },
        {
            "task_name": "任务2",
            "start_date": monday2.isoformat(),
            "weeks_count": 3,
            "teacher_ids": [created_teacher["id"]],
            "teacher_priority_rule": "price_desc",
            "balance_insufficient_action": "terminate",
            "interrupt_on_conflict": True
        },
        {
            "task_name": "任务3",
            "start_date": monday3.isoformat(),
            "weeks_count": 1,
            "teacher_ids": [created_teacher["id"]],
            "teacher_priority_rule": "region_first",
            "balance_insufficient_action": "skip",
            "interrupt_on_conflict": False
        }
    ]

    for task_data in task_data_list:
        response = client.post(
            "/api/v1/admin/fixed-schedule/tasks",
            json=task_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        tasks.append(data["data"])

    return tasks


@pytest.fixture
def executed_schedule_task(client: TestClient, admin_token, created_schedule_task):
    """创建并执行一个排课任务"""
    task_id = created_schedule_task["id"]
    
    # 执行任务
    response = client.post(
        f"/api/v1/admin/fixed-schedule/tasks/{task_id}/execute",
        headers={"Authorization": f"Bearer {admin_token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    
    return created_schedule_task


@pytest.fixture
def schedule_task_with_logs(client: TestClient, admin_token, executed_schedule_task):
    """创建一个有日志记录的排课任务"""
    # 执行任务后应该会有日志记录
    return executed_schedule_task
