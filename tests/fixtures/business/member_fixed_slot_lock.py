"""会员固定课位锁定相关测试fixtures"""
import pytest
from datetime import time
from sqlmodel import Session
from sqlalchemy import text


@pytest.fixture
def created_lock(test_session: Session, created_member, created_fixed_slot, created_admin_user):
    """创建单个测试锁定记录"""
    from app.features.members.fixed_lock_service import MemberFixedSlotLockService
    from app.features.members.fixed_lock_schemas import MemberFixedSlotLockCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
    
    lock_data = MemberFixedSlotLockCreate(
        member_id=created_member["id"],
        teacher_fixed_slot_id=created_fixed_slot["id"],
        created_by=created_admin_user["id"]
    )
    
    lock = service.create_lock(lock_data, created_admin_user["id"], created_admin_user["username"])
    test_session.commit()
    
    return {
        "id": lock.id,
        "member_id": lock.member_id,
        "teacher_fixed_slot_id": lock.teacher_fixed_slot_id,
        "teacher_id": lock.teacher_id,
        "weekday": lock.weekday,
        "start_time": lock.start_time,
        "locked_at": lock.locked_at,
        "tenant_id": lock.tenant_id,
        "created_by": lock.created_by,
        "created_at": lock.created_at,
        "updated_at": lock.updated_at
    }


@pytest.fixture
def created_multiple_locks(test_session: Session, created_member, created_multiple_fixed_slots, created_admin_user):
    """创建多个测试锁定记录"""
    from app.features.members.fixed_lock_service import MemberFixedSlotLockService
    from app.features.members.fixed_lock_schemas import MemberFixedSlotLockCreate
    
    service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
    
    created_locks = []
    # 只锁定前3个可用且可见的时间段
    available_slots = [
        slot for slot in created_multiple_fixed_slots
        if slot["is_available"] and slot["is_visible_to_members"]
    ][:3]
    
    for i, slot in enumerate(available_slots):
        lock_data = MemberFixedSlotLockCreate(
            member_id=created_member["id"],
            teacher_fixed_slot_id=slot["id"],
            created_by=created_admin_user["id"]
        )
        
        lock = service.create_lock(lock_data, created_admin_user["id"], created_admin_user["username"])
        created_locks.append({
            "id": lock.id,
            "member_id": lock.member_id,
            "teacher_fixed_slot_id": lock.teacher_fixed_slot_id,
            "teacher_id": lock.teacher_id,
            "weekday": lock.weekday,
            "start_time": lock.start_time,
            "locked_at": lock.locked_at,
            "tenant_id": lock.tenant_id,
            "created_by": lock.created_by
        })
    
    test_session.commit()
    return created_locks


@pytest.fixture
def created_member_2_locks(test_session: Session, created_member_2, created_teacher_2_with_slots, created_admin_user):
    """为第二个会员创建锁定记录"""
    from app.features.members.fixed_lock_service import MemberFixedSlotLockService
    from app.features.members.fixed_lock_schemas import MemberFixedSlotLockCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = MemberFixedSlotLockService(test_session, created_admin_user["tenant_id"])
    
    created_locks = []
    # 为第二个会员锁定第二个教师的时间段
    for slot in created_teacher_2_with_slots["slots"]:
        lock_data = MemberFixedSlotLockCreate(
            member_id=created_member_2["id"],
            teacher_fixed_slot_id=slot["id"],
            created_by=created_admin_user["id"]
        )
        
        lock = service.create_lock(lock_data, created_admin_user["id"], created_admin_user["username"])
        created_locks.append({
            "id": lock.id,
            "member_id": lock.member_id,
            "teacher_fixed_slot_id": lock.teacher_fixed_slot_id,
            "teacher_id": lock.teacher_id,
            "weekday": lock.weekday,
            "start_time": lock.start_time,
        })
    
    test_session.commit()
    return {
        "member": created_member_2,
        "locks": created_locks
    }


@pytest.fixture
def query_test_data():
    """查询测试数据"""
    return {
        "basic_query": {
            "member_id": 1,
            "page": 1,
            "size": 10
        },
        "filtered_query": {
            "member_id": 1,
            "page": 1,
            "size": 20
        },
        "teacher_query": {
            "teacher_id": 1,
            "page": 1,
            "size": 50
        },
        "time_range_query": {
            "member_id": 1,
            "start_time_from": time(8, 0),
            "start_time_to": time(18, 0),
            "page": 1,
            "size": 50
        }
    }


@pytest.fixture
def available_slot_query_data():
    """可用时间段查询测试数据"""
    return {
        "basic_query": {
            "teacher_id": 1,
            "only_available": True,
            "only_visible": True,
            "exclude_locked": True
        },
        "weekday_filter": {
            "teacher_id": 1,
            "weekdays": [1, 2, 3],  # 周一到周三
            "only_available": True,
            "only_visible": True,
            "exclude_locked": True
        },
        "time_range_filter": {
            "teacher_id": 1,
            "start_time_from": time(9, 0),
            "start_time_to": time(17, 0),
            "only_available": True,
            "only_visible": True,
            "exclude_locked": True
        }
    }
