"""教师固定时间段相关测试fixtures"""
import pytest
from datetime import time
from sqlmodel import Session
from sqlalchemy import text

from app.features.teachers.fixed_slots_models import Weekday


@pytest.fixture
def sample_fixed_slot_data():
    """基础固定时间段数据"""
    return {
        "teacher_id": 1,  # 将在fixture中替换为实际的teacher_id
        "weekday": Weekday.MONDAY,
        "start_time": time(8, 30),
        "duration_minutes": 25,
        "is_available": True,
        "is_visible_to_members": True,
        "created_by": 1  # 将在fixture中替换为实际的created_by
    }


@pytest.fixture
def sample_fixed_slot_data_list():
    """多个固定时间段数据"""
    return [
        {
            "weekday": Weekday.MONDAY,
            "start_time": time(8, 30),
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True
        },
        {
            "weekday": Weekday.MONDAY,
            "start_time": time(9, 0),
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True
        },
        {
            "weekday": Weekday.TUESDAY,
            "start_time": time(8, 30),
            "duration_minutes": 30,
            "is_available": False,
            "is_visible_to_members": True
        },
        {
            "weekday": Weekday.WEDNESDAY,
            "start_time": time(10, 0),
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": False
        },
        {
            "weekday": Weekday.FRIDAY,
            "start_time": time(14, 30),
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True
        }
    ]


@pytest.fixture
def weekly_schedule_data():
    """一周完整的时间安排数据"""
    schedule = {}
    
    # 星期一：上午和下午各两节课
    schedule[Weekday.MONDAY] = [
        {"start_time": time(8, 30), "duration_minutes": 25},
        {"start_time": time(9, 0), "duration_minutes": 25},
        {"start_time": time(14, 0), "duration_minutes": 25},
        {"start_time": time(14, 30), "duration_minutes": 25}
    ]
    
    # 星期二：上午三节课
    schedule[Weekday.TUESDAY] = [
        {"start_time": time(8, 0), "duration_minutes": 30},
        {"start_time": time(9, 0), "duration_minutes": 30},
        {"start_time": time(10, 0), "duration_minutes": 30}
    ]
    
    # 星期三：下午课程
    schedule[Weekday.WEDNESDAY] = [
        {"start_time": time(15, 0), "duration_minutes": 25},
        {"start_time": time(15, 30), "duration_minutes": 25},
        {"start_time": time(16, 0), "duration_minutes": 25}
    ]
    
    # 星期四：晚上课程
    schedule[Weekday.THURSDAY] = [
        {"start_time": time(19, 0), "duration_minutes": 25},
        {"start_time": time(19, 30), "duration_minutes": 25}
    ]
    
    # 星期五：全天课程
    schedule[Weekday.FRIDAY] = [
        {"start_time": time(9, 0), "duration_minutes": 25},
        {"start_time": time(10, 0), "duration_minutes": 25},
        {"start_time": time(14, 0), "duration_minutes": 25},
        {"start_time": time(15, 0), "duration_minutes": 25},
        {"start_time": time(20, 0), "duration_minutes": 25}
    ]
    
    return schedule


@pytest.fixture
def conflict_time_data():
    """用于测试时间冲突的数据"""
    return [
        # 基础时间段
        {"weekday": Weekday.MONDAY, "start_time": time(8, 30), "duration_minutes": 25},
        
        # 冲突的时间段（重叠）
        {"weekday": Weekday.MONDAY, "start_time": time(8, 45), "duration_minutes": 25},  # 与第一个重叠
        {"weekday": Weekday.MONDAY, "start_time": time(8, 15), "duration_minutes": 30},  # 包含第一个
        
        # 不冲突的时间段
        {"weekday": Weekday.MONDAY, "start_time": time(9, 0), "duration_minutes": 25},   # 紧接着
        {"weekday": Weekday.MONDAY, "start_time": time(8, 0), "duration_minutes": 25},   # 之前
        {"weekday": Weekday.TUESDAY, "start_time": time(8, 30), "duration_minutes": 25}, # 不同星期
    ]


@pytest.fixture
def created_fixed_slot(test_session: Session, created_teacher, created_admin_user):
    """创建单个测试固定时间段"""
    from app.features.teachers.fixed_slots_service import TeacherFixedSlotService
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
    
    slot_data = TeacherFixedSlotCreate(
        teacher_id=created_teacher["id"],
        weekday=Weekday.MONDAY,
        start_time=time(8, 30),
        duration_minutes=25,
        is_available=True,
        is_visible_to_members=True,
        created_by=created_admin_user["id"]
    )
    
    slot = service.create_slot(slot_data, created_admin_user["id"], created_admin_user["username"])
    test_session.commit()
    
    return {
        "id": slot.id,
        "teacher_id": slot.teacher_id,
        "weekday": slot.weekday,
        "start_time": slot.start_time,
        "duration_minutes": slot.duration_minutes,
        "is_available": slot.is_available,
        "is_visible_to_members": slot.is_visible_to_members,
        "tenant_id": slot.tenant_id,
        "created_by": slot.created_by,
        "created_at": slot.created_at,
        "updated_at": slot.updated_at
    }


@pytest.fixture
def created_multiple_fixed_slots(test_session: Session, created_teacher, created_admin_user, sample_fixed_slot_data_list):
    """创建多个测试固定时间段"""
    from app.features.teachers.fixed_slots_service import TeacherFixedSlotService
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
    
    created_slots = []
    for slot_data in sample_fixed_slot_data_list:
        slot_create = TeacherFixedSlotCreate(
            teacher_id=created_teacher["id"],
            created_by=created_admin_user["id"],
            **slot_data
        )
        
        slot = service.create_slot(slot_create, created_admin_user["id"], created_admin_user["username"])
        created_slots.append({
            "id": slot.id,
            "teacher_id": slot.teacher_id,
            "weekday": slot.weekday,
            "start_time": slot.start_time,
            "duration_minutes": slot.duration_minutes,
            "is_available": slot.is_available,
            "is_visible_to_members": slot.is_visible_to_members,
            "tenant_id": slot.tenant_id,
            "created_by": slot.created_by
        })
    
    test_session.commit()
    return created_slots


@pytest.fixture
def created_weekly_schedule(test_session: Session, created_teacher, created_admin_user, weekly_schedule_data):
    """创建完整的周时间安排"""
    from app.features.teachers.fixed_slots_service import TeacherFixedSlotService
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
    
    weekly_slots = {}
    for weekday, slots_data in weekly_schedule_data.items():
        weekday_slots = []
        for slot_data in slots_data:
            slot_create = TeacherFixedSlotCreate(
                teacher_id=created_teacher["id"],
                weekday=weekday,
                is_available=True,
                is_visible_to_members=True,
                created_by=created_admin_user["id"],
                **slot_data
            )
            
            slot = service.create_slot(slot_create, created_admin_user["id"], created_admin_user["username"])
            weekday_slots.append({
                "id": slot.id,
                "teacher_id": slot.teacher_id,
                "weekday": slot.weekday,
                "start_time": slot.start_time,
                "duration_minutes": slot.duration_minutes,
                "is_available": slot.is_available,
                "is_visible_to_members": slot.is_visible_to_members
            })
        
        weekly_slots[weekday] = weekday_slots
    
    test_session.commit()
    return weekly_slots


@pytest.fixture
def created_teacher_2_with_slots(test_session: Session, created_teacher_2, created_admin_user):
    """为第二个教师创建时间段"""
    from app.features.teachers.fixed_slots_service import TeacherFixedSlotService
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotCreate
    
    # 设置RLS上下文
    test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
    
    service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
    
    # 为第二个教师创建不同的时间安排
    slots_data = [
        {"weekday": Weekday.MONDAY, "start_time": time(10, 0), "duration_minutes": 30},
        {"weekday": Weekday.TUESDAY, "start_time": time(14, 0), "duration_minutes": 25},
        {"weekday": Weekday.WEDNESDAY, "start_time": time(16, 30), "duration_minutes": 25},
    ]
    
    created_slots = []
    for slot_data in slots_data:
        slot_create = TeacherFixedSlotCreate(
            teacher_id=created_teacher_2["id"],
            is_available=True,
            is_visible_to_members=True,
            created_by=created_admin_user["id"],
            **slot_data
        )
        
        slot = service.create_slot(slot_create, created_admin_user["id"], created_admin_user["username"])
        created_slots.append({
            "id": slot.id,
            "teacher_id": slot.teacher_id,
            "weekday": slot.weekday,
            "start_time": slot.start_time,
            "duration_minutes": slot.duration_minutes
        })
    
    test_session.commit()
    return {
        "teacher": created_teacher_2,
        "slots": created_slots
    }


@pytest.fixture
def batch_create_data():
    """批量创建时间段的测试数据"""
    return {
        "valid_slots": [
            {"weekday": Weekday.SATURDAY, "start_time": time(9, 0), "duration_minutes": 25},
            {"weekday": Weekday.SATURDAY, "start_time": time(10, 0), "duration_minutes": 25},
            {"weekday": Weekday.SUNDAY, "start_time": time(14, 0), "duration_minutes": 30},
        ],
        "conflict_slots": [
            {"weekday": Weekday.MONDAY, "start_time": time(8, 30), "duration_minutes": 25},  # 与existing冲突
            {"weekday": Weekday.SATURDAY, "start_time": time(9, 15), "duration_minutes": 25}, # 与batch内冲突
        ]
    }


@pytest.fixture
def batch_update_data():
    """批量更新时间段的测试数据"""
    return [
        {"id": 1, "is_available": False},  # id将在测试中替换
        {"id": 2, "duration_minutes": 30, "is_visible_to_members": False},
        {"id": 3, "start_time": time(15, 0)},
    ]


@pytest.fixture
def query_test_data():
    """查询测试数据"""
    return {
        "basic_query": {
            "teacher_id": 1,
            "page": 1,
            "size": 10
        },
        "filtered_query": {
            "teacher_id": 1,
            "weekday": Weekday.MONDAY,
            "is_available": True,
            "page": 1,
            "size": 20
        },
        "time_range_query": {
            "teacher_id": 1,
            "start_time_from": time(8, 0),
            "start_time_to": time(18, 0),
            "page": 1,
            "size": 50
        }
    }
