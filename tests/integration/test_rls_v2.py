"""
测试新版本的RLS策略

验证修复后的RLS策略能正确工作：
1. 全局模式下可以看到所有数据
2. 租户模式下只能看到本租户数据
3. 超级管理员在任何租户上下文中都可见（仅在全局模式）
4. 普通租户用户不能看到超级管理员
"""

import pytest
from sqlmodel import Session, select, text
from app.features.users.models import User, UserRole, UserStatus
from app.features.tenants.models import Tenant
from app.utils.security import get_password_hash
from app.db.rls_v2 import setup_rls_policies_v2
import uuid


class TestRLSPoliciesV2:
    """测试新版本的RLS策略"""

    def test_rls_v2_with_null_super_admin(self, test_session: Session):
        """测试使用NULL标识超级管理员的RLS策略v2（修复版）"""
        # 设置新的RLS策略（使用NULL标识超级管理员）
        setup_rls_policies_v2()
        
        # 创建租户
        tenant = Tenant(
            name="测试租户",
            code=f"tenant_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000001",
            address="地址"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        
        # 在全局上下文中创建超级管理员
        test_session.exec(text("RESET app.current_tenant_id"))
        super_admin = User(
            username=f"superadmin_{uuid.uuid4().hex[:8]}",
            email=f"superadmin_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="超级管理员",
            role=UserRole.SUPER_ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=None  # 超级管理员使用NULL
        )
        test_session.add(super_admin)
        test_session.commit()
        test_session.refresh(super_admin)
        
        # 在租户上下文中创建租户用户
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_user = User(
            username=f"user_{uuid.uuid4().hex[:8]}",
            email=f"user_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant.id
        )
        test_session.add(tenant_user)
        test_session.commit()
        test_session.refresh(tenant_user)
        
        # 测试全局上下文（应该能看到所有用户）
        test_session.exec(text("RESET app.current_tenant_id"))
        global_users = test_session.exec(select(User)).all()
        global_user_ids = {u.id for u in global_users}
        
        # 测试租户上下文（应该只能看到租户用户，不能看到超级管理员）
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_users = test_session.exec(select(User)).all()
        tenant_user_ids = {u.id for u in tenant_users}
        
        # 断言
        assert super_admin.id in global_user_ids, "全局上下文应该能看到超级管理员"
        assert tenant_user.id in global_user_ids, "全局上下文应该能看到租户用户"
        assert tenant_user.id in tenant_user_ids, "租户上下文应该能看到租户用户"
        assert super_admin.id not in tenant_user_ids, "租户上下文不应该看到超级管理员"
        
        print(f"✅ RLS v2 (NULL模式) 测试通过: 全局用户数={len(global_users)}, 租户用户数={len(tenant_users)}")
        print(f"✅ 租户用户无法看到超级管理员")
