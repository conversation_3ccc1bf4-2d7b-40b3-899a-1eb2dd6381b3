"""
操作日志记录集成测试
"""
import pytest
from datetime import time
from sqlmodel import Session, select

from app.features.teachers.fixed_slots_service import TeacherFixedSlotService
from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotCreate, TeacherFixedSlotUpdate
from app.features.members.fixed_lock_service import MemberFixedSlotLockService
from app.features.members.fixed_lock_schemas import MemberFixedSlotLockCreate
from app.features.courses.operations.service import TeacherFixedSlotOperationLogService, MemberFixedLockOperationLogService
from app.features.courses.operations.models import (
    TeacherFixedSlotOperationLog, MemberFixedLockOperationLog,
    TeacherSlotOperationType, MemberLockOperationType
)


class TestOperationLoggingIntegration:
    """操作日志记录集成测试"""

    def test_teacher_slot_create_logging(self, session: Session, created_teacher, created_user):
        """测试教师时间段创建时的日志记录"""
        tenant_id = 1
        teacher_service = TeacherFixedSlotService(session, tenant_id)
        log_service = TeacherFixedSlotOperationLogService(session, tenant_id)
        
        # 创建时间段
        slot_data = TeacherFixedSlotCreate(
            teacher_id=created_teacher["id"],
            weekday=1,
            start_time=time(9, 0),
            duration_minutes=25,
            is_available=True,
            is_visible_to_members=True
        )
        
        # 记录创建前的日志数量
        initial_count = len(session.exec(select(TeacherFixedSlotOperationLog)).all())
        
        # 执行创建操作
        slot = teacher_service.create_slot(slot_data, created_user["id"])
        
        # 验证日志记录
        logs = session.exec(select(TeacherFixedSlotOperationLog)).all()
        assert len(logs) == initial_count + 1
        
        # 验证日志内容
        log = logs[-1]  # 最新的日志
        assert log.teacher_fixed_slot_id == slot.id
        assert log.operation_type == TeacherSlotOperationType.CREATE
        assert log.operator_id == created_user["id"]
        assert log.teacher_id == created_teacher["id"]
        assert log.weekday == 1
        assert "创建教师固定时间段" in log.operation_description

    def test_teacher_slot_delete_logging(self, session: Session, created_teacher, created_user):
        """测试教师时间段删除时的日志记录"""
        tenant_id = 1
        teacher_service = TeacherFixedSlotService(session, tenant_id)
        
        # 先创建一个时间段
        slot_data = TeacherFixedSlotCreate(
            teacher_id=created_teacher["id"],
            weekday=2,
            start_time=time(10, 0),
            duration_minutes=25,
            is_available=True,
            is_visible_to_members=True
        )
        slot = teacher_service.create_slot(slot_data, created_user["id"])
        
        # 记录删除前的日志数量
        initial_count = len(session.exec(select(TeacherFixedSlotOperationLog)).all())
        
        # 执行删除操作
        teacher_service.delete_slot(slot.id, operator_id=created_user["id"], reason="测试删除")
        
        # 验证日志记录
        logs = session.exec(select(TeacherFixedSlotOperationLog)).all()
        assert len(logs) == initial_count + 1
        
        # 验证日志内容
        log = logs[-1]  # 最新的日志
        assert log.teacher_fixed_slot_id == slot.id
        assert log.operation_type == TeacherSlotOperationType.DELETE
        assert log.operator_id == created_user["id"]
        assert log.reason == "测试删除"
        assert "删除教师固定时间段" in log.operation_description

    def test_teacher_slot_visibility_change_logging(self, session: Session, created_teacher, created_user):
        """测试教师时间段可见性变更时的日志记录"""
        tenant_id = 1
        teacher_service = TeacherFixedSlotService(session, tenant_id)
        
        # 先创建一个时间段
        slot_data = TeacherFixedSlotCreate(
            teacher_id=created_teacher["id"],
            weekday=3,
            start_time=time(11, 0),
            duration_minutes=25,
            is_available=True,
            is_visible_to_members=True
        )
        slot = teacher_service.create_slot(slot_data, created_user["id"])
        
        # 记录更新前的日志数量
        initial_count = len(session.exec(select(TeacherFixedSlotOperationLog)).all())
        
        # 执行可见性变更操作
        update_data = TeacherFixedSlotUpdate(is_visible_to_members=False)
        teacher_service.update_slot(slot.id, update_data, operator_id=created_user["id"], reason="隐藏时间段")
        
        # 验证日志记录
        logs = session.exec(select(TeacherFixedSlotOperationLog)).all()
        assert len(logs) == initial_count + 1
        
        # 验证日志内容
        log = logs[-1]  # 最新的日志
        assert log.teacher_fixed_slot_id == slot.id
        assert log.operation_type == TeacherSlotOperationType.SET_INVISIBLE
        assert log.operator_id == created_user["id"]
        assert log.reason == "隐藏时间段"
        assert "设置对会员不可见" in log.operation_description

    def test_member_lock_create_logging(self, session: Session, created_teacher, created_member, created_user):
        """测试会员锁定创建时的日志记录"""
        tenant_id = 1
        teacher_service = TeacherFixedSlotService(session, tenant_id)
        member_service = MemberFixedSlotLockService(session, tenant_id)
        
        # 先创建一个教师时间段
        slot_data = TeacherFixedSlotCreate(
            teacher_id=created_teacher["id"],
            weekday=4,
            start_time=time(14, 0),
            duration_minutes=25,
            is_available=True,
            is_visible_to_members=True
        )
        slot = teacher_service.create_slot(slot_data, created_user["id"])
        
        # 记录创建前的日志数量
        initial_count = len(session.exec(select(MemberFixedLockOperationLog)).all())
        
        # 创建会员锁定
        lock_data = MemberFixedSlotLockCreate(
            member_id=created_member["id"],
            teacher_fixed_slot_id=slot.id
        )
        lock = member_service.create_lock(lock_data, created_user["id"])
        
        # 验证日志记录
        logs = session.exec(select(MemberFixedLockOperationLog)).all()
        assert len(logs) == initial_count + 1
        
        # 验证日志内容
        log = logs[-1]  # 最新的日志
        assert log.member_fixed_slot_lock_id == lock.id
        assert log.operation_type == MemberLockOperationType.CREATE
        assert log.operator_id == created_user["id"]
        assert log.member_id == created_member["id"]
        assert log.teacher_id == created_teacher["id"]
        assert "会员锁定固定时间段" in log.operation_description

    def test_member_lock_delete_by_admin_logging(self, session: Session, created_teacher, created_member, created_user):
        """测试管理员删除会员锁定时的日志记录"""
        tenant_id = 1
        teacher_service = TeacherFixedSlotService(session, tenant_id)
        member_service = MemberFixedSlotLockService(session, tenant_id)
        
        # 先创建教师时间段和会员锁定
        slot_data = TeacherFixedSlotCreate(
            teacher_id=created_teacher["id"],
            weekday=5,
            start_time=time(15, 0),
            duration_minutes=25,
            is_available=True,
            is_visible_to_members=True
        )
        slot = teacher_service.create_slot(slot_data, created_user["id"])
        
        lock_data = MemberFixedSlotLockCreate(
            member_id=created_member["id"],
            teacher_fixed_slot_id=slot.id
        )
        lock = member_service.create_lock(lock_data, created_user["id"])
        
        # 记录删除前的日志数量
        initial_count = len(session.exec(select(MemberFixedLockOperationLog)).all())
        
        # 执行管理员删除操作
        member_service.delete_lock(
            lock.id, 
            operator_id=created_user["id"], 
            is_member_operation=False, 
            reason="管理员强制删除"
        )
        
        # 验证日志记录
        logs = session.exec(select(MemberFixedLockOperationLog)).all()
        assert len(logs) == initial_count + 1
        
        # 验证日志内容
        log = logs[-1]  # 最新的日志
        assert log.member_fixed_slot_lock_id == lock.id
        assert log.operation_type == MemberLockOperationType.DELETE_BY_ADMIN
        assert log.operator_id == created_user["id"]
        assert log.reason == "管理员强制删除"
        assert "管理员删除锁定" in log.operation_description

    def test_member_lock_delete_by_member_logging(self, session: Session, created_teacher, created_member, created_user):
        """测试会员删除锁定时的日志记录"""
        tenant_id = 1
        teacher_service = TeacherFixedSlotService(session, tenant_id)
        member_service = MemberFixedSlotLockService(session, tenant_id)
        
        # 先创建教师时间段和会员锁定
        slot_data = TeacherFixedSlotCreate(
            teacher_id=created_teacher["id"],
            weekday=6,
            start_time=time(16, 0),
            duration_minutes=25,
            is_available=True,
            is_visible_to_members=True
        )
        slot = teacher_service.create_slot(slot_data, created_user["id"])
        
        lock_data = MemberFixedSlotLockCreate(
            member_id=created_member["id"],
            teacher_fixed_slot_id=slot.id
        )
        lock = member_service.create_lock(lock_data, created_user["id"])
        
        # 记录删除前的日志数量
        initial_count = len(session.exec(select(MemberFixedLockOperationLog)).all())
        
        # 执行会员删除操作
        member_service.delete_lock(
            lock.id, 
            operator_id=created_member["id"], 
            is_member_operation=True, 
            reason="会员主动取消"
        )
        
        # 验证日志记录
        logs = session.exec(select(MemberFixedLockOperationLog)).all()
        assert len(logs) == initial_count + 1
        
        # 验证日志内容
        log = logs[-1]  # 最新的日志
        assert log.member_fixed_slot_lock_id == lock.id
        assert log.operation_type == MemberLockOperationType.DELETE_BY_MEMBER
        assert log.operator_id == created_member["id"]
        assert log.reason == "会员主动取消"
        assert "会员取消锁定" in log.operation_description

    def test_operation_log_query(self, session: Session, created_teacher, created_user):
        """测试操作日志查询功能"""
        tenant_id = 1
        teacher_service = TeacherFixedSlotService(session, tenant_id)
        log_service = TeacherFixedSlotOperationLogService(session, tenant_id)
        
        # 创建多个时间段以产生日志
        for i in range(3):
            slot_data = TeacherFixedSlotCreate(
                teacher_id=created_teacher["id"],
                weekday=1,
                start_time=time(9 + i, 0),
                duration_minutes=25,
                is_available=True,
                is_visible_to_members=True
            )
            teacher_service.create_slot(slot_data, created_user["id"])
        
        # 查询特定教师的操作日志
        logs = log_service.get_teacher_operation_logs(created_teacher["id"], limit=10)
        assert len(logs) >= 3
        
        # 验证日志按时间倒序排列
        for i in range(len(logs) - 1):
            assert logs[i].created_at >= logs[i + 1].created_at
