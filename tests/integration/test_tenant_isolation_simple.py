"""
简单但有效的租户隔离测试

这个测试文件专门用于快速验证租户隔离功能的有效性，
包含最关键的测试用例，确保多租户系统中的数据隔离机制正常工作。
"""

import pytest
from sqlmodel import Session, select, text
from app.features.users.models import User, UserRole
from app.features.members.models import Member
from app.features.member_cards.models import MemberCard
from app.features.members.fixed_lock_models import MemberFixedSlotLock
import uuid


class TestTenantIsolationSimple:
    """简单但有效的租户隔离测试"""

    def test_basic_user_isolation(self, test_session: Session):
        """测试基本的用户数据隔离"""
        from app.features.tenants.models import Tenant
        from app.features.users.models import User, UserRole, UserStatus
        from app.utils.security import get_password_hash
        
        # 创建两个租户
        tenant1 = Tenant(
            name="租户1",
            code=f"tenant1_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000001",
            address="地址1"
        )
        tenant2 = Tenant(
            name="租户2",
            code=f"tenant2_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000002",
            address="地址2"
        )
        test_session.add_all([tenant1, tenant2])
        test_session.commit()
        test_session.refresh(tenant1)
        test_session.refresh(tenant2)
        
        # 为租户1创建用户
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        user1 = User(
            username=f"user1_{uuid.uuid4().hex[:8]}",
            email=f"user1_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户1用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant1.id
        )
        test_session.add(user1)
        test_session.commit()
        test_session.refresh(user1)
        
        # 为租户2创建用户
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        user2 = User(
            username=f"user2_{uuid.uuid4().hex[:8]}",
            email=f"user2_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户2用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant2.id
        )
        test_session.add(user2)
        test_session.commit()
        test_session.refresh(user2)
        
        # 验证租户1只能看到自己的用户
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        tenant1_users = test_session.exec(select(User)).all()
        tenant1_user_ids = {u.id for u in tenant1_users}
        
        # 验证租户2只能看到自己的用户
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        tenant2_users = test_session.exec(select(User)).all()
        tenant2_user_ids = {u.id for u in tenant2_users}
        
        # 基本隔离断言
        assert user1.id in tenant1_user_ids, "租户1应该能看到自己的用户"
        assert user2.id in tenant2_user_ids, "租户2应该能看到自己的用户"
        assert user1.id not in tenant2_user_ids, "租户2不应该看到租户1的用户"
        assert user2.id not in tenant1_user_ids, "租户1不应该看到租户2的用户"
        assert tenant1_user_ids.isdisjoint(tenant2_user_ids), "两个租户的用户集合应该完全分离"

        
        print(f"✅ 用户隔离测试通过: 租户1用户数={len(tenant1_users)}, 租户2用户数={len(tenant2_users)}")

    def test_member_data_isolation(self, test_session: Session):
        """测试会员数据隔离"""
        from app.features.tenants.models import Tenant
        from app.features.members.models import Member, MemberType, MemberStatus
        
        # 创建两个租户
        tenant1 = Tenant(
            name="租户1",
            code=f"tenant1_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000001",
            address="地址1"
        )
        tenant2 = Tenant(
            name="租户2",
            code=f"tenant2_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000002",
            address="地址2"
        )
        test_session.add_all([tenant1, tenant2])
        test_session.commit()
        test_session.refresh(tenant1)
        test_session.refresh(tenant2)
        
        # 为租户1创建会员
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        member1 = Member(
            name="会员1",
            phone=f"138{uuid.uuid4().hex[:8]}",
            email=f"member1_{uuid.uuid4().hex[:8]}@test.com",
            gender="male",
            member_type=MemberType.TRIAL,
            status=MemberStatus.ACTIVE,
            tenant_id=tenant1.id
        )
        test_session.add(member1)
        test_session.commit()
        test_session.refresh(member1)
        
        # 为租户2创建会员
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        member2 = Member(
            name="会员2",
            phone=f"138{uuid.uuid4().hex[:8]}",
            email=f"member2_{uuid.uuid4().hex[:8]}@test.com",
            gender="female",
            member_type=MemberType.FORMAL,
            status=MemberStatus.ACTIVE,
            tenant_id=tenant2.id
        )
        test_session.add(member2)
        test_session.commit()
        test_session.refresh(member2)
        
        # 验证租户1只能看到自己的会员
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        tenant1_members = test_session.exec(select(Member)).all()
        tenant1_member_ids = {m.id for m in tenant1_members}
        
        # 验证租户2只能看到自己的会员
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        tenant2_members = test_session.exec(select(Member)).all()
        tenant2_member_ids = {m.id for m in tenant2_members}
        
        # 断言隔离性
        assert member1.id in tenant1_member_ids, "租户1应该能看到自己的会员"
        assert member2.id in tenant2_member_ids, "租户2应该能看到自己的会员"
        assert member1.id not in tenant2_member_ids, "租户2不应该看到租户1的会员"
        assert member2.id not in tenant1_member_ids, "租户1不应该看到租户2的会员"
        assert tenant1_member_ids.isdisjoint(tenant2_member_ids), "两个租户的会员集合应该完全分离"
        
        print(f"✅ 会员隔离测试通过: 租户1会员数={len(tenant1_members)}, 租户2会员数={len(tenant2_members)}")

    def test_cross_tenant_direct_access_blocked(self, test_session: Session):
        """测试跨租户直接访问阻止（修正版）"""
        from app.features.tenants.models import Tenant
        from app.features.users.models import User, UserRole, UserStatus
        from app.utils.security import get_password_hash
        
        # 创建两个租户
        tenant1 = Tenant(
            name="租户1",
            code=f"tenant1_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000001",
            address="地址1"
        )
        tenant2 = Tenant(
            name="租户2", 
            code=f"tenant2_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000002",
            address="地址2"
        )
        test_session.add_all([tenant1, tenant2])
        test_session.commit()
        test_session.refresh(tenant1)
        test_session.refresh(tenant2)
        
        # 保存租户ID和用户ID，避免对象生命周期问题
        tenant1_id = tenant1.id
        tenant2_id = tenant2.id
        user1_id = None
        user2_id = None
        
        # 在租户1上下文中创建用户1
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant1_id}'"))
        user1 = User(
            username=f"user1_{uuid.uuid4().hex[:8]}",
            email=f"user1_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户1用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant1_id
        )
        test_session.add(user1)
        test_session.commit()
        test_session.refresh(user1)
        user1_id = user1.id
        
        # 在租户2上下文中创建用户2
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant2_id}'"))
        user2 = User(
            username=f"user2_{uuid.uuid4().hex[:8]}",
            email=f"user2_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户2用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant2_id
        )
        test_session.add(user2)
        test_session.commit()
        test_session.refresh(user2)
        user2_id = user2.id
        
        # 只移除用户对象，避免对象生命周期问题
        # test_session.expunge(user1)
        # test_session.expunge(user2)
        
        # 在租户1上下文中尝试通过ID直接访问租户2的用户
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant1_id}'"))
        user2_from_tenant1 = test_session.exec(select(User).where(User.id == user2_id)).first()
        print(f"user2_from_tenant1: {user2_from_tenant1}")
        assert user2_from_tenant1 is None, "租户1不应该能直接访问租户2的用户数据"

        # 在租户2上下文中尝试通过ID直接访问租户1的用户
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant2_id}'"))
        # print(f"user1.id: {user1.id}") #会报错 user1已经被销毁了，在使用RLS策略时，要特别注意SQLAlchemy的对象生命周期管理。当对象在不同租户上下文中可见性发生变化时，可能会导致意外的"对象已删除"错误。
        user1_from_tenant2 = test_session.exec(select(User).where(User.id == user1_id)).first()
        print(f"user1_from_tenant2: {user1_from_tenant2}")
        assert user1_from_tenant2 is None, "租户2不应该能直接访问租户1的用户数据"
        
        # 验证基本的查询隔离仍然有效
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant1_id}'"))
        tenant1_users = test_session.exec(select(User)).all()
        tenant1_user_ids = {u.id for u in tenant1_users}
        
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant2_id}'"))
        tenant2_users = test_session.exec(select(User)).all()
        tenant2_user_ids = {u.id for u in tenant2_users}
        
        # 这个断言应该总是成功的
        assert user1_id not in tenant2_user_ids, "租户2查询不应该返回租户1的用户"
        assert user2_id not in tenant1_user_ids, "租户1查询不应该返回租户2的用户"
        
        print(f"✅ 跨租户查询隔离测试通过: 租户1用户数={len(tenant1_users)}, 租户2用户数={len(tenant2_users)}")
        print(f"✅ 租户1无法直接访问租户2的用户数据")
        print(f"✅ 租户2无法直接访问租户1的用户数据")

    def test_super_admin_global_access(self, test_session: Session):
        """测试超级管理员全局访问（修正版）"""
        from app.features.tenants.models import Tenant
        from app.features.users.models import User, UserRole, UserStatus
        from app.utils.security import get_password_hash
        
        # 创建租户
        tenant = Tenant(
            name="测试租户",
            code=f"tenant_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000001",
            address="地址"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        
        # 在全局上下文中创建超级管理员
        test_session.exec(text("RESET app.current_tenant_id"))
        super_admin = User(
            username=f"superadmin_{uuid.uuid4().hex[:8]}",
            email=f"superadmin_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="超级管理员",
            role=UserRole.SUPER_ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=None
        )
        test_session.add(super_admin)
        test_session.commit()
        test_session.refresh(super_admin)
        
        # 在租户上下文中创建租户用户
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_user = User(
            username=f"user_{uuid.uuid4().hex[:8]}",
            email=f"user_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant.id
        )
        test_session.add(tenant_user)
        test_session.commit()
        test_session.refresh(tenant_user)
        
        # 测试全局上下文（超级管理员模式）
        test_session.exec(text("RESET app.current_tenant_id"))
        global_users = test_session.exec(select(User)).all()
        global_user_ids = {u.id for u in global_users}
        
        # 测试租户上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_users = test_session.exec(select(User)).all()
        tenant_user_ids = {u.id for u in tenant_users}
        
        # 断言：全局上下文应该能看到所有用户
        assert super_admin.id in global_user_ids, "全局上下文应该能看到超级管理员"
        assert tenant_user.id in global_user_ids, "全局上下文应该能看到租户用户"
        
        # 关于租户上下文的断言需要根据实际RLS策略调整
        # 如果当前策略允许租户用户看到超级管理员，我们记录这个行为
        assert super_admin.id not in tenant_user_ids, "租户用户不应该能看到超级管理员"

        if super_admin.id in tenant_user_ids:
            print(f"⚠️  当前RLS策略允许租户用户看到超级管理员")
        else:
            print(f"✅ 租户用户无法看到超级管理员")
            
        assert tenant_user.id in tenant_user_ids, "租户上下文应该能看到租户用户"
        
        print(f"✅ 超级管理员全局访问测试通过: 全局用户数={len(global_users)}, 租户用户数={len(tenant_users)}")

    def test_rls_policies_exist(self, test_session: Session):
        """测试RLS策略存在性检查"""
        # 检查关键表的RLS策略
        tables_to_check = [
            "users",
            "members", 
            "member_fixed_slot_locks",
            "course_system_configs"
        ]
        
        for table_name in tables_to_check:
            # 检查表是否启用了RLS
            rls_enabled = test_session.exec(text(f"""
                SELECT relrowsecurity FROM pg_class 
                WHERE relname = '{table_name}'
            """)).first()
            
            # 检查策略数量
            policy_count_result = test_session.exec(text(f"""
                SELECT COUNT(*) FROM pg_policies 
                WHERE tablename = '{table_name}'
            """)).first()
            
            # 正确处理返回值
            policy_count = policy_count_result if isinstance(policy_count_result, int) else policy_count_result[0]
            
            assert rls_enabled, f"{table_name} 表应该启用RLS"
            assert policy_count > 0, f"{table_name} 表应该有RLS策略"
            
            print(f"✅ {table_name} 表RLS策略检查通过: RLS启用={rls_enabled}, 策略数={policy_count}")

    def test_empty_tenant_context_behavior(self, test_session: Session):
        """测试空租户上下文行为（修正版）"""
        from app.features.tenants.models import Tenant
        from app.features.users.models import User, UserRole, UserStatus
        from app.utils.security import get_password_hash
        
        # 创建租户
        tenant = Tenant(
            name="测试租户",
            code=f"tenant_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000001",
            address="地址"
        )
        test_session.add(tenant)
        test_session.commit()
        test_session.refresh(tenant)
        
        # 在全局上下文中创建超级管理员
        test_session.exec(text("RESET app.current_tenant_id"))
        super_admin = User(
            username=f"superadmin_{uuid.uuid4().hex[:8]}",
            email=f"superadmin_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="超级管理员",
            role=UserRole.SUPER_ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=None
        )
        test_session.add(super_admin)
        test_session.commit()
        test_session.refresh(super_admin)
        
        # 创建租户用户
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_user = User(
            username=f"user_{uuid.uuid4().hex[:8]}",
            email=f"user_{uuid.uuid4().hex[:8]}@test.com",
            password_hash=get_password_hash("password123"),
            real_name="租户用户",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            tenant_id=tenant.id
        )
        test_session.add(tenant_user)
        test_session.commit()
        test_session.refresh(tenant_user)
        
        # 空上下文应该能看到所有用户（超级管理员模式）
        test_session.exec(text("RESET app.current_tenant_id"))
        all_users = test_session.exec(select(User)).all()
        all_user_ids = {u.id for u in all_users}
        
        # 设置租户上下文应该有所限制
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant.id}'"))
        tenant_users = test_session.exec(select(User)).all()
        tenant_user_ids = {u.id for u in tenant_users}
        
        # 断言
        assert super_admin.id in all_user_ids, "空上下文应该能看到超级管理员"
        assert tenant_user.id in all_user_ids, "空上下文应该能看到租户用户"
        assert tenant_user.id in tenant_user_ids, "租户上下文应该能看到租户用户"
        
        # 根据实际RLS策略调整断言
        assert super_admin.id not in tenant_user_ids, "租户用户不应该能看到超级管理员"
        
        if super_admin.id in tenant_user_ids:
            print(f"⚠️  当前RLS策略允许租户上下文看到超级管理员")
        else:
            print(f"✅ 租户上下文无法看到超级管理员")
            
        assert len(all_users) >= len(tenant_users), "全局用户数应该大于等于租户用户数"
        
        print(f"✅ 空租户上下文行为测试通过: 全局用户数={len(all_users)}, 租户用户数={len(tenant_users)}")

    def test_member_fixed_slot_locks_isolation(self, test_session: Session):
        """测试会员固定课位锁定数据隔离"""
        from app.features.tenants.models import Tenant
        from app.features.users.models import User, UserRole, UserStatus
        from app.features.members.models import Member, MemberType, MemberStatus
        from app.features.teachers.models import Teacher, TeacherStatus
        from app.features.teachers.fixed_slots_models import TeacherFixedSlot
        from app.features.members.fixed_lock_models import MemberFixedSlotLock
        from app.utils.security import get_password_hash
        from datetime import time
        
        # 创建两个租户
        tenant1 = Tenant(
            name="租户1",
            code=f"tenant1_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000001",
            address="地址1"
        )
        tenant2 = Tenant(
            name="租户2",
            code=f"tenant2_{uuid.uuid4().hex[:8]}",
            email="<EMAIL>",
            phone="13800000002",
            address="地址2"
        )
        test_session.add_all([tenant1, tenant2])
        test_session.commit()
        test_session.refresh(tenant1)
        test_session.refresh(tenant2)
        
        # 为租户1创建数据
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        
        # 创建会员
        member1 = Member(
            name="会员1",
            phone=f"138{uuid.uuid4().hex[:8]}",
            email=f"member1_{uuid.uuid4().hex[:8]}@test.com",
            gender="male",
            member_type=MemberType.TRIAL,
            status=MemberStatus.ACTIVE,
            tenant_id=tenant1.id
        )
        test_session.add(member1)
        
        # 创建教师
        from app.features.teachers.models import TeacherCategory, TeacherRegion
        teacher1 = Teacher(
            name="教师1",
            phone=f"139{uuid.uuid4().hex[:8]}",
            email=f"teacher1_{uuid.uuid4().hex[:8]}@test.com",
            gender="male",
            status=TeacherStatus.ACTIVE,
            teacher_category=TeacherCategory.CHINESE,
            region=TeacherRegion.CHINA,
            tenant_id=tenant1.id
        )
        test_session.add(teacher1)
        test_session.commit()
        test_session.refresh(member1)
        test_session.refresh(teacher1)
        
        # 创建教师固定时间段
        slot1 = TeacherFixedSlot(
            teacher_id=teacher1.id,
            weekday=1,
            start_time=time(9, 0),
            duration_minutes=60,
            is_available=True,
            is_visible_to_members=True,
            tenant_id=tenant1.id
        )
        test_session.add(slot1)
        test_session.commit()
        test_session.refresh(slot1)
        
        # 创建锁定记录
        lock1 = MemberFixedSlotLock(
            member_id=member1.id,
            teacher_fixed_slot_id=slot1.id,
            teacher_id=teacher1.id,
            weekday=1,
            start_time=time(9, 0),
            tenant_id=tenant1.id
        )
        test_session.add(lock1)
        test_session.commit()
        test_session.refresh(lock1)
        
        # 为租户2创建数据
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        
        member2 = Member(
            name="会员2",
            phone=f"138{uuid.uuid4().hex[:8]}",
            email=f"member2_{uuid.uuid4().hex[:8]}@test.com",
            gender="female",
            member_type=MemberType.FORMAL,
            status=MemberStatus.ACTIVE,
            tenant_id=tenant2.id
        )
        test_session.add(member2)
        
        teacher2 = Teacher(
            name="教师2",
            phone=f"139{uuid.uuid4().hex[:8]}",
            email=f"teacher2_{uuid.uuid4().hex[:8]}@test.com",
            gender="female",
            status=TeacherStatus.ACTIVE,
            teacher_category=TeacherCategory.FILIPINO,
            region=TeacherRegion.PHILIPPINES,
            tenant_id=tenant2.id
        )
        test_session.add(teacher2)
        test_session.commit()
        test_session.refresh(member2)
        test_session.refresh(teacher2)
        
        slot2 = TeacherFixedSlot(
            teacher_id=teacher2.id,
            weekday=2,
            start_time=time(10, 0),
            duration_minutes=60,
            is_available=True,
            is_visible_to_members=True,
            tenant_id=tenant2.id
        )
        test_session.add(slot2)
        test_session.commit()
        test_session.refresh(slot2)
        
        lock2 = MemberFixedSlotLock(
            member_id=member2.id,
            teacher_fixed_slot_id=slot2.id,
            teacher_id=teacher2.id,
            weekday=2,
            start_time=time(10, 0),
            tenant_id=tenant2.id
        )
        test_session.add(lock2)
        test_session.commit()
        test_session.refresh(lock2)
        
        # 验证租户1只能看到自己的锁定记录
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant1.id}'"))
        tenant1_locks = test_session.exec(select(MemberFixedSlotLock)).all()
        tenant1_lock_ids = {l.id for l in tenant1_locks}
        
        # 验证租户2只能看到自己的锁定记录
        test_session.exec(text(f"SET app.current_tenant_id = '{tenant2.id}'"))
        tenant2_locks = test_session.exec(select(MemberFixedSlotLock)).all()
        tenant2_lock_ids = {l.id for l in tenant2_locks}
        
        # 断言隔离性
        assert lock1.id in tenant1_lock_ids, "租户1应该能看到自己的锁定记录"
        assert lock2.id in tenant2_lock_ids, "租户2应该能看到自己的锁定记录"
        assert lock1.id not in tenant2_lock_ids, "租户2不应该看到租户1的锁定记录"
        assert lock2.id not in tenant1_lock_ids, "租户1不应该看到租户2的锁定记录"
        assert tenant1_lock_ids.isdisjoint(tenant2_lock_ids), "两个租户的锁定记录集合应该完全分离"
        
        print(f"✅ 会员固定课位锁定隔离测试通过: 租户1锁定数={len(tenant1_locks)}, 租户2锁定数={len(tenant2_locks)}") 