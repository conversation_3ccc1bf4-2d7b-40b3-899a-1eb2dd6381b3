"""会员端固定课程API集成测试"""
import pytest
from fastapi.testclient import TestClient


class TestMemberFixedCoursesAPI:
    """会员端固定课程API测试"""
    
    def test_get_my_fixed_schedule(self, client: TestClient, member_token):
        """测试获取我的固定课程安排"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/fixed-courses/my-schedule",
            headers=headers
        )
        
        print(f"test_get_my_fixed_schedule response: {response.json()}")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert isinstance(data["data"], list)

    def test_get_available_slots(self, client: TestClient, member_token):
        """测试获取可锁定的时间段"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/fixed-courses/available-slots",
            headers=headers
        )
        
        print(f"test_get_available_slots response: {response.json()}")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert isinstance(data["data"], list)

    def test_get_available_slots_with_filters(self, client: TestClient, member_token):
        """测试带筛选条件的可锁定时间段查询"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/fixed-courses/available-slots?weekdays=1,2,3&teacher_id=1",
            headers=headers
        )
        
        print(f"test_get_available_slots_with_filters response: {response.json()}")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    def test_get_my_locks(self, client: TestClient, member_token):
        """测试获取我的锁定记录"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/fixed-courses/my-locks",
            headers=headers
        )
        
        print(f"test_get_my_locks response: {response.json()}")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert isinstance(data["data"], list)

    def test_request_slot_lock(self, client: TestClient, member_token):
        """测试申请锁定固定时间段"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        # 首先获取可用时间段
        available_response = client.get(
            "/api/v1/member/fixed-courses/available-slots",
            headers=headers
        )
        
        if available_response.status_code == 200:
            available_data = available_response.json()
            if available_data["data"]:
                # 尝试锁定第一个可用时间段
                slot = available_data["data"][0]
                lock_request = {
                    "teacher_fixed_slot_id": slot["id"],
                    "status": "active"
                }
                
                response = client.post(
                    "/api/v1/member/fixed-courses/lock-request",
                    headers=headers,
                    json=lock_request
                )
                
                print(f"test_request_slot_lock response: {response.json()}")
                # 可能因为业务规则失败，但应该返回明确的错误信息
                assert response.status_code in [200, 400, 409]
                data = response.json()
                assert "success" in data

    def test_cancel_my_lock(self, client: TestClient, member_token):
        """测试取消我的锁定"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        # 首先获取我的锁定记录
        locks_response = client.get(
            "/api/v1/member/fixed-courses/my-locks",
            headers=headers
        )
        
        if locks_response.status_code == 200:
            locks_data = locks_response.json()
            if locks_data["data"]:
                # 尝试取消第一个锁定
                lock = locks_data["data"][0]
                
                response = client.delete(
                    f"/api/v1/member/fixed-courses/locks/{lock['id']}",
                    headers=headers
                )
                
                print(f"test_cancel_my_lock response: {response.json()}")
                # 可能因为业务规则失败，但应该返回明确的错误信息
                assert response.status_code in [200, 400, 404]
                data = response.json()
                assert "success" in data

    def test_invalid_weekdays_format(self, client: TestClient, member_token):
        """测试无效的星期格式"""
        headers = {"Authorization": f"Bearer {member_token}"}
        
        response = client.get(
            "/api/v1/member/fixed-courses/available-slots?weekdays=invalid",
            headers=headers
        )
        
        print(f"test_invalid_weekdays_format response: {response.json()}")
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "星期格式错误" in data["message"]

    def test_unauthorized_access(self, client: TestClient):
        """测试未授权访问"""
        response = client.get("/api/v1/member/fixed-courses/my-schedule")
        
        print(f"test_unauthorized_access response: {response.json()}")
        assert response.status_code == 401
