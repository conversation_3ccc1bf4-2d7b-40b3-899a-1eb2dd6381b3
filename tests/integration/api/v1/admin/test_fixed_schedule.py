"""固定课排课API集成测试"""

import pytest
from fastapi.testclient import TestClient
from datetime import datetime, date, timedelta
from app.api.common.exceptions import GlobalErrorCode


class TestAdminFixedScheduleAPI:
    """固定课排课API集成测试"""

    @pytest.fixture
    def next_monday(self):
        """获取下一个周一的日期"""
        today = date.today()
        days_ahead = 0 - today.weekday()  # 0 = Monday
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7
        return today + timedelta(days_ahead)

    @pytest.fixture
    def valid_task_data(self, next_monday, created_teacher):
        """有效的排课任务数据"""
        return {
            "task_name": "测试排课任务",
            "start_date": next_monday.isoformat(),
            "weeks_count": 4,
            "teacher_ids": [created_teacher["id"]],
            "teacher_priority_rule": "region_first",
            "balance_insufficient_action": "skip",
            "interrupt_on_conflict": False,
            "remark": "集成测试任务"
        }

    def test_create_schedule_task_success(self, client: TestClient, admin_token, valid_task_data):
        """测试管理员成功创建排课任务"""
        response = client.post(
            "/api/v1/admin/fixed-schedule/tasks",
            json=valid_task_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 201
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        task = data["data"]
        assert task["task_name"] == valid_task_data["task_name"]
        assert task["weeks_count"] == valid_task_data["weeks_count"]
        assert task["teacher_priority_rule"] == valid_task_data["teacher_priority_rule"]
        assert task["balance_insufficient_action"] == valid_task_data["balance_insufficient_action"]
        assert task["status"] == "pending"
        assert "id" in task
        assert "created_at" in task

    def test_create_schedule_task_unauthorized(self, client: TestClient, valid_task_data):
        """测试未授权创建排课任务"""
        response = client.post(
            "/api/v1/admin/fixed-schedule/tasks",
            json=valid_task_data
        )

        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False

    def test_create_schedule_task_invalid_data(self, client: TestClient, admin_token):
        """测试创建排课任务数据验证失败"""
        # 缺少必填字段
        task_data = {
            "task_name": "测试排课任务"
            # 缺少其他必填字段
        }

        response = client.post(
            "/api/v1/admin/fixed-schedule/tasks",
            json=task_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 422
        data = response.json()
        assert data["success"] is False

    def test_create_schedule_task_invalid_date(self, client: TestClient, admin_token, created_teacher):
        """测试创建排课任务 - 无效日期"""
        # 使用过去的日期
        past_date = date.today() - timedelta(days=1)
        task_data = {
            "task_name": "测试排课任务",
            "start_date": past_date.isoformat(),
            "weeks_count": 4,
            "teacher_ids": [created_teacher["id"]],
            "teacher_priority_rule": "region_first",
            "balance_insufficient_action": "skip",
            "interrupt_on_conflict": False
        }

        response = client.post(
            "/api/v1/admin/fixed-schedule/tasks",
            json=task_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 422
        data = response.json()
        assert data["success"] is False

    def test_get_schedule_tasks_list(self, client: TestClient, admin_token):
        """测试获取排课任务列表"""
        response = client.get(
            "/api/v1/admin/fixed-schedule/tasks",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data
        assert isinstance(data["data"], list)
        assert "total" in data
        assert "page" in data
        assert "size" in data

        # 验证分页信息
        assert isinstance(data["data"], list)
        assert isinstance(data["total"], int)
        assert data["page"] == 1
        assert data["size"] == 20

    def test_get_schedule_tasks_with_filters(self, client: TestClient, admin_token):
        """测试带筛选条件获取排课任务列表"""
        params = {
            "task_name": "测试",
            "status": "pending",
            "page": 1,
            "size": 10
        }

        response = client.get(
            "/api/v1/admin/fixed-schedule/tasks",
            params=params,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        assert data["success"] is True
        assert data["page"] == 1
        assert data["size"] == 10

    def test_get_schedule_task_detail(self, client: TestClient, admin_token, created_schedule_task):
        """测试获取排课任务详情"""
        task_id = created_schedule_task["id"]

        response = client.get(
            f"/api/v1/admin/fixed-schedule/tasks/{task_id}",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        task = data["data"]
        assert task["id"] == task_id
        assert "task_name" in task
        assert "status" in task
        assert "created_at" in task

    def test_get_schedule_task_not_found(self, client: TestClient, admin_token):
        """测试获取不存在的排课任务"""
        response = client.get(
            "/api/v1/admin/fixed-schedule/tasks/999999",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        print("test_get_schedule_task_not_found response:", response.json())
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False

    def test_update_schedule_task_success(self, client: TestClient, admin_token, created_schedule_task):
        """测试成功更新排课任务"""
        task_id = created_schedule_task["id"]
        update_data = {
            "task_name": "更新后的任务名称",
            "remark": "更新后的备注"
        }

        response = client.put(
            f"/api/v1/admin/fixed-schedule/tasks/{task_id}",
            json=update_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        task = data["data"]
        assert task["task_name"] == update_data["task_name"]
        assert task["remark"] == update_data["remark"]

    def test_execute_schedule_task(self, client: TestClient, admin_token, created_schedule_task):
        """测试执行排课任务"""
        task_id = created_schedule_task["id"]

        response = client.post(
            f"/api/v1/admin/fixed-schedule/tasks/{task_id}/execute",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        result = data["data"]
        assert result["task_id"] == task_id
        assert "message" in result
        assert "status" in result

    def test_get_task_execution_status(self, client: TestClient, admin_token, created_schedule_task):
        """测试获取任务执行状态"""
        task_id = created_schedule_task["id"]

        response = client.get(
            f"/api/v1/admin/fixed-schedule/tasks/{task_id}/status",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        status = data["data"]
        assert status["task_id"] == task_id
        assert "status" in status

    def test_create_and_execute_schedule_task(self, client: TestClient, admin_token, valid_task_data):
        """测试创建并立即执行排课任务"""
        response = client.post(
            "/api/v1/admin/fixed-schedule/tasks/create-and-execute",
            json=valid_task_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data

        result = data["data"]
        assert "task_id" in result
        assert "task_name" in result
        assert "message" in result
        assert "status" in result

    def test_get_task_statistics(self, client: TestClient, admin_token):
        """测试获取任务统计信息"""
        response = client.get(
            "/api/v1/admin/fixed-schedule/tasks/statistics",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()
    
        # 验证响应格式
        assert data["success"] is True
        assert "data" in data
    
        stats = data["data"]
        assert "total_tasks" in stats
        assert "pending_tasks" in stats
        assert "running_tasks" in stats
        assert "completed_tasks" in stats
        assert "failed_tasks" in stats

    def test_get_task_logs(self, client: TestClient, admin_token, created_schedule_task):
        """测试获取任务日志"""
        task_id = created_schedule_task["id"]

        response = client.get(
            f"/api/v1/admin/fixed-schedule/tasks/{task_id}/logs",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data
        assert isinstance(data["data"], list)
        assert "total" in data
        assert "page" in data
        assert "size" in data

    def test_get_all_logs(self, client: TestClient, admin_token):
        """测试获取所有排课日志"""
        response = client.get(
            "/api/v1/admin/fixed-schedule/logs",
            headers={"Authorization": f"Bearer {admin_token}"}
        )

        assert response.status_code == 200
        data = response.json()

        # 验证响应格式
        assert data["success"] is True
        assert "data" in data
        assert isinstance(data["data"], list)
        assert "total" in data
        assert "page" in data
        assert "size" in data


