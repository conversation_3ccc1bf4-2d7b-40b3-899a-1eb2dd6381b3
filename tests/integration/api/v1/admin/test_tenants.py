import pytest
from fastapi.testclient import TestClient
from app.api.common.exceptions import GlobalErrorCode


class TestAdminTenantAPI:
    """租户API集成测试"""
    
    def test_create_tenant_success(self, client: TestClient, super_admin_token):
        """测试超级管理员成功创建租户"""
        import uuid
        unique_code = f"test_tenant_{uuid.uuid4().hex[:8]}"
        tenant_data = {
            "code": unique_code,
            "name": "测试租户",
            "status": "active",
            "plan_code": "basic"
        }
        
        response = client.post(
            "/api/v1/admin/tenants/",
            json=tenant_data,
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )

        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.text}")
        assert response.status_code == 201
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "租户创建成功"
        assert "data" in data
        
        tenant_data_resp = data["data"]
        assert tenant_data_resp["code"] == tenant_data["code"]
        assert tenant_data_resp["name"] == tenant_data["name"]
        assert tenant_data_resp["status"] == tenant_data["status"]
        assert "id" in tenant_data_resp
    
    def test_verify_admin_user_role(self, test_session, created_admin_user):
        """验证创建的管理员用户角色"""
        from app.features.users.models import User, UserRole

        # 验证创建的用户确实是普通管理员
        assert created_admin_user["id"] is not None
        print(f"Created admin user ID: {created_admin_user['id']}")
        print(f"Created admin user email: {created_admin_user['email']}")

        # 使用同一个测试会话查询用户
        from sqlmodel import select
        from sqlalchemy import text

        # 重置租户上下文以查看所有用户
        test_session.exec(text("RESET app.current_tenant_id"))

        # 查询所有用户
        all_users = test_session.exec(select(User)).all()
        print(f"Total users in test session: {len(all_users)}")
        for user in all_users:
            print(f"User ID: {user.id}, Email: {user.email}, Role: {user.role}, Tenant: {user.tenant_id}")

        # 查询特定用户
        user = test_session.get(User, created_admin_user["id"])
        assert user is not None
        print(f"Target user role from test session: {user.role}")
        print(f"Target user email from test session: {user.email}")

        # 现在验证用户角色
        assert user.role == UserRole.ADMIN
        assert user.tenant_id is not None

    def test_create_tenant_forbidden_for_admin(self, client: TestClient, admin_token):
        """测试普通管理员无法创建租户"""
        tenant_data = {
            "code": "test_tenant_002",
            "name": "测试租户2",
            "status": "active",
            "plan_code": "basic"
        }
        
        response = client.post(
            "/api/v1/admin/tenants/",
            json=tenant_data,
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 403
        data = response.json()
        print(f"test_create_tenant_forbidden_for_admin Response: {data}")
        assert data["success"] is False
        assert "需要超级管理员权限" in data["message"]
    
    def test_create_tenant_unauthorized(self, client: TestClient):
        """测试未认证用户无法创建租户"""
        tenant_data = {
            "code": "test_tenant_003",
            "name": "测试租户3",
            "status": "active",
            "plan_code": "basic"
        }
        
        response = client.post("/api/v1/admin/tenants/", json=tenant_data)
        assert response.status_code == 401
    
    def test_get_tenants_success(self, client: TestClient, super_admin_token):
        """测试超级管理员获取租户列表"""
        response = client.get(
            "/api/v1/admin/tenants/",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "获取租户列表成功"
        assert "data" in data
        assert "total" in data
        assert isinstance(data["data"], list)
        assert isinstance(data["total"], int)
    
    def test_get_tenants_forbidden_for_admin(self, client: TestClient, admin_token):
        """测试普通管理员无法获取租户列表"""
        response = client.get(
            "/api/v1/admin/tenants/",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        
        assert response.status_code == 403
        data = response.json()
        assert data["success"] is False
        assert "需要超级管理员权限" in data["message"]
    
    def test_get_tenant_by_id_success(self, client: TestClient, super_admin_token, created_tenant):
        """测试超级管理员根据ID获取租户"""
        response = client.get(
            f"/api/v1/admin/tenants/{created_tenant['id']}",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "获取租户详情成功"
        assert data["data"]["id"] == created_tenant["id"]
        assert data["data"]["code"] == created_tenant["code"]
    
    def test_get_tenant_by_code_success(self, client: TestClient, super_admin_token, created_tenant):
        """测试超级管理员根据代码获取租户"""
        response = client.get(
            f"/api/v1/admin/tenants/code/{created_tenant['code']}",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "获取租户详情成功"
        assert data["data"]["code"] == created_tenant["code"]
        assert data["data"]["name"] == created_tenant["name"]
    
    def test_update_tenant_success(self, client: TestClient, super_admin_token, created_tenant):
        """测试超级管理员更新租户"""
        update_data = {
            "name": "更新后的租户名称",
            "status": "active"
        }
        
        response = client.post(
            f"/api/v1/admin/tenants/{created_tenant['id']}",
            json=update_data,
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "租户信息更新成功"
        assert data["data"]["name"] == update_data["name"]
        assert data["data"]["id"] == created_tenant["id"]
    
    def test_delete_tenant_success(self, client: TestClient, super_admin_token):
        """测试超级管理员删除租户"""
        # 先创建一个租户
        tenant_data = {
            "code": "delete_test_tenant",
            "name": "待删除租户",
            "status": "active",
            "plan_code": "basic"
        }
        
        create_response = client.post(
            "/api/v1/admin/tenants/",
            json=tenant_data,
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        created_tenant = create_response.json()
        
        # 删除租户
        response = client.post(
            f"/api/v1/admin/tenants/{created_tenant['data']['id']}/delete",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert "删除成功" in data["message"]
    
    def test_get_plan_templates_success(self, client: TestClient, super_admin_token):
        """测试超级管理员获取套餐模板"""
        response = client.get(
            "/api/v1/admin/tenants/plans/templates",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "获取套餐模板成功"
        assert "data" in data
        assert isinstance(data["data"], list)
    
    def test_activate_tenant_success(self, client: TestClient, super_admin_token, created_tenant):
        """测试超级管理员激活租户"""
        response = client.post(
            f"/api/v1/admin/tenants/{created_tenant['id']}/activate",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "租户激活成功"
        assert "data" in data
    
    def test_suspend_tenant_success(self, client: TestClient, super_admin_token, created_tenant):
        """测试超级管理员暂停租户"""
        response = client.post(
            f"/api/v1/admin/tenants/{created_tenant['id']}/suspend",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "租户暂停成功"
        assert data["data"]["status"] == "suspended"
    
    def test_create_tenant_duplicate_code(self, client: TestClient, super_admin_token, sample_tenant_data):
        """测试创建重复代码租户"""
        # 先创建一个租户
        client.post(
            "/api/v1/admin/tenants/", 
            json=sample_tenant_data,
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        # 尝试创建相同代码的租户
        response = client.post(
            "/api/v1/admin/tenants/", 
            json=sample_tenant_data,
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert "已存在" in data["message"]
    
    def test_create_tenant_invalid_data(self, client: TestClient, super_admin_token):
        """测试创建租户时数据验证"""
        invalid_data = {
            "name": "",  # 空名称
            "code": "test",
            "contact_email": "invalid-email"  # 无效邮箱
        }
        
        response = client.post(
            "/api/v1/admin/tenants/", 
            json=invalid_data,
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        assert response.status_code == 422  # 验证错误
    
    def test_get_tenants_list(self, client: TestClient, super_admin_token):
        """测试获取租户列表"""
        # 先创建几个租户
        for i in range(3):
            tenant_data = {
                "name": f"测试机构{i}",
                "code": f"test_org_{i}",
                "contact_name": "张三",
                "contact_phone": "13800138000",
                "contact_email": f"test{i}@example.com",
                "address": "测试地址"
            }
            client.post(
                "/api/v1/admin/tenants/", 
                json=tenant_data,
                headers={"Authorization": f"Bearer {super_admin_token}"}
            )
        
        response = client.get(
            "/api/v1/admin/tenants/",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert data["message"] == "获取租户列表成功"
        assert isinstance(data["data"], list)
        assert len(data["data"]) == 3
    
    def test_get_tenants_pagination(self, client: TestClient, super_admin_token):
        """测试租户列表分页"""
        # 创建5个租户
        for i in range(5):
            tenant_data = {
                "name": f"测试机构{i}",
                "code": f"test_org_{i}",
                "contact_name": "张三",
                "contact_phone": "13800138000",
                "contact_email": f"test{i}@example.com",
                "address": "测试地址"
            }
            client.post(
                "/api/v1/admin/tenants/", 
                json=tenant_data,
                headers={"Authorization": f"Bearer {super_admin_token}"}
            )
        
        # 测试分页
        response = client.get(
            "/api/v1/admin/tenants/?skip=0&limit=3",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert len(data["data"]) == 3
        
        response = client.get(
            "/api/v1/admin/tenants/?skip=3&limit=3",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        assert response.status_code == 200
        data = response.json()
        
        # 验证新的响应格式
        assert data["success"] is True
        assert len(data["data"]) == 2
    
    def test_get_nonexistent_tenant(self, client: TestClient, super_admin_token):
        """测试获取不存在的租户"""
        response = client.get(
            "/api/v1/admin/tenants/99999",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        
        response = client.get(
            "/api/v1/admin/tenants/code/nonexistent",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
    
    def test_update_tenant(self, client: TestClient, super_admin_token, created_tenant):
        """测试更新租户"""
        tenant_id = created_tenant["id"]
        update_data = {
            "name": "更新后的机构名称",
            "contact_name": "李四",
            "contact_phone": "13900139000"
        }
        
        response = client.post(
            f"/api/v1/admin/tenants/{tenant_id}", 
            json=update_data,
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()["data"]
        assert data["name"] == "更新后的机构名称"
        assert data["contact_name"] == "李四"
        assert data["contact_phone"] == "13900139000"
    
    def test_update_nonexistent_tenant(self, client: TestClient, super_admin_token):
        """测试更新不存在的租户"""
        update_data = {"name": "新名称"}
        
        response = client.post(
            "/api/v1/admin/tenants/99999", 
            json=update_data,
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        assert response.status_code == 404
    
    def test_delete_tenant(self, client: TestClient, super_admin_token, created_tenant):
        """测试删除租户"""
        tenant_id = created_tenant["id"]
        
        response = client.post(
            f"/api/v1/admin/tenants/{tenant_id}/delete",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        assert "删除成功" in response.json()["message"]
        
        # 验证租户状态变为terminated
        get_response = client.get(
            f"/api/v1/admin/tenants/{tenant_id}",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        assert get_response.status_code == 200
        assert get_response.json()["data"]["status"] == "terminated"
    
    def test_delete_nonexistent_tenant(self, client: TestClient, super_admin_token):
        """测试删除不存在的租户"""
        response = client.post(
            "/api/v1/admin/tenants/99999/delete",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        assert response.json()["business_code"] == GlobalErrorCode.BUSINESS_ERROR.value
    
    def test_activate_tenant(self, client: TestClient, super_admin_token, created_tenant):
        """测试激活租户"""
        tenant_id = created_tenant["id"]
        
        response = client.post(
            f"/api/v1/admin/tenants/{tenant_id}/activate",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()["data"]
        assert data["status"] == "active"
    
    def test_suspend_tenant(self, client: TestClient, super_admin_token, created_tenant):
        """测试暂停租户"""
        tenant_id = created_tenant["id"]
        
        response = client.post(
            f"/api/v1/admin/tenants/{tenant_id}/suspend?reason=测试暂停",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()["data"]
        assert data["status"] == "suspended"
    
    def test_apply_plan_template(self, client: TestClient, super_admin_token, created_tenant, sample_plan_template):
        """测试应用套餐模板"""
        tenant_id = created_tenant["id"]
        plan_code = sample_plan_template.plan_code
        
        response = client.post(
            f"/api/v1/admin/tenants/{tenant_id}/apply-plan/{plan_code}",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        
        assert response.status_code == 200
        data = response.json()["data"]
        assert data["plan_type"] == plan_code
        assert data["max_teachers"] == sample_plan_template.max_teachers
        assert data["max_members"] == sample_plan_template.max_members
        assert float(data["monthly_fee"]) == float(sample_plan_template.monthly_price)
    
    def test_apply_nonexistent_plan(self, client: TestClient, super_admin_token, created_tenant):
        """测试应用不存在的套餐模板"""
        tenant_id = created_tenant["id"]
        
        response = client.post(
            f"/api/v1/admin/tenants/{tenant_id}/apply-plan/nonexistent",
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        assert response.status_code == 404
    
    def test_api_error_handling(self, client: TestClient, super_admin_token):
        """测试API错误处理"""
        # 测试无效的JSON
        response = client.post(
            "/api/v1/admin/tenants/",
            data="invalid json",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {super_admin_token}"
            }
        )
        assert response.status_code == 422
        
        # 测试缺少必填字段
        response = client.post(
            "/api/v1/admin/tenants/", 
            json={},
            headers={"Authorization": f"Bearer {super_admin_token}"}
        )
        assert response.status_code == 422 