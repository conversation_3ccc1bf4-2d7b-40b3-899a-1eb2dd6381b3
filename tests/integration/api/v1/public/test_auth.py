import pytest
from fastapi.testclient import TestClient


class TestAdminAuthAPI:
    """管理员认证API集成测试"""
    
    def test_admin_login_success(self, client: TestClient, created_admin_user, created_tenant):
        """测试管理员登录成功"""
        login_data = {
            "username": created_admin_user["username"],
            "password": created_admin_user["password"]
        }
        
        response = client.post("/api/v1/auth/admin/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "登录成功"
        assert "data" in data
        
        auth_data = data["data"]
        assert "access_token" in auth_data
        assert auth_data["token_type"] == "bearer"
        assert "user" in auth_data
        assert auth_data["user"]["username"] == created_admin_user["username"]
        assert "tenant" in auth_data
        assert auth_data["tenant"]["code"] == created_tenant["code"]
    
    def test_admin_login_wrong_password(self, client: <PERSON><PERSON><PERSON>, created_admin_user, created_tenant):
        """测试管理员登录密码错误"""
        login_data = {
            "username": created_admin_user["username"],
            "password": "wrongpassword"
        }
        
        response = client.post("/api/v1/auth/admin/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert "用户名或密码错误" in data["message"]
    
    def test_admin_login_nonexistent_user(self, client: TestClient, created_tenant):
        """测试登录不存在的管理员"""
        login_data = {
            "username": "username_nonexisten",
            "password": "password123"
        }
        
        response = client.post("/api/v1/auth/admin/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert "用户名或密码错误" in data["message"]
    
    def test_admin_login_inactive_user(self, client: TestClient, created_tenant, test_session):
        """测试登录已停用的管理员"""
        from app.features.users.models import User, UserRole, UserStatus
        from app.utils.security import get_password_hash
        from sqlalchemy import text
        
        # 设置租户上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_tenant['id']}'"))
        
        # 创建一个停用的用户
        inactive_user = User(
            username="inactiveadmin",
            password_hash=get_password_hash("password123"),
            real_name="停用管理员",
            role=UserRole.ADMIN,
            email="<EMAIL>",
            tenant_id=created_tenant["id"],
            status=UserStatus.INACTIVE
        )
        
        test_session.add(inactive_user)
        test_session.commit()
        
        # 尝试登录
        login_data = {
            "username": "inactiveadmin",
            "password": "password123"
        }
        
        response = client.post("/api/v1/auth/admin/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert "用户账户已被禁用" in data["message"]
    
    def test_superadmin_login(self, client: TestClient, test_session):
        """测试超级管理员登录"""
        from app.features.users.models import User, UserRole, UserStatus
        from app.utils.security import get_password_hash
        
        # 创建超级管理员
        super_admin = User(
            username="superadmin",
            password_hash=get_password_hash("superpassword"),
            real_name="超级管理员",
            role=UserRole.SUPER_ADMIN,
            email="<EMAIL>",
            tenant_id=None,  # 超级管理员没有租户
            status=UserStatus.ACTIVE
        )
        
        test_session.add(super_admin)
        test_session.commit()
        
        # 不提供tenant_code
        login_data = {
            "username": "superadmin",
            "password": "superpassword"
        }
        
        response = client.post("/api/v1/auth/admin/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "登录成功"
        
        auth_data = data["data"]
        assert "access_token" in auth_data
        assert auth_data["user"]["role"] == "super_admin"
        assert "tenant" in auth_data  # 超级管理员可以包含租户信息，但为 None
        assert auth_data["tenant"] is None  # 确保租户信息为 None


class TestMemberAuthAPI:
    """会员认证API集成测试"""
    
    def test_member_login_success(self, client: TestClient, created_member, created_tenant):
        """测试会员登录成功"""
        login_data = {
            "tenant_code": created_tenant["code"],
            "phone": created_member["phone"],
            "verification_code": "1234"  # 测试验证码
        }
        
        response = client.post("/api/v1/auth/member/login", json=login_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "登录成功"
        assert "data" in data
        
        auth_data = data["data"]
        assert "access_token" in auth_data
        assert auth_data["token_type"] == "bearer"
        assert "member" in auth_data
        assert auth_data["member"]["phone"] == created_member["phone"]
        assert "tenant" in auth_data
        assert auth_data["tenant"]["code"] == created_tenant["code"]
    
    def test_member_login_wrong_verification_code(self, client: TestClient, created_member, created_tenant):
        """测试会员登录验证码错误"""
        login_data = {
            "tenant_code": created_tenant["code"],
            "phone": created_member["phone"],
            "verification_code": "0000"  # 错误验证码
        }
        
        response = client.post("/api/v1/auth/member/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert "验证码错误" in data["message"]
    
    def test_member_login_nonexistent_member(self, client: TestClient, created_tenant):
        """测试登录不存在的会员"""
        login_data = {
            "tenant_code": created_tenant["code"],
            "phone": "18888888888",  # 不存在的手机号
            "verification_code": "1234"
        }
        
        response = client.post("/api/v1/auth/member/login", json=login_data)
        
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        assert "会员不存在" in data["message"]
    
    def test_member_login_nonexistent_tenant(self, client: TestClient, created_member):
        """测试会员使用不存在的租户登录"""
        login_data = {
            "tenant_code": "nonexistent",
            "phone": created_member["phone"],
            "verification_code": "1234"
        }
        
        response = client.post("/api/v1/auth/member/login", json=login_data)
        
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        assert "租户不存在" in data["message"]
    
    def test_member_login_inactive_member(self, client: TestClient, created_tenant, test_session):
        """测试登录已停用的会员"""
        from app.features.members.models import Member, MemberStatus, MemberType
        from sqlalchemy import text
        
        # 设置租户上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_tenant['id']}'"))
        
        # 创建一个停用的会员
        inactive_member = Member(
            name="停用会员",  # Member模型需要name字段，不是real_name
            phone="18999999999",
            tenant_id=created_tenant["id"],
            member_status=MemberStatus.FROZEN,
            member_type=MemberType.TRIAL
        )
        
        test_session.add(inactive_member)
        test_session.commit()
        
        # 尝试登录
        login_data = {
            "tenant_code": created_tenant["code"],
            "phone": "18999999999",
            "verification_code": "1234"
        }
        
        response = client.post("/api/v1/auth/member/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert data["success"] is False
        assert "会员账户已被禁用" in data["message"]


class TestAuthTokenValidation:
    """认证令牌验证测试"""
    
    def test_admin_token_validation(self, client: TestClient, created_admin_user, created_tenant):
        """测试管理员令牌验证"""
        # 先登录获取令牌
        login_data = {
            "username": created_admin_user["username"],
            "password": created_admin_user["password"]
        }
        
        response = client.post("/api/v1/auth/admin/login", json=login_data)
        assert response.status_code == 200
        
        token = response.json()["data"]["access_token"]
        
        # 使用令牌访问需要认证的接口
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/admin/users/", headers=headers)
        
        # 应该能成功访问
        assert response.status_code == 200
    
    def test_member_token_validation(self, client: TestClient, created_member, created_tenant):
        """测试会员令牌验证"""
        # 先登录获取令牌
        login_data = {
            "tenant_code": created_tenant["code"],
            "phone": created_member["phone"],
            "verification_code": "1234"
        }
        
        login_response = client.post("/api/v1/auth/member/login", json=login_data)
        assert login_response.status_code == 200
        
        token = login_response.json()["data"]["access_token"]
        
        # 使用令牌访问需要认证的接口（如果有会员专用接口）
        headers = {"Authorization": f"Bearer {token}"}
        # 这里可以测试会员专用的接口
        # response = client.get("/api/v1/admin/members/profile", headers=headers)
        # assert response.status_code == 200
    
    def test_invalid_token(self, client: TestClient):
        """测试无效令牌"""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/admin/users/", headers=headers)
        
        # 应该返回401
        assert response.status_code == 401
    
    def test_missing_token(self, client: TestClient):
        """测试缺少令牌"""
        response = client.get("/api/v1/admin/users/")
        
        # 应该返回401
        assert response.status_code == 401
    
    def test_cross_user_type_access(self, client: TestClient, created_member, created_tenant):
        """测试跨用户类型访问（会员令牌访问管理员接口）"""
        # 使用会员令牌登录
        login_data = {
            "tenant_code": created_tenant["code"],
            "phone": created_member["phone"],
            "verification_code": "1234"
        }
        
        login_response = client.post("/api/v1/auth/member/login", json=login_data)
        assert login_response.status_code == 200
        
        token = login_response.json()["data"]["access_token"]
        
        # 尝试使用会员令牌访问管理员接口
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/admin/users/", headers=headers)
        
        # 应该被拒绝（具体状态码取决于权限控制实现）
        assert response.status_code in [401, 403] 