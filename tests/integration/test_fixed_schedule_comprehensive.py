"""
固定课排课系统全面集成测试

测试范围：
1. 多模块协作测试：教师、会员、时间段、锁定、排课等模块的协作
2. 复杂业务场景测试：多教师多会员的复杂排课场景
3. 异常处理集成测试：各种异常情况的端到端处理
4. 数据一致性验证测试：跨模块的数据一致性检查
5. 性能基准集成测试：大规模数据下的性能表现
"""

import pytest
import time
from datetime import date, datetime, timedelta
from typing import List, Dict, Any
from starlette.testclient import TestClient

from tests.fixtures.database import test_session
from tests.fixtures.client import client
from tests.fixtures.business.tenant import created_tenant
from tests.fixtures.business.user import created_admin_user, admin_token


class TestFixedScheduleComprehensive:
    """固定课排课系统全面集成测试"""

    def test_multi_module_collaboration(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """测试多模块协作：验证教师、会员、时间段、锁定、排课模块的协作"""
        print("\n🎯 测试多模块协作集成")
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        
        # 第一阶段：创建基础数据
        print("📋 阶段1: 创建基础数据")
        
        # 1.1 创建多个教师（不同地区和价格）
        teachers = []
        teacher_configs = [
            {"region": "europe", "category": "european", "price": 150},
            {"region": "south_africa", "category": "south_african", "price": 120},
            {"region": "philippines", "category": "filipino", "price": 80},
        ]
        
        for i, config in enumerate(teacher_configs):
            teacher_data = {
                "name": f"协作测试教师_{config['region']}_{int(time.time())}_{i}",
                "email": f"collab_teacher_{config['region']}_{int(time.time())}_{i}@test.com",
                "phone": f"138{(int(time.time()) + i) % 100000000:08d}",
                "specialties": ["英语口语"],
                "introduction": f"{config['region']}地区教师",
                "price_per_class": config["price"],
                "teacher_category": config["category"],
                "region": config["region"],
                "display_code": f"T{int(time.time()) % 10000 + i:04d}"
            }
            
            response = client.post("/api/v1/admin/teachers/", json=teacher_data, headers=headers)
            assert response.status_code == 201, f"创建教师失败: {response.json()}"
            teacher = response.json()["data"]
            
            # 激活教师
            response = client.post(f"/api/v1/admin/teachers/{teacher['id']}/activate", headers=headers)
            assert response.status_code == 200, f"激活教师失败: {response.json()}"
            teacher = response.json()["data"]
            
            teachers.append(teacher)
        
        print(f"✅ 创建并激活了 {len(teachers)} 个教师")
        
        # 1.2 创建多个会员
        members = []
        for i in range(5):
            member_data = {
                "name": f"协作测试会员_{int(time.time())}_{i}",
                "phone": f"139{(int(time.time()) + i) % 100000000:08d}",
                "email": f"collab_member_{int(time.time())}_{i}@test.com",
                "gender": "male" if i % 2 == 0 else "female",
                "age": 20 + i,
                "level": "intermediate",
                "timezone": "Asia/Shanghai"
            }
            
            response = client.post("/api/v1/admin/members/", json=member_data, headers=headers)
            assert response.status_code == 201, f"创建会员失败: {response.json()}"
            member = response.json()["data"]
            
            # 为会员充值
            recharge_data = {
                "member_card_id": member['primary_member_card_id'],
                "amount": 10000,  # 充值100元
                "bonus_amount": 0,
                "payment_method": "wechat",
                "notes": "协作测试充值"
            }
            
            response = client.post(
                f"/api/v1/admin/member-cards/cards/{member['primary_member_card_id']}/recharge",
                json=recharge_data,
                headers=headers
            )
            assert response.status_code == 200, f"会员充值失败: {response.json()}"
            
            members.append(member)
        
        print(f"✅ 创建并充值了 {len(members)} 个会员")
        
        # 第二阶段：创建时间段和锁定关系
        print("📋 阶段2: 创建时间段和锁定关系")
        
        # 2.1 为每个教师创建多个固定时间段
        teacher_slots = []
        weekdays = [1, 2, 3, 4, 5]  # 周一到周五
        time_slots = ["09:00", "10:00", "11:00", "14:00", "15:00"]
        
        for teacher in teachers:
            for weekday in weekdays[:2]:  # 每个教师只创建2天的时间段
                for time_slot in time_slots[:3]:  # 每天3个时间段
                    slot_data = {
                        "teacher_id": teacher["id"],
                        "weekday": weekday,
                        "start_time": time_slot,
                        "duration_minutes": 25,
                        "is_available": True,
                        "is_visible_to_members": True,
                        "created_by": created_admin_user["id"]
                    }
                    
                    response = client.post("/api/v1/admin/teachers/fixed-slots/", json=slot_data, headers=headers)
                    assert response.status_code == 201, f"创建教师固定时间段失败: {response.json()}"
                    slot = response.json()["data"]
                    teacher_slots.append(slot)
        
        print(f"✅ 创建了 {len(teacher_slots)} 个教师固定时间段")
        
        # 2.2 创建会员固定位锁定（每个会员锁定2-3个时间段）
        member_locks = []
        slot_index = 0
        
        for member in members:
            # 每个会员锁定2-3个不同教师的时间段
            locks_per_member = 2 + (len(member_locks) % 2)  # 2或3个锁定
            
            for _ in range(locks_per_member):
                if slot_index < len(teacher_slots):
                    slot = teacher_slots[slot_index]
                    slot_index += 1
                    
                    lock_data = {
                        "member_id": member["id"],
                        "teacher_fixed_slot_id": slot["id"],
                        "status": "active",
                        "created_by": created_admin_user["id"]
                    }
                    
                    response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=headers)
                    assert response.status_code == 201, f"创建会员固定位锁定失败: {response.json()}"
                    lock = response.json()["data"]
                    member_locks.append(lock)
        
        print(f"✅ 创建了 {len(member_locks)} 个会员固定位锁定")
        
        # 第三阶段：执行排课并验证协作
        print("📋 阶段3: 执行排课并验证模块协作")
        
        # 3.1 创建排课任务
        today = date.today()
        days_ahead = 7 - today.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        next_monday = today + timedelta(days=days_ahead)
        
        task_data = {
            "task_name": f"多模块协作测试任务_{int(time.time())}",
            "start_date": next_monday.isoformat(),
            "end_date": (next_monday + timedelta(days=13)).isoformat(),  # 2周
            "teacher_ids": [t["id"] for t in teachers],
            "teacher_priority_rule": "region_first",
            "balance_insufficient_action": "skip",
            "description": "多模块协作集成测试任务"
        }
        
        response = client.post("/api/v1/admin/fixed-schedule/tasks", json=task_data, headers=headers)
        assert response.status_code == 201, f"创建排课任务失败: {response.json()}"
        task = response.json()["data"]
        
        # 3.2 执行排课任务
        response = client.post(
            f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/execute",
            headers=headers
        )
        assert response.status_code == 200, f"执行排课任务失败: {response.json()}"
        
        # 3.3 等待任务完成
        max_wait_time = 30
        wait_time = 0
        
        while wait_time < max_wait_time:
            response = client.get(
                f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/status",
                headers=headers
            )
            assert response.status_code == 200
            
            status_data = response.json()["data"]
            if status_data["status"] in ["completed", "failed"]:
                break
            
            time.sleep(2)
            wait_time += 2
        
        # 第四阶段：验证模块协作结果
        print("📋 阶段4: 验证模块协作结果")
        
        # 4.1 验证排课任务结果
        response = client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}", headers=headers)
        assert response.status_code == 200
        task_detail = response.json()["data"]
        
        assert task_detail["status"] == "completed", f"排课任务应该完成，当前状态: {task_detail['status']}"
        assert task_detail["total_teachers"] == len(teachers), "应该处理所有教师"
        assert task_detail["successful_teachers"] > 0, "应该有成功排课的教师"
        assert task_detail["total_classes"] > 0, "应该生成课程"
        
        print(f"✅ 排课任务完成：{task_detail['successful_teachers']}/{task_detail['total_teachers']} 教师成功，生成 {task_detail['total_classes']} 节课程")
        
        # 4.2 验证会员余额扣减
        total_deducted = 0
        for member in members:
            response = client.get(
                f"/api/v1/admin/member-cards/cards/{member['primary_member_card_id']}",
                headers=headers
            )
            assert response.status_code == 200
            card = response.json()["data"]
            
            # 原始余额：默认1000 + 充值10000 = 11000
            original_balance = 11000
            current_balance = card["balance"]
            deducted = original_balance - current_balance
            total_deducted += deducted
            
            if deducted > 0:
                print(f"   会员 {member['name']}: 扣减 {deducted} 元")
        
        assert total_deducted > 0, "应该有会员余额被扣减"
        print(f"✅ 总计扣减会员余额: {total_deducted} 元")
        
        # 4.3 验证数据一致性
        assert task_detail["total_amount"] == total_deducted, f"任务统计金额({task_detail['total_amount']})应该等于实际扣减金额({total_deducted})"
        
        print("✅ 多模块协作集成测试通过")
        
        return {
            "teachers": teachers,
            "members": members,
            "teacher_slots": teacher_slots,
            "member_locks": member_locks,
            "task": task_detail,
            "total_deducted": total_deducted
        }

    def test_complex_business_scenarios(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """测试复杂业务场景：多教师多会员的复杂排课场景"""
        print("\n🎯 测试复杂业务场景集成")

        headers = {"Authorization": f"Bearer {admin_token}"}

        # 场景1：教师优先级排序验证
        print("📋 场景1: 教师优先级排序验证")

        # 创建不同地区的教师，验证排课优先级
        teachers = []
        teacher_configs = [
            {"region": "philippines", "category": "filipino", "price": 80, "display_code": "T9999"},  # 低优先级
            {"region": "europe", "category": "european", "price": 150, "display_code": "T0001"},     # 高优先级
            {"region": "south_africa", "category": "south_african", "price": 120, "display_code": "T5000"}, # 中优先级
        ]

        for i, config in enumerate(teacher_configs):
            teacher_data = {
                "name": f"优先级测试教师_{config['region']}_{i}",
                "email": f"priority_teacher_{config['region']}_{i}@test.com",
                "phone": f"138{(12345 + i):08d}",
                "specialties": ["英语口语"],
                "introduction": f"{config['region']}地区教师",
                "price_per_class": config["price"],
                "teacher_category": config["category"],
                "region": config["region"],
                "display_code": config["display_code"]
            }

            response = client.post("/api/v1/admin/teachers/", json=teacher_data, headers=headers)
            assert response.status_code == 201
            teacher = response.json()["data"]

            # 激活教师
            response = client.post(f"/api/v1/admin/teachers/{teacher['id']}/activate", headers=headers)
            assert response.status_code == 200
            teacher = response.json()["data"]

            teachers.append(teacher)

        # 创建会员
        member_data = {
            "name": f"优先级测试会员_{int(time.time())}",
            "phone": f"139{12345678:08d}",
            "email": f"<EMAIL>",
            "gender": "male",
            "age": 25,
            "level": "intermediate",
            "timezone": "Asia/Shanghai"
        }

        response = client.post("/api/v1/admin/members/", json=member_data, headers=headers)
        assert response.status_code == 201
        member = response.json()["data"]

        # 充值
        recharge_data = {
            "member_card_id": member['primary_member_card_id'],
            "amount": 50000,  # 充值500元，足够支付所有课程
            "bonus_amount": 0,
            "payment_method": "wechat",
            "notes": "优先级测试充值"
        }

        response = client.post(
            f"/api/v1/admin/member-cards/cards/{member['primary_member_card_id']}/recharge",
            json=recharge_data,
            headers=headers
        )
        assert response.status_code == 200

        # 为每个教师创建相同的时间段
        teacher_slots = []
        for teacher in teachers:
            slot_data = {
                "teacher_id": teacher["id"],
                "weekday": 1,  # 周一
                "start_time": "09:00",
                "duration_minutes": 25,
                "is_available": True,
                "is_visible_to_members": True,
                "created_by": created_admin_user["id"]
            }

            response = client.post("/api/v1/admin/teachers/fixed-slots/", json=slot_data, headers=headers)
            assert response.status_code == 201
            slot = response.json()["data"]
            teacher_slots.append(slot)

        # 会员锁定所有教师的时间段
        member_locks = []
        for slot in teacher_slots:
            lock_data = {
                "member_id": member["id"],
                "teacher_fixed_slot_id": slot["id"],
                "status": "active",
                "created_by": created_admin_user["id"]
            }

            response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=headers)
            assert response.status_code == 201
            lock = response.json()["data"]
            member_locks.append(lock)

        # 执行排课
        today = date.today()
        days_ahead = 7 - today.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        next_monday = today + timedelta(days=days_ahead)

        task_data = {
            "task_name": f"优先级测试任务_{int(time.time())}",
            "start_date": next_monday.isoformat(),
            "end_date": (next_monday + timedelta(days=6)).isoformat(),  # 1周
            "teacher_ids": [t["id"] for t in teachers],
            "teacher_priority_rule": "region_first",  # 按地区优先级排序
            "balance_insufficient_action": "skip",
            "description": "教师优先级排序验证测试"
        }

        response = client.post("/api/v1/admin/fixed-schedule/tasks", json=task_data, headers=headers)
        assert response.status_code == 201
        task = response.json()["data"]

        # 执行排课
        response = client.post(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/execute", headers=headers)
        assert response.status_code == 200

        # 等待完成
        max_wait_time = 20
        wait_time = 0

        while wait_time < max_wait_time:
            response = client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/status", headers=headers)
            assert response.status_code == 200

            status_data = response.json()["data"]
            if status_data["status"] in ["completed", "failed"]:
                break

            time.sleep(2)
            wait_time += 2

        # 验证排课结果
        response = client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}", headers=headers)
        assert response.status_code == 200
        task_detail = response.json()["data"]

        assert task_detail["status"] == "completed", "排课任务应该完成"
        assert task_detail["total_classes"] > 0, "应该生成课程"

        # 验证优先级：欧美教师应该优先被排课
        response = client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/logs", headers=headers)
        assert response.status_code == 200
        logs_data = response.json()

        # 查找教师排课顺序的日志
        teacher_order_logs = []
        for log in logs_data.get("data", []):
            message = log.get("message", "")
            if "开始为教师" in message and "执行排课" in message:
                teacher_order_logs.append(message)

        # 验证欧美教师（europe）应该在菲教（philippines）之前被处理
        europe_teacher_log = None
        philippines_teacher_log = None

        for i, log_message in enumerate(teacher_order_logs):
            if "europe" in log_message or "欧美" in log_message:
                europe_teacher_log = i
            elif "philippines" in log_message or "菲教" in log_message:
                philippines_teacher_log = i

        if europe_teacher_log is not None and philippines_teacher_log is not None:
            assert europe_teacher_log < philippines_teacher_log, "欧美教师应该在菲教之前被处理"

        print("✅ 教师优先级排序验证通过")

        return {
            "teachers": teachers,
            "member": member,
            "task": task_detail
        }

    def test_exception_handling_integration(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """测试异常处理集成：各种异常情况的端到端处理"""
        print("\n🎯 测试异常处理集成")

        headers = {"Authorization": f"Bearer {admin_token}"}

        # 场景1：余额不足处理
        print("📋 场景1: 余额不足异常处理")

        # 创建教师
        teacher_data = {
            "name": f"异常测试教师_{int(time.time())}",
            "email": f"<EMAIL>",
            "phone": f"138{87654321:08d}",
            "specialties": ["英语口语"],
            "introduction": "异常处理测试教师",
            "price_per_class": 200,  # 高价格
            "teacher_category": "european",
            "region": "europe",
            "display_code": f"T{8888:04d}"
        }

        response = client.post("/api/v1/admin/teachers/", json=teacher_data, headers=headers)
        assert response.status_code == 201
        teacher = response.json()["data"]

        # 激活教师
        response = client.post(f"/api/v1/admin/teachers/{teacher['id']}/activate", headers=headers)
        assert response.status_code == 200
        teacher = response.json()["data"]

        # 创建会员（余额不足）
        member_data = {
            "name": f"余额不足测试会员_{int(time.time())}",
            "phone": f"139{87654321:08d}",
            "email": f"<EMAIL>",
            "gender": "female",
            "age": 30,
            "level": "beginner",
            "timezone": "Asia/Shanghai"
        }

        response = client.post("/api/v1/admin/members/", json=member_data, headers=headers)
        assert response.status_code == 201
        member = response.json()["data"]

        # 只充值少量金额（不足以支付4周课程）
        recharge_data = {
            "member_card_id": member['primary_member_card_id'],
            "amount": 500,  # 只充值5元，不足以支付高价课程
            "bonus_amount": 0,
            "payment_method": "wechat",
            "notes": "余额不足测试充值"
        }

        response = client.post(
            f"/api/v1/admin/member-cards/cards/{member['primary_member_card_id']}/recharge",
            json=recharge_data,
            headers=headers
        )
        assert response.status_code == 200

        # 创建教师时间段
        slot_data = {
            "teacher_id": teacher["id"],
            "weekday": 1,  # 周一
            "start_time": "09:00",
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": created_admin_user["id"]
        }

        response = client.post("/api/v1/admin/teachers/fixed-slots/", json=slot_data, headers=headers)
        assert response.status_code == 201
        slot = response.json()["data"]

        # 创建会员锁定
        lock_data = {
            "member_id": member["id"],
            "teacher_fixed_slot_id": slot["id"],
            "status": "active",
            "created_by": created_admin_user["id"]
        }

        response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=headers)
        assert response.status_code == 201
        lock = response.json()["data"]

        # 执行排课（应该处理余额不足）
        today = date.today()
        days_ahead = 7 - today.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        next_monday = today + timedelta(days=days_ahead)

        task_data = {
            "task_name": f"余额不足测试任务_{int(time.time())}",
            "start_date": next_monday.isoformat(),
            "end_date": (next_monday + timedelta(days=27)).isoformat(),  # 4周，需要大量费用
            "teacher_ids": [teacher["id"]],
            "teacher_priority_rule": "region_first",
            "balance_insufficient_action": "skip",  # 跳过余额不足的会员
            "description": "余额不足异常处理测试"
        }

        response = client.post("/api/v1/admin/fixed-schedule/tasks", json=task_data, headers=headers)
        assert response.status_code == 201
        task = response.json()["data"]

        # 执行排课
        response = client.post(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/execute", headers=headers)
        assert response.status_code == 200

        # 等待完成
        max_wait_time = 20
        wait_time = 0

        while wait_time < max_wait_time:
            response = client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/status", headers=headers)
            assert response.status_code == 200

            status_data = response.json()["data"]
            if status_data["status"] in ["completed", "failed"]:
                break

            time.sleep(2)
            wait_time += 2

        # 验证异常处理结果
        response = client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}", headers=headers)
        assert response.status_code == 200
        task_detail = response.json()["data"]

        assert task_detail["status"] == "completed", "任务应该完成（跳过余额不足的会员）"

        # 验证日志中包含余额不足的警告
        response = client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/logs", headers=headers)
        assert response.status_code == 200
        logs_data = response.json()

        insufficient_balance_logs = []
        for log in logs_data.get("data", []):
            message = log.get("message", "")
            if "余额不足" in message or "insufficient" in message.lower():
                insufficient_balance_logs.append(message)

        assert len(insufficient_balance_logs) > 0, "应该有余额不足的日志记录"
        print(f"✅ 余额不足异常处理验证通过，发现 {len(insufficient_balance_logs)} 条相关日志")

        # 场景2：数据验证异常处理
        print("📋 场景2: 数据验证异常处理")

        # 尝试创建无效的排课任务
        invalid_task_data = {
            "task_name": "",  # 空名称
            "start_date": "invalid-date",  # 无效日期
            "end_date": next_monday.isoformat(),
            "teacher_ids": [99999],  # 不存在的教师ID
            "teacher_priority_rule": "invalid_rule",  # 无效规则
            "balance_insufficient_action": "invalid_action",  # 无效动作
            "description": "数据验证异常测试"
        }

        response = client.post("/api/v1/admin/fixed-schedule/tasks", json=invalid_task_data, headers=headers)
        assert response.status_code == 422, "应该返回数据验证错误"

        error_data = response.json()
        assert "detail" in error_data, "应该包含错误详情"

        print("✅ 数据验证异常处理验证通过")

        return {
            "teacher": teacher,
            "member": member,
            "task": task_detail,
            "insufficient_balance_logs": insufficient_balance_logs
        }

    def test_data_consistency_validation(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """测试数据一致性验证：验证排课过程中的数据一致性"""
        print("\n🎯 测试数据一致性验证")

        headers = {"Authorization": f"Bearer {admin_token}"}

        # 创建测试数据
        print("📋 阶段1: 创建测试数据")

        # 创建教师
        teacher_data = {
            "name": f"一致性测试教师_{int(time.time())}",
            "email": f"<EMAIL>",
            "phone": f"138{11111111:08d}",
            "specialties": ["英语口语"],
            "introduction": "数据一致性测试教师",
            "price_per_class": 100,
            "teacher_category": "european",
            "region": "europe",
            "display_code": f"T{1111:04d}"
        }

        response = client.post("/api/v1/admin/teachers/", json=teacher_data, headers=headers)
        assert response.status_code == 201
        teacher = response.json()["data"]

        # 激活教师
        response = client.post(f"/api/v1/admin/teachers/{teacher['id']}/activate", headers=headers)
        assert response.status_code == 200
        teacher = response.json()["data"]

        # 创建会员
        member_data = {
            "name": f"一致性测试会员_{int(time.time())}",
            "phone": f"139{11111111:08d}",
            "email": f"<EMAIL>",
            "gender": "male",
            "age": 25,
            "level": "intermediate",
            "timezone": "Asia/Shanghai"
        }

        response = client.post("/api/v1/admin/members/", json=member_data, headers=headers)
        assert response.status_code == 201
        member = response.json()["data"]

        # 记录初始余额
        response = client.get(
            f"/api/v1/admin/member-cards/cards/{member['primary_member_card_id']}",
            headers=headers
        )
        assert response.status_code == 200
        initial_card = response.json()["data"]
        initial_balance = initial_card["balance"]

        # 充值
        recharge_amount = 5000  # 充值50元
        recharge_data = {
            "member_card_id": member['primary_member_card_id'],
            "amount": recharge_amount,
            "bonus_amount": 0,
            "payment_method": "wechat",
            "notes": "一致性测试充值"
        }

        response = client.post(
            f"/api/v1/admin/member-cards/cards/{member['primary_member_card_id']}/recharge",
            json=recharge_data,
            headers=headers
        )
        assert response.status_code == 200

        # 验证充值后余额
        response = client.get(
            f"/api/v1/admin/member-cards/cards/{member['primary_member_card_id']}",
            headers=headers
        )
        assert response.status_code == 200
        after_recharge_card = response.json()["data"]
        after_recharge_balance = after_recharge_card["balance"]

        expected_balance = initial_balance + recharge_amount
        assert after_recharge_balance == expected_balance, f"充值后余额应该是 {expected_balance}，实际是 {after_recharge_balance}"

        # 创建教师时间段
        slot_data = {
            "teacher_id": teacher["id"],
            "weekday": 1,  # 周一
            "start_time": "09:00",
            "duration_minutes": 25,
            "is_available": True,
            "is_visible_to_members": True,
            "created_by": created_admin_user["id"]
        }

        response = client.post("/api/v1/admin/teachers/fixed-slots/", json=slot_data, headers=headers)
        assert response.status_code == 201
        slot = response.json()["data"]

        # 创建会员锁定
        lock_data = {
            "member_id": member["id"],
            "teacher_fixed_slot_id": slot["id"],
            "status": "active",
            "created_by": created_admin_user["id"]
        }

        response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=headers)
        assert response.status_code == 201
        lock = response.json()["data"]

        print("✅ 测试数据创建完成")

        # 执行排课
        print("📋 阶段2: 执行排课")

        today = date.today()
        days_ahead = 7 - today.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        next_monday = today + timedelta(days=days_ahead)

        task_data = {
            "task_name": f"一致性测试任务_{int(time.time())}",
            "start_date": next_monday.isoformat(),
            "end_date": (next_monday + timedelta(days=13)).isoformat(),  # 2周
            "teacher_ids": [teacher["id"]],
            "teacher_priority_rule": "region_first",
            "balance_insufficient_action": "skip",
            "description": "数据一致性验证测试"
        }

        response = client.post("/api/v1/admin/fixed-schedule/tasks", json=task_data, headers=headers)
        assert response.status_code == 201
        task = response.json()["data"]

        # 执行排课
        response = client.post(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/execute", headers=headers)
        assert response.status_code == 200

        # 等待完成
        max_wait_time = 20
        wait_time = 0

        while wait_time < max_wait_time:
            response = client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/status", headers=headers)
            assert response.status_code == 200

            status_data = response.json()["data"]
            if status_data["status"] in ["completed", "failed"]:
                break

            time.sleep(2)
            wait_time += 2

        # 验证数据一致性
        print("📋 阶段3: 验证数据一致性")

        # 3.1 获取排课任务结果
        response = client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}", headers=headers)
        assert response.status_code == 200
        task_detail = response.json()["data"]

        assert task_detail["status"] == "completed", "排课任务应该完成"

        # 3.2 验证会员余额扣减一致性
        response = client.get(
            f"/api/v1/admin/member-cards/cards/{member['primary_member_card_id']}",
            headers=headers
        )
        assert response.status_code == 200
        final_card = response.json()["data"]
        final_balance = final_card["balance"]

        actual_deducted = after_recharge_balance - final_balance
        expected_deducted = task_detail["total_amount"]

        assert actual_deducted == expected_deducted, f"实际扣减金额({actual_deducted})应该等于任务统计金额({expected_deducted})"

        # 3.3 验证课程生成数量一致性
        expected_classes = task_detail["total_classes"]
        assert expected_classes > 0, "应该生成课程"

        # 3.4 验证会员卡统计数据一致性
        total_consumed = final_card["total_consumed"]
        expected_total_consumed = actual_deducted

        assert total_consumed >= expected_total_consumed, f"会员卡总消费({total_consumed})应该包含本次扣减({expected_total_consumed})"

        # 3.5 验证日志记录一致性
        response = client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/logs", headers=headers)
        assert response.status_code == 200
        logs_data = response.json()

        # 检查关键日志是否存在
        key_log_types = {
            "task_created": False,
            "task_started": False,
            "teacher_processed": False,
            "member_processed": False,
            "balance_deducted": False,
            "classes_created": False,
            "task_completed": False
        }

        for log in logs_data.get("data", []):
            message = log.get("message", "").lower()

            if "创建排课任务" in message:
                key_log_types["task_created"] = True
            elif "开始执行" in message:
                key_log_types["task_started"] = True
            elif "开始为教师" in message:
                key_log_types["teacher_processed"] = True
            elif "成功生成" in message and "课程" in message:
                key_log_types["classes_created"] = True
            elif "扣除" in message and "余额" in message:
                key_log_types["balance_deducted"] = True
            elif "排课完成" in message:
                key_log_types["task_completed"] = True

        missing_logs = [log_type for log_type, found in key_log_types.items() if not found]
        assert len(missing_logs) == 0, f"缺少关键日志类型: {missing_logs}"

        print("✅ 数据一致性验证通过")
        print(f"   - 余额扣减一致性: {actual_deducted} 元")
        print(f"   - 课程生成数量: {expected_classes} 节")
        print(f"   - 日志记录完整性: 所有关键日志都存在")

        return {
            "teacher": teacher,
            "member": member,
            "task": task_detail,
            "initial_balance": initial_balance,
            "final_balance": final_balance,
            "actual_deducted": actual_deducted,
            "expected_deducted": expected_deducted
        }
