"""固定课排课服务层单元测试"""

import pytest
from datetime import datetime, date, time
from unittest.mock import Mock, patch
from sqlmodel import Session

from app.features.courses.scheduling.service import (
    FixedScheduleTaskService, FixedScheduleTaskLogService, FixedScheduleTaskExecutor
)
from app.features.courses.scheduling.schemas import (
    FixedScheduleTaskCreate, FixedScheduleTaskUpdate, FixedScheduleTaskQuery,
    FixedScheduleTaskLogCreate, FixedScheduleTaskLogQuery
)
from app.features.courses.scheduling.models import (
    FixedScheduleTask, FixedScheduleTaskLog, ScheduleTaskStatus,
    TeacherPriorityRule, BalanceInsufficientAction, LogLevel, OperationType
)
from app.features.courses.scheduling.exceptions import (
    SchedulingTaskNotFoundError, SchedulingBusinessException
)


class TestFixedScheduleTaskService:
    """固定课排课任务服务测试类"""
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def service(self, mock_session):
        """创建服务实例"""
        return FixedScheduleTaskService(mock_session, tenant_id=1)
    
    @pytest.fixture
    def sample_task_data(self):
        """示例任务创建数据"""
        return FixedScheduleTaskCreate(
            task_name="测试排课任务",
            start_date=date(2024, 1, 1),  # 周一
            weeks_count=4,
            teacher_ids=[1, 2],
            teacher_priority_rule=TeacherPriorityRule.REGION_FIRST,
            balance_insufficient_action=BalanceInsufficientAction.SKIP,
            interrupt_on_conflict=False,
            remark="测试备注"
        )
    
    @pytest.fixture
    def sample_task(self):
        """示例任务对象"""
        return FixedScheduleTask(
            id=1,
            tenant_id=1,
            task_name="测试排课任务",
            start_date=datetime(2024, 1, 1),
            weeks_count=4,
            teacher_ids='[1, 2]',
            teacher_priority_rule=TeacherPriorityRule.REGION_FIRST,
            balance_insufficient_action=BalanceInsufficientAction.SKIP,
            interrupt_on_conflict=False,
            status=ScheduleTaskStatus.PENDING,
            created_by=1,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    def test_service_initialization(self, service):
        """测试服务初始化"""
        assert service.tenant_id == 1
        assert service.model_class == FixedScheduleTask
    
    def test_create_task_success(self, service, sample_task_data, mock_session):
        """测试创建任务成功"""
        # Mock验证函数
        with patch('app.features.courses.scheduling.service.validate_scheduling_params') as mock_validate:
            mock_validate.return_value = []  # 无验证错误
            
            # Mock数据库操作
            mock_session.add = Mock()
            mock_session.commit = Mock()
            mock_session.refresh = Mock()
            
            # 创建任务
            task = service.create_task(sample_task_data, created_by=1)
            
            # 验证结果
            assert task.task_name == "测试排课任务"
            assert task.tenant_id == 1
            assert task.status == ScheduleTaskStatus.PENDING
            assert task.created_by == 1
            
            # 验证数据库操作（会创建任务和日志，所以add被调用两次）
            assert mock_session.add.call_count == 2
            mock_session.commit.assert_called()
            mock_session.refresh.assert_called()
    
    def test_create_task_validation_error(self, service, sample_task_data):
        """测试创建任务验证失败"""
        # Mock验证函数返回错误
        with patch('app.features.courses.scheduling.service.validate_scheduling_params') as mock_validate:
            mock_validate.return_value = ["开始日期必须是周一"]
            
            # 应该抛出业务异常
            with pytest.raises(SchedulingBusinessException):
                service.create_task(sample_task_data, created_by=1)
    
    def test_get_task_success(self, service, sample_task, mock_session):
        """测试获取任务成功"""
        # Mock数据库查询
        mock_session.get.return_value = sample_task
        
        result = service.get_task(1)
        
        assert result == sample_task
        mock_session.get.assert_called_once_with(FixedScheduleTask, 1)
    
    def test_get_task_not_found(self, service, mock_session):
        """测试获取任务不存在"""
        # Mock数据库查询返回None
        mock_session.get.return_value = None
        
        with pytest.raises(SchedulingTaskNotFoundError):
            service.get_task(999)
    
    def test_update_task_success(self, service, sample_task, mock_session):
        """测试更新任务成功"""
        # Mock数据库查询
        mock_session.get.return_value = sample_task
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        update_data = FixedScheduleTaskUpdate(
            task_name="更新后的任务名称",
            remark="更新后的备注"
        )
        
        result = service.update_task(1, update_data)
        
        assert result.task_name == "更新后的任务名称"
        assert result.remark == "更新后的备注"
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
    
    def test_delete_task_success(self, service, sample_task, mock_session):
        """测试删除任务成功"""
        # Mock数据库查询
        mock_session.get.return_value = sample_task
        mock_session.delete = Mock()
        mock_session.commit = Mock()

        # Mock日志查询返回空列表
        mock_session.exec.return_value.all.return_value = []

        service.delete_task(1)

        # 验证数据库操作
        mock_session.delete.assert_called_once_with(sample_task)
        mock_session.commit.assert_called()
    
    def test_query_tasks_basic(self, service, mock_session):
        """测试基本任务查询"""
        # Mock查询结果
        mock_tasks = [Mock(), Mock()]
        mock_total = 2
        
        # 设置mock_session.exec().all()返回mock_tasks
        mock_session.exec.return_value.all.return_value = mock_tasks
        # 设置mock_session.exec().one()返回mock_total
        mock_session.exec.return_value.one.return_value = mock_total
        
        query = FixedScheduleTaskQuery(
            task_name="测试",
            status=ScheduleTaskStatus.PENDING,
            page=1,
            size=20
        )
        
        tasks, total = service.query_tasks(query)
        
        assert tasks == mock_tasks
        assert total == mock_total
        # 验证exec被调用
        assert mock_session.exec.call_count >= 2
    
    def test_get_task_statistics_basic(self, service):
        """测试获取任务统计基本功能"""
        # 测试统计方法存在
        assert hasattr(service, 'get_task_statistics')
        assert callable(getattr(service, 'get_task_statistics'))


class TestFixedScheduleTaskLogService:
    """固定课排课任务日志服务测试类"""
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def log_service(self, mock_session):
        """创建日志服务实例"""
        return FixedScheduleTaskLogService(mock_session, tenant_id=1)
    
    @pytest.fixture
    def sample_log_data(self):
        """示例日志创建数据"""
        return FixedScheduleTaskLogCreate(
            task_id=1,
            log_level=LogLevel.INFO,
            message="测试日志消息",
            teacher_id=1,
            member_id=1,
            operation_type=OperationType.CLASS_CREATE
        )
    
    def test_log_service_initialization(self, log_service):
        """测试日志服务初始化"""
        assert log_service.tenant_id == 1
        assert log_service.model_class == FixedScheduleTaskLog
    
    def test_create_log_success(self, log_service, sample_log_data, mock_session):
        """测试创建日志成功"""
        # Mock数据库操作
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        log = log_service.create_log(sample_log_data)
        
        assert log.task_id == 1
        assert log.log_level == LogLevel.INFO
        assert log.message == "测试日志消息"
        assert log.tenant_id == 1
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
    
    def test_query_logs_basic(self, log_service, mock_session):
        """测试基本日志查询"""
        # Mock查询结果
        mock_logs = [Mock(), Mock()]
        mock_total = 2
        
        # 设置mock_session.exec().all()返回mock_logs
        mock_session.exec.return_value.all.return_value = mock_logs
        # 设置mock_session.exec().one()返回mock_total
        mock_session.exec.return_value.one.return_value = mock_total
        
        query = FixedScheduleTaskLogQuery(
            task_id=1,
            log_level=LogLevel.INFO,
            page=1,
            size=50
        )
        
        logs, total = log_service.query_logs(query)
        
        assert logs == mock_logs
        assert total == mock_total
        # 验证exec被调用
        assert mock_session.exec.call_count >= 2


class TestFixedScheduleTaskExecutor:
    """固定课排课任务执行器测试类"""
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def executor(self, mock_session):
        """创建执行器实例"""
        return FixedScheduleTaskExecutor(mock_session, tenant_id=1)
    
    @pytest.fixture
    def sample_task(self):
        """示例任务对象"""
        return FixedScheduleTask(
            id=1,
            tenant_id=1,
            task_name="测试排课任务",
            start_date=datetime(2024, 1, 1),
            weeks_count=4,
            teacher_ids='[1, 2]',
            teacher_priority_rule=TeacherPriorityRule.REGION_FIRST,
            balance_insufficient_action=BalanceInsufficientAction.SKIP,
            interrupt_on_conflict=False,
            status=ScheduleTaskStatus.PENDING,
            created_by=1,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    def test_executor_initialization(self, executor):
        """测试执行器初始化"""
        assert executor.tenant_id == 1
    
    def test_execute_task_basic(self, executor):
        """测试执行任务基本功能"""
        # 测试执行方法存在
        assert hasattr(executor, 'execute_task')
        assert callable(getattr(executor, 'execute_task'))
    
    def test_execute_task_not_found(self, executor, mock_session):
        """测试执行不存在的任务"""
        # Mock数据库查询返回None
        mock_session.get.return_value = None
        
        with pytest.raises(SchedulingTaskNotFoundError):
            executor.execute_task(999, force_restart=False)
    
    def test_get_task_execution_status_basic(self, executor):
        """测试获取任务执行状态基本功能"""
        # 测试状态查询方法存在
        assert hasattr(executor, 'get_task_execution_status')
        assert callable(getattr(executor, 'get_task_execution_status'))
