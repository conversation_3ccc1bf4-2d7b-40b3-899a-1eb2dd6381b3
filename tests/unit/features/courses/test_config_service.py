"""课程系统配置服务层单元测试"""
import pytest
from datetime import time, datetime, timezone
from sqlmodel import Session
from sqlalchemy import text

from app.features.courses.config_service import CourseSystemConfigService
from app.features.courses.config_schemas import CourseSystemConfigCreate, CourseSystemConfigUpdate
from app.features.courses.config_exceptions import CourseConfigNotFoundError, CourseConfigBusinessException


class TestCourseSystemConfigService:
    """课程系统配置服务测试"""

    def test_create_config_success(self, test_session: Session, created_admin_user, sample_course_config_data):
        """测试成功创建配置"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
        config_create = CourseSystemConfigCreate(**sample_course_config_data)
        
        config = service.create_config(config_create, created_by=created_admin_user["id"])
        
        assert config is not None
        assert config.tenant_id == created_admin_user["tenant_id"]
        assert config.default_slot_duration_minutes == sample_course_config_data["default_slot_duration_minutes"]
        assert config.direct_booking_enabled == sample_course_config_data["direct_booking_enabled"]
        assert config.created_by == created_admin_user["id"]
        assert config.created_at is not None
        assert config.updated_at is not None

    def test_create_config_returns_existing_if_exists(self, test_session: Session, created_course_config):
        """测试创建配置时如果已存在则返回现有配置"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        config_create = CourseSystemConfigCreate(default_slot_duration_minutes=45)
        
        # 尝试再次创建配置
        config = service.create_config(config_create)
        
        # 应该返回现有配置，而不是新创建的
        assert config.id == created_course_config["id"]
        assert config.default_slot_duration_minutes == created_course_config["default_slot_duration_minutes"]  # 保持原值

    def test_get_config_success(self, test_session: Session, created_course_config):
        """测试成功获取配置"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        config = service.get_config()
        
        assert config is not None
        assert config.id == created_course_config["id"]
        assert config.tenant_id == created_course_config["tenant_id"]

    def test_get_config_not_found(self, test_session: Session, created_admin_user):
        """测试获取不存在的配置"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
        
        config = service.get_config()
        
        assert config is None

    def test_get_config_or_create_default_existing(self, test_session: Session, created_course_config):
        """测试获取现有配置"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        config = service.get_config_or_create_default()
        
        assert config.id == created_course_config["id"]

    def test_get_config_or_create_default_create_new(self, test_session: Session, created_admin_user):
        """测试创建默认配置"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
        
        config = service.get_config_or_create_default(created_by=created_admin_user["id"])
        
        assert config is not None
        assert config.tenant_id == created_admin_user["tenant_id"]
        assert config.default_slot_duration_minutes == 25  # 默认值
        assert config.direct_booking_enabled is True  # 默认值

    def test_update_config_success(self, test_session: Session, created_course_config, course_config_update_data):
        """测试成功更新配置"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        config_update = CourseSystemConfigUpdate(**course_config_update_data)
        
        updated_config = service.update_config(config_update, updated_by=created_course_config["created_by"])
        
        assert updated_config.id == created_course_config["id"]
        assert updated_config.default_slot_duration_minutes == course_config_update_data["default_slot_duration_minutes"]
        assert updated_config.max_advance_days == course_config_update_data["max_advance_days"]
        assert updated_config.teacher_can_add_slots == course_config_update_data["teacher_can_add_slots"]
        assert updated_config.updated_at > updated_config.created_at

    def test_update_config_not_found(self, test_session: Session, created_admin_user):
        """测试更新不存在的配置"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
        config_update = CourseSystemConfigUpdate(default_slot_duration_minutes=45)
        
        with pytest.raises(CourseConfigNotFoundError):
            service.update_config(config_update)

    def test_update_config_partial(self, test_session: Session, created_course_config, course_config_partial_update_data):
        """测试部分更新配置"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        config_update = CourseSystemConfigUpdate(**course_config_partial_update_data)
        
        original_duration = created_course_config["default_slot_duration_minutes"]
        
        updated_config = service.update_config(config_update)
        
        # 更新的字段应该改变
        assert updated_config.booking_time_from == course_config_partial_update_data["booking_time_from"]
        assert updated_config.require_material == course_config_partial_update_data["require_material"]
        # 未更新的字段应该保持原值
        assert updated_config.default_slot_duration_minutes == original_duration

    def test_reset_config_to_default(self, test_session: Session, created_course_config):
        """测试重置配置为默认值"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        reset_config = service.reset_config_to_default(reset_by=created_course_config["created_by"])
        
        assert reset_config.id == created_course_config["id"]
        assert reset_config.default_slot_duration_minutes == 25  # 默认值
        assert reset_config.default_slot_interval_minutes == 5   # 默认值
        assert reset_config.direct_booking_enabled is True       # 默认值

    def test_reset_config_not_found(self, test_session: Session, created_admin_user):
        """测试重置不存在的配置"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
        
        with pytest.raises(CourseConfigNotFoundError):
            service.reset_config_to_default()

    def test_get_config_by_field_existing(self, test_session: Session, created_course_config):
        """测试获取现有配置的字段值"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        duration = service.get_config_by_field("default_slot_duration_minutes")
        
        assert duration == created_course_config["default_slot_duration_minutes"]

    def test_get_config_by_field_default(self, test_session: Session, created_admin_user):
        """测试获取不存在配置的字段默认值"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
        
        duration = service.get_config_by_field("default_slot_duration_minutes")
        
        assert duration == 25  # 默认值

    def test_update_config_field_success(self, test_session: Session, created_course_config):
        """测试成功更新单个配置字段"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        updated_config = service.update_config_field(
            "default_slot_duration_minutes", 
            50, 
            updated_by=created_course_config["created_by"]
        )
        
        assert updated_config.default_slot_duration_minutes == 50
        assert updated_config.updated_at > updated_config.created_at

    def test_update_config_field_invalid_field(self, test_session: Session, created_course_config):
        """测试更新无效字段名"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        with pytest.raises(CourseConfigBusinessException):
            service.update_config_field("invalid_field", "value")

    def test_update_config_field_not_found(self, test_session: Session, created_admin_user):
        """测试更新不存在配置的字段"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
        
        with pytest.raises(CourseConfigNotFoundError):
            service.update_config_field("default_slot_duration_minutes", 50)

    def test_validate_config_consistency_valid(self, test_session: Session, created_course_config):
        """测试验证有效配置一致性"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        result = service.validate_config_consistency()
        
        assert result is True

    def test_validate_config_consistency_not_found(self, test_session: Session, created_admin_user):
        """测试验证不存在配置的一致性"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
        
        result = service.validate_config_consistency()
        
        assert result is False

    def test_get_booking_time_range_existing(self, test_session: Session, created_course_config):
        """测试获取现有配置的约课时间范围"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        time_range = service.get_booking_time_range()
        
        assert time_range["booking_time_from"] == created_course_config["booking_time_from"]
        assert time_range["booking_time_to"] == created_course_config["booking_time_to"]

    def test_get_booking_time_range_default(self, test_session: Session, created_admin_user):
        """测试获取默认约课时间范围"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
        
        time_range = service.get_booking_time_range()
        
        # 应该返回默认值
        assert time_range["booking_time_from"] is None
        assert time_range["booking_time_to"] is None

    def test_is_direct_booking_enabled(self, test_session: Session, created_course_config):
        """测试检查是否启用直接约课"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        enabled = service.is_direct_booking_enabled()
        
        assert enabled == created_course_config["direct_booking_enabled"]

    def test_is_fixed_booking_enabled(self, test_session: Session, created_course_config):
        """测试检查是否启用固定课表"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        enabled = service.is_fixed_booking_enabled()
        
        assert enabled == created_course_config["fixed_booking_enabled"]

    def test_is_auto_schedule_enabled(self, test_session: Session, created_course_config):
        """测试检查是否启用自动排课"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        enabled = service.is_auto_schedule_enabled()
        
        assert enabled == created_course_config["auto_schedule_enabled"]

    def test_get_teacher_permissions(self, test_session: Session, created_course_config):
        """测试获取教师权限配置"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        permissions = service.get_teacher_permissions()
        
        assert permissions["teacher_can_add_slots"] == created_course_config["teacher_can_add_slots"]
        assert permissions["teacher_can_delete_empty_slots"] == created_course_config["teacher_can_delete_empty_slots"]
        assert permissions["teacher_can_cancel_booking"] == created_course_config["teacher_can_cancel_booking"]
        assert permissions["teacher_need_confirm"] == created_course_config["teacher_need_confirm"]

    def test_get_schedule_config(self, test_session: Session, created_course_config):
        """测试获取排课配置"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        schedule_config = service.get_schedule_config()
        
        assert schedule_config["auto_schedule_day"] == created_course_config["auto_schedule_day"]
        assert schedule_config["auto_schedule_time"] == created_course_config["auto_schedule_time"]
        assert schedule_config["default_schedule_weeks"] == created_course_config["default_schedule_weeks"]

    def test_ensure_config_exists_create_new(self, test_session: Session, created_admin_user):
        """测试确保配置存在（创建新配置）"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        service = CourseSystemConfigService(test_session, created_admin_user["tenant_id"])
        
        config = service.ensure_config_exists(created_by=created_admin_user["id"])
        
        assert config is not None
        assert config.tenant_id == created_admin_user["tenant_id"]
        assert config.created_by == created_admin_user["id"]

    def test_ensure_config_exists_return_existing(self, test_session: Session, created_course_config):
        """测试确保配置存在（返回现有配置）"""
        service = CourseSystemConfigService(test_session, created_course_config["tenant_id"])
        
        config = service.ensure_config_exists()
        
        assert config.id == created_course_config["id"]

    def test_initialize_tenant_config(self, test_session: Session, created_admin_user):
        """测试初始化租户配置（类方法）"""
        # 设置RLS上下文
        test_session.exec(text(f"SET app.current_tenant_id = '{created_admin_user['tenant_id']}'"))
        
        config = CourseSystemConfigService.initialize_tenant_config(
            test_session, 
            created_admin_user["tenant_id"], 
            created_by=created_admin_user["id"]
        )
        
        assert config is not None
        assert config.tenant_id == created_admin_user["tenant_id"]
        assert config.created_by == created_admin_user["id"]

    def test_data_isolation_between_tenants(self, test_session: Session, multiple_tenant_configs):
        """测试租户间数据隔离"""
        tenant1_config = multiple_tenant_configs[0]
        tenant2_config = multiple_tenant_configs[1]
        
        # 测试第一个租户只能看到自己的配置
        service1 = CourseSystemConfigService(test_session, tenant1_config["tenant_id"])
        config1 = service1.get_config()
        assert config1.id == tenant1_config["config"].id
        assert config1.default_slot_duration_minutes == 25
        
        # 测试第二个租户只能看到自己的配置
        service2 = CourseSystemConfigService(test_session, tenant2_config["tenant_id"])
        config2 = service2.get_config()
        assert config2.id == tenant2_config["config"].id
        assert config2.default_slot_duration_minutes == 45
        
        # 确保两个配置不同
        assert config1.id != config2.id
        assert config1.default_slot_duration_minutes != config2.default_slot_duration_minutes 