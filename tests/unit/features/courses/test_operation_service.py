"""操作记录服务单元测试"""

import pytest
from datetime import datetime, date, time
from unittest.mock import Mock, patch
from sqlmodel import Session

from app.features.courses.operations.service import (
    ScheduledClassOperationLogService,
    TeacherFixedSlotOperationLogService,
    MemberFixedLockOperationLogService,
    OperationLogStatisticsService
)
from app.features.courses.operations.schemas import (
    ScheduledClassOperationLogCreate, ScheduledClassOperationLogQuery,
    TeacherFixedSlotOperationLogCreate, TeacherFixedSlotOperationLogQuery,
    MemberFixedLockOperationLogCreate, MemberFixedLockOperationLogQuery
)
from app.features.courses.operations.models import (
    ScheduledClassOperationLog, TeacherFixedSlotOperationLog, MemberFixedLockOperationLog,
    ClassOperationType, TeacherSlotOperationType, MemberLockOperationType,
    OperationStatus
)


class TestScheduledClassOperationLogService:
    """课程操作记录服务测试类"""
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def service(self, mock_session):
        """创建服务实例"""
        return ScheduledClassOperationLogService(mock_session, tenant_id=1)
    
    def test_operation_types_exist(self):
        """测试操作类型枚举存在"""
        assert hasattr(ClassOperationType, 'CREATE')
        assert hasattr(ClassOperationType, 'UPDATE')
        assert hasattr(ClassOperationType, 'DELETE')
    
    def test_service_initialization(self, service):
        """测试服务初始化"""
        assert service.tenant_id == 1
        assert service.model_class == ScheduledClassOperationLog
    
    def test_service_methods_exist(self, service):
        """测试服务方法存在"""
        assert hasattr(service, 'create_operation_log')
        assert hasattr(service, 'query_operation_logs')
        assert callable(getattr(service, 'create_operation_log'))
        assert callable(getattr(service, 'query_operation_logs'))
    
    def test_query_logs_basic(self, service, mock_session):
        """测试基本日志查询"""
        # Mock查询结果
        mock_logs = [Mock(), Mock()]
        mock_total = 2
        
        with patch('app.features.courses.operations.service.search_with_pagination') as mock_search:
            mock_search.return_value = (mock_logs, mock_total)
            
            query = ScheduledClassOperationLogQuery(
                class_id=1,
                operation_type=ClassOperationType.CREATE,
                page=1,
                size=20
            )
            
            logs, total = service.query_operation_logs(query)
            
            assert logs == mock_logs
            assert total == mock_total
            mock_search.assert_called_once()
    
    def test_operation_status_enum(self):
        """测试操作状态枚举"""
        assert hasattr(OperationStatus, 'SUCCESS')
        assert hasattr(OperationStatus, 'FAILED')
        assert hasattr(OperationStatus, 'PENDING')


class TestTeacherFixedSlotOperationLogService:
    """教师固定时间段操作记录服务测试类"""
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def service(self, mock_session):
        """创建服务实例"""
        return TeacherFixedSlotOperationLogService(mock_session, tenant_id=1)
    
    def test_service_initialization(self, service):
        """测试服务初始化"""
        assert service.tenant_id == 1
        assert service.model_class == TeacherFixedSlotOperationLog

    def test_teacher_slot_operation_types(self):
        """测试教师时间段操作类型"""
        assert hasattr(TeacherSlotOperationType, 'CREATE')
        assert hasattr(TeacherSlotOperationType, 'DELETE')
    
    def test_query_logs_basic(self, service, mock_session):
        """测试基本日志查询"""
        # Mock查询结果
        mock_logs = [Mock(), Mock()]
        mock_total = 2
        
        with patch('app.features.courses.operations.service.search_with_pagination') as mock_search:
            mock_search.return_value = (mock_logs, mock_total)
            
            query = TeacherFixedSlotOperationLogQuery(
                slot_id=1,
                operation_type=TeacherSlotOperationType.CREATE,
                teacher_id=1,
                page=1,
                size=20
            )
            
            logs, total = service.query_operation_logs(query)
            
            assert logs == mock_logs
            assert total == mock_total
            mock_search.assert_called_once()


class TestMemberFixedLockOperationLogService:
    """会员固定位锁定操作记录服务测试类"""
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def service(self, mock_session):
        """创建服务实例"""
        return MemberFixedLockOperationLogService(mock_session, tenant_id=1)
    
    def test_service_initialization(self, service):
        """测试服务初始化"""
        assert service.tenant_id == 1
        assert service.model_class == MemberFixedLockOperationLog

    def test_member_lock_operation_types(self):
        """测试会员锁定操作类型"""
        assert hasattr(MemberLockOperationType, 'CREATE')
        assert hasattr(MemberLockOperationType, 'DELETE_BY_MEMBER')
    
    def test_query_logs_basic(self, service, mock_session):
        """测试基本日志查询"""
        # Mock查询结果
        mock_logs = [Mock(), Mock()]
        mock_total = 2
        
        with patch('app.features.courses.operations.service.search_with_pagination') as mock_search:
            mock_search.return_value = (mock_logs, mock_total)
            
            query = MemberFixedLockOperationLogQuery(
                lock_id=1,
                operation_type=MemberLockOperationType.CREATE,
                member_id=1,
                page=1,
                size=20
            )
            
            logs, total = service.query_operation_logs(query)
            
            assert logs == mock_logs
            assert total == mock_total
            mock_search.assert_called_once()


class TestOperationLogStatisticsService:
    """操作统计服务测试类"""
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def service(self, mock_session):
        """创建服务实例"""
        return OperationLogStatisticsService(mock_session, tenant_id=1)
    
    def test_service_initialization(self, service):
        """测试服务初始化"""
        assert service.tenant_id == 1
    
    def test_statistics_service_basic(self, service):
        """测试统计服务基本功能"""
        # 测试服务对象创建成功
        assert service is not None
        assert service.tenant_id == 1
