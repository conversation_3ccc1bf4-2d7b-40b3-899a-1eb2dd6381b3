"""固定课排课算法单元测试"""

import pytest
from datetime import datetime, date, time
from unittest.mock import Mock, patch
from sqlmodel import Session

from app.features.courses.scheduling.algorithm import (
    FixedScheduleAlgorithm, SchedulingContext, TeacherSchedulingResult
)
from app.features.courses.scheduling.models import (
    FixedScheduleTask, ScheduleTaskStatus, TeacherPriorityRule,
    BalanceInsufficientAction
)
from app.features.teachers.models import Teacher
from app.features.members.models import Member
from app.features.member_cards.models import MemberCard
from app.features.teachers.fixed_slots_models import TeacherFixedSlot, Weekday
from app.features.members.fixed_lock_models import MemberFixedSlotLock


class TestFixedScheduleAlgorithm:
    """固定课排课算法测试类"""
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def sample_task(self):
        """示例排课任务"""
        return FixedScheduleTask(
            id=1,
            tenant_id=1,
            task_name="测试排课任务",
            start_date=datetime(2024, 1, 1),
            weeks_count=4,
            teacher_ids='[1, 2]',
            teacher_priority_rule=TeacherPriorityRule.REGION_FIRST,
            balance_insufficient_action=BalanceInsufficientAction.SKIP,
            interrupt_on_conflict=False,
            status=ScheduleTaskStatus.PENDING,
            created_by=1,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @pytest.fixture
    def scheduling_context(self, mock_session, sample_task):
        """排课上下文"""
        return SchedulingContext(
            task=sample_task,
            session=mock_session,
            tenant_id=1,
            start_date=date(2024, 1, 1),
            weeks_count=4,
            teacher_ids=[1, 2],
            teacher_priority_rule=TeacherPriorityRule.REGION_FIRST,
            balance_insufficient_action=BalanceInsufficientAction.SKIP,
            interrupt_on_conflict=False
        )
    
    @pytest.fixture
    def sample_teachers(self):
        """示例教师数据"""
        from app.features.teachers.models import TeacherCategory, TeacherRegion, TeacherStatus
        return [
            Teacher(
                id=1,
                tenant_id=1,
                name="张老师",
                teacher_category=TeacherCategory.EUROPEAN,
                region=TeacherRegion.EUROPE,
                price_per_class=100,
                status=TeacherStatus.ACTIVE
            ),
            Teacher(
                id=2,
                tenant_id=1,
                name="李老师",
                teacher_category=TeacherCategory.EUROPEAN,
                region=TeacherRegion.NORTH_AMERICA,
                price_per_class=120,
                status=TeacherStatus.ACTIVE
            )
        ]
    
    @pytest.fixture
    def sample_teacher_slots(self):
        """示例教师固定时间段"""
        return [
            TeacherFixedSlot(
                id=1,
                tenant_id=1,
                teacher_id=1,
                weekday=Weekday.MONDAY,
                start_time=time(9, 0),
                duration_minutes=25,
                is_visible_to_members=True
            ),
            TeacherFixedSlot(
                id=2,
                tenant_id=1,
                teacher_id=1,
                weekday=Weekday.TUESDAY,
                start_time=time(10, 0),
                duration_minutes=25,
                is_visible_to_members=True
            ),
            TeacherFixedSlot(
                id=3,
                tenant_id=1,
                teacher_id=2,
                weekday=Weekday.MONDAY,
                start_time=time(14, 0),
                duration_minutes=25,
                is_visible_to_members=True
            )
        ]
    
    @pytest.fixture
    def sample_member_locks(self):
        """示例会员固定位锁定"""
        return [
            MemberFixedSlotLock(
                id=1,
                tenant_id=1,
                member_id=1,
                teacher_id=1,
                weekday=Weekday.MONDAY,
                start_time=time(9, 0),
            ),
            MemberFixedSlotLock(
                id=2,
                tenant_id=1,
                member_id=2,
                teacher_id=1,
                weekday=Weekday.TUESDAY,
                start_time=time(10, 0),
            )
        ]
    
    @pytest.fixture
    def sample_members(self):
        """示例会员数据"""
        return [
            Member(
                id=1,
                tenant_id=1,
                member_name="学员A",
                member_number="M001",
                member_card_id=1,
                member_card_name="标准卡"
            ),
            Member(
                id=2,
                tenant_id=1,
                member_name="学员B",
                member_number="M002",
                member_card_id=2,
                member_card_name="VIP卡"
            )
        ]
    
    @pytest.fixture
    def sample_member_cards(self):
        """示例会员卡数据"""
        return [
            MemberCard(
                id=1,
                tenant_id=1,
                member_id=1,
                balance=1000,
                template_id=1
            ),
            MemberCard(
                id=2,
                tenant_id=1,
                member_id=2,
                balance=500,
                template_id=1
            )
        ]
    
    def test_algorithm_initialization(self, scheduling_context):
        """测试算法初始化"""
        algorithm = FixedScheduleAlgorithm(scheduling_context)

        assert algorithm.context == scheduling_context
        assert algorithm.session == scheduling_context.session
        assert algorithm.tenant_id == scheduling_context.tenant_id
    
    def test_algorithm_context_access(self, scheduling_context):
        """测试算法上下文访问"""
        algorithm = FixedScheduleAlgorithm(scheduling_context)

        # 测试可以访问上下文属性
        assert algorithm.context.tenant_id == 1
        assert algorithm.context.weeks_count == 4
        assert algorithm.session == scheduling_context.session
    
    def test_teacher_models_creation(self, sample_teachers):
        """测试教师模型创建"""
        assert len(sample_teachers) == 2
        assert sample_teachers[0].name == "张老师"
        assert sample_teachers[1].name == "李老师"

        from app.features.teachers.models import TeacherRegion
        assert sample_teachers[0].region == TeacherRegion.EUROPE
        assert sample_teachers[1].region == TeacherRegion.NORTH_AMERICA
    
    def test_sort_teachers_by_priority_price_desc(self, scheduling_context, sample_teachers):
        """测试按价格倒序排序教师"""
        # 修改排序规则为价格倒序
        scheduling_context.teacher_priority_rule = TeacherPriorityRule.PRICE_DESC
        algorithm = FixedScheduleAlgorithm(scheduling_context)

        sorted_teachers = algorithm._sort_teachers_by_priority(sample_teachers)

        # 价格高的教师应该排在前面
        assert sorted_teachers[0].price_per_class == 120
        assert sorted_teachers[1].price_per_class == 100
    
    def test_scheduling_context_properties(self, scheduling_context):
        """测试排课上下文属性"""
        assert scheduling_context.tenant_id == 1
        assert scheduling_context.start_date == date(2024, 1, 1)
        assert scheduling_context.weeks_count == 4
        assert scheduling_context.teacher_ids == [1, 2]
        assert scheduling_context.teacher_priority_rule == TeacherPriorityRule.REGION_FIRST
        assert scheduling_context.balance_insufficient_action == BalanceInsufficientAction.SKIP
        assert scheduling_context.interrupt_on_conflict is False
    
    def test_teacher_scheduling_result_creation(self):
        """测试教师排课结果创建"""
        result = TeacherSchedulingResult(
            teacher_id=1,
            teacher_name="张老师",
            teacher_display_code="T001",
            success=True,
            total_members=2,
            successful_members=2,
            failed_members=0,
            total_classes=8,
            total_amount=800,
            error_message=None
        )

        assert result.teacher_id == 1
        assert result.teacher_name == "张老师"
        assert result.teacher_display_code == "T001"
        assert result.success is True
        assert result.total_members == 2
        assert result.successful_members == 2
        assert result.failed_members == 0
        assert result.total_classes == 8
        assert result.total_amount == 800
        assert result.error_message is None

    def test_enum_values(self):
        """测试枚举值"""
        assert TeacherPriorityRule.REGION_FIRST == "region_first"
        assert BalanceInsufficientAction.SKIP == "skip"
        assert BalanceInsufficientAction.TERMINATE == "terminate"

    def test_algorithm_basic_functionality(self, scheduling_context):
        """测试算法基本功能"""
        algorithm = FixedScheduleAlgorithm(scheduling_context)

        # 测试算法对象创建成功
        assert algorithm is not None
        assert algorithm.context == scheduling_context


