"""
操作日志记录器测试
"""
import pytest
from unittest.mock import Mock, patch
from datetime import datetime, time

from app.features.courses.operations.operation_logger import OperationLogger
from app.features.courses.operations.models import TeacherSlotOperationType, MemberLockOperationType
from app.features.teachers.fixed_slots_models import TeacherFixedSlot
from app.features.members.fixed_lock_models import MemberFixedSlotLock
from app.features.teachers.models import Teacher
from app.features.members.models import Member


class TestOperationLogger:
    """操作日志记录器测试"""

    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        session = Mock()
        return session

    @pytest.fixture
    def mock_teacher_log_service(self):
        """模拟教师操作日志服务"""
        service = Mock()
        return service

    @pytest.fixture
    def mock_member_log_service(self):
        """模拟会员操作日志服务"""
        service = Mock()
        return service

    @pytest.fixture
    def operation_logger(self, mock_session, mock_teacher_log_service, mock_member_log_service):
        """操作日志记录器实例"""
        logger = OperationLogger(mock_session, tenant_id=1)
        logger.teacher_log_service = mock_teacher_log_service
        logger.member_log_service = mock_member_log_service
        return logger

    @pytest.fixture
    def sample_teacher_slot(self):
        """示例教师时间段"""
        return TeacherFixedSlot(
            id=1,
            tenant_id=1,
            teacher_id=1,
            weekday=1,
            start_time=time(9, 0),
            duration_minutes=25,
            is_available=True,
            is_visible_to_members=True,
            created_by=1,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

    @pytest.fixture
    def sample_member_lock(self):
        """示例会员锁定"""
        return MemberFixedSlotLock(
            id=1,
            tenant_id=1,
            member_id=1,
            teacher_fixed_slot_id=1,
            teacher_id=1,
            weekday=1,
            start_time=time(9, 0),
            member_name="张三",
            member_phone="13800138000",
            created_by=1,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            locked_at=datetime.now()
        )

    @pytest.fixture
    def sample_teacher(self):
        """示例教师"""
        return Teacher(
            id=1,
            tenant_id=1,
            name="李老师",
            phone="13900139000",
            email="<EMAIL>",
            created_by=1,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

    @pytest.fixture
    def sample_member(self):
        """示例会员"""
        return Member(
            id=1,
            tenant_id=1,
            name="张三",
            phone="13800138000",
            email="<EMAIL>",
            created_by=1,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

    def test_log_teacher_slot_create(self, operation_logger, sample_teacher_slot, sample_teacher, mock_session):
        """测试记录教师时间段创建操作"""
        # 设置模拟
        mock_session.get.return_value = sample_teacher
        
        # 执行操作
        operation_logger.log_teacher_slot_create(
            slot=sample_teacher_slot,
            operator_id=1,
            operator_name="管理员",
            operator_type="admin",
            reason="创建新时间段"
        )
        
        # 验证调用
        operation_logger.teacher_log_service.create_operation_log.assert_called_once()
        call_args = operation_logger.teacher_log_service.create_operation_log.call_args[0][0]
        
        assert call_args.teacher_fixed_slot_id == 1
        assert call_args.operation_type == TeacherSlotOperationType.CREATE
        assert call_args.operator_id == 1
        assert call_args.operator_name == "管理员"
        assert call_args.teacher_name == "李老师"
        assert call_args.reason == "创建新时间段"

    def test_log_teacher_slot_delete(self, operation_logger, sample_teacher_slot, sample_teacher, mock_session):
        """测试记录教师时间段删除操作"""
        # 设置模拟
        mock_session.get.return_value = sample_teacher
        
        # 执行操作
        operation_logger.log_teacher_slot_delete(
            slot=sample_teacher_slot,
            operator_id=1,
            operator_name="管理员",
            operator_type="admin",
            reason="删除无用时间段"
        )
        
        # 验证调用
        operation_logger.teacher_log_service.create_operation_log.assert_called_once()
        call_args = operation_logger.teacher_log_service.create_operation_log.call_args[0][0]
        
        assert call_args.operation_type == TeacherSlotOperationType.DELETE
        assert call_args.reason == "删除无用时间段"

    def test_log_teacher_slot_visibility_change(self, operation_logger, sample_teacher_slot, sample_teacher, mock_session):
        """测试记录教师时间段可见性变更操作"""
        # 设置模拟
        mock_session.get.return_value = sample_teacher
        
        # 执行操作 - 设置为不可见
        operation_logger.log_teacher_slot_visibility_change(
            slot=sample_teacher_slot,
            old_visible=True,
            new_visible=False,
            operator_id=1,
            operator_name="管理员",
            operator_type="admin"
        )
        
        # 验证调用
        operation_logger.teacher_log_service.create_operation_log.assert_called_once()
        call_args = operation_logger.teacher_log_service.create_operation_log.call_args[0][0]
        
        assert call_args.operation_type == TeacherSlotOperationType.SET_INVISIBLE
        assert "设置对会员不可见" in call_args.operation_description

    def test_log_member_lock_create(self, operation_logger, sample_member_lock, sample_member, sample_teacher, mock_session):
        """测试记录会员锁定创建操作"""
        # 设置模拟
        def mock_get(model_class, id):
            if model_class == Member:
                return sample_member
            elif model_class == Teacher:
                return sample_teacher
            return None
        
        mock_session.get.side_effect = mock_get
        
        # 执行操作
        operation_logger.log_member_lock_create(
            lock=sample_member_lock,
            operator_id=1,
            operator_name="管理员",
            operator_type="admin",
            reason="会员申请锁定"
        )
        
        # 验证调用
        operation_logger.member_log_service.create_operation_log.assert_called_once()
        call_args = operation_logger.member_log_service.create_operation_log.call_args[0][0]
        
        assert call_args.member_fixed_slot_lock_id == 1
        assert call_args.operation_type == MemberLockOperationType.CREATE
        assert call_args.member_name == "张三"
        assert call_args.teacher_name == "李老师"

    def test_log_member_lock_delete_by_admin(self, operation_logger, sample_member_lock, sample_member, sample_teacher, mock_session):
        """测试记录管理员删除会员锁定操作"""
        # 设置模拟
        def mock_get(model_class, id):
            if model_class == Member:
                return sample_member
            elif model_class == Teacher:
                return sample_teacher
            return None
        
        mock_session.get.side_effect = mock_get
        
        # 执行操作
        operation_logger.log_member_lock_delete(
            lock=sample_member_lock,
            operator_id=1,
            operator_name="管理员",
            operator_type="admin",
            is_member_operation=False,
            reason="管理员强制删除"
        )
        
        # 验证调用
        operation_logger.member_log_service.create_operation_log.assert_called_once()
        call_args = operation_logger.member_log_service.create_operation_log.call_args[0][0]
        
        assert call_args.operation_type == MemberLockOperationType.DELETE_BY_ADMIN
        assert "管理员删除锁定" in call_args.operation_description

    def test_log_member_lock_delete_by_member(self, operation_logger, sample_member_lock, sample_member, sample_teacher, mock_session):
        """测试记录会员删除锁定操作"""
        # 设置模拟
        def mock_get(model_class, id):
            if model_class == Member:
                return sample_member
            elif model_class == Teacher:
                return sample_teacher
            return None
        
        mock_session.get.side_effect = mock_get
        
        # 执行操作
        operation_logger.log_member_lock_delete(
            lock=sample_member_lock,
            operator_id=1,
            operator_name="张三",
            operator_type="member",
            is_member_operation=True,
            reason="会员主动取消"
        )
        
        # 验证调用
        operation_logger.member_log_service.create_operation_log.assert_called_once()
        call_args = operation_logger.member_log_service.create_operation_log.call_args[0][0]
        
        assert call_args.operation_type == MemberLockOperationType.DELETE_BY_MEMBER
        assert "会员取消锁定" in call_args.operation_description

    def test_batch_operations(self, operation_logger, sample_teacher_slot, sample_member_lock, mock_session):
        """测试批量操作日志记录"""
        # 设置模拟
        mock_session.get.return_value = Mock(name="测试用户")
        
        # 测试批量创建教师时间段
        slots = [sample_teacher_slot, sample_teacher_slot]
        operation_logger.log_teacher_slots_batch_create(
            slots=slots,
            operator_id=1,
            operator_name="管理员",
            operator_type="admin"
        )
        
        # 验证调用次数
        assert operation_logger.teacher_log_service.create_operation_log.call_count == 2
        
        # 重置模拟
        operation_logger.teacher_log_service.reset_mock()
        
        # 测试批量创建会员锁定
        locks = [sample_member_lock, sample_member_lock]
        operation_logger.log_member_locks_batch_create(
            locks=locks,
            operator_id=1,
            operator_name="管理员",
            operator_type="admin"
        )
        
        # 验证调用次数
        assert operation_logger.member_log_service.create_operation_log.call_count == 2

    def test_format_weekday(self, operation_logger):
        """测试星期格式化"""
        assert operation_logger._format_weekday(1) == "周一"
        assert operation_logger._format_weekday(7) == "周日"
        assert operation_logger._format_weekday(8) == "周8"  # 边界情况
