"""会员卡服务单元测试"""
import pytest
from datetime import datetime, timezone, timedelta
from sqlmodel import Session

from app.features.member_cards.card_service import MemberCardService
from app.features.member_cards.schemas import MemberCardCreate, MemberCardUpdate, MemberCardQuery
from app.features.member_cards.models import CardType, CardStatus
from app.features.member_cards.exceptions import MemberCardBusinessException, MemberCardNotFoundError


class TestMemberCardService:
    """会员卡服务单元测试"""
    
    def test_create_card_success(self, test_session: Session, sample_card_data, created_admin_user):
        """测试成功创建会员卡"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        card_create = MemberCardCreate(
            member_id=sample_card_data["member_id"],
            template_id=sample_card_data["template_id"]
        )
        
        card = service.create_card(card_create, created_by=created_admin_user["id"])
        
        assert card.id is not None
        assert card.member_id == sample_card_data["member_id"]
        assert card.template_id == sample_card_data["template_id"]
        assert card.card_type.value == sample_card_data["card_type"]
        assert card.balance > 0  # 从模板获取的余额
        assert card.status == CardStatus.ACTIVE
        assert card.card_number is not None
        assert card.tenant_id == created_admin_user["tenant_id"]
        assert card.created_by == created_admin_user["id"]
        assert card.total_recharged == card.balance
        assert card.total_consumed == 0
    
    def test_create_duplicate_template_card(self, test_session: Session, created_card, created_admin_user):
        """测试创建重复模板卡片失败"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 尝试为同一会员创建来自相同模板的卡片
        duplicate_card_data = MemberCardCreate(
            member_id=created_card["member_id"],
            template_id=created_card["template_id"]
        )
        
        with pytest.raises(MemberCardBusinessException) as exc_info:
            service.create_card(duplicate_card_data, created_by=created_admin_user["id"])
        
        assert "已存在该卡片" in exc_info.value.message
        assert "来自模板" in exc_info.value.message
    
    def test_create_card_with_template_expiry(self, test_session: Session, sample_card_data, created_admin_user):
        """测试创建有期限卡片自动设置过期时间"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        card_create = MemberCardCreate(
            member_id=sample_card_data["member_id"],
            template_id=sample_card_data["template_id"]
        )
        
        card = service.create_card(card_create, created_by=created_admin_user["id"])
        
        # 验证过期时间已设置
        assert card.expires_at is not None
        
        # 验证过期时间大约是90天后（允许几分钟误差）
        expected_expiry = datetime.now() + timedelta(days=90)
        time_diff = abs((card.expires_at - expected_expiry).total_seconds())
        assert time_diff < 300  # 5分钟误差范围
    
    def test_create_duplicate_value_card(self, test_session: Session, sample_value_card_data, created_admin_user):
        """测试创建重复储值卡失败"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        card_create = MemberCardCreate(
            member_id=sample_value_card_data["member_id"],
            template_id=sample_value_card_data["template_id"]
        )
        
        # 创建用户本身已经创建了一张储值卡了,这里再次为同一会员创建储值卡应该失败
        with pytest.raises(MemberCardBusinessException) as exc_info:
            service.create_card(card_create, created_by=created_admin_user["id"])
        assert "已存在该卡片" in exc_info.value.message
            
    
    def test_update_card_success(self, test_session: Session, created_card, created_admin_user):
        """测试成功更新会员卡"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        update_data = MemberCardUpdate(
            balance=15,
            freeze_reason="测试冻结原因"
        )
        
        updated_card = service.update_card(
            created_card["id"],
            update_data,
            created_admin_user["id"]
        )
        
        assert updated_card.balance == 15
        assert updated_card.freeze_reason == "测试冻结原因"
        # 其他字段保持不变
        assert updated_card.card_type.value == created_card["card_type"]
        assert updated_card.member_id == created_card["member_id"]
    
    def test_update_card_invalid_status_transition(self, test_session: Session, created_card, created_admin_user):
        """测试无效状态转换失败"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 先注销卡片
        service.update_card(
            created_card["id"],
            MemberCardUpdate(status=CardStatus.CANCELLED),
            created_admin_user["id"]
        )
        
        # 尝试从注销状态转换到激活状态应该失败
        with pytest.raises(MemberCardBusinessException) as exc_info:
            service.update_card(
                created_card["id"],
                MemberCardUpdate(status=CardStatus.ACTIVE),
                created_admin_user["id"]
            )
        assert "无效的状态转换" in exc_info.value.message
    
    def test_update_card_negative_balance(self, test_session: Session, created_card, created_admin_user):
        """测试设置负余额失败"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])

        # 创建一个绕过Pydantic验证的更新请求
        update_data = MemberCardUpdate(balance=0)
        update_data.balance = -100  # 手动设置负值

        with pytest.raises(MemberCardBusinessException) as exc_info:
            service.update_card(
                created_card["id"],
                update_data,
                created_admin_user["id"]
            )
        assert "不允许余额为负数" in exc_info.value.message
            
    
    def test_get_card_success(self, test_session: Session, created_card, created_admin_user):
        """测试成功获取会员卡"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        card = service.get_card(created_card["id"])
        
        assert card is not None
        assert card.id == created_card["id"]
        assert card.member_id == created_card["member_id"]
    
    def test_get_card_not_found(self, test_session: Session, created_admin_user):
        """测试获取不存在的会员卡"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        with pytest.raises(MemberCardNotFoundError):
            service.get_card(99999)
    
    def test_get_cards_with_pagination(self, test_session: Session, created_card, created_value_card, created_admin_user):
        """测试分页获取会员卡列表"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        query_params = MemberCardQuery(
            page=1,
            size=10,
            sort_by="created_at",
            sort_order="desc"
        )
        
        cards, total = service.get_cards(query_params)
        
        assert len(cards) >= 2  # 至少有创建的两张卡
        assert total >= 2
        assert cards[0].created_at >= cards[1].created_at  # 降序排列
    
    def test_get_cards_with_filter(self, test_session: Session, created_card, created_value_card, created_admin_user):
        """测试筛选获取会员卡列表"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        query_params = MemberCardQuery(
            member_id=created_card["member_id"],
            card_type=CardType.TIMES_LIMITED
        )
        
        cards, total = service.get_cards(query_params)
        
        # 验证所有返回的卡片都符合筛选条件
        for card in cards:
            assert card.member_id == created_card["member_id"]
            assert card.card_type == CardType.TIMES_LIMITED
    
    def test_get_member_cards(self, test_session: Session, created_card, created_value_card, created_member, created_admin_user):
        """测试获取会员的所有卡片"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])

        cards = service.get_member_cards(created_member["id"])

        assert len(cards) >= 2  # 至少有创建的两张卡

        # 验证返回的是完整的MemberCard对象
        for card in cards:
            assert hasattr(card, 'id')
            assert hasattr(card, 'card_type')
            assert hasattr(card, 'balance')
            assert hasattr(card, 'status')
            assert hasattr(card, 'member_id')
            assert hasattr(card, 'created_at')

    def test_get_member_card_summaries(self, test_session: Session, created_card, created_value_card, created_member, created_admin_user):
        """测试获取会员的所有卡片摘要"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])

        summaries = service.get_member_card_summaries(created_member["id"])

        assert len(summaries) >= 2  # 至少有创建的两张卡

        # 验证返回的是摘要格式
        for summary in summaries:
            assert hasattr(summary, 'id')
            assert hasattr(summary, 'card_type')
            assert hasattr(summary, 'balance')
            assert hasattr(summary, 'status')
            # 摘要不包含member_id和created_at等详细信息
    
    def test_get_member_active_cards(self, test_session: Session, created_card, created_member, created_admin_user):
        """测试获取会员的激活状态卡片"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        active_cards = service.get_member_active_cards(created_member["id"])
        
        # 验证所有返回的卡片都是激活状态
        for card in active_cards:
            assert card.status == CardStatus.ACTIVE
            assert card.member_id == created_member["id"]
    
    def test_freeze_card(self, test_session: Session, created_card, created_admin_user):
        """测试冻结会员卡"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        frozen_card = service.freeze_card(
            created_card["id"],
            "测试冻结",
            created_admin_user["id"]
        )
        
        assert frozen_card.status == CardStatus.FROZEN
        assert frozen_card.freeze_reason == "测试冻结"
    
    def test_unfreeze_card(self, test_session: Session, created_card, created_admin_user):
        """测试解冻会员卡"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 先冻结
        service.freeze_card(created_card["id"], "测试冻结", created_admin_user["id"])
        
        # 再解冻
        unfrozen_card = service.unfreeze_card(created_card["id"], created_admin_user["id"])
        
        assert unfrozen_card.status == CardStatus.ACTIVE
        assert unfrozen_card.freeze_reason is None
    
    def test_cancel_card(self, test_session: Session, created_card, created_admin_user):
        """测试注销会员卡"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        cancelled_card = service.cancel_card(
            created_card["id"],
            "测试注销",
            created_admin_user["id"]
        )
        
        assert cancelled_card.status == CardStatus.CANCELLED
        assert cancelled_card.cancel_reason == "测试注销"
    
    def test_check_card_usability_active(self, test_session: Session, created_card, created_admin_user):
        """测试检查激活卡片可用性"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        result = service.check_card_usability(created_card["id"])
        
        assert result["usable"] is True
        assert result["reason"] is None
    
    def test_check_card_usability_frozen(self, test_session: Session, created_card, created_admin_user):
        """测试检查冻结卡片可用性"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        # 先冻结卡片
        service.freeze_card(created_card["id"], "测试冻结", created_admin_user["id"])
        
        result = service.check_card_usability(created_card["id"])
        
        assert result["usable"] is False
        assert "frozen" in result["reason"]
    
    def test_update_last_used_time(self, test_session: Session, created_card, created_admin_user):
        """测试更新最后使用时间"""
        service = MemberCardService(test_session, tenant_id=created_admin_user["tenant_id"])
        
        success = service.update_last_used_time(created_card["id"])
        
        assert success is True
        
        # 验证时间已更新
        updated_card = service.get_card(created_card["id"])
        assert updated_card.last_used_at is not None
