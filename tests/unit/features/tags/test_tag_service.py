"""标签服务单元测试"""
import pytest
from sqlmodel import Session
from app.features.tags.service import get_tag_category_service, get_tag_service
from app.features.tags.schemas import (
    TagCategoryCreate, TagCategoryUpdate, TagCategoryQuery,
    TagCreate, TagUpdate, TagQuery, TagBatchCreate, TagBatchUpdate
)
from app.features.tags.models import TagStatus
from app.features.tags.exceptions import (
    TagBusinessException, TagCategoryNotFoundError, TagNotFoundError
)


class TestTagCategoryService:
    """标签分类服务单元测试"""
    
    def test_create_category_with_valid_data_should_return_category(
        self, test_session: Session, sample_tag_category_data, created_tenant, created_admin_user
    ):
        """测试使用有效数据创建标签分类应该返回分类对象"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        category_create = TagCategoryCreate(**sample_tag_category_data)

        # Act
        category = service.create_category(category_create, created_by=created_admin_user["id"])
        
        # Assert
        assert category is not None
        assert category.id is not None
        assert category.name == sample_tag_category_data["name"]
        assert category.description == sample_tag_category_data["description"]
        assert category.sort_order == sample_tag_category_data["sort_order"]
        assert category.tenant_id == created_tenant["id"]
        assert category.created_by == created_admin_user["id"]
        assert category.created_at is not None
        assert category.updated_at is not None
    
    def test_create_category_with_duplicate_name_should_raise_error(
        self, test_session: Session, created_tag_category, created_tenant
    ):
        """测试创建重复名称的标签分类应该抛出异常"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        category_create = TagCategoryCreate(
            name=created_tag_category["name"],
            description="重复名称测试",
            sort_order=2
        )
        
        # Act & Assert
        with pytest.raises(TagBusinessException) as exc_info:
            service.create_category(category_create, created_by=1)
        
        assert "已存在" in str(exc_info.value)
    
    def test_get_category_with_valid_id_should_return_category(
        self, test_session: Session, created_tag_category, created_tenant
    ):
        """测试使用有效ID获取标签分类应该返回分类对象"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        
        # Act
        category = service.get_category(created_tag_category["id"])
        
        # Assert
        assert category is not None
        assert category.id == created_tag_category["id"]
        assert category.name == created_tag_category["name"]
    
    def test_get_category_with_invalid_id_should_return_none(
        self, test_session: Session, created_tenant
    ):
        """测试使用无效ID获取标签分类应该返回None"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        
        # Act
        category = service.get_category(99999)
        
        # Assert
        assert category is None
    
    def test_get_categories_with_pagination_should_return_paginated_results(
        self, test_session: Session, multiple_tag_categories, created_tenant
    ):
        """测试获取标签分类应该返回所有结果（已移除分页）"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        query_params = TagCategoryQuery()

        # Act
        categories = service.get_categories(query_params)

        # Assert
        assert len(categories) >= len(multiple_tag_categories)
        assert all(category.tenant_id == created_tenant["id"] for category in categories)
    
    def test_get_categories_with_name_filter_should_return_filtered_results(
        self, test_session: Session, multiple_tag_categories, created_tenant
    ):
        """测试按名称筛选标签分类应该返回筛选结果"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        search_name = multiple_tag_categories[0]["name"][:5]  # 取前5个字符进行模糊搜索
        query_params = TagCategoryQuery(name=search_name)

        # Act
        categories = service.get_categories(query_params)

        # Assert
        assert len(categories) >= 1
        assert any(search_name in category.name for category in categories)
    
    def test_update_category_with_valid_data_should_return_updated_category(
        self, test_session: Session, created_tag_category, created_tenant
    ):
        """测试使用有效数据更新标签分类应该返回更新后的分类"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        update_data = TagCategoryUpdate(
            name="更新后的分类名称",
            description="更新后的描述",
            sort_order=10
        )
        
        # Act
        updated_category = service.update_category(created_tag_category["id"], update_data)
        
        # Assert
        assert updated_category.id == created_tag_category["id"]
        assert updated_category.name == "更新后的分类名称"
        assert updated_category.description == "更新后的描述"
        assert updated_category.sort_order == 10
        assert updated_category.updated_at is not None
    
    def test_update_category_with_invalid_id_should_raise_error(
        self, test_session: Session, created_tenant
    ):
        """测试更新不存在的标签分类应该抛出异常"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        update_data = TagCategoryUpdate(name="不存在的分类")
        
        # Act & Assert
        with pytest.raises(TagCategoryNotFoundError):
            service.update_category(99999, update_data)
    
    def test_delete_category_with_no_tags_should_succeed(
        self, test_session: Session, created_tag_category, created_tenant
    ):
        """测试删除没有标签的分类应该成功"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        
        # Act
        service.delete_category(created_tag_category["id"])
        
        # Assert
        deleted_category = service.get_category(created_tag_category["id"])
        assert deleted_category is None
    
    def test_delete_category_with_tags_should_raise_error(
        self, test_session: Session, created_tag, created_tenant
    ):
        """测试删除有标签的分类应该抛出异常"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        
        # Act & Assert
        with pytest.raises(TagBusinessException) as exc_info:
            service.delete_category(created_tag["category_id"])
        
        assert "还有" in str(exc_info.value) and "个标签" in str(exc_info.value)
    
    def test_delete_category_with_invalid_id_should_raise_error(
        self, test_session: Session, created_tenant
    ):
        """测试删除不存在的分类应该抛出异常"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        
        # Act & Assert
        with pytest.raises(TagCategoryNotFoundError):
            service.delete_category(99999)
    
    def test_get_category_with_tag_count_should_return_count(
        self, test_session: Session, created_tag, created_tenant
    ):
        """测试获取分类及标签数量应该返回正确计数"""
        # Arrange
        service = get_tag_category_service(test_session, created_tenant["id"])
        
        # Act
        result = service.get_category_with_tag_count(created_tag["category_id"])
        
        # Assert
        assert result is not None
        assert result["id"] == created_tag["category_id"]
        assert result["tag_count"] >= 1


class TestTagService:
    """标签服务单元测试"""

    def test_create_tag_with_valid_data_should_return_tag(
        self, test_session: Session, sample_tag_data, created_tag_category, created_admin_user
    ):
        """测试使用有效数据创建标签应该返回标签对象"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        tag_data = {**sample_tag_data, "category_id": created_tag_category["id"]}
        tag_create = TagCreate(**tag_data)

        # Act
        tag = service.create_tag(tag_create, created_by=created_admin_user["id"])

        # Assert
        assert tag is not None
        assert tag.id is not None
        assert tag.name == sample_tag_data["name"]
        assert tag.description == sample_tag_data["description"]
        assert tag.status == TagStatus.ACTIVE
        assert tag.category_id == created_tag_category["id"]
        assert tag.tenant_id == created_admin_user["tenant_id"]
        assert tag.created_by == created_admin_user["id"]
        assert tag.created_at is not None
        assert tag.updated_at is not None

    def test_create_tag_with_invalid_category_should_raise_error(
        self, test_session: Session, sample_tag_data, created_admin_user
    ):
        """测试使用无效分类ID创建标签应该抛出异常"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        tag_data = {**sample_tag_data, "category_id": 99999}
        tag_create = TagCreate(**tag_data)

        # Act & Assert
        with pytest.raises(TagCategoryNotFoundError):
            service.create_tag(tag_create, created_by=created_admin_user["id"])

    def test_create_tag_with_duplicate_name_in_category_should_raise_error(
        self, test_session: Session, created_tag, created_admin_user
    ):
        """测试在同一分类下创建重复名称的标签应该抛出异常"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        tag_create = TagCreate(
            name=created_tag["name"],
            category_id=created_tag["category_id"],
            description="重复名称测试"
        )

        # Act & Assert
        with pytest.raises(TagBusinessException) as exc_info:
            service.create_tag(tag_create, created_by=created_admin_user["id"])

        assert "已存在" in str(exc_info.value)

    def test_get_tag_with_valid_id_should_return_tag(
        self, test_session: Session, created_tag, created_admin_user
    ):
        """测试使用有效ID获取标签应该返回标签对象"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])

        # Act
        tag = service.get_tag(created_tag["id"])

        # Assert
        assert tag is not None
        assert tag.id == created_tag["id"]
        assert tag.name == created_tag["name"]

    def test_get_tag_with_invalid_id_should_return_none(
        self, test_session: Session, created_admin_user
    ):
        """测试使用无效ID获取标签应该返回None"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])

        # Act
        tag = service.get_tag(99999)

        # Assert
        assert tag is None

    def test_get_tags_with_pagination_should_return_paginated_results(
        self, test_session: Session, multiple_tags, created_admin_user
    ):
        """测试获取标签应该返回所有结果（已移除分页）"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        query_params = TagQuery()

        # Act
        tags = service.get_tags(query_params)

        # Assert
        assert len(tags) >= len(multiple_tags)
        assert all(tag.tenant_id == created_admin_user["tenant_id"] for tag in tags)

    def test_get_tags_with_status_filter_should_return_filtered_results(
        self, test_session: Session, multiple_tags, created_admin_user
    ):
        """测试按状态筛选标签应该返回筛选结果"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        query_params = TagQuery(status=TagStatus.ACTIVE)

        # Act
        tags = service.get_tags(query_params)

        # Assert
        assert len(tags) >= 1
        assert all(tag.status == TagStatus.ACTIVE for tag in tags)

    def test_get_tags_with_category_filter_should_return_filtered_results(
        self, test_session: Session, created_tag, created_admin_user
    ):
        """测试按分类筛选标签应该返回筛选结果"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        query_params = TagQuery(category_id=created_tag["category_id"])

        # Act
        tags = service.get_tags(query_params)

        # Assert
        assert len(tags) >= 1
        assert all(tag.category_id == created_tag["category_id"] for tag in tags)

    def test_update_tag_with_valid_data_should_return_updated_tag(
        self, test_session: Session, created_tag, created_admin_user
    ):
        """测试使用有效数据更新标签应该返回更新后的标签"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        update_data = TagUpdate(
            name="更新后的标签名称",
            description="更新后的描述",
            status=TagStatus.INACTIVE
        )

        # Act
        updated_tag = service.update_tag(created_tag["id"], update_data)

        # Assert
        assert updated_tag.id == created_tag["id"]
        assert updated_tag.name == "更新后的标签名称"
        assert updated_tag.description == "更新后的描述"
        assert updated_tag.status == TagStatus.INACTIVE
        assert updated_tag.updated_at is not None

    def test_update_tag_with_invalid_id_should_raise_error(
        self, test_session: Session, created_admin_user
    ):
        """测试更新不存在的标签应该抛出异常"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        update_data = TagUpdate(name="不存在的标签")

        # Act & Assert
        with pytest.raises(TagNotFoundError):
            service.update_tag(99999, update_data)

    def test_delete_tag_should_succeed(
        self, test_session: Session, created_tag, created_admin_user
    ):
        """测试删除标签应该成功"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])

        # Act
        service.delete_tag(created_tag["id"])

        # Assert
        deleted_tag = service.get_tag(created_tag["id"])
        assert deleted_tag is None

    def test_delete_tag_with_invalid_id_should_raise_error(
        self, test_session: Session, created_admin_user
    ):
        """测试删除不存在的标签应该抛出异常"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])

        # Act & Assert
        with pytest.raises(TagNotFoundError):
            service.delete_tag(99999)

    def test_batch_create_tags_should_return_created_tags(
        self, test_session: Session, batch_tag_names, created_tag_category, created_admin_user
    ):
        """测试批量创建标签应该返回创建的标签列表"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        batch_data = TagBatchCreate(
            category_id=created_tag_category["id"],
            tags=batch_tag_names
        )

        # Act
        created_tags = service.batch_create_tags(batch_data, created_by=created_admin_user["id"])

        # Assert
        assert len(created_tags) == len(batch_tag_names)
        assert all(tag.category_id == created_tag_category["id"] for tag in created_tags)
        assert all(tag.name in batch_tag_names for tag in created_tags)
        assert all(tag.status == TagStatus.ACTIVE for tag in created_tags)

    def test_batch_update_tags_should_return_updated_tags(
        self, test_session: Session, multiple_tags, created_admin_user
    ):
        """测试批量更新标签应该返回更新的标签列表"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        tag_ids = [tag["id"] for tag in multiple_tags]
        batch_data = TagBatchUpdate(
            tag_ids=tag_ids,
            status=TagStatus.INACTIVE
        )

        # Act
        updated_tags = service.batch_update_tags(batch_data)

        # Assert
        assert len(updated_tags) == len(multiple_tags)
        assert all(tag.status == TagStatus.INACTIVE for tag in updated_tags)

    def test_get_tags_with_category_should_return_tags_with_category_info(
        self, test_session: Session, created_tag, created_admin_user
    ):
        """测试获取带分类信息的标签应该返回包含分类名称的标签"""
        # Arrange
        service = get_tag_service(test_session, created_admin_user["tenant_id"])
        query_params = TagQuery()

        # Act
        tags_with_category, total = service.get_tags_with_category(query_params)

        # Assert
        assert len(tags_with_category) >= 1

        # 检查第一个标签是否包含分类信息
        tag_dict = tags_with_category[0]
        assert "category_name" in tag_dict
        assert tag_dict["category_name"] is not None
