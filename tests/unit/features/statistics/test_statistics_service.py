"""统计服务单元测试"""

import pytest
from datetime import datetime, date, timedelta
from unittest.mock import Mock, patch
from sqlmodel import Session

from app.features.teachers.statistics_service import TeacherStatisticsService
from app.features.members.statistics_service import MemberStatisticsService
from app.features.teachers.statistics_models import (
    TeacherStatistics, TeacherStatisticsCreate, TeacherStatisticsIncrement,
    TeacherStatisticsAggregation
)
from app.features.members.statistics_models import (
    MemberStatistics, MemberStatisticsCreate, MemberStatisticsIncrement,
    MemberStatisticsAggregation
)


class TestTeacherStatisticsService:
    """教师统计服务测试类"""
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def service(self, mock_session):
        """创建教师统计服务实例"""
        return TeacherStatisticsService(mock_session, tenant_id=1)
    
    @pytest.fixture
    def sample_statistics(self):
        """示例教师统计数据"""
        return TeacherStatistics(
            teacher_id=1,
            tenant_id=1,
            total_classes=10,
            completed_classes=8,
            cancelled_classes=1,
            no_show_classes=1,
            total_earnings=1000,
            current_month_earnings=300,
            average_rating=4.5,
            total_ratings=20,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    def test_service_initialization(self, service):
        """测试服务初始化"""
        assert service.tenant_id == 1
        assert service.model_class == TeacherStatistics
    
    def test_get_or_create_statistics_existing(self, service, sample_statistics, mock_session):
        """测试获取已存在的统计记录"""
        # Mock数据库查询返回已存在的记录
        mock_session.get.return_value = sample_statistics
        
        result = service.get_or_create_statistics(1)
        
        assert result == sample_statistics
        mock_session.get.assert_called_once_with(TeacherStatistics, 1)
        # 不应该创建新记录
        mock_session.add.assert_not_called()
    
    def test_get_or_create_statistics_new(self, service, mock_session):
        """测试创建新的统计记录"""
        # Mock数据库查询返回None（不存在）
        mock_session.get.return_value = None
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        result = service.get_or_create_statistics(1)
        
        assert result.teacher_id == 1
        assert result.tenant_id == 1
        assert result.total_classes == 0
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
    
    def test_get_statistics_success(self, service, sample_statistics, mock_session):
        """测试获取统计信息成功"""
        mock_session.get.return_value = sample_statistics
        
        result = service.get_statistics(1)
        
        assert result == sample_statistics
        mock_session.get.assert_called_once_with(TeacherStatistics, 1)
    
    def test_get_statistics_not_found(self, service, mock_session):
        """测试获取不存在的统计信息"""
        mock_session.get.return_value = None
        
        result = service.get_statistics(999)
        
        assert result is None
    
    def test_increment_statistics_class_completed(self, service, sample_statistics, mock_session):
        """测试增量更新统计 - 完成课程"""
        # Mock获取已存在的统计记录
        mock_session.get.return_value = sample_statistics
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        increment_data = TeacherStatisticsIncrement(
            class_completed=True,
            earnings_added=100
        )
        
        result = service.increment_statistics(1, increment_data)
        
        # 验证统计数据更新
        assert result.total_classes == 11  # 原来10 + 1
        assert result.completed_classes == 9  # 原来8 + 1
        assert result.total_earnings == 1100  # 原来1000 + 100
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
    
    def test_increment_statistics_class_cancelled(self, service, sample_statistics, mock_session):
        """测试增量更新统计 - 取消课程"""
        mock_session.get.return_value = sample_statistics
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        increment_data = TeacherStatisticsIncrement(
            class_cancelled=True
        )
        
        result = service.increment_statistics(1, increment_data)
        
        # 验证统计数据更新
        assert result.total_classes == 11  # 原来10 + 1
        assert result.cancelled_classes == 2  # 原来1 + 1
        assert result.completed_classes == 8  # 保持不变
    
    def test_get_teacher_statistics_list_basic(self, service, mock_session):
        """测试获取教师统计列表基本功能"""
        # Mock查询结果
        mock_stats = [Mock(), Mock()]
        mock_session.exec.return_value.all.return_value = mock_stats

        result = service.get_teacher_statistics_list(teacher_ids=[1, 2])

        assert result == mock_stats
        # 验证exec被调用（可能被调用多次，所以不检查具体次数）
        assert mock_session.exec.called
    
    def test_service_methods_exist(self, service):
        """测试服务方法存在"""
        assert hasattr(service, 'get_or_create_statistics')
        assert hasattr(service, 'get_statistics')
        assert hasattr(service, 'increment_statistics')
        assert hasattr(service, 'get_teacher_statistics_list')
        assert hasattr(service, 'get_tenant_aggregation')
        
        # 验证方法可调用
        assert callable(getattr(service, 'get_or_create_statistics'))
        assert callable(getattr(service, 'get_statistics'))
        assert callable(getattr(service, 'increment_statistics'))


class TestMemberStatisticsService:
    """会员统计服务测试类"""
    
    @pytest.fixture
    def mock_session(self):
        """模拟数据库会话"""
        return Mock(spec=Session)
    
    @pytest.fixture
    def service(self, mock_session):
        """创建会员统计服务实例"""
        return MemberStatisticsService(mock_session, tenant_id=1)
    
    @pytest.fixture
    def sample_statistics(self):
        """示例会员统计数据"""
        return MemberStatistics(
            member_id=1,
            tenant_id=1,
            total_classes=15,
            completed_classes=12,
            cancelled_classes=2,
            no_show_classes=1,
            total_spent=1500,
            current_month_spent=400,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    def test_service_initialization(self, service):
        """测试服务初始化"""
        assert service.tenant_id == 1
        assert service.model_class == MemberStatistics
    
    def test_get_or_create_statistics_existing(self, service, sample_statistics, mock_session):
        """测试获取已存在的统计记录"""
        # Mock数据库查询返回已存在的记录
        mock_session.get.return_value = sample_statistics
        
        result = service.get_or_create_statistics(1)
        
        assert result == sample_statistics
        mock_session.get.assert_called_once_with(MemberStatistics, 1)
        # 不应该创建新记录
        mock_session.add.assert_not_called()
    
    def test_get_or_create_statistics_new(self, service, mock_session):
        """测试创建新的统计记录"""
        # Mock数据库查询返回None（不存在）
        mock_session.get.return_value = None
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        result = service.get_or_create_statistics(1)
        
        assert result.member_id == 1
        assert result.tenant_id == 1
        assert result.total_classes == 0
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
    
    def test_increment_statistics_class_completed(self, service, sample_statistics, mock_session):
        """测试增量更新统计 - 完成课程"""
        # Mock获取已存在的统计记录
        mock_session.get.return_value = sample_statistics
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        increment_data = MemberStatisticsIncrement(
            class_completed=True,
            amount_spent=150
        )
        
        result = service.increment_statistics(1, increment_data)
        
        # 验证统计数据更新
        assert result.total_classes == 16  # 原来15 + 1
        assert result.completed_classes == 13  # 原来12 + 1
        assert result.total_spent == 1650  # 原来1500 + 150
        
        # 验证数据库操作
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.refresh.assert_called_once()
    
    def test_increment_statistics_class_no_show(self, service, sample_statistics, mock_session):
        """测试增量更新统计 - 缺席课程"""
        mock_session.get.return_value = sample_statistics
        mock_session.add = Mock()
        mock_session.commit = Mock()
        mock_session.refresh = Mock()
        
        increment_data = MemberStatisticsIncrement(
            class_no_show=True
        )
        
        result = service.increment_statistics(1, increment_data)
        
        # 验证统计数据更新
        assert result.total_classes == 16  # 原来15 + 1
        assert result.no_show_classes == 2  # 原来1 + 1
        assert result.completed_classes == 12  # 保持不变
    
    def test_service_methods_exist(self, service):
        """测试服务方法存在"""
        assert hasattr(service, 'get_or_create_statistics')
        assert hasattr(service, 'get_statistics')
        assert hasattr(service, 'increment_statistics')
        assert hasattr(service, 'get_tenant_aggregation')
        
        # 验证方法可调用
        assert callable(getattr(service, 'get_or_create_statistics'))
        assert callable(getattr(service, 'get_statistics'))
        assert callable(getattr(service, 'increment_statistics'))
    
    def test_statistics_models_creation(self):
        """测试统计模型创建"""
        # 测试教师统计增量模型
        teacher_increment = TeacherStatisticsIncrement(
            class_completed=True,
            earnings_added=100
        )
        assert teacher_increment.class_completed is True
        assert teacher_increment.earnings_added == 100
        
        # 测试会员统计增量模型
        member_increment = MemberStatisticsIncrement(
            class_completed=True,
            amount_spent=150
        )
        assert member_increment.class_completed is True
        assert member_increment.amount_spent == 150
