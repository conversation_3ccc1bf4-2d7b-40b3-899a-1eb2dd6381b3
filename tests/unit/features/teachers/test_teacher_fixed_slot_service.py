"""教师固定时间段服务层单元测试"""
import pytest
from datetime import time
from sqlmodel import Session

from app.features.teachers.fixed_slots_service import TeacherFixedSlotService
from app.features.teachers.fixed_slots_schemas import (
    TeacherFixedSlotCreate, TeacherFixedSlotUpdate, TeacherFixedSlotQ<PERSON>y,
    TeacherFixedSlotBatchCreate, TeacherFixedSlotBatchUpdate, TeacherFixedSlotBatchDelete,
    AvailableTimesQuery, TimeSlotConflictCheck
)
from app.features.teachers.fixed_slots_models import Weekday
from app.features.teachers.fixed_slots_exceptions import (
    TeacherFixedSlotNotFoundError, TeacherFixedSlotBusinessException
)


class TestTeacherFixedSlotService:
    """教师固定时间段服务测试类"""

    def test_create_slot_success(self, test_session: Session, created_teacher, created_admin_user):
        """测试成功创建时间段"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        slot_data = TeacherFixedSlotCreate(
            teacher_id=created_teacher["id"],
            weekday=Weekday.MONDAY,
            start_time=time(8, 30),
            duration_minutes=25,
            is_available=True,
            is_visible_to_members=True,
            created_by=created_admin_user["id"]
        )
        
        slot = service.create_slot(slot_data, created_admin_user["id"], created_admin_user["username"])
        
        assert slot.id is not None
        assert slot.teacher_id == created_teacher["id"]
        assert slot.weekday == Weekday.MONDAY
        assert slot.start_time == time(8, 30)
        assert slot.duration_minutes == 25
        assert slot.is_available is True
        assert slot.is_visible_to_members is True
        assert slot.tenant_id == created_admin_user["tenant_id"]
        assert slot.created_by == created_admin_user["id"]
        assert slot.created_at is not None
        assert slot.updated_at is not None

    def test_create_slot_teacher_not_found(self, test_session: Session, created_admin_user):
        """测试创建时间段时教师不存在"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        slot_data = TeacherFixedSlotCreate(
            teacher_id=99999,  # 不存在的教师ID
            weekday=Weekday.MONDAY,
            start_time=time(8, 30),
            duration_minutes=25,
            is_available=True,
            is_visible_to_members=True,
            created_by=created_admin_user["id"]
        )
        
        with pytest.raises(TeacherFixedSlotBusinessException) as exc_info:
            service.create_slot(slot_data, created_admin_user["id"], created_admin_user["username"])
        
        assert "教师不存在" in str(exc_info.value)

    def test_create_slot_time_conflict(self, test_session: Session, created_fixed_slot, created_admin_user):
        """测试创建时间段时发生冲突"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        # 尝试创建与现有时间段冲突的时间段
        slot_data = TeacherFixedSlotCreate(
            teacher_id=created_fixed_slot["teacher_id"],
            weekday=created_fixed_slot["weekday"],
            start_time=created_fixed_slot["start_time"],  # 相同时间
            duration_minutes=25,
            is_available=True,
            is_visible_to_members=True,
            created_by=created_admin_user["id"]
        )
        
        with pytest.raises(TeacherFixedSlotBusinessException) as exc_info:
            service.create_slot(slot_data, created_admin_user["id"], created_admin_user["username"])
        
        assert "时间段已存在" in str(exc_info.value)

    def test_get_slot_success(self, test_session: Session, created_fixed_slot, created_admin_user):
        """测试成功获取时间段"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        slot = service.get_slot(created_fixed_slot["id"])
        
        assert slot is not None
        assert slot.id == created_fixed_slot["id"]
        assert slot.teacher_id == created_fixed_slot["teacher_id"]
        assert slot.weekday == created_fixed_slot["weekday"]

    def test_get_slot_not_found(self, test_session: Session, created_admin_user):
        """测试获取不存在的时间段"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        slot = service.get_slot(99999)
        assert slot is None

    def test_get_slots_with_query(self, test_session: Session, created_multiple_fixed_slots, created_admin_user):
        """测试查询时间段列表"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        # 基础查询
        query = TeacherFixedSlotQuery(
            teacher_id=created_multiple_fixed_slots[0]["teacher_id"],
            page=1,
            size=10
        )
        
        slots, total = service.get_slots(query)
        
        assert len(slots) > 0
        assert total > 0
        assert all(slot.teacher_id == created_multiple_fixed_slots[0]["teacher_id"] for slot in slots)

    def test_get_slots_with_filters(self, test_session: Session, created_multiple_fixed_slots, created_admin_user):
        """测试带筛选条件的查询"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        # 筛选查询
        query = TeacherFixedSlotQuery(
            teacher_id=created_multiple_fixed_slots[0]["teacher_id"],
            weekday=Weekday.MONDAY,
            is_available=True,
            page=1,
            size=10
        )
        
        slots, total = service.get_slots(query)
        
        # 验证筛选结果
        for slot in slots:
            assert slot.teacher_id == created_multiple_fixed_slots[0]["teacher_id"]
            assert slot.weekday == Weekday.MONDAY
            assert slot.is_available is True

    def test_update_slot_success(self, test_session: Session, created_fixed_slot, created_admin_user):
        """测试成功更新时间段"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        update_data = TeacherFixedSlotUpdate(
            duration_minutes=30,
            is_available=False
        )
        
        updated_slot = service.update_slot(created_fixed_slot["id"], update_data)
        
        assert updated_slot.id == created_fixed_slot["id"]
        assert updated_slot.duration_minutes == 30
        assert updated_slot.is_available is False
        assert updated_slot.updated_at > updated_slot.created_at

    def test_update_slot_not_found(self, test_session: Session, created_admin_user):
        """测试更新不存在的时间段"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        update_data = TeacherFixedSlotUpdate(is_available=False)
        
        with pytest.raises(TeacherFixedSlotNotFoundError):
            service.update_slot(99999, update_data)

    def test_update_slot_time_conflict(self, test_session: Session, created_multiple_fixed_slots, created_admin_user):
        """测试更新时间段时发生冲突"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        # 尝试将第二个时间段的时间改为与第一个冲突
        first_slot = created_multiple_fixed_slots[0]
        second_slot = created_multiple_fixed_slots[1]
        
        update_data = TeacherFixedSlotUpdate(
            weekday=first_slot["weekday"],
            start_time=first_slot["start_time"]
        )
        
        with pytest.raises(TeacherFixedSlotBusinessException) as exc_info:
            service.update_slot(second_slot["id"], update_data)
        
        assert "时间段已存在" in str(exc_info.value)

    def test_delete_slot_success(self, test_session: Session, created_fixed_slot, created_admin_user):
        """测试成功删除时间段"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        # 删除时间段
        service.delete_slot(created_fixed_slot["id"])
        
        # 验证已删除
        slot = service.get_slot(created_fixed_slot["id"])
        assert slot is None

    def test_delete_slot_not_found(self, test_session: Session, created_admin_user):
        """测试删除不存在的时间段"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        with pytest.raises(TeacherFixedSlotNotFoundError):
            service.delete_slot(99999)

    def test_check_time_conflict(self, test_session: Session, created_fixed_slot, created_admin_user):
        """测试时间冲突检测"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        # 测试冲突检测
        conflict_check = TimeSlotConflictCheck(
            teacher_id=created_fixed_slot["teacher_id"],
            weekday=created_fixed_slot["weekday"],
            start_time=created_fixed_slot["start_time"],
            duration_minutes=25
        )
        
        has_conflict = service.check_time_conflict(conflict_check)
        assert has_conflict is True
        
        # 测试无冲突情况
        no_conflict_check = TimeSlotConflictCheck(
            teacher_id=created_fixed_slot["teacher_id"],
            weekday=Weekday.TUESDAY,  # 不同星期
            start_time=created_fixed_slot["start_time"],
            duration_minutes=25
        )
        
        has_conflict = service.check_time_conflict(no_conflict_check)
        assert has_conflict is False

    def test_get_available_times(self, test_session: Session, created_multiple_fixed_slots, created_admin_user):
        """测试获取可用时间段"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        query = AvailableTimesQuery(
            teacher_id=created_multiple_fixed_slots[0]["teacher_id"],
            only_available=True,
            only_visible=True
        )
        
        available_slots = service.get_available_times(query)
        
        assert len(available_slots) > 0
        for slot in available_slots:
            assert slot.is_available is True
            assert slot.is_visible_to_members is True

    def test_get_slot_statistics(self, test_session: Session, created_multiple_fixed_slots, created_admin_user):
        """测试获取时间段统计信息"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        teacher_id = created_multiple_fixed_slots[0]["teacher_id"]
        stats = service.get_slot_statistics(teacher_id)
        
        assert stats.teacher_id == teacher_id
        assert stats.total_slots > 0
        assert stats.available_slots >= 0
        assert stats.visible_slots >= 0
        assert isinstance(stats.weekday_distribution, dict)

    def test_get_teacher_weekly_schedule(self, test_session: Session, created_weekly_schedule, created_admin_user):
        """测试获取教师周时间安排"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])
        
        # 获取第一个教师的ID
        first_weekday_slots = list(created_weekly_schedule.values())[0]
        teacher_id = first_weekday_slots[0]["teacher_id"]
        
        weekly_schedule = service.get_teacher_weekly_schedule(teacher_id)
        
        assert isinstance(weekly_schedule, dict)
        assert len(weekly_schedule) == 7  # 一周7天
        
        # 验证有数据的星期
        total_slots = sum(len(slots) for slots in weekly_schedule.values())
        assert total_slots > 0


class TestTeacherFixedSlotBatchOperations:
    """教师固定时间段批量操作测试类"""

    def test_batch_create_slots_success(self, test_session: Session, created_teacher, created_admin_user, batch_create_data):
        """测试批量创建时间段成功"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])

        # 准备批量创建数据
        slots_data = []
        for slot_data in batch_create_data["valid_slots"]:
            slots_data.append(TeacherFixedSlotCreate(
                teacher_id=created_teacher["id"],
                created_by=created_admin_user["id"],
                **slot_data
            ))

        batch_data = TeacherFixedSlotBatchCreate(
            teacher_id=created_teacher["id"],
            slots=slots_data,
            created_by=created_admin_user["id"]
        )

        created_slots = service.batch_create_slots(batch_data, created_admin_user["username"])

        assert len(created_slots) == len(batch_create_data["valid_slots"])
        for slot in created_slots:
            assert slot.teacher_id == created_teacher["id"]
            assert slot.tenant_id == created_admin_user["tenant_id"]

    def test_batch_create_slots_with_conflicts(self, test_session: Session, created_fixed_slot, created_admin_user, batch_create_data):
        """测试批量创建时间段时有冲突"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])

        # 包含冲突的批量创建数据
        slots_data = []
        for slot_data in batch_create_data["valid_slots"] + batch_create_data["conflict_slots"]:
            slots_data.append(TeacherFixedSlotCreate(
                teacher_id=created_fixed_slot["teacher_id"],
                created_by=created_admin_user["id"],
                **slot_data
            ))

        batch_data = TeacherFixedSlotBatchCreate(
            teacher_id=created_fixed_slot["teacher_id"],
            slots=slots_data,
            created_by=created_admin_user["id"]
        )

        created_slots = service.batch_create_slots(batch_data, created_admin_user["username"])

        # 应该只创建了无冲突的时间段
        assert len(created_slots) < len(slots_data)
        assert len(created_slots) == len(batch_create_data["valid_slots"])

    def test_batch_update_slots_success(self, test_session: Session, created_multiple_fixed_slots, created_admin_user):
        """测试批量更新时间段成功"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])

        # 准备批量更新数据
        updates = [
            {"id": created_multiple_fixed_slots[0]["id"], "is_available": False},
            {"id": created_multiple_fixed_slots[1]["id"], "duration_minutes": 30},
        ]

        batch_data = TeacherFixedSlotBatchUpdate(updates=updates)
        updated_slots = service.batch_update_slots(batch_data)

        assert len(updated_slots) == 2

        # 验证更新结果
        updated_slot_1 = service.get_slot(created_multiple_fixed_slots[0]["id"])
        assert updated_slot_1.is_available is False

        updated_slot_2 = service.get_slot(created_multiple_fixed_slots[1]["id"])
        assert updated_slot_2.duration_minutes == 30

    def test_batch_delete_slots_success(self, test_session: Session, created_multiple_fixed_slots, created_admin_user):
        """测试批量删除时间段成功"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])

        # 准备批量删除数据
        ids_to_delete = [slot["id"] for slot in created_multiple_fixed_slots[:2]]
        batch_data = TeacherFixedSlotBatchDelete(ids=ids_to_delete)

        deleted_count = service.batch_delete_slots(batch_data)

        assert deleted_count == 2

        # 验证删除结果
        for slot_id in ids_to_delete:
            slot = service.get_slot(slot_id)
            assert slot is None

    def test_clear_teacher_slots(self, test_session: Session, created_multiple_fixed_slots, created_admin_user):
        """测试清空教师时间段"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])

        teacher_id = created_multiple_fixed_slots[0]["teacher_id"]
        deleted_count = service.clear_teacher_slots(teacher_id)

        assert deleted_count > 0

        # 验证清空结果
        query = TeacherFixedSlotQuery(teacher_id=teacher_id, page=1, size=100)
        slots, total = service.get_slots(query)
        assert total == 0

    def test_copy_teacher_slots(self, test_session: Session, created_teacher, created_teacher_2, created_multiple_fixed_slots, created_admin_user):
        """测试复制教师时间段"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])

        source_teacher_id = created_multiple_fixed_slots[0]["teacher_id"]
        target_teacher_id = created_teacher_2["id"]

        copied_slots = service.copy_teacher_slots(
            source_teacher_id,
            target_teacher_id,
            created_admin_user["id"]
        )

        assert len(copied_slots) > 0

        # 验证复制结果
        for slot in copied_slots:
            assert slot.teacher_id == target_teacher_id
            assert slot.created_by == created_admin_user["id"]


class TestTeacherFixedSlotEdgeCases:
    """教师固定时间段边界情况测试类"""

    def test_create_slot_with_default_duration(self, test_session: Session, created_teacher, created_admin_user):
        """测试创建时间段使用默认时长"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])

        slot_data = TeacherFixedSlotCreate(
            teacher_id=created_teacher["id"],
            weekday=Weekday.SUNDAY,
            start_time=time(10, 0),
            duration_minutes=None,  # 使用默认时长
            is_available=True,
            is_visible_to_members=True,
            created_by=created_admin_user["id"]
        )

        slot = service.create_slot(slot_data, created_admin_user["id"], created_admin_user["username"])

        assert slot.duration_minutes is None  # 数据库中存储为NULL

    def test_query_with_time_range(self, test_session: Session, created_multiple_fixed_slots, created_admin_user):
        """测试时间范围查询"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])

        query = TeacherFixedSlotQuery(
            teacher_id=created_multiple_fixed_slots[0]["teacher_id"],
            start_time_from=time(8, 0),
            start_time_to=time(12, 0),
            page=1,
            size=10
        )

        slots, total = service.get_slots(query)

        # 验证时间范围
        for slot in slots:
            assert time(8, 0) <= slot.start_time <= time(12, 0)

    def test_available_times_with_weekday_filter(self, test_session: Session, created_weekly_schedule, created_admin_user):
        """测试按星期筛选可用时间"""
        service = TeacherFixedSlotService(test_session, created_admin_user["tenant_id"])

        # 获取第一个教师的ID
        first_weekday_slots = list(created_weekly_schedule.values())[0]
        teacher_id = first_weekday_slots[0]["teacher_id"]

        query = AvailableTimesQuery(
            teacher_id=teacher_id,
            weekdays=[Weekday.MONDAY, Weekday.TUESDAY],
            only_available=True
        )

        available_slots = service.get_available_times(query)

        # 验证星期筛选
        for slot in available_slots:
            assert slot.weekday in [Weekday.MONDAY, Weekday.TUESDAY]

    def test_empty_batch_operations(self):
        """测试空的批量操作"""
        # 空的批量删除 - 应该在Schema层就被拦截
        with pytest.raises(ValueError, match="ID列表不能为空"):
            TeacherFixedSlotBatchDelete(ids=[])
