"""
测试配置文件
集中管理pytest配置和全局fixtures
"""
import pytest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def pytest_addoption(parser):
    """添加自定义命令行选项"""
    parser.addoption(
        "--keep-test-data",
        action="store_true",
        default=False,
        help="保留测试数据用于调试（不回滚事务）"
    )
    parser.addoption(
        "--test-db-name",
        action="store",
        default="course_booking_test_db",
        help="指定测试数据库名称"
    )
    parser.addoption(
        "--debug-rls",
        action="store_true",
        default=False,
        help="启用RLS调试信息"
    )
    parser.addoption(
        "--use-improved-fixtures",
        action="store_true",
        default=False,
        help="使用改进的数据库fixtures"
    )
    parser.addoption(
        "--enable-route-logging",
        action="store_true",
        default=False,
        help="启用路由日志记录"
    )


def pytest_configure(config):
    """pytest配置"""
    # 添加自定义标记
    config.addinivalue_line(
        "markers", "rls_debug: 需要RLS调试信息的测试"
    )
    config.addinivalue_line(
        "markers", "keep_data: 需要保留测试数据的测试"
    )
    config.addinivalue_line(
        "markers", "multi_tenant: 多租户相关测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为多租户测试添加标记
    for item in items:
        if "tenant" in item.name.lower() or "multi_tenant" in item.keywords:
            item.add_marker(pytest.mark.multi_tenant)


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment(request):
    """设置测试环境"""
    print("\n🚀 Setting up test environment...")
    
    # 设置测试环境变量
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "INFO"
    
    # 处理路由日志记录参数
    if request.config.getoption("--enable-route-logging"):
        os.environ["ENABLE_ROUTE_LOGGING"] = "true"
        print("📊 启用路由日志记录")
    else:
        os.environ["ENABLE_ROUTE_LOGGING"] = "false"
    
    yield
    
    print("\n👋 Cleaning up test environment...")


@pytest.fixture(autouse=True)
def reset_global_state():
    """重置全局状态（每个测试前后执行）"""
    # 测试前重置
    yield
    # 测试后清理（如果需要）


# 注意：不在这里直接导入fixtures，避免PytestAssertRewriteWarning
# fixtures通过pytest_plugins机制在根目录conftest.py中加载

# 条件性导入改进的fixtures
def pytest_sessionstart(session):
    """会话开始时的钩子"""
    config = session.config
    if config.getoption("--use-improved-fixtures"):
        print("🔧 Using improved database fixtures...")
        # 这里可以动态替换fixtures
        # 或者提供选择机制


# 调试相关的fixtures
@pytest.fixture
def debug_mode(request):
    """检查是否处于调试模式"""
    return (
        request.config.getoption("--keep-test-data") or
        request.config.getoption("--debug-rls") or
        hasattr(request, 'node') and 'debug' in request.node.name
    )


@pytest.fixture
def test_info(request):
    """测试信息fixture"""
    return {
        "test_name": request.node.name,
        "test_file": request.node.fspath.basename,
        "test_class": request.node.cls.__name__ if request.node.cls else None,
        "markers": [mark.name for mark in request.node.iter_markers()],
        "keep_data": request.config.getoption("--keep-test-data"),
        "debug_rls": request.config.getoption("--debug-rls"),
        "enable_route_logging": request.config.getoption("--enable-route-logging"),
    }


@pytest.fixture
def print_test_info(test_info, debug_mode):
    """打印测试信息（调试模式下）"""
    if debug_mode:
        print(f"\n📋 Test Info:")
        print(f"  Name: {test_info['test_name']}")
        print(f"  File: {test_info['test_file']}")
        print(f"  Class: {test_info['test_class']}")
        print(f"  Markers: {test_info['markers']}")
        print(f"  Keep Data: {test_info['keep_data']}")
        print(f"  Debug RLS: {test_info['debug_rls']}")
        print(f"  Route Logging: {test_info['enable_route_logging']}")


# 测试报告相关
def pytest_runtest_makereport(item, call):
    """生成测试报告"""
    if call.when == "call":
        # 测试执行阶段
        if call.excinfo is not None:
            # 测试失败时的处理
            if hasattr(item, 'config') and item.config.getoption("--keep-test-data"):
                print(f"\n❌ Test failed: {item.name}")
                print("💾 Test data has been preserved for debugging")


# 性能监控
@pytest.fixture(autouse=True)
def monitor_test_performance(request):
    """监控测试性能"""
    import time
    start_time = time.time()
    
    yield
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 如果测试时间超过阈值，发出警告
    if duration > 10:  # 10秒
        print(f"\n⚠️  Slow test detected: {request.node.name} took {duration:.2f}s")


# 数据库连接池监控
@pytest.fixture(scope="session")
def db_connection_monitor():
    """数据库连接池监控"""
    connections = {"active": 0, "total": 0}
    
    def track_connection():
        connections["active"] += 1
        connections["total"] += 1
    
    def release_connection():
        connections["active"] -= 1
    
    def get_stats():
        return connections.copy()
    
    yield {
        "track": track_connection,
        "release": release_connection,
        "stats": get_stats
    }
    
    # 会话结束时检查是否有连接泄漏
    if connections["active"] > 0:
        print(f"\n⚠️  Connection leak detected: {connections['active']} active connections")


# 错误收集
@pytest.fixture(scope="session")
def error_collector():
    """收集测试过程中的错误"""
    errors = []
    
    def add_error(error_type, message, context=None):
        errors.append({
            "type": error_type,
            "message": message,
            "context": context,
            "timestamp": time.time()
        })
    
    def get_errors():
        return errors.copy()
    
    yield {
        "add": add_error,
        "get": get_errors
    }
    
    # 会话结束时报告错误统计
    if errors:
        print(f"\n📊 Error Summary: {len(errors)} errors collected")
        error_types = {}
        for error in errors:
            error_type = error["type"]
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        for error_type, count in error_types.items():
            print(f"  {error_type}: {count}")


# 测试数据统计
@pytest.fixture(scope="session")
def test_data_stats():
    """测试数据统计"""
    stats = {
        "tenants_created": 0,
        "users_created": 0,
        "members_created": 0,
        "teachers_created": 0,
        "locks_created": 0
    }
    
    def increment(key):
        if key in stats:
            stats[key] += 1
    
    def get_stats():
        return stats.copy()
    
    yield {
        "increment": increment,
        "get": get_stats
    }
    
    # 会话结束时报告统计
    print(f"\n📊 Test Data Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


# 清理钩子
def pytest_sessionfinish(session, exitstatus):
    """会话结束时的清理"""
    print(f"\n✅ Test session finished with exit status: {exitstatus}")
    
    # 这里可以添加额外的清理逻辑
    # 比如清理临时文件、发送测试报告等
