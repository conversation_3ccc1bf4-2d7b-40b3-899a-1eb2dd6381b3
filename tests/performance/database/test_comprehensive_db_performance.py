"""
全面的数据库性能测试

重点测试真实业务场景下的数据库操作性能
"""

import pytest
import time
from sqlmodel import select, func, and_, or_
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer


class TestComprehensiveDatabasePerformance:
    """全面的数据库性能测试"""
    
    def test_tenant_queries_performance(self, fixed_performance_session, realistic_test_data):
        """租户相关查询性能测试"""
        
        session = fixed_performance_session
        
        print(f"\n🏢 租户查询性能测试")
        print(f"=" * 60)
        
        from app.features.tenants.models import Tenant
        
        # 1. 基础查询性能
        print(f"\n1️⃣ 基础租户查询")
        query_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                result = session.exec(select(Tenant).limit(10)).all()
            query_times.append(timer.elapsed_time)
        
        metrics = PerformanceAnalyzer.analyze_response_times(query_times)
        print(f"   平均查询时间: {metrics.avg_time*1000:.2f}ms")
        print(f"   查询QPS: {1/metrics.avg_time:.1f}")
        
        # 2. 条件查询性能
        print(f"\n2️⃣ 条件查询性能")
        condition_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                result = session.exec(
                    select(Tenant).where(Tenant.status == "active")
                ).all()
            condition_times.append(timer.elapsed_time)
        
        condition_metrics = PerformanceAnalyzer.analyze_response_times(condition_times)
        print(f"   平均查询时间: {condition_metrics.avg_time*1000:.2f}ms")
        print(f"   查询QPS: {1/condition_metrics.avg_time:.1f}")
        
        # 3. 计数查询性能
        print(f"\n3️⃣ 计数查询性能")
        count_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                count = session.exec(select(func.count(Tenant.id))).first()
            count_times.append(timer.elapsed_time)
        
        count_metrics = PerformanceAnalyzer.analyze_response_times(count_times)
        print(f"   平均计数时间: {count_metrics.avg_time*1000:.2f}ms")
        print(f"   计数QPS: {1/count_metrics.avg_time:.1f}")
        
        return {
            "basic_query": metrics,
            "condition_query": condition_metrics,
            "count_query": count_metrics
        }
    
    def test_user_queries_performance(self, fixed_performance_session, realistic_test_data):
        """用户相关查询性能测试"""
        
        session = fixed_performance_session
        
        print(f"\n👤 用户查询性能测试")
        print(f"=" * 60)
        
        from app.features.users.models import User
        
        # 1. 邮箱查询性能（登录场景）
        print(f"\n1️⃣ 邮箱查询性能（登录场景）")
        tenant_data = realistic_test_data["tenant_0"]
        admin_user = tenant_data["admin_users"][0]
        
        email_query_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                user = session.exec(
                    select(User).where(User.email == admin_user.email)
                ).first()
            email_query_times.append(timer.elapsed_time)
        
        email_metrics = PerformanceAnalyzer.analyze_response_times(email_query_times)
        print(f"   平均查询时间: {email_metrics.avg_time*1000:.2f}ms")
        print(f"   查询QPS: {1/email_metrics.avg_time:.1f}")
        
        # 2. 租户用户列表查询
        print(f"\n2️⃣ 租户用户列表查询")
        tenant = tenant_data["tenant"]
        
        list_query_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                users = session.exec(
                    select(User).where(User.tenant_id == tenant.id).limit(20)
                ).all()
            list_query_times.append(timer.elapsed_time)
        
        list_metrics = PerformanceAnalyzer.analyze_response_times(list_query_times)
        print(f"   平均查询时间: {list_metrics.avg_time*1000:.2f}ms")
        print(f"   查询QPS: {1/list_metrics.avg_time:.1f}")
        
        # 3. 复合条件查询
        print(f"\n3️⃣ 复合条件查询")
        complex_query_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                users = session.exec(
                    select(User).where(
                        and_(
                            User.tenant_id == tenant.id,
                            User.status == "active",
                            User.role == "admin"
                        )
                    )
                ).all()
            complex_query_times.append(timer.elapsed_time)
        
        complex_metrics = PerformanceAnalyzer.analyze_response_times(complex_query_times)
        print(f"   平均查询时间: {complex_metrics.avg_time*1000:.2f}ms")
        print(f"   查询QPS: {1/complex_metrics.avg_time:.1f}")
        
        return {
            "email_query": email_metrics,
            "list_query": list_metrics,
            "complex_query": complex_metrics
        }
    
    def test_teacher_queries_performance(self, fixed_performance_session, realistic_test_data):
        """教师查询性能测试（高频场景）"""
        
        session = fixed_performance_session
        
        print(f"\n👨‍🏫 教师查询性能测试")
        print(f"=" * 60)
        
        from app.features.teachers.models import Teacher
        
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        
        # 1. 教师列表查询（分页）
        print(f"\n1️⃣ 教师列表查询（分页）")
        list_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                teachers = session.exec(
                    select(Teacher)
                    .where(Teacher.tenant_id == tenant.id)
                    .offset(0)
                    .limit(20)
                ).all()
            list_times.append(timer.elapsed_time)
        
        list_metrics = PerformanceAnalyzer.analyze_response_times(list_times)
        print(f"   平均查询时间: {list_metrics.avg_time*1000:.2f}ms")
        print(f"   查询QPS: {1/list_metrics.avg_time:.1f}")
        
        # 2. 教师筛选查询（按状态和分类）
        print(f"\n2️⃣ 教师筛选查询")
        filter_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                teachers = session.exec(
                    select(Teacher).where(
                        and_(
                            Teacher.tenant_id == tenant.id,
                            Teacher.status == "active",
                            Teacher.teacher_category == "european"
                        )
                    )
                ).all()
            filter_times.append(timer.elapsed_time)
        
        filter_metrics = PerformanceAnalyzer.analyze_response_times(filter_times)
        print(f"   平均查询时间: {filter_metrics.avg_time*1000:.2f}ms")
        print(f"   查询QPS: {1/filter_metrics.avg_time:.1f}")
        
        # 3. 教师搜索查询（模糊匹配）
        print(f"\n3️⃣ 教师搜索查询")
        search_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                teachers = session.exec(
                    select(Teacher).where(
                        and_(
                            Teacher.tenant_id == tenant.id,
                            Teacher.name.contains("测试")
                        )
                    )
                ).all()
            search_times.append(timer.elapsed_time)
        
        search_metrics = PerformanceAnalyzer.analyze_response_times(search_times)
        print(f"   平均查询时间: {search_metrics.avg_time*1000:.2f}ms")
        print(f"   查询QPS: {1/search_metrics.avg_time:.1f}")
        
        return {
            "list_query": list_metrics,
            "filter_query": filter_metrics,
            "search_query": search_metrics
        }
    
    def test_member_queries_performance(self, fixed_performance_session, realistic_test_data):
        """会员查询性能测试"""
        
        session = fixed_performance_session
        
        print(f"\n👥 会员查询性能测试")
        print(f"=" * 60)
        
        from app.features.members.models import Member
        
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        
        # 1. 会员列表查询
        print(f"\n1️⃣ 会员列表查询")
        list_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                members = session.exec(
                    select(Member)
                    .where(Member.tenant_id == tenant.id)
                    .offset(0)
                    .limit(20)
                ).all()
            list_times.append(timer.elapsed_time)
        
        list_metrics = PerformanceAnalyzer.analyze_response_times(list_times)
        print(f"   平均查询时间: {list_metrics.avg_time*1000:.2f}ms")
        print(f"   查询QPS: {1/list_metrics.avg_time:.1f}")
        
        # 2. 会员状态统计
        print(f"\n2️⃣ 会员状态统计")
        stats_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                active_count = session.exec(
                    select(func.count(Member.id)).where(
                        and_(
                            Member.tenant_id == tenant.id,
                            Member.status == "active"
                        )
                    )
                ).first()
            stats_times.append(timer.elapsed_time)
        
        stats_metrics = PerformanceAnalyzer.analyze_response_times(stats_times)
        print(f"   平均统计时间: {stats_metrics.avg_time*1000:.2f}ms")
        print(f"   统计QPS: {1/stats_metrics.avg_time:.1f}")
        
        return {
            "list_query": list_metrics,
            "stats_query": stats_metrics
        }
    
    def test_database_transaction_performance(self, fixed_performance_session, realistic_test_data):
        """数据库事务性能测试"""
        
        session = fixed_performance_session
        
        print(f"\n💾 数据库事务性能测试")
        print(f"=" * 60)
        
        from app.features.tenants.models import Tenant
        
        # 1. 简单事务性能
        print(f"\n1️⃣ 简单事务性能")
        simple_transaction_times = []
        
        for i in range(5):  # 减少次数，避免创建太多测试数据
            with PerformanceTimer() as timer:
                # 开始事务
                new_tenant = Tenant(
                    name=f"事务测试租户{i}",
                    code=f"tx_test_{i}_{int(time.time())}",
                    contact_email=f"tx_test_{i}@test.com",
                    contact_phone=f"138{i:08d}",
                    address=f"事务测试地址{i}",
                    status="active",
                    created_by=1
                )
                session.add(new_tenant)
                session.commit()
                session.refresh(new_tenant)
                
                # 立即删除测试数据
                session.delete(new_tenant)
                session.commit()
            
            simple_transaction_times.append(timer.elapsed_time)
        
        simple_metrics = PerformanceAnalyzer.analyze_response_times(simple_transaction_times)
        print(f"   平均事务时间: {simple_metrics.avg_time*1000:.2f}ms")
        print(f"   事务QPS: {1/simple_metrics.avg_time:.1f}")
        
        # 2. 批量操作性能
        print(f"\n2️⃣ 批量操作性能")
        batch_times = []
        
        for i in range(3):  # 减少批次
            with PerformanceTimer() as timer:
                # 批量创建
                tenants = []
                for j in range(5):  # 每批5个
                    tenant = Tenant(
                        name=f"批量测试租户{i}_{j}",
                        code=f"batch_test_{i}_{j}_{int(time.time())}",
                        contact_email=f"batch_test_{i}_{j}@test.com",
                        contact_phone=f"139{i}{j:07d}",
                        address=f"批量测试地址{i}_{j}",
                        status="active",
                        created_by=1
                    )
                    session.add(tenant)
                    tenants.append(tenant)
                
                session.commit()
                
                # 批量删除
                for tenant in tenants:
                    session.delete(tenant)
                session.commit()
            
            batch_times.append(timer.elapsed_time)
        
        batch_metrics = PerformanceAnalyzer.analyze_response_times(batch_times)
        print(f"   平均批量操作时间: {batch_metrics.avg_time*1000:.2f}ms")
        print(f"   批量操作QPS: {1/batch_metrics.avg_time:.1f}")
        
        return {
            "simple_transaction": simple_metrics,
            "batch_operation": batch_metrics
        }
    
    def test_database_performance_summary(self, fixed_performance_session, realistic_test_data):
        """数据库性能测试总结"""
        
        print(f"\n📊 数据库性能测试总结")
        print(f"=" * 60)
        
        # 运行所有测试并收集结果
        tenant_results = self.test_tenant_queries_performance(fixed_performance_session, realistic_test_data)
        user_results = self.test_user_queries_performance(fixed_performance_session, realistic_test_data)
        teacher_results = self.test_teacher_queries_performance(fixed_performance_session, realistic_test_data)
        member_results = self.test_member_queries_performance(fixed_performance_session, realistic_test_data)
        transaction_results = self.test_database_transaction_performance(fixed_performance_session, realistic_test_data)
        
        # 汇总性能数据
        print(f"\n📈 性能汇总报告")
        print(f"{'测试类型':<20} {'平均时间':<12} {'QPS':<8} {'性能评级'}")
        print(f"-" * 55)
        
        all_results = [
            ("租户基础查询", tenant_results["basic_query"]),
            ("用户邮箱查询", user_results["email_query"]),
            ("教师列表查询", teacher_results["list_query"]),
            ("会员列表查询", member_results["list_query"]),
            ("简单事务操作", transaction_results["simple_transaction"]),
        ]
        
        for name, metrics in all_results:
            qps = 1 / metrics.avg_time
            
            if metrics.avg_time < 0.01:  # 10ms
                level = "🟢 优秀"
            elif metrics.avg_time < 0.05:  # 50ms
                level = "🟡 良好"
            elif metrics.avg_time < 0.1:  # 100ms
                level = "🟠 一般"
            else:
                level = "🔴 需优化"
            
            print(f"{name:<20} {metrics.avg_time*1000:>8.1f}ms   {qps:>6.1f}  {level}")
        
        return {
            "tenant": tenant_results,
            "user": user_results,
            "teacher": teacher_results,
            "member": member_results,
            "transaction": transaction_results
        }
