"""
并发测试框架

基于threading实现的简单并发测试框架
"""

import pytest
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Callable
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer, ConcurrentTester


class TestConcurrentFramework:
    """并发测试框架测试"""
    
    @pytest.mark.concurrent
    def test_concurrent_tester_basic(self, concurrent_tester: ConcurrentTester):
        """测试并发测试器基本功能"""
        
        def simple_task(task_id: int, delay: float = 0.01):
            """简单的测试任务"""
            time.sleep(delay)
            return f"Task {task_id} completed"
        
        # 准备测试参数
        test_args_list = [(i, 0.01) for i in range(5)]
        
        print(f"\n🚀 测试并发测试器基本功能 - {len(test_args_list)}个并发任务")
        
        start_time = time.time()
        results = concurrent_tester.run_concurrent_test(simple_task, test_args_list, timeout=10)
        end_time = time.time()
        
        # 验证结果
        assert len(results) == len(test_args_list)
        
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]
        
        print(f"\n📊 并发测试结果:")
        print(f"   总任务数: {len(results)}")
        print(f"   成功任务: {len(successful_results)}")
        print(f"   失败任务: {len(failed_results)}")
        print(f"   成功率: {len(successful_results)/len(results)*100:.2f}%")
        print(f"   总耗时: {end_time - start_time:.3f}秒")
        
        # 验证并发执行效果（应该比顺序执行快）
        sequential_time = len(test_args_list) * 0.01  # 顺序执行预期时间
        concurrent_time = end_time - start_time
        
        print(f"   顺序执行预期时间: {sequential_time:.3f}秒")
        print(f"   并发执行实际时间: {concurrent_time:.3f}秒")
        print(f"   性能提升: {sequential_time/concurrent_time:.2f}x")
        
        assert len(successful_results) >= 4  # 至少80%成功
        assert concurrent_time < sequential_time * 0.8  # 并发应该更快
    
    @pytest.mark.concurrent
    def test_concurrent_database_queries(self, fixed_performance_session, realistic_test_data, concurrent_tester):
        """测试并发数据库查询（修复版）"""

        def query_tenant(query_id: int):
            """查询租户的任务"""
            try:
                from app.features.tenants.models import Tenant
                from sqlmodel import select, Session

                # 使用固定的性能测试引擎创建新会话
                from tests.performance.utils.benchmark_fixtures import fixed_performance_engine

                # 获取测试数据
                tenant_key = f"tenant_{query_id % 3}"
                tenant_data = realistic_test_data[tenant_key]
                target_tenant = tenant_data["tenant"]

                # 创建线程安全的数据库会话
                with Session(fixed_performance_session.bind) as session:
                    result = session.exec(select(Tenant).where(Tenant.id == target_tenant.id)).first()
                    if result:
                        return {"query_id": query_id, "tenant_id": result.id, "tenant_name": result.name}
                    else:
                        raise Exception(f"Tenant not found for query {query_id}")
            except Exception as e:
                raise Exception(f"Query {query_id} failed: {str(e)}")
    
    @pytest.mark.concurrent
    def test_concurrent_api_calls(self, client, created_tenant, created_admin_user, concurrent_tester):
        """测试并发API调用"""
        
        def api_call_task(call_id: int):
            """API调用任务"""
            try:
                # 测试健康检查API
                response = client.get("/api/v1/info/health")
                if response.status_code == 200:
                    return {
                        "call_id": call_id,
                        "status_code": response.status_code,
                        "response_time": response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0
                    }
                else:
                    raise Exception(f"API call {call_id} failed with status {response.status_code}")
            except Exception as e:
                raise Exception(f"API call {call_id} failed: {str(e)}")
        
        # 准备并发API调用参数
        test_args_list = [(i,) for i in range(15)]
        
        print(f"\n🚀 测试并发API调用 - {len(test_args_list)}个并发请求")
        
        start_time = time.time()
        results = concurrent_tester.run_concurrent_test(api_call_task, test_args_list, timeout=30)
        end_time = time.time()
        
        # 分析结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]
        
        print(f"\n📊 并发API调用结果:")
        print(f"   总请求数: {len(results)}")
        print(f"   成功请求: {len(successful_results)}")
        print(f"   失败请求: {len(failed_results)}")
        print(f"   成功率: {len(successful_results)/len(results)*100:.2f}%")
        print(f"   总耗时: {end_time - start_time:.3f}秒")
        print(f"   平均QPS: {len(results)/(end_time - start_time):.2f}")
        
        # 打印失败的请求信息
        if failed_results:
            print(f"\n❌ 失败的请求:")
            for failed in failed_results[:3]:
                print(f"   请求ID: {failed['args'][0]}, 错误: {failed['error']}")
        
        # 验证结果
        assert len(successful_results) >= len(test_args_list) * 0.8  # 至少80%成功
        
        # 验证响应数据
        for result in successful_results[:3]:
            if result['success'] and result['result']:
                api_result = result['result']['result']
                assert api_result['status_code'] == 200
                print(f"   ✅ API调用{api_result['call_id']}: 状态码={api_result['status_code']}")
    
    @pytest.mark.concurrent
    @pytest.mark.slow_performance
    def test_high_concurrency_stress(self, concurrent_tester, performance_thresholds):
        """高并发压力测试"""
        
        def cpu_intensive_task(task_id: int):
            """CPU密集型任务"""
            # 简单的计算任务
            result = 0
            for i in range(1000):
                result += i * i
            return {"task_id": task_id, "result": result}
        
        # 高并发测试
        concurrency_levels = [10, 20, 30]
        
        for concurrency in concurrency_levels:
            print(f"\n🚀 高并发压力测试 - 并发级别: {concurrency}")
            
            test_args_list = [(i,) for i in range(concurrency)]
            
            start_time = time.time()
            results = concurrent_tester.run_concurrent_test(cpu_intensive_task, test_args_list, timeout=60)
            end_time = time.time()
            
            # 分析结果
            successful_results = [r for r in results if r['success']]
            failed_results = [r for r in results if not r['success']]
            
            success_rate = len(successful_results) / len(results)
            total_time = end_time - start_time
            qps = len(results) / total_time
            
            print(f"   总任务数: {len(results)}")
            print(f"   成功任务: {len(successful_results)}")
            print(f"   失败任务: {len(failed_results)}")
            print(f"   成功率: {success_rate*100:.2f}%")
            print(f"   总耗时: {total_time:.3f}秒")
            print(f"   QPS: {qps:.2f}")
            
            # 验证性能要求
            if success_rate >= performance_thresholds["concurrent_success_rate"]:
                print(f"   ✅ 成功率符合要求 ({success_rate:.2%} >= {performance_thresholds['concurrent_success_rate']:.2%})")
            else:
                print(f"   ⚠️ 警告: 成功率低于阈值 ({success_rate:.2%} < {performance_thresholds['concurrent_success_rate']:.2%})")
            
            # 验证基本要求
            assert success_rate >= 0.7  # 至少70%成功率
            assert len(successful_results) > 0  # 至少有成功的任务
    
    @pytest.mark.concurrent
    def test_thread_safety_validation(self, concurrent_tester):
        """线程安全验证测试"""
        
        # 共享计数器（非线程安全）
        counter = {"value": 0}
        lock = threading.Lock()
        
        def unsafe_increment(task_id: int):
            """非线程安全的递增操作"""
            current = counter["value"]
            time.sleep(0.001)  # 模拟处理时间
            counter["value"] = current + 1
            return {"task_id": task_id, "final_value": counter["value"]}
        
        def safe_increment(task_id: int):
            """线程安全的递增操作"""
            with lock:
                current = counter["value"]
                time.sleep(0.001)  # 模拟处理时间
                counter["value"] = current + 1
                return {"task_id": task_id, "final_value": counter["value"]}
        
        # 测试非线程安全操作
        print(f"\n🚀 测试线程安全性 - 非线程安全操作")
        counter["value"] = 0
        test_args_list = [(i,) for i in range(10)]
        
        results = concurrent_tester.run_concurrent_test(unsafe_increment, test_args_list, timeout=10)
        unsafe_final_value = counter["value"]
        
        print(f"   非线程安全最终值: {unsafe_final_value} (期望: {len(test_args_list)})")
        
        # 测试线程安全操作
        print(f"\n🚀 测试线程安全性 - 线程安全操作")
        counter["value"] = 0
        
        results = concurrent_tester.run_concurrent_test(safe_increment, test_args_list, timeout=10)
        safe_final_value = counter["value"]
        
        print(f"   线程安全最终值: {safe_final_value} (期望: {len(test_args_list)})")
        
        # 验证线程安全性
        print(f"\n📊 线程安全性验证:")
        print(f"   非线程安全操作准确性: {unsafe_final_value == len(test_args_list)}")
        print(f"   线程安全操作准确性: {safe_final_value == len(test_args_list)}")
        
        # 线程安全的操作应该得到正确的结果
        assert safe_final_value == len(test_args_list), f"线程安全操作失败: {safe_final_value} != {len(test_args_list)}"
