"""
业务逻辑性能深度分析

专门分析get_teacher_tags和get_user_context等业务逻辑的性能瓶颈
"""

import pytest
import time
from sqlmodel import Session, select, text
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer


class TestBusinessLogicPerformanceAnalysis:
    """业务逻辑性能深度分析"""
    
    def test_get_teacher_tags_performance_analysis(self, fixed_performance_session, realistic_test_data):
        """深度分析get_teacher_tags方法的性能"""
        
        print(f"\n🔍 get_teacher_tags方法性能深度分析")
        print(f"=" * 60)
        
        from app.features.teachers.service import TeacherService
        from app.features.teachers.models import Teacher, TeacherTag
        from app.features.tags.models import Tag
        from sqlmodel import and_
        
        session = fixed_performance_session
        tenant_data = realistic_test_data["tenant_0"]
        tenant_id = tenant_data["tenant"].id
        
        # 创建测试服务
        teacher_service = TeacherService(session, tenant_id)
        
        # 获取一个教师ID用于测试
        teachers = tenant_data["teachers"]
        if not teachers:
            print("   ⚠️ 没有测试教师数据，跳过分析")
            return
        
        teacher_id = teachers[0].id
        
        # 1. 分析原始get_teacher_tags方法性能
        print(f"\n1️⃣ 原始get_teacher_tags方法性能")
        original_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                tags = teacher_service.get_teacher_tags(teacher_id)
            original_times.append(timer.elapsed_time)
        
        original_avg = sum(original_times) / len(original_times)
        print(f"   平均执行时间: {original_avg*1000:.2f}ms")
        print(f"   QPS: {1/original_avg:.1f}")
        
        # 2. 分析JOIN查询的SQL执行计划
        print(f"\n2️⃣ JOIN查询SQL执行计划分析")
        
        # 获取实际的SQL查询
        statement = select(Tag, TeacherTag).join(
            TeacherTag, Tag.id == TeacherTag.tag_id
        ).where(TeacherTag.teacher_id == teacher_id)
        
        # 执行EXPLAIN ANALYZE
        try:
            explain_query = f"""
            EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) 
            {str(statement.compile(compile_kwargs={"literal_binds": True}))}
            """
            
            explain_times = []
            for i in range(3):
                with PerformanceTimer() as timer:
                    result = session.exec(text(explain_query)).first()
                explain_times.append(timer.elapsed_time)
            
            explain_avg = sum(explain_times) / len(explain_times)
            print(f"   EXPLAIN ANALYZE平均时间: {explain_avg*1000:.2f}ms")
            
            if result:
                import json
                plan = json.loads(result[0])
                execution_time = plan[0]["Execution Time"]
                planning_time = plan[0]["Planning Time"]
                print(f"   数据库执行时间: {execution_time:.2f}ms")
                print(f"   查询规划时间: {planning_time:.2f}ms")
                
        except Exception as e:
            print(f"   ⚠️ EXPLAIN分析失败: {e}")
        
        # 3. 分析数据处理开销
        print(f"\n3️⃣ 数据处理开销分析")
        
        # 只执行查询，不处理数据
        query_only_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                results = session.exec(statement).all()
            query_only_times.append(timer.elapsed_time)
        
        query_only_avg = sum(query_only_times) / len(query_only_times)
        
        # 数据处理开销
        processing_overhead = original_avg - query_only_avg
        
        print(f"   纯查询时间: {query_only_avg*1000:.2f}ms")
        print(f"   数据处理时间: {processing_overhead*1000:.2f}ms")
        print(f"   处理开销占比: {processing_overhead/original_avg*100:.1f}%")
        
        # 4. 优化版本性能测试
        print(f"\n4️⃣ 优化版本性能对比")
        
        def optimized_get_teacher_tags(teacher_id: int):
            """优化版本的get_teacher_tags"""
            # 使用更简单的查询，只选择需要的字段
            statement = select(Tag.id, Tag.name, Tag.description, TeacherTag.created_at).select_from(
                Tag
            ).join(TeacherTag, Tag.id == TeacherTag.tag_id).where(
                TeacherTag.teacher_id == teacher_id
            )

            results = session.exec(statement).all()
            tags = []
            for tag_id, name, description, assigned_at in results:
                tags.append({
                    'id': tag_id,
                    'name': name,
                    'description': description,
                    'assigned_at': assigned_at
                })
            return tags
        
        optimized_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                tags = optimized_get_teacher_tags(teacher_id)
            optimized_times.append(timer.elapsed_time)
        
        optimized_avg = sum(optimized_times) / len(optimized_times)
        improvement = original_avg / optimized_avg
        
        print(f"   优化版平均时间: {optimized_avg*1000:.2f}ms")
        print(f"   性能提升: {improvement:.1f}x")
        
        return {
            "original_avg": original_avg,
            "query_only_avg": query_only_avg,
            "processing_overhead": processing_overhead,
            "optimized_avg": optimized_avg,
            "improvement": improvement
        }
    
    def test_get_user_context_performance_analysis(self, fixed_performance_client, realistic_test_data):
        """深度分析get_user_context方法的性能"""
        
        print(f"\n🔍 get_user_context方法性能深度分析")
        print(f"=" * 60)
        
        from app.core.dependencies import get_user_context, get_current_user
        from app.features.users.models import User
        from app.features.tenants.models import Tenant
        
        # 1. 分析get_current_user的性能
        print(f"\n1️⃣ get_current_user性能分析")
        
        # 模拟token验证过程
        tenant_data = realistic_test_data["tenant_0"]
        admin_user = tenant_data["admin_users"][0]
        
        # 创建一个模拟的认证token
        from app.utils.security import create_access_token
        token_data = {
            "sub": str(admin_user.id),
            "tenant_id": admin_user.tenant_id,
            "user_type": "admin"
        }
        test_token = create_access_token(token_data)
        
        # 测试token验证性能
        from app.utils.security import verify_token
        token_verify_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                payload = verify_token(test_token)
            token_verify_times.append(timer.elapsed_time)
        
        token_verify_avg = sum(token_verify_times) / len(token_verify_times)
        print(f"   Token验证平均时间: {token_verify_avg*1000:.2f}ms")
        
        # 2. 分析数据库用户查询性能
        print(f"\n2️⃣ 数据库用户查询性能分析")
        
        from tests.performance.utils.benchmark_fixtures import fixed_performance_engine
        with Session(fixed_performance_engine()) as test_session:
            # 设置RLS上下文
            test_session.exec(text(f"SET app.current_tenant_id = '{admin_user.tenant_id}'"))
            
            user_query_times = []
            for i in range(10):
                with PerformanceTimer() as timer:
                    user = test_session.get(User, admin_user.id)
                user_query_times.append(timer.elapsed_time)
            
            user_query_avg = sum(user_query_times) / len(user_query_times)
            print(f"   用户查询平均时间: {user_query_avg*1000:.2f}ms")
        
        # 3. 分析租户查询性能
        print(f"\n3️⃣ 租户查询性能分析")
        
        with Session(fixed_performance_engine()) as test_session:
            tenant_query_times = []
            for i in range(10):
                with PerformanceTimer() as timer:
                    tenant = test_session.get(Tenant, admin_user.tenant_id)
                tenant_query_times.append(timer.elapsed_time)
            
            tenant_query_avg = sum(tenant_query_times) / len(tenant_query_times)
            print(f"   租户查询平均时间: {tenant_query_avg*1000:.2f}ms")
        
        # 4. 分析RLS设置开销
        print(f"\n4️⃣ RLS设置开销分析")
        
        with Session(fixed_performance_engine()) as test_session:
            rls_times = []
            for i in range(10):
                with PerformanceTimer() as timer:
                    test_session.exec(text(f"SET app.current_tenant_id = '{admin_user.tenant_id}'"))
                rls_times.append(timer.elapsed_time)
            
            rls_avg = sum(rls_times) / len(rls_times)
            print(f"   RLS设置平均时间: {rls_avg*1000:.2f}ms")
        
        # 5. 总体性能分析
        print(f"\n5️⃣ get_user_context总体性能分析")
        
        total_estimated = token_verify_avg + user_query_avg + tenant_query_avg + rls_avg
        
        print(f"   Token验证: {token_verify_avg*1000:.2f}ms ({token_verify_avg/total_estimated*100:.1f}%)")
        print(f"   用户查询: {user_query_avg*1000:.2f}ms ({user_query_avg/total_estimated*100:.1f}%)")
        print(f"   租户查询: {tenant_query_avg*1000:.2f}ms ({tenant_query_avg/total_estimated*100:.1f}%)")
        print(f"   RLS设置: {rls_avg*1000:.2f}ms ({rls_avg/total_estimated*100:.1f}%)")
        print(f"   预估总时间: {total_estimated*1000:.2f}ms")
        
        return {
            "token_verify_avg": token_verify_avg,
            "user_query_avg": user_query_avg,
            "tenant_query_avg": tenant_query_avg,
            "rls_avg": rls_avg,
            "total_estimated": total_estimated
        }
    
    def test_n_plus_1_query_analysis(self, fixed_performance_session, realistic_test_data):
        """分析N+1查询问题"""
        
        print(f"\n🔍 N+1查询问题分析")
        print(f"=" * 60)
        
        from app.features.teachers.service import TeacherService
        from app.features.teachers.schemas import TeacherQuery
        
        session = fixed_performance_session
        tenant_data = realistic_test_data["tenant_0"]
        tenant_id = tenant_data["tenant"].id
        
        teacher_service = TeacherService(session, tenant_id)
        
        # 1. 分析当前实现的N+1问题
        print(f"\n1️⃣ 当前实现N+1查询分析")
        
        # 获取教师列表
        query_params = TeacherQuery(page=1, size=5)  # 只取5个教师进行分析
        
        with PerformanceTimer() as timer:
            teachers, total = teacher_service.get_teachers(query_params)
        teachers_query_time = timer.elapsed_time
        
        print(f"   获取{len(teachers)}个教师基础信息: {teachers_query_time*1000:.2f}ms")
        
        # 为每个教师获取标签（N+1问题）
        individual_tag_times = []
        for teacher in teachers:
            with PerformanceTimer() as timer:
                tags = teacher_service.get_teacher_tags(teacher.id)
            individual_tag_times.append(timer.elapsed_time)
        
        total_tag_time = sum(individual_tag_times)
        avg_tag_time = total_tag_time / len(teachers) if teachers else 0
        
        print(f"   逐个获取标签总时间: {total_tag_time*1000:.2f}ms")
        print(f"   平均每个教师标签查询: {avg_tag_time*1000:.2f}ms")
        print(f"   总查询次数: {len(teachers) + 1} (1次教师查询 + {len(teachers)}次标签查询)")
        
        # 2. 优化版本：批量查询
        print(f"\n2️⃣ 优化版本：批量查询标签")
        
        def get_teachers_tags_batch(teacher_ids: list):
            """批量获取多个教师的标签"""
            from app.features.teachers.models import TeacherTag
            from app.features.tags.models import Tag
            from sqlmodel import and_
            
            if not teacher_ids:
                return {}
            
            statement = select(Tag, TeacherTag).join(
                TeacherTag, Tag.id == TeacherTag.tag_id
            ).where(TeacherTag.teacher_id.in_(teacher_ids))
            
            results = session.exec(statement).all()
            
            # 按教师ID分组
            teacher_tags = {}
            for tag, teacher_tag in results:
                teacher_id = teacher_tag.teacher_id
                if teacher_id not in teacher_tags:
                    teacher_tags[teacher_id] = []
                
                tag_dict = tag.model_dump()
                tag_dict['assigned_at'] = teacher_tag.created_at
                teacher_tags[teacher_id].append(tag_dict)
            
            return teacher_tags
        
        teacher_ids = [t.id for t in teachers]
        
        with PerformanceTimer() as timer:
            batch_tags = get_teachers_tags_batch(teacher_ids)
        batch_query_time = timer.elapsed_time
        
        print(f"   批量查询标签时间: {batch_query_time*1000:.2f}ms")
        print(f"   查询次数: 2 (1次教师查询 + 1次批量标签查询)")
        
        # 3. 性能对比
        print(f"\n3️⃣ 性能对比分析")
        
        n_plus_1_total = teachers_query_time + total_tag_time
        batch_total = teachers_query_time + batch_query_time
        improvement = n_plus_1_total / batch_total
        
        print(f"   N+1方式总时间: {n_plus_1_total*1000:.2f}ms")
        print(f"   批量查询总时间: {batch_total*1000:.2f}ms")
        print(f"   性能提升: {improvement:.1f}x")
        print(f"   查询次数减少: {len(teachers)}次 → 1次")
        
        return {
            "teachers_count": len(teachers),
            "n_plus_1_total": n_plus_1_total,
            "batch_total": batch_total,
            "improvement": improvement,
            "avg_tag_time": avg_tag_time,
            "batch_query_time": batch_query_time
        }
