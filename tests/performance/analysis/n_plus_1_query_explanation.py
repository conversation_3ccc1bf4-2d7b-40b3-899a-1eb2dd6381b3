"""
N+1 查询问题解释及解决方案

本文件解释什么是N+1查询问题，以及如何在SQLModel/SQLAlchemy环境中解决此类问题。
"""

"""
什么是N+1查询问题?
---------------

N+1查询问题是一种常见的数据库性能反模式，通常出现在关联数据查询中。问题具体表现为：

1. 执行1次查询获取主表的N条记录
2. 然后为这N条记录中的每一条执行1次额外查询，获取关联数据
3. 总共执行了1+N次查询，而不是理想的1次查询

举例：在我们的教师管理系统中，获取教师列表时同时需要获取每个教师的标签：

```python
# N+1问题示例
teachers = teacher_service.get_teachers(query_params)  # 1次查询，获取N个教师
for teacher in teachers:
    tags = teacher_service.get_teacher_tags(teacher.id)  # 对每个教师额外执行1次查询
    # 处理标签数据...
```

这种模式在以下情况下特别有害：
- 数据量大（N值大）时
- 网络延迟高时（比如数据库和应用在不同服务器上）
- 数据库连接数有限时
"""


"""
为什么N+1查询问题会发生?
--------------------

N+1查询问题通常由以下原因导致：

1. **懒加载（Lazy Loading）**: ORM框架默认使用懒加载策略，只有在实际需要关联数据时才加载
2. **代码组织不当**: 在循环内执行查询，而非一次性获取所需数据
3. **封装不当**: 数据访问方法过度封装，无法进行批量操作
4. **缺乏性能意识**: 开发者可能不了解数据库查询优化
"""


"""
如何发现N+1查询问题?
----------------

可以通过以下方法识别N+1查询问题：

1. **数据库慢查询日志**: 查看是否有大量类似但参数不同的查询
2. **性能监控工具**: 如Django Debug Toolbar, SQLAlchemy Query Events等
3. **代码审查**: 检查循环中是否有数据库查询
4. **响应时间分析**: 分析API端点在数据量增加时性能是否显著下降
5. **SQL计数器**: 在请求期间计算执行的SQL查询数量
"""


"""
SQLModel/SQLAlchemy中解决N+1查询问题的方法
-----------------------------------

1. **使用JOIN查询**

最常见的解决方法是使用JOIN查询一次性获取主表和关联表数据：

```python
def get_teachers_with_tags_in_one_query(self, query_params):
    # 先获取符合条件的教师列表
    teachers = self.session.exec(teacher_statement).all()
    
    # 获取教师ID列表
    teacher_ids = [teacher.id for teacher in teachers]
    
    # 一次性JOIN查询所有教师的标签
    tag_statement = (
        select(TeacherTag, Tag)
        .join(Tag, TeacherTag.tag_id == Tag.id)
        .where(TeacherTag.teacher_id.in_(teacher_ids))
    )
    tag_results = self.session.exec(tag_statement).all()
    
    # 将标签按教师ID分组
    teacher_tags = {}
    for teacher_tag, tag in tag_results:
        if teacher_tag.teacher_id not in teacher_tags:
            teacher_tags[teacher_tag.teacher_id] = []
        teacher_tags[teacher_tag.teacher_id].append(tag)
    
    # 合并数据
    result = []
    for teacher in teachers:
        teacher_dict = teacher.model_dump()
        teacher_dict['tags'] = teacher_tags.get(teacher.id, [])
        result.append(teacher_dict)
    
    return result
```

2. **使用SELECT IN查询**

另一种方法是使用IN操作符一次查询所有关联数据：

```python
def get_teachers_and_tags(self, query_params):
    # 获取教师列表
    teachers = self.get_teachers(query_params)
    
    # 一次性获取所有教师的标签
    teacher_ids = [t.id for t in teachers]
    tag_statement = select(TeacherTag, Tag).join(
        Tag, TeacherTag.tag_id == Tag.id
    ).where(TeacherTag.teacher_id.in_(teacher_ids))
    
    # 执行查询和处理结果
    # ...
```

3. **使用SQLAlchemy的relationship和joinedload**

如果使用完整的SQLAlchemy ORM设置，可以利用relationship和joinedload:

```python
# 在模型定义中
class Teacher(SQLModel, table=True):
    # ... 其他字段 ...
    tags: List["Tag"] = Relationship(
        back_populates="teachers",
        link_model=TeacherTag
    )

# 查询时
teachers = session.exec(
    select(Teacher).options(
        joinedload(Teacher.tags)
    )
).all()
```

4. **数据批处理**

对于大量数据，可以考虑批处理方式：

```python
def get_teachers_with_tags_batch(self, query_params, batch_size=100):
    # 获取总教师数
    total_teachers = self.count_teachers()
    result = []
    
    # 批量处理
    for offset in range(0, total_teachers, batch_size):
        # 获取一批教师
        batch_query_params = copy.copy(query_params)
        batch_query_params.offset = offset
        batch_query_params.limit = batch_size
        
        # 为这批教师获取标签
        batch_result = self.get_teachers_with_tags_in_one_query(batch_query_params)
        result.extend(batch_result)
    
    return result
```
"""


"""
最佳实践
------

1. **提前规划数据访问模式**: 在设计API和数据模型时就考虑查询模式
2. **使用预加载/即时加载**: 在需要关联数据时使用JOIN或SELECT IN
3. **优化数据模型**: 考虑数据规范化与反规范化的权衡
4. **缓存常用数据**: 使用Redis等缓存关联数据
5. **监控数据库性能**: 定期审查慢查询和执行计划
6. **编写单元测试**: 包含验证查询次数的测试
7. **代码审查**: 特别关注数据访问模式
8. **GraphQL考虑**: 如使用GraphQL，注意N+1问题的特殊处理
"""


"""
SQLModel特有的注意事项
------------------

SQLModel作为SQLAlchemy和Pydantic的结合，有一些特有的注意事项：

1. SQLModel使用了SQLAlchemy Core和ORM的混合模式，查询可以通过session.exec()执行
2. 关系定义使用Relationship类，支持back_populates和link_model
3. 可以结合FastAPI的依赖注入系统优化数据库会话管理
4. 需要注意select()中的模型顺序，它决定了结果集的结构
"""


"""
结论
----

N+1查询问题是一个常见但可避免的性能陷阱。通过JOIN查询、批量查询和适当的关系加载策略，
可以显著提高应用性能，特别是在处理大量数据时。

良好的数据访问层设计应该提供灵活的查询接口，允许调用者指定需要加载的关联数据，
同时在内部实现中优化查询以避免N+1问题。
"""
