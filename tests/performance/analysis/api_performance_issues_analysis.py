"""
API性能问题深度分析

针对教师列表查询和登录API的性能问题进行详细分析和修复
"""

import pytest
import time
import os
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer


class TestAPIPerformanceIssuesAnalysis:
    """API性能问题分析"""
    
    def test_teacher_api_redirect_issue_analysis(self, authenticated_client):
        """分析教师API的重定向问题"""
        
        print(f"\n🔍 教师API重定向问题分析")
        print(f"=" * 60)
        
        client_data = authenticated_client
        client = client_data["client"]
        
        # 测试不同的URL格式
        test_urls = [
            {
                "name": "无尾斜杠",
                "url": "/api/v1/admin/teachers?page=1&size=20",
                "expected_redirects": 1
            },
            {
                "name": "有尾斜杠", 
                "url": "/api/v1/admin/teachers/?page=1&size=20",
                "expected_redirects": 0
            },
            {
                "name": "无查询参数",
                "url": "/api/v1/admin/teachers",
                "expected_redirects": 1
            },
            {
                "name": "无查询参数+尾斜杠",
                "url": "/api/v1/admin/teachers/",
                "expected_redirects": 0
            }
        ]
        
        results = []
        
        for test_case in test_urls:
            print(f"\n📊 测试 {test_case['name']}: {test_case['url']}")
            
            # 测试5次取平均值
            response_times = []
            redirect_counts = []
            
            for i in range(5):
                with PerformanceTimer() as timer:
                    response = client.get(test_case["url"])
                response_times.append(timer.elapsed_time)
                
                # 计算重定向次数（通过检查history）
                redirect_count = len(response.history) if hasattr(response, 'history') else 0
                redirect_counts.append(redirect_count)
            
            avg_time = sum(response_times) / len(response_times)
            avg_redirects = sum(redirect_counts) / len(redirect_counts)
            
            print(f"   平均响应时间: {avg_time*1000:.2f}ms")
            print(f"   平均重定向次数: {avg_redirects:.1f}")
            print(f"   状态码: {response.status_code}")
            
            results.append({
                "name": test_case["name"],
                "url": test_case["url"],
                "avg_time": avg_time,
                "avg_redirects": avg_redirects,
                "status_code": response.status_code
            })
        
        # 分析结果
        print(f"\n📈 重定向问题分析")
        print(f"{'URL类型':<15} {'平均时间':<12} {'重定向次数':<12} {'性能影响'}")
        print(f"-" * 60)
        
        baseline_time = None
        for result in results:
            if result["avg_redirects"] == 0:
                baseline_time = result["avg_time"]
                break
        
        for result in results:
            if baseline_time:
                performance_impact = f"{result['avg_time']/baseline_time:.1f}x" if baseline_time > 0 else "N/A"
            else:
                performance_impact = "N/A"
            
            print(f"{result['name']:<15} {result['avg_time']*1000:>8.1f}ms   "
                  f"{result['avg_redirects']:>8.1f}      {performance_impact}")
        
        # 建议
        print(f"\n💡 优化建议:")
        if any(r["avg_redirects"] > 0 for r in results):
            print(f"   ⚠️  发现重定向问题，建议:")
            print(f"   1. 在API路由中统一使用尾斜杠")
            print(f"   2. 或者配置FastAPI自动处理重定向")
            print(f"   3. 客户端调用时使用正确的URL格式")
        else:
            print(f"   ✅ 未发现重定向问题")
        
        return results
    
    def test_login_api_bcrypt_configuration_analysis(self, fixed_performance_client, realistic_test_data):
        """分析登录API的bcrypt配置问题"""
        
        print(f"\n🔐 登录API bcrypt配置分析")
        print(f"=" * 60)
        
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        admin_user = tenant_data["admin_users"][0]
        
        login_data = {
            "email": admin_user.email,
            "password": "password123",
            "tenant_code": tenant.code
        }
        
        # 测试当前配置
        print(f"\n1️⃣ 当前配置性能测试")
        current_times = []
        for i in range(5):
            with PerformanceTimer() as timer:
                response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
                assert response.status_code == 200
            current_times.append(timer.elapsed_time)
        
        current_avg = sum(current_times) / len(current_times)
        print(f"   当前平均响应时间: {current_avg*1000:.2f}ms")
        print(f"   当前QPS: {1/current_avg:.1f}")
        
        # 检查当前bcrypt配置
        print(f"\n2️⃣ bcrypt配置检查")
        from app.utils.security import get_bcrypt_rounds
        current_rounds = get_bcrypt_rounds()
        print(f"   当前bcrypt rounds: {current_rounds}")
        
        # 分析环境变量
        env_vars = {
            "BCRYPT_ROUNDS": os.getenv("BCRYPT_ROUNDS"),
            "ENVIRONMENT": os.getenv("ENVIRONMENT"),
            "TESTING": os.getenv("TESTING")
        }
        
        print(f"\n3️⃣ 环境变量检查")
        for key, value in env_vars.items():
            print(f"   {key}: {value}")
        
        # 性能分析
        print(f"\n4️⃣ 性能分析")
        if current_avg > 0.015:  # 15ms
            print(f"   ⚠️  响应时间偏慢 ({current_avg*1000:.2f}ms)")
            if current_rounds >= 12:
                print(f"   🔍 可能原因: bcrypt rounds过高 ({current_rounds})")
                print(f"   💡 建议: 设置BCRYPT_ROUNDS=8用于开发/测试环境")
            else:
                print(f"   🔍 可能原因: 其他性能瓶颈")
        else:
            print(f"   ✅ 响应时间正常")
        
        return {
            "current_avg_time": current_avg,
            "current_rounds": current_rounds,
            "env_vars": env_vars
        }
    
    def test_optimized_login_performance(self, fixed_performance_client, realistic_test_data):
        """测试优化后的登录性能"""
        
        print(f"\n⚡ 优化登录性能测试")
        print(f"=" * 60)
        
        # 设置优化环境变量
        original_rounds = os.getenv("BCRYPT_ROUNDS")
        os.environ["BCRYPT_ROUNDS"] = "8"
        
        try:
            # 重新导入以应用新配置
            import importlib
            import app.utils.security
            importlib.reload(app.utils.security)
            
            tenant_data = realistic_test_data["tenant_0"]
            tenant = tenant_data["tenant"]
            admin_user = tenant_data["admin_users"][0]
            
            login_data = {
                "email": admin_user.email,
                "password": "password123",
                "tenant_code": tenant.code
            }
            
            print(f"\n1️⃣ 优化后性能测试 (BCRYPT_ROUNDS=8)")
            optimized_times = []
            for i in range(5):
                with PerformanceTimer() as timer:
                    response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
                    assert response.status_code == 200
                optimized_times.append(timer.elapsed_time)
            
            optimized_avg = sum(optimized_times) / len(optimized_times)
            print(f"   优化后平均响应时间: {optimized_avg*1000:.2f}ms")
            print(f"   优化后QPS: {1/optimized_avg:.1f}")
            
            # 与基准对比
            baseline_time = 0.018  # 18ms基准
            improvement = baseline_time / optimized_avg
            
            print(f"\n2️⃣ 性能提升分析")
            print(f"   基准响应时间: {baseline_time*1000:.2f}ms")
            print(f"   优化后响应时间: {optimized_avg*1000:.2f}ms")
            print(f"   性能提升: {improvement:.1f}x")
            
            if improvement > 1.5:
                print(f"   ✅ 显著性能提升")
            elif improvement > 1.1:
                print(f"   🟡 轻微性能提升")
            else:
                print(f"   ⚠️  性能提升不明显")
            
            return {
                "optimized_avg_time": optimized_avg,
                "improvement": improvement
            }
            
        finally:
            # 恢复原始环境变量
            if original_rounds:
                os.environ["BCRYPT_ROUNDS"] = original_rounds
            elif "BCRYPT_ROUNDS" in os.environ:
                del os.environ["BCRYPT_ROUNDS"]
    
    def test_teacher_api_url_optimization(self, authenticated_client):
        """测试教师API URL优化"""
        
        print(f"\n🔧 教师API URL优化测试")
        print(f"=" * 60)
        
        client_data = authenticated_client
        client = client_data["client"]
        
        # 测试优化后的URL（带尾斜杠）
        optimized_url = "/api/v1/admin/teachers/?page=1&size=20"
        
        print(f"\n1️⃣ 优化URL性能测试")
        print(f"   URL: {optimized_url}")
        
        optimized_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                response = client.get(optimized_url)
                assert response.status_code == 200
            optimized_times.append(timer.elapsed_time)
        
        optimized_avg = sum(optimized_times) / len(optimized_times)
        print(f"   平均响应时间: {optimized_avg*1000:.2f}ms")
        print(f"   QPS: {1/optimized_avg:.1f}")
        
        # 与基准对比
        baseline_time = 0.013  # 13ms基准（有重定向的时间）
        improvement = baseline_time / optimized_avg
        
        print(f"\n2️⃣ 优化效果分析")
        print(f"   基准响应时间: {baseline_time*1000:.2f}ms (含重定向)")
        print(f"   优化后响应时间: {optimized_avg*1000:.2f}ms (无重定向)")
        print(f"   性能提升: {improvement:.1f}x")
        
        if improvement > 1.5:
            print(f"   ✅ 显著性能提升")
        elif improvement > 1.1:
            print(f"   🟡 轻微性能提升")
        else:
            print(f"   ⚠️  性能提升不明显")
        
        return {
            "optimized_avg_time": optimized_avg,
            "improvement": improvement
        }
    
    def test_comprehensive_performance_comparison(self, authenticated_client, fixed_performance_client, realistic_test_data):
        """综合性能对比测试"""
        
        print(f"\n📊 综合性能对比测试")
        print(f"=" * 60)
        
        # 设置优化环境
        os.environ["BCRYPT_ROUNDS"] = "8"
        
        try:
            # 重新导入以应用新配置
            import importlib
            import app.utils.security
            importlib.reload(app.utils.security)
            
            client_data = authenticated_client
            client = client_data["client"]
            
            tenant_data = realistic_test_data["tenant_0"]
            tenant = tenant_data["tenant"]
            admin_user = tenant_data["admin_users"][0]
            
            login_data = {
                "email": admin_user.email,
                "password": "password123",
                "tenant_code": tenant.code
            }
            
            # 测试用例
            test_cases = [
                {
                    "name": "教师列表(优化URL)",
                    "method": "GET",
                    "url": "/api/v1/admin/teachers/?page=1&size=20",
                    "client": client,
                    "data": None
                },
                {
                    "name": "会员列表(优化URL)",
                    "method": "GET", 
                    "url": "/api/v1/admin/members/?page=1&size=20",
                    "client": client,
                    "data": None
                },
                {
                    "name": "登录API(优化bcrypt)",
                    "method": "POST",
                    "url": "/api/v1/auth/admin/login",
                    "client": fixed_performance_client,
                    "data": login_data
                }
            ]
            
            results = []
            
            for test_case in test_cases:
                print(f"\n📊 测试 {test_case['name']}")
                
                response_times = []
                for i in range(5):
                    with PerformanceTimer() as timer:
                        if test_case["method"] == "GET":
                            response = test_case["client"].get(test_case["url"])
                        else:
                            response = test_case["client"].post(test_case["url"], json=test_case["data"])
                        
                        assert response.status_code == 200
                    response_times.append(timer.elapsed_time)
                
                avg_time = sum(response_times) / len(response_times)
                qps = 1 / avg_time
                
                print(f"   平均响应时间: {avg_time*1000:.2f}ms")
                print(f"   QPS: {qps:.1f}")
                
                # 性能评级
                if avg_time < 0.005:  # 5ms
                    grade = "🟢 优秀"
                elif avg_time < 0.010:  # 10ms
                    grade = "🟡 良好"
                elif avg_time < 0.020:  # 20ms
                    grade = "🟠 一般"
                else:
                    grade = "🔴 需优化"
                
                print(f"   性能评级: {grade}")
                
                results.append({
                    "name": test_case["name"],
                    "avg_time": avg_time,
                    "qps": qps,
                    "grade": grade
                })
            
            # 总结报告
            print(f"\n📈 优化后性能总结")
            print(f"{'API类型':<20} {'响应时间':<12} {'QPS':<8} {'评级'}")
            print(f"-" * 55)
            
            for result in results:
                print(f"{result['name']:<20} {result['avg_time']*1000:>8.1f}ms   "
                      f"{result['qps']:>6.1f}  {result['grade']}")
            
            return results
            
        finally:
            # 清理环境变量
            if "BCRYPT_ROUNDS" in os.environ:
                del os.environ["BCRYPT_ROUNDS"]
