"""
深度性能瓶颈分析

找出教师API和登录API的真正性能瓶颈
"""

import pytest
import time
from tests.performance.utils.performance_utils import PerformanceTimer


class TestDeepBottleneckAnalysis:
    """深度性能瓶颈分析"""
    
    def test_database_vs_api_performance_comparison(self, fixed_performance_session, authenticated_client):
        """对比数据库直接查询 vs API查询的性能"""
        
        print(f"\n🔍 数据库 vs API性能对比分析")
        print(f"=" * 60)
        
        from app.features.teachers.models import Teacher
        from sqlmodel import select
        
        session = fixed_performance_session
        client_data = authenticated_client
        client = client_data["client"]
        
        # 1. 直接数据库查询性能
        print(f"\n1️⃣ 直接数据库查询性能")
        db_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                # 模拟API中的查询逻辑
                teachers = session.exec(
                    select(Teacher)
                    .offset(0)
                    .limit(20)
                ).all()
            db_times.append(timer.elapsed_time)
        
        db_avg = sum(db_times) / len(db_times)
        print(f"   数据库查询平均时间: {db_avg*1000:.2f}ms")
        print(f"   数据库查询QPS: {1/db_avg:.1f}")
        
        # 2. API查询性能
        print(f"\n2️⃣ API查询性能")
        api_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                response = client.get("/api/v1/admin/teachers?page=1&size=20")
                assert response.status_code == 200
            api_times.append(timer.elapsed_time)
        
        api_avg = sum(api_times) / len(api_times)
        print(f"   API查询平均时间: {api_avg*1000:.2f}ms")
        print(f"   API查询QPS: {1/api_avg:.1f}")
        
        # 3. 性能差异分析
        overhead = api_avg - db_avg
        overhead_percentage = (overhead / api_avg) * 100
        
        print(f"\n3️⃣ 性能差异分析")
        print(f"   数据库查询: {db_avg*1000:.2f}ms")
        print(f"   API总时间: {api_avg*1000:.2f}ms")
        print(f"   框架开销: {overhead*1000:.2f}ms ({overhead_percentage:.1f}%)")
        
        if overhead_percentage > 80:
            print(f"   ⚠️  框架开销过大，主要瓶颈在API框架层")
        elif overhead_percentage > 50:
            print(f"   🟡 框架开销较大，需要优化API层")
        else:
            print(f"   ✅ 框架开销合理")
        
        return {
            "db_avg": db_avg,
            "api_avg": api_avg,
            "overhead": overhead,
            "overhead_percentage": overhead_percentage
        }
    
    def test_login_api_component_breakdown(self, fixed_performance_client, realistic_test_data):
        """登录API组件性能分解"""
        
        print(f"\n🔍 登录API组件性能分解")
        print(f"=" * 60)
        
        from app.features.users.models import User
        from app.utils.security import verify_password, create_access_token
        from sqlmodel import select
        
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        admin_user = tenant_data["admin_users"][0]
        
        # 1. 数据库用户查询性能
        print(f"\n1️⃣ 数据库用户查询性能")
        session = fixed_performance_client._transport.app.dependency_overrides.get(None)
        
        # 直接测试数据库查询
        from tests.performance.utils.benchmark_fixtures import fixed_performance_engine
        from sqlmodel import Session
        
        with Session(fixed_performance_engine()) as test_session:
            query_times = []
            for i in range(10):
                with PerformanceTimer() as timer:
                    user = test_session.exec(
                        select(User).where(User.email == admin_user.email)
                    ).first()
                query_times.append(timer.elapsed_time)
            
            query_avg = sum(query_times) / len(query_times)
            print(f"   用户查询平均时间: {query_avg*1000:.2f}ms")
        
        # 2. 密码验证性能
        print(f"\n2️⃣ 密码验证性能")
        password_times = []
        for i in range(5):  # 减少次数，因为bcrypt较慢
            with PerformanceTimer() as timer:
                result = verify_password("password123", admin_user.password_hash)
                assert result == True
            password_times.append(timer.elapsed_time)
        
        password_avg = sum(password_times) / len(password_times)
        print(f"   密码验证平均时间: {password_avg*1000:.2f}ms")
        
        # 3. JWT生成性能
        print(f"\n3️⃣ JWT生成性能")
        jwt_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                token = create_access_token(data={"sub": admin_user.email})
            jwt_times.append(timer.elapsed_time)
        
        jwt_avg = sum(jwt_times) / len(jwt_times)
        print(f"   JWT生成平均时间: {jwt_avg*1000:.2f}ms")
        
        # 4. 完整API性能
        print(f"\n4️⃣ 完整API性能")
        login_data = {
            "email": admin_user.email,
            "password": "password123",
            "tenant_code": tenant.code
        }
        
        api_times = []
        for i in range(5):
            with PerformanceTimer() as timer:
                response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
                assert response.status_code == 200
            api_times.append(timer.elapsed_time)
        
        api_avg = sum(api_times) / len(api_times)
        print(f"   完整API平均时间: {api_avg*1000:.2f}ms")
        
        # 5. 性能分解分析
        print(f"\n5️⃣ 性能分解分析")
        component_total = query_avg + password_avg + jwt_avg
        framework_overhead = api_avg - component_total
        
        print(f"   数据库查询: {query_avg*1000:.2f}ms ({query_avg/api_avg*100:.1f}%)")
        print(f"   密码验证: {password_avg*1000:.2f}ms ({password_avg/api_avg*100:.1f}%)")
        print(f"   JWT生成: {jwt_avg*1000:.2f}ms ({jwt_avg/api_avg*100:.1f}%)")
        print(f"   框架开销: {framework_overhead*1000:.2f}ms ({framework_overhead/api_avg*100:.1f}%)")
        print(f"   总计: {api_avg*1000:.2f}ms")
        
        # 瓶颈识别
        components = [
            ("数据库查询", query_avg),
            ("密码验证", password_avg),
            ("JWT生成", jwt_avg),
            ("框架开销", framework_overhead)
        ]
        
        bottleneck = max(components, key=lambda x: x[1])
        print(f"\n🎯 主要瓶颈: {bottleneck[0]} ({bottleneck[1]*1000:.2f}ms)")
        
        return {
            "query_avg": query_avg,
            "password_avg": password_avg,
            "jwt_avg": jwt_avg,
            "api_avg": api_avg,
            "framework_overhead": framework_overhead,
            "bottleneck": bottleneck[0]
        }
    
    def test_fastapi_framework_overhead_analysis(self, authenticated_client):
        """FastAPI框架开销分析"""
        
        print(f"\n🔍 FastAPI框架开销分析")
        print(f"=" * 60)
        
        client_data = authenticated_client
        client = client_data["client"]
        
        # 测试不同复杂度的API端点
        test_endpoints = [
            {
                "name": "健康检查(最简单)",
                "url": "/api/v1/info/health",
                "description": "无数据库操作，无认证"
            },
            {
                "name": "版本信息(简单)",
                "url": "/api/v1/info/version", 
                "description": "无数据库操作，无认证"
            },
            {
                "name": "会员列表(复杂)",
                "url": "/api/v1/admin/members/?page=1&size=20",
                "description": "有数据库操作，有认证"
            }
        ]
        
        results = []
        
        for endpoint in test_endpoints:
            print(f"\n📊 测试 {endpoint['name']}")
            print(f"   描述: {endpoint['description']}")
            
            response_times = []
            for i in range(10):
                with PerformanceTimer() as timer:
                    if endpoint["name"].startswith("健康检查") or endpoint["name"].startswith("版本"):
                        # 无认证端点，使用固定客户端
                        from fastapi.testclient import TestClient
                        from app.main import app
                        simple_client = TestClient(app)
                        response = simple_client.get(endpoint["url"])
                    else:
                        # 需要认证的端点
                        response = client.get(endpoint["url"])
                    
                    assert response.status_code == 200
                response_times.append(timer.elapsed_time)
            
            avg_time = sum(response_times) / len(response_times)
            print(f"   平均响应时间: {avg_time*1000:.2f}ms")
            print(f"   QPS: {1/avg_time:.1f}")
            
            results.append({
                "name": endpoint["name"],
                "avg_time": avg_time,
                "description": endpoint["description"]
            })
        
        # 框架开销分析
        print(f"\n📈 框架开销分析")
        print(f"{'端点类型':<20} {'响应时间':<12} {'QPS':<8} {'复杂度'}")
        print(f"-" * 60)
        
        for result in results:
            complexity = "简单" if "无数据库" in result["description"] else "复杂"
            print(f"{result['name']:<20} {result['avg_time']*1000:>8.1f}ms   "
                  f"{1/result['avg_time']:>6.1f}  {complexity}")
        
        # 基准对比
        simple_endpoints = [r for r in results if "无数据库" in r["description"]]
        complex_endpoints = [r for r in results if "有数据库" in r["description"]]
        
        if simple_endpoints and complex_endpoints:
            simple_avg = sum(r["avg_time"] for r in simple_endpoints) / len(simple_endpoints)
            complex_avg = sum(r["avg_time"] for r in complex_endpoints) / len(complex_endpoints)
            
            print(f"\n🎯 复杂度影响分析")
            print(f"   简单端点平均: {simple_avg*1000:.2f}ms")
            print(f"   复杂端点平均: {complex_avg*1000:.2f}ms")
            print(f"   复杂度开销: {(complex_avg-simple_avg)*1000:.2f}ms")
            print(f"   复杂度倍数: {complex_avg/simple_avg:.1f}x")
        
        return results
    
    def test_performance_optimization_recommendations(self):
        """性能优化建议"""
        
        print(f"\n💡 性能优化建议")
        print(f"=" * 60)
        
        recommendations = [
            {
                "category": "API路由优化",
                "issues": ["307重定向问题"],
                "solutions": [
                    "统一API路由使用尾斜杠",
                    "配置FastAPI redirect_slashes=False",
                    "客户端调用时使用正确URL格式"
                ]
            },
            {
                "category": "数据库查询优化", 
                "issues": ["查询性能可能不是瓶颈"],
                "solutions": [
                    "添加数据库查询日志分析",
                    "检查是否有N+1查询问题",
                    "考虑添加适当的数据库索引"
                ]
            },
            {
                "category": "FastAPI框架优化",
                "issues": ["框架开销可能是主要瓶颈"],
                "solutions": [
                    "检查中间件配置",
                    "优化依赖注入",
                    "考虑使用异步处理",
                    "减少不必要的数据序列化"
                ]
            },
            {
                "category": "认证优化",
                "issues": ["bcrypt已优化但仍有开销"],
                "solutions": [
                    "考虑JWT token缓存",
                    "优化用户查询逻辑",
                    "减少认证相关的数据库查询"
                ]
            }
        ]
        
        for rec in recommendations:
            print(f"\n🔧 {rec['category']}")
            print(f"   问题: {', '.join(rec['issues'])}")
            print(f"   解决方案:")
            for solution in rec['solutions']:
                print(f"     - {solution}")
        
        return recommendations
