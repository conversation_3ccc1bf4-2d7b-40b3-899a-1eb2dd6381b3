# 性能测试模块

## 📁 目录结构

```
tests/performance/
├── api/                    # API 性能测试
│   ├── test_auth_api.py   # 认证 API 性能测试
│   ├── test_member_api.py # 会员 API 性能测试
│   ├── test_teacher_api.py # 教师 API 性能测试
│   └── test_booking_api.py # 预约 API 性能测试
├── analysis/               # 性能问题分析
│   ├── api_performance_issues_analysis.py  # API 性能问题分析
│   ├── business_logic_performance_analysis.py # 业务逻辑性能分析
│   ├── deep_bottleneck_analysis.py # 深度瓶颈分析
│   └── n_plus_1_query_explanation.py # N+1 查询问题解释
├── database/              # 数据库性能测试
│   ├── test_basic_queries.py    # 基础查询性能
│   ├── test_complex_queries.py  # 复杂查询性能
│   ├── test_rls_performance.py  # RLS 策略性能
│   └── test_large_dataset.py    # 大数据量性能
├── concurrent/            # 并发性能测试
│   ├── test_concurrent_booking.py # 并发预约测试
│   ├── test_concurrent_locks.py   # 并发锁定测试
│   └── test_connection_pool.py    # 连接池并发测试
├── reports/               # 性能报告
│   └── README.md          # 报告说明
└── utils/                 # 性能测试工具
    ├── benchmark_fixtures.py     # 性能测试 fixtures
    ├── data_generators.py      # 数据生成器
    └── performance_utils.py    # 性能测试工具函数
```

## 📊 测试数据创建方法

### 核心数据生成器方法

下表列出了主要的数据生成方法及其用途：

| 方法名称                     | 位置                          | 功能               | 状态 | 适用场景                 |
| ---------------------------- | ----------------------------- | ------------------ | ---- | ------------------------ |
| `performance_data_generator` | `utils/benchmark_fixtures.py` | 生成并入库数据     | 推荐 | 标准性能测试场景         |
| `realistic_test_data`        | `utils/benchmark_fixtures.py` | 预生成真实数据集   | 推荐 | 需要完整数据上下文的测试 |
| `thread_safe_data_generator` | `utils/benchmark_fixtures.py` | 线程安全的数据生成 | 推荐 | 并发测试场景             |
| `random_entity_generator`    | `utils/data_generators.py`    | 生成随机实体数据   | 推荐 | 需要大量随机数据的测试   |

### 详细说明

#### 1. `performance_data_generator`

用于标准性能测试场景的数据生成方法，它会创建基础测试数据并保存到数据库中。

- **参数**：
  - `session`: 数据库会话
  - `test_scale`: 测试规模 (small, medium, large)
  - `data_types`: 要生成的数据类型列表
- **返回值**：包含生成数据 ID 的字典
- **使用场景**：标准 API 和数据库性能测试
- **位置**: `utils/benchmark_fixtures.py`

#### 2. `realistic_test_data`

提供预定义的、真实场景下的测试数据集，用于复杂业务逻辑测试。

- **参数**：
  - `session`: 数据库会话
  - `scenario`: 测试场景名称
- **返回值**：包含真实业务场景数据的字典
- **使用场景**：业务逻辑性能测试、复杂查询测试
- **位置**: `utils/benchmark_fixtures.py`

#### 3. `thread_safe_data_generator`

线程安全版本的数据生成器，专为并发测试设计。

- **参数**：
  - `engine`: 数据库引擎
  - `isolation_level`: 事务隔离级别
  - `test_scale`: 测试规模
- **返回值**：包含生成数据的字典和独立的会话
- **使用场景**：并发性能测试、死锁测试
- **位置**: `utils/benchmark_fixtures.py`

#### 4. `random_entity_generator`

生成不保存到数据库的随机实体对象。

- **参数**：
  - `entity_type`: 实体类型
  - `count`: 生成数量
  - `**kwargs`: 自定义属性
- **返回值**：生成的实体对象列表
- **使用场景**：内存中的性能测试，批量操作测试
- **位置**: `utils/data_generators.py`

## 🚀 使用指南

### 基本使用步骤

1. 导入所需的 fixture

   ```python
   import pytest
   from tests.performance.utils.benchmark_fixtures import performance_data_generator
   ```

2. 在测试函数中使用
   ```python
   def test_api_performance(performance_session):
       # 生成测试数据
       data = performance_data_generator(
           session=performance_session,
           test_scale="medium",
           data_types=["tenants", "users", "members"]
       )

       # 执行性能测试
       # ...
   ```

### 场景示例

#### API 性能测试

```python
@pytest.mark.benchmark
def test_list_teachers_api_performance(authenticated_client, performance_session):
    # 准备测试数据
    data = performance_data_generator(
        session=performance_session,
        data_types=["teachers", "tags"]
    )

    # 执行 API 请求并测量性能
    with timer() as t:
        response = authenticated_client.get("/api/v1/admin/teachers/")

    # 断言响应时间
    assert t.elapsed_ms < 200, f"API 响应时间过长: {t.elapsed_ms} ms"
    assert response.status_code == 200
```

#### 数据库查询性能测试

```python
@pytest.mark.benchmark
def test_complex_join_query_performance(performance_session):
    # 准备测试数据
    data = realistic_test_data(
        session=performance_session,
        scenario="course_booking_flow"
    )

    # 执行查询并测量性能
    with timer() as t:
        # 复杂查询测试
        result = performance_session.execute(complex_query).fetchall()

    # 断言查询时间
    assert t.elapsed_ms < 100, f"查询执行时间过长: {t.elapsed_ms} ms"
```

## 🔍 常见问题解决

### 测试数据创建问题

1. **数据创建过慢**

   - 尝试使用小规模数据 (`test_scale="small"`)
   - 只生成必要的数据类型
   - 使用 `realistic_test_data` 替代完整生成

2. **数据冲突错误**
   - 确保使用独立的测试会话
   - 检查是否有残留的测试数据
   - 使用 `session.rollback()` 处理错误

## 📝 改进建议

1. **优化测试数据生成**

   - 使用批量插入替代单条插入
   - 实现更细粒度的数据生成控制
   - 添加数据缓存机制避免重复生成

2. **改进性能指标收集**
   - 实现自动化性能报告生成
   - 添加 SQL 查询计数和性能分析
   - 集成内存使用分析
