"""
固定课排课性能测试

测试目标：
1. 验证排课算法在实际业务场景下的性能表现
2. 测试不同数据规模下的排课效率
3. 识别性能瓶颈并提供优化建议
4. 确保排课系统能够满足实际业务需求

测试场景：
- 小规模：5教师、20会员、100锁定
- 中规模：20教师、100会员、500锁定  
- 大规模：50教师、500会员、2000锁定
"""

import pytest
import time
import random
from datetime import date, timedelta
from typing import Dict, List, Any
from starlette.testclient import TestClient

from tests.fixtures.database import test_session
from tests.fixtures.client import client
from tests.fixtures.business.tenant import created_tenant
from tests.fixtures.business.user import created_admin_user, admin_token
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer


class SchedulingPerformanceMetrics:
    """排课性能指标"""
    
    def __init__(self):
        self.total_execution_time = 0.0
        self.data_preparation_time = 0.0
        self.algorithm_execution_time = 0.0
        self.database_operation_time = 0.0
        self.total_teachers = 0
        self.total_members = 0
        self.total_locks = 0
        self.total_classes_generated = 0
        self.successful_teachers = 0
        self.failed_teachers = 0
        self.total_amount_processed = 0
        self.classes_per_second = 0.0
        self.locks_per_second = 0.0
        
    def calculate_derived_metrics(self):
        """计算衍生指标"""
        if self.algorithm_execution_time > 0:
            self.classes_per_second = self.total_classes_generated / self.algorithm_execution_time
            self.locks_per_second = self.total_locks / self.algorithm_execution_time
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "total_execution_time": self.total_execution_time,
            "data_preparation_time": self.data_preparation_time,
            "algorithm_execution_time": self.algorithm_execution_time,
            "database_operation_time": self.database_operation_time,
            "total_teachers": self.total_teachers,
            "total_members": self.total_members,
            "total_locks": self.total_locks,
            "total_classes_generated": self.total_classes_generated,
            "successful_teachers": self.successful_teachers,
            "failed_teachers": self.failed_teachers,
            "total_amount_processed": self.total_amount_processed,
            "classes_per_second": self.classes_per_second,
            "locks_per_second": self.locks_per_second,
            "success_rate": self.successful_teachers / max(self.total_teachers, 1) * 100
        }


class SchedulingPerformanceTester:
    """排课性能测试器"""
    
    def __init__(self, client: TestClient, headers: Dict[str, str], admin_user: Dict):
        self.client = client
        self.headers = headers
        self.admin_user = admin_user
        self.metrics = SchedulingPerformanceMetrics()
    
    def create_test_data(self, teacher_count: int, member_count: int, locks_per_member: int) -> Dict[str, Any]:
        """创建测试数据"""
        print(f"📋 创建测试数据：{teacher_count}教师、{member_count}会员、{locks_per_member}锁定/会员")
        
        with PerformanceTimer() as timer:
            # 创建教师
            teachers = self._create_teachers(teacher_count)
            
            # 创建会员
            members = self._create_members(member_count)
            
            # 创建教师时间段
            teacher_slots = self._create_teacher_slots(teachers)
            print(f"teacher_slots 数量: {len(teacher_slots)}")
            
            # 创建会员锁定
            member_locks = self._create_member_locks(members, teacher_slots, locks_per_member)
        
        self.metrics.data_preparation_time = timer.elapsed_time
        self.metrics.total_teachers = len(teachers)
        self.metrics.total_members = len(members)
        self.metrics.total_locks = len(member_locks)
        
        print(f"✅ 测试数据创建完成，耗时: {timer.elapsed_time:.3f}秒")
        
        return {
            "teachers": teachers,
            "members": members,
            "teacher_slots": teacher_slots,
            "member_locks": member_locks
        }
    
    def _create_teachers(self, count: int) -> List[Dict]:
        """创建教师"""
        teachers = []
        teacher_configs = [
            {"region": "europe", "category": "european", "price": 150},
            {"region": "south_africa", "category": "south_african", "price": 120},
            {"region": "philippines", "category": "filipino", "price": 80},
        ]

        # 使用时间戳和随机数确保唯一性
        timestamp = int(time.time())
        random_suffix = random.randint(1000, 9999)

        for i in range(count):
            config = teacher_configs[i % len(teacher_configs)]
            teacher_data = {
                "name": f"性能测试教师_{timestamp}_{random_suffix}_{i+1}",
                "email": f"perf_teacher_{timestamp}_{random_suffix}_{i+1}@test.com",
                "phone": f"138{(timestamp + random_suffix + i) % 100000000:08d}",
                "specialties": ["英语口语"],
                "introduction": f"性能测试教师{timestamp}_{random_suffix}_{i+1}",
                "price_per_class": config["price"],
                "teacher_category": config["category"],
                "region": config["region"],
                "display_code": f"PT{(timestamp + random_suffix + i) % 10000:04d}"
            }
            
            response = self.client.post("/api/v1/admin/teachers/", json=teacher_data, headers=self.headers)
            assert response.status_code == 201
            teacher = response.json()["data"]
            
            # 激活教师
            response = self.client.post(f"/api/v1/admin/teachers/{teacher['id']}/activate", headers=self.headers)
            assert response.status_code == 200
            teacher = response.json()["data"]
            
            teachers.append(teacher)
        
        return teachers
    
    def _create_members(self, count: int) -> List[Dict]:
        """创建会员"""
        members = []

        # 使用时间戳和随机数确保唯一性
        timestamp = int(time.time())
        random_suffix = random.randint(1000, 9999)

        for i in range(count):
            member_data = {
                "name": f"性能测试会员_{timestamp}_{random_suffix}_{i+1}",
                "phone": f"139{(timestamp + random_suffix + i) % 100000000:08d}",
                "email": f"perf_member_{timestamp}_{random_suffix}_{i+1}@test.com",
                "gender": "male" if i % 2 == 0 else "female",
                "age": 20 + (i % 30),
                "level": "intermediate",
                "timezone": "Asia/Shanghai"
            }
            
            response = self.client.post("/api/v1/admin/members/", json=member_data, headers=self.headers)
            assert response.status_code == 201
            member = response.json()["data"]
            
            # 为会员充值（充值足够的金额）
            recharge_data = {
                "member_card_id": member['primary_member_card_id'],
                "amount": 50000,  # 充值500元，足够支付课程
                "bonus_amount": 0,
                "payment_method": "wechat",
                "notes": "性能测试充值"
            }
            
            response = self.client.post(
                f"/api/v1/admin/member-cards/cards/{member['primary_member_card_id']}/recharge",
                json=recharge_data,
                headers=self.headers
            )
            assert response.status_code == 200
            
            members.append(member)
        
        return members
    
    def _create_teacher_slots(self, teachers: List[Dict]) -> List[Dict]:
        """创建教师时间段"""
        teacher_slots = []
        weekdays = [1, 2, 3, 4, 5]  # 周一到周五
        time_slots = ["09:00", "10:00", "11:00", "14:00", "15:00", "16:00"]
        
        for teacher in teachers:
            # 每个教师创建多个时间段
            for weekday in weekdays[:4]:  # 每个教师3天
                for time_slot in time_slots[:5]:  # 每天4个时间段
                    slot_data = {
                        "teacher_id": teacher["id"],
                        "weekday": weekday,
                        "start_time": time_slot,
                        "duration_minutes": 25,
                        "is_available": True,
                        "is_visible_to_members": True,
                        "created_by": self.admin_user["id"]
                    }
                    
                    response = self.client.post("/api/v1/admin/teachers/fixed-slots/", json=slot_data, headers=self.headers)
                    assert response.status_code == 201
                    slot = response.json()["data"]
                    teacher_slots.append(slot)
        
        return teacher_slots
    
    def _create_member_locks(self, members: List[Dict], teacher_slots: List[Dict], locks_per_member: int) -> List[Dict]:
        """创建会员锁定"""
        member_locks = []
        slot_index = 0
        
        for member in members:
            # 每个会员锁定指定数量的时间段
            for _ in range(locks_per_member):
                if slot_index < len(teacher_slots):
                    slot = teacher_slots[slot_index]
                    slot_index = (slot_index + 1) % len(teacher_slots)  # 循环使用时间段
                    
                    lock_data = {
                        "member_id": member["id"],
                        "teacher_fixed_slot_id": slot["id"],
                        "status": "active",
                        "created_by": self.admin_user["id"]
                    }
                    
                    response = self.client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=self.headers)
                    # print(f"_create_member_locks response: {response.json()}")
                    assert response.status_code == 201
                    lock = response.json()["data"]
                    member_locks.append(lock)
        
        return member_locks
    
    def execute_scheduling_performance_test(self, teachers: List[Dict], weeks: int = 4, task_id: str = None, use_optimized: bool = False) -> Dict[str, Any]:
        """执行排课性能测试
        
        Args:
            teachers: 教师列表
            weeks: 排课周数
            task_id: 任务ID（可选，默认自动生成）
            use_optimized: 是否使用优化算法（默认False）
        """
        print(f"🚀 开始{'优化' if use_optimized else ''}排课性能测试：{len(teachers)}教师、{weeks}周")
        
        # 创建排课任务
        today = date.today()
        days_ahead = 7 - today.weekday()
        if days_ahead <= 0:
            days_ahead += 7
        next_monday = today + timedelta(days=days_ahead)
        
        # 如果没有提供task_id，则自动生成
        if not task_id:
            task_id = f"perf_test_{int(time.time())}"
            
        task_data = {
            "task_name": f"性能测试任务_{task_id}",
            "task_id": task_id,  # 使用传入的task_id或自动生成的
            "start_date": next_monday.isoformat(),
            "end_date": (next_monday + timedelta(days=weeks*7-1)).isoformat(),
            "teacher_ids": [t["id"] for t in teachers],
            "teacher_priority_rule": "region_first",
            "balance_insufficient_action": "skip",
            "description": f"{'优化算法' if use_optimized else '原始算法'}性能测试任务-{len(teachers)}教师{weeks}周",
            "use_optimized_algorithm": use_optimized  # 是否使用优化算法
        }
        
        # 创建任务
        response = self.client.post("/api/v1/admin/fixed-schedule/tasks", json=task_data, headers=self.headers)
        assert response.status_code == 201
        task = response.json()["data"]
        
        # 执行排课并计时
        with PerformanceTimer() as timer:
            response = self.client.post(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}/execute", headers=self.headers)
            assert response.status_code == 200
            
            # 等待任务完成
            self._wait_for_task_completion(task['id'])
        
        self.metrics.algorithm_execution_time = timer.elapsed_time
        
        # 获取任务结果
        response = self.client.get(f"/api/v1/admin/fixed-schedule/tasks/{task['id']}", headers=self.headers)
        assert response.status_code == 200
        task_result = response.json()["data"]
        
        # 更新性能指标
        self.metrics.total_classes_generated = task_result.get("total_classes", 0)
        self.metrics.successful_teachers = task_result.get("successful_teachers", 0)
        self.metrics.failed_teachers = task_result.get("failed_teachers", 0)
        self.metrics.total_amount_processed = task_result.get("total_amount", 0)
        
        algorithm_type = "优化算法" if use_optimized else "原始算法"
        print(f"✅ {algorithm_type}排课完成，耗时: {timer.elapsed_time:.3f}秒")
        print(f"   - 生成课程: {self.metrics.total_classes_generated}节")
        print(f"   - 成功教师: {self.metrics.successful_teachers}/{self.metrics.total_teachers}")
        print(f"   - 处理金额: {self.metrics.total_amount_processed}元")
        
        return task_result
    
    def _wait_for_task_completion(self, task_id: int, max_wait_time: int = 60):
        """等待任务完成"""
        wait_time = 0
        
        while wait_time < max_wait_time:
            response = self.client.get(f"/api/v1/admin/fixed-schedule/tasks/{task_id}/status", headers=self.headers)
            assert response.status_code == 200
            
            status_data = response.json()["data"]
            if status_data["status"] in ["completed", "failed"]:
                break
            
            time.sleep(1)
            wait_time += 1
        
        if wait_time >= max_wait_time:
            raise TimeoutError(f"排课任务{task_id}执行超时")
    
    def get_performance_metrics(self) -> SchedulingPerformanceMetrics:
        """获取性能指标"""
        self.metrics.total_execution_time = self.metrics.data_preparation_time + self.metrics.algorithm_execution_time
        self.metrics.calculate_derived_metrics()
        return self.metrics


class TestFixedSchedulePerformance:
    """固定课排课性能测试"""
    
    def test_small_scale_scheduling_performance(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """小规模排课性能测试：5教师、20会员、40锁定"""
        print("\n🎯 小规模排课性能测试")
        
        headers = {"Authorization": f"Bearer {admin_token}"}
        tester = SchedulingPerformanceTester(client, headers, created_admin_user)
        
        # 创建测试数据
        test_data = tester.create_test_data(
            teacher_count=5,
            member_count=20,
            locks_per_member=2
        )
        
        # 执行排课性能测试
        task_result = tester.execute_scheduling_performance_test(test_data["teachers"], weeks=2)
        
        # 获取性能指标
        metrics = tester.get_performance_metrics()
        
        # 性能断言
        assert metrics.algorithm_execution_time < 10.0, f"小规模排课应在10秒内完成，实际耗时: {metrics.algorithm_execution_time:.3f}秒"
        assert metrics.total_classes_generated > 0, "应该生成课程"
        assert metrics.successful_teachers > 0, "应该有成功的教师"
        
        # 打印性能报告
        self._print_performance_report("小规模", metrics)
        
        return metrics.to_dict()

    def test_medium_scale_scheduling_performance(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """中规模排课性能测试：20教师、100会员、300锁定"""
        print("\n🎯 中规模排课性能测试")

        headers = {"Authorization": f"Bearer {admin_token}"}
        tester = SchedulingPerformanceTester(client, headers, created_admin_user)

        # 创建测试数据
        test_data = tester.create_test_data(
            teacher_count=20,
            member_count=100,
            locks_per_member=3
        )

        # 执行排课性能测试
        task_result = tester.execute_scheduling_performance_test(test_data["teachers"], weeks=4)

        # 获取性能指标
        metrics = tester.get_performance_metrics()

        # 性能断言
        assert metrics.algorithm_execution_time < 30.0, f"中规模排课应在30秒内完成，实际耗时: {metrics.algorithm_execution_time:.3f}秒"
        assert metrics.total_classes_generated > 0, "应该生成课程"
        assert metrics.successful_teachers > 0, "应该有成功的教师"
        assert metrics.classes_per_second > 5.0, f"课程生成速度应大于5节/秒，实际: {metrics.classes_per_second:.1f}节/秒"

        # 打印性能报告
        self._print_performance_report("中规模", metrics)

        return metrics.to_dict()

    def test_large_scale_scheduling_performance(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """大规模排课性能测试：50教师、300会员、900锁定"""
        print("\n🎯 大规模排课性能测试")

        headers = {"Authorization": f"Bearer {admin_token}"}
        tester = SchedulingPerformanceTester(client, headers, created_admin_user)

        # 创建测试数据
        test_data = tester.create_test_data(
            teacher_count=50,
            member_count=300,
            locks_per_member=3
        )

        # 执行排课性能测试
        task_result = tester.execute_scheduling_performance_test(test_data["teachers"], weeks=4)

        # 获取性能指标
        metrics = tester.get_performance_metrics()

        # 性能断言
        assert metrics.algorithm_execution_time < 60.0, f"大规模排课应在60秒内完成，实际耗时: {metrics.algorithm_execution_time:.3f}秒"
        assert metrics.total_classes_generated > 0, "应该生成课程"
        assert metrics.successful_teachers > 0, "应该有成功的教师"
        assert metrics.classes_per_second > 3.0, f"课程生成速度应大于3节/秒，实际: {metrics.classes_per_second:.1f}节/秒"

        # 打印性能报告
        self._print_performance_report("大规模", metrics)

        return metrics.to_dict()

    def test_scheduling_performance_comparison(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """排课性能对比测试"""
        print("\n🎯 排课性能对比测试")

        headers = {"Authorization": f"Bearer {admin_token}"}

        # 测试不同规模的性能
        test_scenarios = [
            {"name": "微型", "teachers": 3, "members": 10, "locks_per_member": 2, "weeks": 2},
            {"name": "小型", "teachers": 10, "members": 50, "locks_per_member": 2, "weeks": 2},
            {"name": "中型", "teachers": 25, "members": 150, "locks_per_member": 3, "weeks": 3},
        ]

        results = []

        for scenario in test_scenarios:
            print(f"\n📋 测试{scenario['name']}规模场景")

            tester = SchedulingPerformanceTester(client, headers, created_admin_user)

            # 创建测试数据
            test_data = tester.create_test_data(
                teacher_count=scenario["teachers"],
                member_count=scenario["members"],
                locks_per_member=scenario["locks_per_member"]
            )

            # 执行排课性能测试
            task_result = tester.execute_scheduling_performance_test(
                test_data["teachers"],
                weeks=scenario["weeks"]
            )

            # 获取性能指标
            metrics = tester.get_performance_metrics()

            # 添加场景信息
            result = metrics.to_dict()
            result["scenario_name"] = scenario["name"]
            results.append(result)

            # 打印性能报告
            self._print_performance_report(scenario["name"], metrics)

        # 打印对比报告
        self._print_comparison_report(results)

        return results

    def _print_business_scenario_report(self, scenario_name: str, metrics: SchedulingPerformanceMetrics, business_info: Dict[str, str]):
        """打印业务场景性能报告"""
        print(f"\n📊 {scenario_name}业务场景性能报告:")
        print(f"   业务特征: {business_info.get('scenario_type', 'N/A')}")
        print(f"   总执行时间: {metrics.total_execution_time:.3f}秒")
        print(f"   算法执行时间: {metrics.algorithm_execution_time:.3f}秒")
        print(f"   数据准备时间: {metrics.data_preparation_time:.3f}秒")
        print(f"   教师数量: {metrics.total_teachers}")
        print(f"   会员数量: {metrics.total_members}")
        print(f"   锁定数量: {metrics.total_locks}")
        print(f"   生成课程: {metrics.total_classes_generated}节")
        print(f"   成功率: {metrics.successful_teachers}/{metrics.total_teachers} ({metrics.successful_teachers/max(metrics.total_teachers,1)*100:.1f}%)")
        print(f"   处理金额: {metrics.total_amount_processed}元")
        print(f"   课程生成速度: {metrics.classes_per_second:.1f}节/秒")
        print(f"   锁定处理速度: {metrics.locks_per_second:.1f}个/秒")

        # 业务指标
        if "average_locks_per_member" in business_info:
            print(f"   平均锁定/会员: {business_info['average_locks_per_member']}")
        if "expected_classes_per_week" in business_info:
            print(f"   预期周课程量: {business_info['expected_classes_per_week']}节")

        # 性能评估
        if metrics.algorithm_execution_time < 10:
            print(f"   ✅ 性能评估: 优秀 - 算法执行快速")
        elif metrics.algorithm_execution_time < 30:
            print(f"   ✅ 性能评估: 良好 - 算法执行正常")
        elif metrics.algorithm_execution_time < 60:
            print(f"   ⚠️  性能评估: 一般 - 算法执行较慢")
        else:
            print(f"   ❌ 性能评估: 需优化 - 算法执行过慢")

    def _print_business_comparison_report(self, results: List[Dict[str, Any]]):
        """打印业务场景对比报告"""
        print(f"\n📈 实际业务场景性能对比报告:")
        print(f"{'场景':<12} {'教师':<6} {'会员':<6} {'锁定':<6} {'课程':<6} {'算法耗时':<10} {'课程/秒':<8} {'成功率':<8}")
        print("-" * 80)

        for result in results:
            print(f"{result['scenario_name']:<12} "
                  f"{result['total_teachers']:<6} "
                  f"{result['total_members']:<6} "
                  f"{result['total_locks']:<6} "
                  f"{result['total_classes_generated']:<6} "
                  f"{result['algorithm_execution_time']:<10.3f} "
                  f"{result['classes_per_second']:<8.1f} "
                  f"{result['success_rate']:<8.1f}%")

        # 业务场景分析
        print(f"\n📊 业务场景分析:")
        for i, result in enumerate(results):
            scenario_name = result['scenario_name']
            classes_per_week = result['total_classes_generated'] / result.get('weeks', 4)

            print(f"   {scenario_name}:")
            print(f"     - 周课程产能: {classes_per_week:.0f}节/周")
            print(f"     - 教师利用率: {result['success_rate']:.1f}%")
            print(f"     - 算法效率: {result['classes_per_second']:.1f}节/秒")

            # 业务建议
            if result['algorithm_execution_time'] < 15 and result['success_rate'] > 90:
                print(f"     - 💡 建议: 性能优秀，可支持业务扩展")
            elif result['algorithm_execution_time'] < 30 and result['success_rate'] > 80:
                print(f"     - 💡 建议: 性能良好，适合当前规模")
            else:
                print(f"     - ⚠️  建议: 需关注性能，考虑优化")

    def _print_performance_report(self, scale: str, metrics: SchedulingPerformanceMetrics):
        """打印性能报告"""
        print(f"\n📊 {scale}排课性能报告:")
        print(f"   总执行时间: {metrics.total_execution_time:.3f}秒")
        print(f"   数据准备时间: {metrics.data_preparation_time:.3f}秒")
        print(f"   算法执行时间: {metrics.algorithm_execution_time:.3f}秒")
        print(f"   教师数量: {metrics.total_teachers}")
        print(f"   会员数量: {metrics.total_members}")
        print(f"   锁定数量: {metrics.total_locks}")
        print(f"   生成课程: {metrics.total_classes_generated}节")
        print(f"   成功率: {metrics.successful_teachers}/{metrics.total_teachers} ({metrics.successful_teachers/max(metrics.total_teachers,1)*100:.1f}%)")
        print(f"   处理金额: {metrics.total_amount_processed}元")
        print(f"   课程生成速度: {metrics.classes_per_second:.1f}节/秒")
        print(f"   锁定处理速度: {metrics.locks_per_second:.1f}个/秒")

    def _print_comparison_report(self, results: List[Dict[str, Any]]):
        """打印对比报告"""
        print(f"\n📈 排课性能对比报告:")
        print(f"{'场景':<8} {'教师':<6} {'会员':<6} {'锁定':<6} {'课程':<6} {'算法耗时':<10} {'课程/秒':<8} {'成功率':<8}")
        print("-" * 70)

        for result in results:
            print(f"{result['scenario_name']:<8} "
                  f"{result['total_teachers']:<6} "
                  f"{result['total_members']:<6} "
                  f"{result['total_locks']:<6} "
                  f"{result['total_classes_generated']:<6} "
                  f"{result['algorithm_execution_time']:<10.3f} "
                  f"{result['classes_per_second']:<8.1f} "
                  f"{result['success_rate']:<8.1f}%")

        # 性能趋势分析
        if len(results) > 1:
            print(f"\n📊 性能趋势分析:")
            for i in range(1, len(results)):
                prev = results[i-1]
                curr = results[i]

                scale_factor = curr['total_locks'] / prev['total_locks']
                time_factor = curr['algorithm_execution_time'] / prev['algorithm_execution_time']

                print(f"   {prev['scenario_name']} → {curr['scenario_name']}: "
                      f"数据量增长{scale_factor:.1f}倍，耗时增长{time_factor:.1f}倍")

                if time_factor > scale_factor * 1.5:
                    print(f"   ⚠️  性能下降明显，建议优化")
                elif time_factor < scale_factor:
                    print(f"   ✅ 性能表现良好，算法效率稳定")

    def test_real_business_scenario_small_institution(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """实际业务场景测试：小型机构（5-10教师，30-50会员）"""
        print("\n🎯 实际业务场景测试：小型机构")

        headers = {"Authorization": f"Bearer {admin_token}"}
        tester = SchedulingPerformanceTester(client, headers, created_admin_user)

        # 小型机构典型配置
        # - 5-10个教师，主要是菲教和少量欧美教师
        # - 30-50个活跃会员
        # - 每个会员平均锁定2-3个时间段
        # - 排课周期：通常排4周课程

        print("📋 场景描述：小型在线英语培训机构")
        print("   - 教师配置：8个教师（2欧美+2南非+4菲教）")
        print("   - 会员规模：40个活跃会员")
        print("   - 锁定配置：每个会员平均2.5个锁定")
        print("   - 排课周期：4周课程")

        # 创建符合小型机构特点的测试数据
        test_data = tester.create_test_data(
            teacher_count=8,
            member_count=40,
            locks_per_member=2  # 平均2个锁定，部分会员会有3个
        )

        # 为部分会员添加额外锁定（模拟真实分布）
        additional_locks = []
        for i in range(0, 20, 2):  # 每隔一个会员添加一个额外锁定
            if i < len(test_data["members"]) and len(test_data["teacher_slots"]) > (i + 40):
                member = test_data["members"][i]
                slot = test_data["teacher_slots"][(i + 40) % len(test_data["teacher_slots"])]

                lock_data = {
                    "member_id": member["id"],
                    "teacher_fixed_slot_id": slot["id"],
                    "status": "active",
                    "created_by": created_admin_user["id"]
                }

                response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=headers)
                if response.status_code == 201:
                    additional_locks.append(response.json()["data"])

        total_locks = len(test_data["member_locks"]) + len(additional_locks)
        print(f"✅ 实际创建锁定数量: {total_locks}")

        # 执行4周排课（典型业务周期）
        task_result = tester.execute_scheduling_performance_test(test_data["teachers"], weeks=4)

        # 获取性能指标
        metrics = tester.get_performance_metrics()
        metrics.total_locks = total_locks  # 更新实际锁定数量
        metrics.calculate_derived_metrics()

        # 业务场景性能断言
        assert metrics.algorithm_execution_time < 15.0, f"小型机构排课应在15秒内完成，实际耗时: {metrics.algorithm_execution_time:.3f}秒"
        assert metrics.total_classes_generated > 100, f"小型机构应生成超过100节课程，实际生成: {metrics.total_classes_generated}节"
        assert metrics.successful_teachers >= 6, f"至少75%教师应成功排课，实际成功: {metrics.successful_teachers}/{metrics.total_teachers}"
        assert metrics.classes_per_second > 10.0, f"课程生成速度应大于10节/秒，实际: {metrics.classes_per_second:.1f}节/秒"

        # 打印业务场景性能报告
        self._print_business_scenario_report("小型机构", metrics, {
            "teacher_distribution": "2欧美+2南非+4菲教",
            "member_activity": "40个活跃会员",
            "average_locks_per_member": f"{total_locks/40:.1f}",
            "scheduling_period": "4周",
            "expected_classes_per_week": f"{metrics.total_classes_generated/4:.0f}"
        })

        return metrics.to_dict()

    def test_real_business_scenario_medium_institution(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """实际业务场景测试：中型机构（15-25教师，80-120会员）"""
        print("\n🎯 实际业务场景测试：中型机构")

        headers = {"Authorization": f"Bearer {admin_token}"}
        tester = SchedulingPerformanceTester(client, headers, created_admin_user)

        # 中型机构典型配置
        print("📋 场景描述：中型在线英语培训机构")
        print("   - 教师配置：20个教师（5欧美+5南非+10菲教）")
        print("   - 会员规模：100个活跃会员")
        print("   - 锁定配置：每个会员平均3个锁定")
        print("   - 排课周期：4周课程")

        # 创建符合中型机构特点的测试数据
        test_data = tester.create_test_data(
            teacher_count=20,
            member_count=100,
            locks_per_member=3
        )

        # 执行4周排课
        task_result = tester.execute_scheduling_performance_test(test_data["teachers"], weeks=4)

        # 获取性能指标
        metrics = tester.get_performance_metrics()

        # 业务场景性能断言
        assert metrics.algorithm_execution_time < 30.0, f"中型机构排课应在30秒内完成，实际耗时: {metrics.algorithm_execution_time:.3f}秒"
        assert metrics.total_classes_generated > 500, f"中型机构应生成超过500节课程，实际生成: {metrics.total_classes_generated}节"
        assert metrics.successful_teachers >= 16, f"至少80%教师应成功排课，实际成功: {metrics.successful_teachers}/{metrics.total_teachers}"
        assert metrics.classes_per_second > 15.0, f"课程生成速度应大于15节/秒，实际: {metrics.classes_per_second:.1f}节/秒"

        # 打印业务场景性能报告
        self._print_business_scenario_report("中型机构", metrics, {
            "teacher_distribution": "5欧美+5南非+10菲教",
            "member_activity": "100个活跃会员",
            "average_locks_per_member": f"{metrics.total_locks/100:.1f}",
            "scheduling_period": "4周",
            "expected_classes_per_week": f"{metrics.total_classes_generated/4:.0f}"
        })

        return metrics.to_dict()

    def test_real_business_scenario_peak_hours(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """实际业务场景测试：高峰时段排课（模拟周末批量排课）"""
        print("\n🎯 实际业务场景测试：高峰时段批量排课")

        headers = {"Authorization": f"Bearer {admin_token}"}
        tester = SchedulingPerformanceTester(client, headers, created_admin_user)

        # 高峰时段特点：
        # - 周末管理员批量处理下周排课
        # - 大量会员同时锁定热门时间段
        # - 教师时间段集中在晚上和周末

        print("📋 场景描述：周末高峰时段批量排课")
        print("   - 教师配置：15个教师（集中在热门时段）")
        print("   - 会员规模：80个会员（高密度锁定）")
        print("   - 锁定特点：集中在热门时间段")
        print("   - 排课特点：批量处理，时间集中")

        # 创建高峰时段测试数据
        test_data = tester.create_test_data(
            teacher_count=15,
            member_count=80,
            locks_per_member=4  # 高峰时段会员锁定更多
        )

        # 模拟高峰时段的连续排课（2周密集排课）
        task_result = tester.execute_scheduling_performance_test(test_data["teachers"], weeks=2)

        # 获取性能指标
        metrics = tester.get_performance_metrics()

        # 高峰时段性能断言（要求更高的性能）
        assert metrics.algorithm_execution_time < 20.0, f"高峰时段排课应在20秒内完成，实际耗时: {metrics.algorithm_execution_time:.3f}秒"
        assert metrics.total_classes_generated > 200, f"高峰时段应生成超过200节课程，实际生成: {metrics.total_classes_generated}节"
        assert metrics.successful_teachers >= 12, f"至少80%教师应成功排课，实际成功: {metrics.successful_teachers}/{metrics.total_teachers}"
        assert metrics.classes_per_second > 12.0, f"高峰时段课程生成速度应大于12节/秒，实际: {metrics.classes_per_second:.1f}节/秒"

        # 打印业务场景性能报告
        self._print_business_scenario_report("高峰时段", metrics, {
            "scenario_type": "周末批量排课",
            "member_activity": "80个高活跃会员",
            "average_locks_per_member": f"{metrics.total_locks/80:.1f}",
            "scheduling_period": "2周密集",
            "peak_characteristics": "时间段集中，锁定密度高"
        })

        return metrics.to_dict()

    def test_real_business_scenario_large_institution(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """实际业务场景测试：大型机构（30+教师，200+会员）"""
        print("\n🎯 实际业务场景测试：大型机构")

        headers = {"Authorization": f"Bearer {admin_token}"}
        tester = SchedulingPerformanceTester(client, headers, created_admin_user)

        # 大型机构典型配置
        print("📋 场景描述：大型在线英语培训机构")
        print("   - 教师配置：35个教师（10欧美+10南非+15菲教）")
        print("   - 会员规模：150个活跃会员（模拟大型机构的部分活跃用户）")
        print("   - 锁定配置：每个会员平均3-4个锁定")
        print("   - 排课周期：4周课程")

        # 创建符合大型机构特点的测试数据
        test_data = tester.create_test_data(
            teacher_count=35,
            member_count=150,
            locks_per_member=3
        )

        # 为部分活跃会员添加额外锁定
        additional_locks = []
        for i in range(0, 50):  # 前50个会员是高活跃用户
            if i < len(test_data["members"]) and len(test_data["teacher_slots"]) > (i + 100):
                member = test_data["members"][i]
                slot = test_data["teacher_slots"][(i + 100) % len(test_data["teacher_slots"])]

                lock_data = {
                    "member_id": member["id"],
                    "teacher_fixed_slot_id": slot["id"],
                    "status": "active",
                    "created_by": created_admin_user["id"]
                }

                response = client.post("/api/v1/admin/members/fixed-locks/", json=lock_data, headers=headers)
                if response.status_code == 201:
                    additional_locks.append(response.json()["data"])

        total_locks = len(test_data["member_locks"]) + len(additional_locks)
        print(f"✅ 实际创建锁定数量: {total_locks}")

        # 执行4周排课
        task_result = tester.execute_scheduling_performance_test(test_data["teachers"], weeks=4)

        # 获取性能指标
        metrics = tester.get_performance_metrics()
        metrics.total_locks = total_locks
        metrics.calculate_derived_metrics()

        # 大型机构性能断言
        assert metrics.algorithm_execution_time < 60.0, f"大型机构排课应在60秒内完成，实际耗时: {metrics.algorithm_execution_time:.3f}秒"
        assert metrics.total_classes_generated > 800, f"大型机构应生成超过800节课程，实际生成: {metrics.total_classes_generated}节"
        assert metrics.successful_teachers >= 28, f"至少80%教师应成功排课，实际成功: {metrics.successful_teachers}/{metrics.total_teachers}"
        assert metrics.classes_per_second > 10.0, f"课程生成速度应大于10节/秒，实际: {metrics.classes_per_second:.1f}节/秒"

        # 打印业务场景性能报告
        self._print_business_scenario_report("大型机构", metrics, {
            "teacher_distribution": "10欧美+10南非+15菲教",
            "member_activity": "150个活跃会员",
            "average_locks_per_member": f"{total_locks/150:.1f}",
            "scheduling_period": "4周",
            "expected_classes_per_week": f"{metrics.total_classes_generated/4:.0f}"
        })

        return metrics.to_dict()

    def test_real_business_comprehensive_comparison(
        self,
        client: TestClient,
        created_tenant,
        created_admin_user,
        admin_token
    ):
        """实际业务场景综合对比测试"""
        print("\n🎯 实际业务场景综合对比测试")

        headers = {"Authorization": f"Bearer {admin_token}"}

        # 定义真实业务场景
        business_scenarios = [
            {
                "name": "初创机构",
                "teachers": 5,
                "members": 25,
                "locks_per_member": 2,
                "weeks": 2,
                "description": "刚起步的小型机构"
            },
            {
                "name": "成长机构",
                "teachers": 12,
                "members": 60,
                "locks_per_member": 3,
                "weeks": 3,
                "description": "快速发展的中小型机构"
            },
            {
                "name": "成熟机构",
                "teachers": 20,
                "members": 100,
                "locks_per_member": 3,
                "weeks": 4,
                "description": "业务稳定的中型机构"
            }
        ]

        results = []

        for scenario in business_scenarios:
            print(f"\n📋 测试{scenario['name']}场景 - {scenario['description']}")

            tester = SchedulingPerformanceTester(client, headers, created_admin_user)

            # 创建测试数据
            test_data = tester.create_test_data(
                teacher_count=scenario["teachers"],
                member_count=scenario["members"],
                locks_per_member=scenario["locks_per_member"]
            )

            # 执行排课性能测试
            task_result = tester.execute_scheduling_performance_test(
                test_data["teachers"],
                weeks=scenario["weeks"]
            )

            # 获取性能指标
            metrics = tester.get_performance_metrics()

            # 添加场景信息
            result = metrics.to_dict()
            result["scenario_name"] = scenario["name"]
            result["scenario_description"] = scenario["description"]
            result["weeks"] = scenario["weeks"]
            results.append(result)

            # 打印场景性能报告
            self._print_business_scenario_report(scenario["name"], metrics, {
                "scenario_type": scenario["description"],
                "teacher_count": scenario["teachers"],
                "member_count": scenario["members"],
                "scheduling_period": f"{scenario['weeks']}周",
                "business_stage": scenario["name"]
            })

        # 打印综合对比报告
        self._print_business_comparison_report(results)

        return results
