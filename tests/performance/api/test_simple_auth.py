"""
简化版认证API性能测试

专注于基础认证API性能测试，避免复杂的业务逻辑依赖
"""

import pytest
import time
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer


class TestSimpleAuthPerformance:
    """简化版认证API性能测试"""
    
    @pytest.mark.benchmark
    def test_login_endpoint_exists(self, client):
        """测试登录端点是否存在（基础连通性测试）"""
        
        def check_login_endpoint():
            # 发送一个基础的POST请求到登录端点
            response = client.post("/api/v1/auth/admin/login", json={
                "email": "<EMAIL>",
                "password": "test123",
                "tenant_code": "test_tenant"
            })
            # 我们期望得到某种响应（可能是400/401/404，但不应该是500）
            assert response.status_code in [400, 401, 404, 422]
            return response.status_code
        
        result = check_login_endpoint()
        print(f"登录端点响应状态码: {result}")
    
    @pytest.mark.benchmark
    def test_auth_endpoint_performance(self, client, benchmark):
        """测试认证端点基础性能"""
        
        def auth_request():
            response = client.post("/api/v1/auth/admin/login", json={
                "email": "<EMAIL>", 
                "password": "test123",
                "tenant_code": "test_tenant"
            })
            # 只要不是服务器错误就算成功
            assert response.status_code < 500
            return response.status_code
        
        result = benchmark(auth_request)
        print(f"认证端点性能测试完成，状态码: {result}")
    
    @pytest.mark.benchmark
    def test_multiple_auth_requests(self, client, performance_thresholds):
        """测试多次认证请求性能"""
        
        response_times = []
        iterations = 20  # 减少迭代次数
        
        print(f"\n🚀 测试多次认证请求性能 - {iterations}次迭代")
        
        for i in range(iterations):
            with PerformanceTimer() as timer:
                response = client.post("/api/v1/auth/admin/login", json={
                    "email": f"test{i}@example.com",
                    "password": "test123", 
                    "tenant_code": "test_tenant"
                })
                # 只要不是服务器错误就算成功
                assert response.status_code < 500
            
            response_times.append(timer.elapsed_time)
            
            if (i + 1) % 5 == 0:
                print(f"   进度: {i + 1}/{iterations}")
        
        # 分析性能指标
        metrics = PerformanceAnalyzer.analyze_response_times(response_times)
        PerformanceAnalyzer.print_performance_report(metrics, "认证端点性能测试")
        
        # 验证性能阈值
        avg_response_time_ms = metrics.avg_time * 1000
        
        print(f"\n📊 认证端点性能验证:")
        print(f"   平均响应时间: {avg_response_time_ms:.2f}ms")
        
        # 宽松的性能要求
        if avg_response_time_ms > 1000:  # 1秒阈值
            print(f"⚠️ 警告: 平均响应时间较长 {avg_response_time_ms:.2f}ms")
        else:
            print(f"✅ 平均响应时间符合要求")
    
    @pytest.mark.benchmark
    def test_auth_error_handling_performance(self, client, benchmark):
        """测试认证错误处理性能"""
        
        def invalid_auth_request():
            response = client.post("/api/v1/auth/admin/login", json={
                "email": "<EMAIL>",
                "password": "wrongpassword",
                "tenant_code": "nonexistent"
            })
            # 期望得到客户端错误响应
            assert 400 <= response.status_code < 500
            return response.status_code
        
        result = benchmark(invalid_auth_request)
        print(f"错误处理性能测试完成，状态码: {result}")
    
    @pytest.mark.benchmark
    def test_auth_payload_validation_performance(self, client):
        """测试认证载荷验证性能"""
        
        test_cases = [
            {},  # 空载荷
            {"email": "<EMAIL>"},  # 缺少字段
            {"password": "test123"},  # 缺少字段
            {"email": "invalid-email", "password": "test123", "tenant_code": "test"},  # 无效邮箱
        ]
        
        response_times = []
        
        print(f"\n🚀 测试认证载荷验证性能 - {len(test_cases)}种情况")
        
        for i, payload in enumerate(test_cases):
            with PerformanceTimer() as timer:
                response = client.post("/api/v1/auth/admin/login", json=payload)
                # 期望得到客户端错误响应
                assert 400 <= response.status_code < 500
            
            response_times.append(timer.elapsed_time)
            print(f"   测试用例{i+1}: {response.status_code}, 耗时: {timer.elapsed_time*1000:.2f}ms")
        
        # 分析结果
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            
            print(f"\n📊 载荷验证性能统计:")
            print(f"   平均时间: {avg_time*1000:.2f}ms")
            print(f"   最大时间: {max_time*1000:.2f}ms")
            print(f"   最小时间: {min_time*1000:.2f}ms")
            
            if avg_time < 0.1:  # 100ms
                print(f"✅ 载荷验证性能良好")
            else:
                print(f"⚠️ 载荷验证性能需要关注")
    
    @pytest.mark.benchmark
    @pytest.mark.slow_performance
    def test_auth_stress_test(self, client):
        """认证端点压力测试"""
        
        iterations = 50  # 减少压力测试的迭代次数
        response_times = []
        status_codes = {}
        
        print(f"\n🚀 认证端点压力测试 - {iterations}次请求")
        
        start_time = time.time()
        
        for i in range(iterations):
            with PerformanceTimer() as timer:
                response = client.post("/api/v1/auth/admin/login", json={
                    "email": f"stress_test_{i}@example.com",
                    "password": "test123",
                    "tenant_code": f"stress_tenant_{i}"
                })
                # 记录状态码分布
                status_code = response.status_code
                status_codes[status_code] = status_codes.get(status_code, 0) + 1
                
                # 只要不是服务器错误就算成功
                assert response.status_code < 500
            
            response_times.append(timer.elapsed_time)
            
            if (i + 1) % 10 == 0:
                print(f"   进度: {i + 1}/{iterations}")
        
        total_time = time.time() - start_time
        
        # 分析结果
        avg_response_time = sum(response_times) / len(response_times)
        qps = iterations / total_time
        
        print(f"\n📊 压力测试结果:")
        print(f"   总请求数: {iterations}")
        print(f"   平均响应时间: {avg_response_time*1000:.2f}ms")
        print(f"   QPS: {qps:.2f}")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   状态码分布: {status_codes}")
        
        # 验证压力测试结果
        if avg_response_time < 0.5:  # 500ms
            print(f"✅ 压力测试性能良好")
        else:
            print(f"⚠️ 压力测试性能需要关注")
        
        if qps >= 10:  # 至少10 QPS
            print(f"✅ QPS符合要求")
        else:
            print(f"⚠️ QPS较低: {qps:.2f}")
        
        # 验证没有服务器错误
        server_errors = sum(count for status, count in status_codes.items() if status >= 500)
        assert server_errors == 0, f"发现{server_errors}个服务器错误"
