"""
性能对比测试 - 排除法分析

通过对比简单API和复杂API的性能，识别性能瓶颈
"""

import pytest
import time
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer


class TestPerformanceComparison:
    """性能对比测试"""
    
    @pytest.mark.benchmark
    def test_simple_health_check_performance(self, fixed_performance_client, benchmark):
        """基准测试：简单的健康检查API"""
        
        def health_check_request():
            response = fixed_performance_client.get("/api/v1/info/health")
            assert response.status_code == 200
            return response.json()
        
        result = benchmark(health_check_request)
        assert result["status"] == "ok"
        print(f"健康检查API基准性能已建立")
    
    @pytest.mark.benchmark
    def test_simple_version_api_performance(self, fixed_performance_client, benchmark):
        """基准测试：简单的版本API"""
        
        def version_request():
            response = fixed_performance_client.get("/api/v1/info/version")
            assert response.status_code == 200
            return response.json()
        
        result = benchmark(version_request)
        assert "version" in result['data']
        print(f"版本API基准性能已建立")
    
    @pytest.mark.benchmark
    def test_database_query_api_performance(self, authenticated_client, benchmark):
        """基准测试：简单的数据库查询API（获取会员卡模板）"""
        
        client_data = authenticated_client
        client = client_data["client"]
        
        def template_query_request():
            response = client.get("/api/v1/admin/member-cards/templates?page=1&size=10")
            # 允许404，因为可能没有数据
            assert response.status_code in [200, 404]
            return response.status_code
        
        result = benchmark(template_query_request)
        print(f"数据库查询API基准性能已建立，状态码: {result}")
    
    @pytest.mark.benchmark
    def test_login_api_performance_detailed(self, fixed_performance_client, realistic_test_data, benchmark):
        """详细测试：登录API性能（与基准对比）"""
        
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        admin_user = tenant_data["admin_users"][0]
        
        login_data = {
            "email": admin_user.email,
            "password": "password123",
            "tenant_code": tenant.code
        }
        
        def login_request():
            response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
            assert response.status_code == 200
            data = response.json()
            assert "access_token" in data["data"]
            return data
        
        result = benchmark(login_request)
        print(f"登录API性能测试完成")
    
    def test_performance_breakdown_analysis(self, fixed_performance_client, realistic_test_data):
        """性能分解分析：逐步测试登录流程的各个组件"""
        
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        admin_user = tenant_data["admin_users"][0]
        
        print(f"\n🔍 登录API性能分解分析")
        print(f"=" * 60)
        
        # 1. 测试租户查询性能
        print(f"\n1️⃣ 测试租户查询性能")
        tenant_query_times = []
        for i in range(10):
            with PerformanceTimer() as timer:
                response = fixed_performance_client.get(f"/api/v1/info/health")  # 简单查询作为对比
            tenant_query_times.append(timer.elapsed_time)
        
        avg_tenant_query = sum(tenant_query_times) / len(tenant_query_times)
        print(f"   平均简单查询时间: {avg_tenant_query*1000:.2f}ms")
        
        # 2. 测试完整登录流程性能
        print(f"\n2️⃣ 测试完整登录流程性能")
        login_times = []
        login_data = {
            "email": admin_user.email,
            "password": "password123",
            "tenant_code": tenant.code
        }
        
        for i in range(10):
            with PerformanceTimer() as timer:
                response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
                assert response.status_code == 200
            login_times.append(timer.elapsed_time)
        
        avg_login_time = sum(login_times) / len(login_times)
        print(f"   平均登录时间: {avg_login_time*1000:.2f}ms")
        
        # 3. 计算性能差异
        print(f"\n3️⃣ 性能差异分析")
        overhead = avg_login_time - avg_tenant_query
        overhead_percentage = (overhead / avg_login_time) * 100
        
        print(f"   简单查询: {avg_tenant_query*1000:.2f}ms")
        print(f"   完整登录: {avg_login_time*1000:.2f}ms")
        print(f"   额外开销: {overhead*1000:.2f}ms ({overhead_percentage:.1f}%)")
        
        # 4. 分析可能的瓶颈
        print(f"\n4️⃣ 瓶颈分析")
        if overhead > 0.1:  # 如果额外开销超过100ms
            print(f"   ⚠️  登录流程存在显著性能开销")
            print(f"   🔍 可能的瓶颈：")
            print(f"      - 密码哈希验证 (bcrypt)")
            print(f"      - JWT token生成")
            print(f"      - 数据库事务处理")
            print(f"      - 用户状态更新操作")
        else:
            print(f"   ✅ 登录流程性能正常")
        
        return {
            "simple_query_avg": avg_tenant_query,
            "login_avg": avg_login_time,
            "overhead": overhead,
            "overhead_percentage": overhead_percentage
        }
    
    def test_password_verification_isolation(self, fixed_performance_client, realistic_test_data):
        """隔离测试：密码验证性能"""
        
        print(f"\n🔐 密码验证性能隔离测试")
        print(f"=" * 60)
        
        # 测试正确密码
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        admin_user = tenant_data["admin_users"][0]
        
        correct_login_data = {
            "email": admin_user.email,
            "password": "password123",
            "tenant_code": tenant.code
        }
        
        # 测试错误密码（应该更快，因为用户不存在时不会进行密码验证）
        wrong_login_data = {
            "email": "<EMAIL>",
            "password": "wrongpassword",
            "tenant_code": tenant.code
        }
        
        # 测试正确密码的性能
        correct_times = []
        for i in range(5):
            with PerformanceTimer() as timer:
                response = fixed_performance_client.post("/api/v1/auth/admin/login", json=correct_login_data)
                assert response.status_code == 200
            correct_times.append(timer.elapsed_time)
        
        # 测试错误密码的性能
        wrong_times = []
        for i in range(5):
            with PerformanceTimer() as timer:
                response = fixed_performance_client.post("/api/v1/auth/admin/login", json=wrong_login_data)
                assert response.status_code in [400, 401, 422]  # 期望认证失败
            wrong_times.append(timer.elapsed_time)
        
        avg_correct = sum(correct_times) / len(correct_times)
        avg_wrong = sum(wrong_times) / len(wrong_times)
        
        print(f"   正确密码平均时间: {avg_correct*1000:.2f}ms")
        print(f"   错误密码平均时间: {avg_wrong*1000:.2f}ms")
        print(f"   密码验证开销: {(avg_correct - avg_wrong)*1000:.2f}ms")
        
        if avg_correct > avg_wrong * 2:
            print(f"   ⚠️  密码验证可能是主要瓶颈")
            print(f"   💡 建议：考虑优化bcrypt配置或使用更快的哈希算法")
        else:
            print(f"   ✅ 密码验证性能正常")
        
        return {
            "correct_password_avg": avg_correct,
            "wrong_password_avg": avg_wrong,
            "password_verification_overhead": avg_correct - avg_wrong
        }
    
    def test_concurrent_simple_vs_complex(self, fixed_performance_client, realistic_test_data, concurrent_tester):
        """并发对比测试：简单API vs 复杂API"""
        
        print(f"\n🚀 并发性能对比测试")
        print(f"=" * 60)
        
        # 简单API并发测试
        def simple_api_task(task_id: int):
            response = fixed_performance_client.get("/api/v1/info/health")
            if response.status_code == 200:
                return {"task_id": task_id, "status": "success"}
            else:
                raise Exception(f"Simple API failed: {response.status_code}")
        
        # 复杂API并发测试
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        admin_user = tenant_data["admin_users"][0]
        
        def complex_api_task(task_id: int):
            login_data = {
                "email": admin_user.email,
                "password": "password123",
                "tenant_code": tenant.code
            }
            response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
            if response.status_code == 200:
                return {"task_id": task_id, "status": "success"}
            else:
                raise Exception(f"Login API failed: {response.status_code}")
        
        # 并发测试参数
        test_args_list = [(i,) for i in range(10)]
        
        # 测试简单API并发性能
        print(f"\n📊 简单API并发测试 (10个并发)")
        start_time = time.time()
        simple_results = concurrent_tester.run_concurrent_test(simple_api_task, test_args_list, timeout=30)
        simple_duration = time.time() - start_time
        
        simple_success = len([r for r in simple_results if r['success']])
        simple_qps = len(test_args_list) / simple_duration
        
        print(f"   成功率: {simple_success}/{len(test_args_list)} ({simple_success/len(test_args_list)*100:.1f}%)")
        print(f"   总耗时: {simple_duration:.3f}秒")
        print(f"   QPS: {simple_qps:.2f}")
        
        # 测试复杂API并发性能
        print(f"\n📊 复杂API并发测试 (10个并发)")
        start_time = time.time()
        complex_results = concurrent_tester.run_concurrent_test(complex_api_task, test_args_list, timeout=60)
        complex_duration = time.time() - start_time
        
        complex_success = len([r for r in complex_results if r['success']])
        complex_qps = len(test_args_list) / complex_duration
        
        print(f"   成功率: {complex_success}/{len(test_args_list)} ({complex_success/len(test_args_list)*100:.1f}%)")
        print(f"   总耗时: {complex_duration:.3f}秒")
        print(f"   QPS: {complex_qps:.2f}")
        
        # 性能对比分析
        print(f"\n📈 并发性能对比")
        qps_ratio = simple_qps / complex_qps if complex_qps > 0 else float('inf')
        print(f"   简单API QPS: {simple_qps:.2f}")
        print(f"   复杂API QPS: {complex_qps:.2f}")
        print(f"   性能差异: {qps_ratio:.1f}x")
        
        if qps_ratio > 10:
            print(f"   ⚠️  复杂API存在严重性能瓶颈")
        elif qps_ratio > 5:
            print(f"   ⚠️  复杂API存在明显性能瓶颈")
        else:
            print(f"   ✅ 性能差异在合理范围内")
        
        return {
            "simple_qps": simple_qps,
            "complex_qps": complex_qps,
            "qps_ratio": qps_ratio,
            "simple_success_rate": simple_success/len(test_args_list),
            "complex_success_rate": complex_success/len(test_args_list)
        }
