"""
修复后的认证API性能测试

测试真实的业务逻辑性能，而不是简化版本
"""

import pytest
import time
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer


class TestFixedAuthAPIPerformance:
    """修复后的认证API性能测试"""
    
    @pytest.mark.benchmark
    def test_real_login_api_performance(self, fixed_performance_client, realistic_test_data, benchmark):
        """测试真实登录API性能"""
        
        # 获取真实测试数据
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        admin_user = tenant_data["admin_users"][0]
        
        login_data = {
            "email": admin_user.email,
            "password": "password123",
            "tenant_code": tenant.code
        }
        
        def login_request():
            response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
            assert response.status_code == 200
            data = response.json()
            assert "access_token" in data["data"]
            return data
        
        result = benchmark(login_request)
        
        # 验证返回的token有效性
        assert result["data"]["token_type"] == "bearer"
        assert len(result["data"]["access_token"]) > 50  # JWT token应该很长
    
    @pytest.mark.benchmark
    def test_login_with_different_tenants(self, fixed_performance_client, realistic_test_data, performance_thresholds):
        """测试不同租户登录性能"""
        
        response_times = []
        iterations = 15  # 测试3个租户，每个5次
        
        print(f"\n🚀 测试多租户登录性能 - {iterations}次迭代")
        
        for i in range(iterations):
            # 轮换使用不同租户
            tenant_key = f"tenant_{i % 3}"
            tenant_data = realistic_test_data[tenant_key]
            tenant = tenant_data["tenant"]
            admin_user = tenant_data["admin_users"][i % 2]  # 轮换用户
            
            login_data = {
                "email": admin_user.email,
                "password": "password123",
                "tenant_code": tenant.code
            }
            
            with PerformanceTimer() as timer:
                response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
                assert response.status_code == 200
                data = response.json()
                assert "access_token" in data["data"]
            
            response_times.append(timer.elapsed_time)
            
            if (i + 1) % 5 == 0:
                print(f"   进度: {i + 1}/{iterations}")
        
        # 分析性能指标
        metrics = PerformanceAnalyzer.analyze_response_times(response_times)
        PerformanceAnalyzer.print_performance_report(metrics, "多租户登录性能测试")
        
        # 验证性能阈值
        avg_response_time_ms = metrics.avg_time * 1000
        
        print(f"\n📊 多租户登录性能验证:")
        print(f"   平均响应时间: {avg_response_time_ms:.2f}ms (阈值: {performance_thresholds['api_response_time_ms']}ms)")
        
        if avg_response_time_ms > performance_thresholds["api_response_time_ms"]:
            print(f"⚠️ 警告: 平均响应时间超过阈值")
        else:
            print(f"✅ 平均响应时间符合要求")
    
    @pytest.mark.benchmark
    def test_token_validation_performance(self, authenticated_client, benchmark):
        """测试token验证性能"""
        
        client_data = authenticated_client
        client = client_data["client"]
        
        def validate_token():
            # 调用需要认证的API来验证token
            response = client.get("/api/v1/info/profile")  # 假设这个端点需要认证
            # 如果端点不存在，至少验证认证头被正确处理
            assert response.status_code in [200, 404, 405]  # 不应该是401未认证
            return response.status_code
        
        result = benchmark(validate_token)
        print(f"Token验证测试完成，状态码: {result}")
    
    @pytest.mark.benchmark
    def test_login_error_handling_performance(self, fixed_performance_client, realistic_test_data, benchmark):
        """测试登录错误处理性能"""
        
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        
        # 测试错误的密码
        invalid_login_data = {
            "email": "<EMAIL>",
            "password": "wrongpassword",
            "tenant_code": tenant.code
        }
        
        def invalid_login_request():
            response = fixed_performance_client.post("/api/v1/auth/admin/login", json=invalid_login_data)
            # 期望得到认证失败的响应
            assert response.status_code in [400, 401, 422]
            return response.status_code
        
        result = benchmark(invalid_login_request)
        print(f"错误处理性能测试完成，状态码: {result}")
    
    @pytest.mark.benchmark
    def test_concurrent_login_performance(self, fixed_performance_client, realistic_test_data, concurrent_tester):
        """测试并发登录性能"""
        
        def concurrent_login_task(task_id: int):
            """并发登录任务"""
            try:
                # 使用不同的租户和用户避免冲突
                tenant_key = f"tenant_{task_id % 3}"
                tenant_data = realistic_test_data[tenant_key]
                tenant = tenant_data["tenant"]
                admin_user = tenant_data["admin_users"][task_id % 2]
                
                login_data = {
                    "email": admin_user.email,
                    "password": "password123",
                    "tenant_code": tenant.code
                }
                
                response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "task_id": task_id,
                        "tenant_code": tenant.code,
                        "access_token": data["data"]["access_token"][:20] + "...",
                        "success": True
                    }
                else:
                    raise Exception(f"Login failed with status {response.status_code}: {response.text}")
                    
            except Exception as e:
                raise Exception(f"Concurrent login {task_id} failed: {str(e)}")
        
        # 并发登录测试
        test_args_list = [(i,) for i in range(12)]  # 12个并发登录
        
        print(f"\n🚀 并发登录性能测试 - {len(test_args_list)}个并发请求")
        
        start_time = time.time()
        results = concurrent_tester.run_concurrent_test(concurrent_login_task, test_args_list, timeout=60)
        end_time = time.time()
        
        # 分析结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]
        
        print(f"\n📊 并发登录结果:")
        print(f"   总登录数: {len(results)}")
        print(f"   成功登录: {len(successful_results)}")
        print(f"   失败登录: {len(failed_results)}")
        print(f"   成功率: {len(successful_results)/len(results)*100:.2f}%")
        print(f"   总耗时: {end_time - start_time:.3f}秒")
        print(f"   平均QPS: {len(results)/(end_time - start_time):.2f}")
        
        # 打印失败信息
        if failed_results:
            print(f"\n❌ 失败的登录:")
            for failed in failed_results[:3]:
                print(f"   任务{failed['args'][0]}: {failed['error']}")
        
        # 验证结果 - 真实业务场景应该有高成功率
        assert len(successful_results) >= len(test_args_list) * 0.8  # 至少80%成功
        
        # 验证返回的数据
        for result in successful_results[:3]:
            if result['success'] and result['result']:
                login_result = result['result']['result']
                assert login_result['access_token'].endswith("...")
                print(f"   ✅ 任务{login_result['task_id']}: 租户={login_result['tenant_code']}")
    
    @pytest.mark.benchmark
    @pytest.mark.slow_performance
    def test_login_stress_test(self, fixed_performance_client, realistic_test_data, performance_thresholds):
        """登录压力测试"""
        
        iterations = 30
        response_times = []
        status_codes = {}
        
        print(f"\n🚀 登录压力测试 - {iterations}次请求")
        
        start_time = time.time()
        
        for i in range(iterations):
            # 轮换使用不同的租户和用户
            tenant_key = f"tenant_{i % 3}"
            tenant_data = realistic_test_data[tenant_key]
            tenant = tenant_data["tenant"]
            admin_user = tenant_data["admin_users"][i % 2]
            
            login_data = {
                "email": admin_user.email,
                "password": "password123",
                "tenant_code": tenant.code
            }
            
            with PerformanceTimer() as timer:
                response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
                status_code = response.status_code
                status_codes[status_code] = status_codes.get(status_code, 0) + 1
                
                # 验证响应
                if status_code == 200:
                    data = response.json()
                    assert "access_token" in data["data"]
                else:
                    print(f"   请求{i+1}失败: {status_code}")
            
            response_times.append(timer.elapsed_time)
            
            if (i + 1) % 10 == 0:
                print(f"   进度: {i + 1}/{iterations}")
        
        total_time = time.time() - start_time
        
        # 分析结果
        successful_requests = status_codes.get(200, 0)
        success_rate = successful_requests / iterations
        avg_response_time = sum(response_times) / len(response_times)
        qps = iterations / total_time
        
        print(f"\n📊 登录压力测试结果:")
        print(f"   总请求数: {iterations}")
        print(f"   成功请求: {successful_requests}")
        print(f"   成功率: {success_rate*100:.2f}%")
        print(f"   平均响应时间: {avg_response_time*1000:.2f}ms")
        print(f"   QPS: {qps:.2f}")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   状态码分布: {status_codes}")
        
        # 验证性能要求
        if success_rate >= 0.9:  # 90%成功率
            print(f"✅ 成功率符合要求")
        else:
            print(f"⚠️ 警告: 成功率较低 {success_rate:.2%}")
        
        if avg_response_time * 1000 <= performance_thresholds["api_response_time_ms"]:
            print(f"✅ 响应时间符合要求")
        else:
            print(f"⚠️ 警告: 响应时间超过阈值")
        
        if qps >= performance_thresholds["min_qps"]:
            print(f"✅ QPS符合要求")
        else:
            print(f"⚠️ 警告: QPS较低")
        
        # 基本验证
        assert success_rate >= 0.8  # 至少80%成功率
        assert successful_requests > 0  # 至少有成功的请求
