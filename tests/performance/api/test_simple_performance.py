"""
简化版性能测试

使用现有测试基础设施进行基础性能测试
"""

import pytest
import time
from fastapi.testclient import TestClient
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer


class TestSimplePerformance:
    """简化版性能测试"""
    
    @pytest.mark.benchmark
    def test_health_check_performance(self, client: TestClient, benchmark):
        """测试健康检查API性能"""
        
        def health_check_request():
            response = client.get("/api/v1/info/health")
            assert response.status_code == 200
            return response.json()
        
        result = benchmark(health_check_request)
        
        # 验证响应内容
        assert result["status"] == "ok"
    
    @pytest.mark.benchmark
    def test_version_api_performance(self, client: TestClient, benchmark):
        """测试版本API性能"""
        
        def version_request():
            response = client.get("/api/v1/info/version")
            assert response.status_code == 200
            return response.json()
        
        result = benchmark(version_request)
        
        # 验证响应内容
        assert "version" in result['data']
    
    @pytest.mark.benchmark
    def test_admin_login_performance(self, client: TestClient, created_tenant, created_admin_user, benchmark):
        """测试管理员登录API性能"""
        
        login_data = {
            "email": created_admin_user["email"],
            "password": "password123",
            "tenant_code": created_tenant["code"]
        }
        
        def login_request():
            response = client.post("/api/v1/auth/admin/login", json=login_data)
            assert response.status_code == 200
            return response.json()
        
        result = benchmark(login_request)
        
        # 验证响应内容
        assert "access_token" in result["data"]
        assert result["data"]["token_type"] == "bearer"
    
    @pytest.mark.benchmark
    def test_admin_login_response_time(self, client: TestClient, created_tenant, created_admin_user, performance_thresholds):
        """测试管理员登录API响应时间"""
        
        login_data = {
            "email": created_admin_user["email"],
            "password": "password123",
            "tenant_code": created_tenant["code"]
        }
        
        response_times = []
        iterations = 20  # 减少迭代次数以加快测试
        
        print(f"\n🚀 测试管理员登录API响应时间 - {iterations}次迭代")
        
        for i in range(iterations):
            with PerformanceTimer() as timer:
                response = client.post("/api/v1/auth/admin/login", json=login_data)
                assert response.status_code == 200
            
            response_times.append(timer.elapsed_time)
            
            if (i + 1) % 5 == 0:
                print(f"   进度: {i + 1}/{iterations}")
        
        # 分析性能指标
        metrics = PerformanceAnalyzer.analyze_response_times(response_times)
        PerformanceAnalyzer.print_performance_report(metrics, "管理员登录API性能测试")
        
        # 验证性能阈值
        avg_response_time_ms = metrics.avg_time * 1000
        p95_response_time_ms = metrics.p95_time * 1000
        
        print(f"\n📊 性能验证:")
        print(f"   平均响应时间: {avg_response_time_ms:.2f}ms (阈值: {performance_thresholds['api_response_time_ms']}ms)")
        print(f"   95%分位响应时间: {p95_response_time_ms:.2f}ms")
        
        # 性能阈值验证（警告而非失败）
        if avg_response_time_ms > performance_thresholds["api_response_time_ms"]:
            print(f"⚠️ 警告: 平均响应时间超过阈值 {avg_response_time_ms:.2f}ms > {performance_thresholds['api_response_time_ms']}ms")
        else:
            print(f"✅ 平均响应时间符合要求")
        
        if p95_response_time_ms > performance_thresholds["api_response_time_ms"] * 2:
            print(f"⚠️ 警告: 95%分位响应时间过长 {p95_response_time_ms:.2f}ms")
        else:
            print(f"✅ 95%分位响应时间符合要求")
    
    @pytest.mark.benchmark
    def test_member_login_performance(self, client: TestClient, created_tenant, created_member, benchmark):
        """测试会员登录API性能"""
        
        # 先发送验证码（模拟）
        send_code_data = {
            "phone": created_member["phone"],
            "tenant_code": created_tenant["code"]
        }
        
        def send_code_request():
            response = client.post("/api/v1/auth/member/send-code", json=send_code_data)
            # 可能返回200或其他状态码，取决于实现
            return response
        
        # 测试发送验证码性能
        send_result = benchmark(send_code_request)
        print(f"发送验证码响应: {send_result.status_code}")
    
    @pytest.mark.benchmark
    def test_multiple_api_calls_performance(self, client: TestClient):
        """测试多个API调用的性能"""
        
        apis = [
            ("/api/v1/info/health", "GET"),
            ("/api/v1/info/version", "GET"),
        ]
        
        response_times = []
        
        print(f"\n🚀 测试多个API调用性能")
        
        for api_path, method in apis:
            for i in range(5):  # 每个API调用5次
                with PerformanceTimer() as timer:
                    if method == "GET":
                        response = client.get(api_path)
                    else:
                        response = client.post(api_path)
                    
                    # 大部分应该成功，但不强制要求
                    if response.status_code < 500:  # 只要不是服务器错误就算成功
                        response_times.append(timer.elapsed_time)
                
                print(f"   {method} {api_path}: {response.status_code} - {timer.elapsed_time*1000:.2f}ms")
        
        if response_times:
            # 分析性能指标
            metrics = PerformanceAnalyzer.analyze_response_times(response_times)
            PerformanceAnalyzer.print_performance_report(metrics, "多API调用性能测试")
    
    @pytest.mark.benchmark
    @pytest.mark.slow_performance
    def test_stress_health_check(self, client: TestClient):
        """健康检查压力测试"""
        
        iterations = 100
        response_times = []
        failed_requests = 0
        
        print(f"\n🚀 健康检查压力测试 - {iterations}次请求")
        
        start_time = time.time()
        
        for i in range(iterations):
            try:
                with PerformanceTimer() as timer:
                    response = client.get("/api/v1/info/health")
                    if response.status_code == 200:
                        response_times.append(timer.elapsed_time)
                    else:
                        failed_requests += 1
            except Exception as e:
                failed_requests += 1
                print(f"   请求{i+1}失败: {e}")
            
            if (i + 1) % 20 == 0:
                print(f"   进度: {i + 1}/{iterations}")
        
        total_time = time.time() - start_time
        
        # 分析结果
        success_rate = len(response_times) / iterations
        qps = iterations / total_time
        
        print(f"\n📊 压力测试结果:")
        print(f"   总请求数: {iterations}")
        print(f"   成功请求: {len(response_times)}")
        print(f"   失败请求: {failed_requests}")
        print(f"   成功率: {success_rate:.2%}")
        print(f"   QPS: {qps:.2f}")
        print(f"   总耗时: {total_time:.2f}秒")
        
        if response_times:
            metrics = PerformanceAnalyzer.analyze_response_times(response_times)
            PerformanceAnalyzer.print_performance_report(metrics, "健康检查压力测试")
        
        # 验证压力测试结果（警告而非失败）
        if success_rate < 0.95:
            print(f"⚠️ 警告: 成功率较低 {success_rate:.2%}")
        else:
            print(f"✅ 成功率符合要求")
        
        if qps < 50:  # 健康检查应该有较高的QPS
            print(f"⚠️ 警告: QPS较低 {qps:.2f}")
        else:
            print(f"✅ QPS符合要求")
