"""
高频API性能测试

专注于真实环境中高并发的核心API测试
"""

import pytest
import time
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer


class TestHighFrequencyAPIs:
    """高频API性能测试"""
    
    @pytest.mark.benchmark
    def test_teacher_list_query_performance(self, authenticated_client, benchmark):
        """教师列表查询性能测试（高频API）- 优化URL"""

        client_data = authenticated_client
        client = client_data["client"]

        def teacher_list_query():
            # 使用优化的URL（带尾斜杠，避免重定向）
            response = client.get("/api/v1/admin/teachers/?page=1&size=20")
            # 允许200或404（可能没有数据）
            assert response.status_code in [200, 404]
            return response.status_code

        result = benchmark(teacher_list_query)
        print(f"教师列表查询性能测试完成，状态码: {result}")
        print(f"💡 注意: 当前性能主要受FastAPI框架开销影响(~15ms)，数据库查询仅占~0.5ms")
    
    @pytest.mark.benchmark
    def test_member_list_query_performance(self, authenticated_client, benchmark):
        """会员列表查询性能测试（高频API）"""
        
        client_data = authenticated_client
        client = client_data["client"]
        
        def member_list_query():
            response = client.get("/api/v1/admin/members?page=1&size=20")
            assert response.status_code in [200, 404]
            return response.status_code
        
        result = benchmark(member_list_query)
        print(f"会员列表查询性能测试完成，状态码: {result}")
    
    @pytest.mark.benchmark
    def test_course_schedule_query_performance(self, authenticated_client, benchmark):
        """课程排课查询性能测试（高频API）"""
        
        client_data = authenticated_client
        client = client_data["client"]
        
        def schedule_query():
            # 查询今天的课程安排
            from datetime import date
            today = date.today().isoformat()
            response = client.get(f"/api/v1/admin/courses/schedule?date={today}")
            assert response.status_code in [200, 404]
            return response.status_code
        
        result = benchmark(schedule_query)
        print(f"课程排课查询性能测试完成，状态码: {result}")
    
    def test_high_frequency_apis_stress_test(self, authenticated_client):
        """高频API压力测试"""
        
        client_data = authenticated_client
        client = client_data["client"]
        
        print(f"\n🚀 高频API压力测试")
        print(f"=" * 60)
        
        # 定义高频API测试用例
        api_tests = [
            {
                "name": "教师列表",
                "url": "/api/v1/admin/teachers?page=1&size=20",
                "description": "教师管理页面主要查询"
            },
            {
                "name": "会员列表", 
                "url": "/api/v1/admin/members?page=1&size=20",
                "description": "会员管理页面主要查询"
            },
            {
                "name": "课程模板",
                "url": "/api/v1/admin/courses/templates?page=1&size=10",
                "description": "课程配置查询"
            },
            {
                "name": "会员卡模板",
                "url": "/api/v1/admin/member-cards/templates?page=1&size=10", 
                "description": "会员卡配置查询"
            }
        ]
        
        results = []
        
        for api_test in api_tests:
            print(f"\n📊 测试 {api_test['name']}")
            print(f"   描述: {api_test['description']}")
            
            # 测试多次请求性能
            response_times = []
            status_codes = {}
            
            for i in range(10):
                with PerformanceTimer() as timer:
                    response = client.get(api_test["url"])
                    status_code = response.status_code
                    status_codes[status_code] = status_codes.get(status_code, 0) + 1
                response_times.append(timer.elapsed_time)
            
            # 分析性能指标
            metrics = PerformanceAnalyzer.analyze_response_times(response_times)
            
            print(f"   平均响应时间: {metrics.avg_time*1000:.2f}ms")
            print(f"   最快响应时间: {metrics.min_time*1000:.2f}ms")
            print(f"   最慢响应时间: {metrics.max_time*1000:.2f}ms")
            print(f"   理论QPS: {1/metrics.avg_time:.1f}")
            print(f"   状态码分布: {status_codes}")
            
            # 性能评估
            if metrics.avg_time < 0.05:  # 50ms
                performance_level = "🟢 优秀"
            elif metrics.avg_time < 0.1:  # 100ms
                performance_level = "🟡 良好"
            elif metrics.avg_time < 0.2:  # 200ms
                performance_level = "🟠 一般"
            else:
                performance_level = "🔴 需优化"
            
            print(f"   性能评级: {performance_level}")
            
            results.append({
                "name": api_test["name"],
                "avg_time": metrics.avg_time,
                "qps": 1/metrics.avg_time,
                "status_codes": status_codes,
                "performance_level": performance_level
            })
        
        # 总结报告
        print(f"\n📈 高频API性能总结")
        print(f"{'API名称':<12} {'平均时间':<12} {'QPS':<8} {'性能评级'}")
        print(f"-" * 50)
        
        for result in results:
            print(f"{result['name']:<12} {result['avg_time']*1000:>8.1f}ms   "
                  f"{result['qps']:>6.1f}  {result['performance_level']}")
        
        return results
    
    def test_concurrent_high_frequency_apis(self, authenticated_client, concurrent_tester):
        """高频API并发性能测试"""
        
        client_data = authenticated_client
        client = client_data["client"]
        
        print(f"\n🚀 高频API并发性能测试")
        print(f"=" * 60)
        
        def concurrent_teacher_query(task_id: int):
            """并发教师查询任务"""
            try:
                response = client.get("/api/v1/admin/teachers?page=1&size=20")
                if response.status_code in [200, 404]:
                    return {
                        "task_id": task_id,
                        "status_code": response.status_code,
                        "success": True
                    }
                else:
                    raise Exception(f"Unexpected status code: {response.status_code}")
            except Exception as e:
                raise Exception(f"Teacher query {task_id} failed: {str(e)}")
        
        # 并发测试
        test_args_list = [(i,) for i in range(15)]  # 15个并发查询
        
        print(f"   测试API: 教师列表查询")
        print(f"   并发数: {len(test_args_list)}")
        
        start_time = time.time()
        results = concurrent_tester.run_concurrent_test(concurrent_teacher_query, test_args_list, timeout=30)
        end_time = time.time()
        
        # 分析结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]
        
        success_rate = len(successful_results) / len(results)
        total_time = end_time - start_time
        qps = len(results) / total_time
        
        print(f"\n📊 并发查询结果:")
        print(f"   总查询数: {len(results)}")
        print(f"   成功查询: {len(successful_results)}")
        print(f"   失败查询: {len(failed_results)}")
        print(f"   成功率: {success_rate*100:.2f}%")
        print(f"   总耗时: {total_time:.3f}秒")
        print(f"   平均QPS: {qps:.2f}")
        
        # 状态码分布
        if successful_results:
            status_codes = {}
            for result in successful_results:
                if result['success'] and 'result' in result:
                    status_code = result['result']['result']['status_code']
                    status_codes[status_code] = status_codes.get(status_code, 0) + 1
            print(f"   状态码分布: {status_codes}")
        
        if failed_results:
            print(f"\n❌ 失败的查询:")
            for failed in failed_results[:3]:
                print(f"   任务{failed['args'][0]}: {failed['error']}")
        
        # 验证结果
        assert success_rate >= 0.8  # 至少80%成功
        
        return {
            "qps": qps,
            "success_rate": success_rate,
            "total_time": total_time
        }
    
    @pytest.mark.benchmark
    def test_login_api_performance_final(self, fixed_performance_client, realistic_test_data, benchmark):
        """登录API最终性能测试（保留一个核心认证API测试）"""
        
        tenant_data = realistic_test_data["tenant_0"]
        tenant = tenant_data["tenant"]
        admin_user = tenant_data["admin_users"][0]
        
        login_data = {
            "email": admin_user.email,
            "password": "password123",
            "tenant_code": tenant.code
        }
        
        def login_request():
            response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
            assert response.status_code == 200
            data = response.json()
            assert "access_token" in data["data"]
            return data
        
        result = benchmark(login_request)
        print(f"登录API最终性能测试完成")
