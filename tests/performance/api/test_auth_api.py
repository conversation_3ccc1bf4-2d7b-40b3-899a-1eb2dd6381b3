"""
用户认证API性能测试

测试登录、注册等认证相关API的性能表现
"""

import pytest
import time
from fastapi.testclient import TestClient
from tests.performance.utils.performance_utils import PerformanceTimer, PerformanceAnalyzer, benchmark_function


class TestAuthAPIPerformance:
    """用户认证API性能测试"""
    
    @pytest.mark.benchmark
    def test_login_api_performance(self, performance_client: TestClient, performance_data_generator, benchmark):
        """测试登录API性能"""
        # 准备测试数据
        tenants = performance_data_generator.create_test_tenants(1)
        users = performance_data_generator.create_test_users(tenants[0].id, 1)

        login_data = {
            "email": users[0].email,
            "password": "password123",
            "tenant_code": tenants[0].code
        }

        # 使用pytest-benchmark进行性能测试
        def login_request():
            response = performance_client.post("/api/v1/auth/admin/login", json=login_data)
            assert response.status_code == 200
            return response.json()

        result = benchmark(login_request)

        # 验证响应内容
        assert "access_token" in result["data"]
        assert result["data"]["token_type"] == "bearer"
    
    @pytest.mark.benchmark
    def test_login_api_response_time(self, performance_client: TestClient, performance_data_generator, performance_thresholds):
        """测试登录API响应时间"""
        # 准备测试数据
        tenants = performance_data_generator.create_test_tenants(1)
        users = performance_data_generator.create_test_users(tenants[0].id, 1)

        login_data = {
            "email": users[0].email,
            "password": "password123",
            "tenant_code": tenants[0].code
        }

        response_times = []
        iterations = 50

        print(f"\n🚀 测试登录API响应时间 - {iterations}次迭代")

        for i in range(iterations):
            with PerformanceTimer() as timer:
                response = performance_client.post("/api/v1/auth/admin/login", json=login_data)
                assert response.status_code == 200

            response_times.append(timer.elapsed_time)

            if (i + 1) % 10 == 0:
                print(f"   进度: {i + 1}/{iterations}")

        # 分析性能指标
        metrics = PerformanceAnalyzer.analyze_response_times(response_times)
        PerformanceAnalyzer.print_performance_report(metrics, "登录API性能测试")

        # 验证性能阈值
        avg_response_time_ms = metrics.avg_time * 1000
        p95_response_time_ms = metrics.p95_time * 1000

        assert avg_response_time_ms < performance_thresholds["api_response_time_ms"], \
            f"平均响应时间过长: {avg_response_time_ms:.2f}ms > {performance_thresholds['api_response_time_ms']}ms"

        assert p95_response_time_ms < performance_thresholds["api_response_time_ms"] * 2, \
            f"95%分位响应时间过长: {p95_response_time_ms:.2f}ms"
    
    @pytest.mark.benchmark
    @pytest.mark.skip(reason="暂时跳过，需要实现对应的API端点")
    def test_token_validation_performance(self, performance_client: TestClient, performance_auth_token, benchmark):
        """测试令牌验证性能"""
        headers = {"Authorization": f"Bearer {performance_auth_token}"}

        def validate_token_request():
            response = performance_client.get("/api/v1/info/health", headers=headers)
            assert response.status_code == 200
            return response.json()

        result = benchmark(validate_token_request)

        # 验证响应内容
        assert "status" in result
    
    @pytest.mark.benchmark
    def test_multiple_login_performance(self, performance_client: TestClient, performance_data_generator):
        """测试多用户登录性能"""
        # 准备多个用户
        tenants = performance_data_generator.create_test_tenants(1)
        users = performance_data_generator.create_test_users(tenants[0].id, 10)
        
        response_times = []
        
        print(f"\n🚀 测试多用户登录性能 - {len(users)}个用户")
        
        for i, user in enumerate(users):
            login_data = {
                "email": user.email,
                "password": "password123",
                "tenant_code": tenants[0].code
            }

            with PerformanceTimer() as timer:
                response = performance_client.post("/api/v1/auth/admin/login", json=login_data)
                assert response.status_code == 200
            
            response_times.append(timer.elapsed_time)
            print(f"   用户{i+1}登录耗时: {timer.elapsed_time*1000:.2f}ms")
        
        # 分析性能指标
        metrics = PerformanceAnalyzer.analyze_response_times(response_times)
        PerformanceAnalyzer.print_performance_report(metrics, "多用户登录性能测试")
        
        # 验证所有登录都成功
        assert len(response_times) == len(users)
    
    @pytest.mark.benchmark
    @pytest.mark.slow_performance
    def test_login_stress_test(self, performance_client: TestClient, performance_data_generator):
        """登录压力测试"""
        # 准备测试数据
        tenants = performance_data_generator.create_test_tenants(1)
        users = performance_data_generator.create_test_users(tenants[0].id, 1)
        
        login_data = {
            "email": users[0].email,
            "password": "password123",
            "tenant_code": tenants[0].code
        }
        
        # 大量请求测试
        iterations = 200
        response_times = []
        failed_requests = 0
        
        print(f"\n🚀 登录压力测试 - {iterations}次请求")
        
        start_time = time.time()
        
        for i in range(iterations):
            try:
                with PerformanceTimer() as timer:
                    response = performance_client.post("/api/v1/auth/admin/login", json=login_data)
                    if response.status_code == 200:
                        response_times.append(timer.elapsed_time)
                    else:
                        failed_requests += 1
            except Exception as e:
                failed_requests += 1
                print(f"   请求{i+1}失败: {e}")
            
            if (i + 1) % 50 == 0:
                print(f"   进度: {i + 1}/{iterations}")
        
        total_time = time.time() - start_time
        
        # 分析结果
        success_rate = len(response_times) / iterations
        qps = iterations / total_time
        
        print(f"\n📊 压力测试结果:")
        print(f"   总请求数: {iterations}")
        print(f"   成功请求: {len(response_times)}")
        print(f"   失败请求: {failed_requests}")
        print(f"   成功率: {success_rate:.2%}")
        print(f"   QPS: {qps:.2f}")
        print(f"   总耗时: {total_time:.2f}秒")
        
        if response_times:
            metrics = PerformanceAnalyzer.analyze_response_times(response_times)
            PerformanceAnalyzer.print_performance_report(metrics, "登录压力测试")
        
        # 验证压力测试结果
        assert success_rate >= 0.95, f"成功率过低: {success_rate:.2%}"
        assert qps >= 10, f"QPS过低: {qps:.2f}"
    
    @pytest.mark.benchmark
    def test_invalid_login_performance(self, performance_client: TestClient, benchmark):
        """测试无效登录的性能（错误处理性能）"""
        invalid_login_data = {
            "email": "<EMAIL>",
            "password": "wrong_password",
            "tenant_code": "nonexistent_tenant"
        }

        def invalid_login_request():
            response = performance_client.post("/api/v1/auth/admin/login", json=invalid_login_data)
            assert response.status_code == 401
            return response.json()
        
        result = benchmark(invalid_login_request)
        
        # 验证错误响应
        assert "error" in result or "detail" in result
