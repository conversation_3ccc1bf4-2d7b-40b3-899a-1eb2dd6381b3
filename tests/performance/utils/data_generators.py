"""
性能测试数据生成器

提供大量测试数据生成功能，用于性能测试场景
"""

import random
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any


class PerformanceDataGenerator:
    """性能测试数据生成器"""
    
    def __init__(self):
        pass
        
    def generate_tenant_data(self, count: int = 100) -> List[Dict[str, Any]]:
        """生成租户测试数据"""
        tenants = []
        for i in range(count):
            tenant_data = {
                "name": f"性能测试机构{i+1}",
                "code": f"tenant_{i+1}_{int(time.time())}_{random.randint(1000, 9999)}",
                "email": f"tenant_{i+1}_{random.randint(1000, 9999)}@test.com",
                "phone": f"139{random.randint(10000000, 99999999)}",
                "address": f"测试地址{i+1}号"
            }
            tenants.append(tenant_data)
        return tenants
    
    def generate_user_data(self, tenant_id: int, count: int = 500) -> List[Dict[str, Any]]:
        """生成用户测试数据"""
        users = []
        for i in range(count):
            user_data = {
                "username": f"user_{tenant_id}_{i+1}_{random.randint(1000, 9999)}",
                "email": f"user_{tenant_id}_{i+1}_{random.randint(1000, 9999)}@test.com",
                "password": "password123",
                "real_name": f"测试用户{i+1}",
                "phone": f"138{random.randint(10000000, 99999999)}",
                "role": random.choice(["admin", "teacher", "staff"]),
                "tenant_id": tenant_id
            }
            users.append(user_data)
        return users
    
    def generate_member_data(self, tenant_id: int, count: int = 1000) -> List[Dict[str, Any]]:
        """生成会员测试数据"""
        members = []
        for i in range(count):
            member_data = {
                "name": f"测试会员{i+1}",
                "phone": f"137{random.randint(10000000, 99999999)}",
                "email": f"member_{tenant_id}_{i+1}_{random.randint(1000, 9999)}@test.com",
                "gender": random.choice(["male", "female"]),
                "member_type": random.choice(["regular", "vip", "premium"]),
                "tenant_id": tenant_id
            }
            members.append(member_data)
        return members
    
    def generate_teacher_data(self, tenant_id: int, count: int = 100) -> List[Dict[str, Any]]:
        """生成教师测试数据"""
        teachers = []
        specialties_options = [
            ["英语口语", "商务英语"],
            ["雅思", "托福"],
            ["少儿英语", "成人英语"],
            ["英语语法", "英语写作"],
            ["英语听力", "英语阅读"]
        ]
        
        for i in range(count):
            teacher_data = {
                "name": f"测试教师{i+1}",
                "email": f"teacher_{tenant_id}_{i+1}_{random.randint(1000, 9999)}@test.com",
                "phone": f"136{random.randint(10000000, 99999999)}",
                "specialties": random.choice(specialties_options),
                "introduction": f"资深英语教师，拥有{random.randint(3, 15)}年教学经验",
                "price_per_class": random.randint(100, 300),
                "teacher_category": random.choice(["european", "american", "chinese"]),
                "region": random.choice(["europe", "america", "asia"]),
                "tenant_id": tenant_id
            }
            teachers.append(teacher_data)
        return teachers
    
    def generate_course_data(self, teacher_ids: List[int], count: int = 1000) -> List[Dict[str, Any]]:
        """生成课程测试数据"""
        courses = []
        subjects = ["英语口语", "商务英语", "雅思", "托福", "少儿英语"]
        
        for i in range(count):
            # 生成未来的课程时间
            future_time = datetime.now() + timedelta(
                days=random.randint(1, 30),
                hours=random.randint(8, 20),
                minutes=random.choice([0, 30])
            )
            
            course_data = {
                "teacher_id": random.choice(teacher_ids),
                "class_datetime": future_time.strftime("%Y-%m-%dT%H:%M:%S"),
                "duration_minutes": random.choice([25, 50]),
                "price": random.randint(100, 300),
                "course_type": random.choice(["one_on_one", "small_group"]),
                "subject": random.choice(subjects),
                "description": f"课程描述 {i+1}"
            }
            courses.append(course_data)
        return courses
    
    def generate_booking_scenarios(self, member_ids: List[int], course_ids: List[int], count: int = 500) -> List[Dict[str, Any]]:
        """生成预约场景数据"""
        scenarios = []
        for i in range(count):
            scenario = {
                "member_id": random.choice(member_ids),
                "course_id": random.choice(course_ids),
                "scenario_type": random.choice(["normal", "concurrent", "peak_time"]),
                "expected_result": "success"
            }
            scenarios.append(scenario)
        return scenarios
    
    def generate_concurrent_test_data(self, base_data: Dict[str, Any], variations: int = 50) -> List[Dict[str, Any]]:
        """生成并发测试数据"""
        test_data = []
        for i in range(variations):
            data = base_data.copy()
            # 添加随机变化
            if "name" in data:
                data["name"] = f"{data['name']}_{i+1}"
            if "email" in data:
                data["email"] = f"concurrent_{i+1}_{data['email']}"
            if "phone" in data:
                data["phone"] = f"139{random.randint(10000000, 99999999)}"
            test_data.append(data)
        return test_data


class LargeDatasetGenerator:
    """大数据集生成器"""
    
    def __init__(self):
        self.data_generator = PerformanceDataGenerator()
    
    def generate_large_tenant_dataset(self, tenant_count: int = 100) -> Dict[str, List[Dict[str, Any]]]:
        """生成大型租户数据集"""
        print(f"🏗️ 生成大型数据集: {tenant_count}个租户...")
        
        dataset = {
            "tenants": [],
            "users": [],
            "members": [],
            "teachers": [],
            "courses": []
        }
        
        # 生成租户
        dataset["tenants"] = self.data_generator.generate_tenant_data(tenant_count)
        
        for i, tenant in enumerate(dataset["tenants"]):
            tenant_id = i + 1  # 假设租户ID从1开始
            
            # 每个租户生成用户、会员、教师
            users = self.data_generator.generate_user_data(tenant_id, count=10)
            members = self.data_generator.generate_member_data(tenant_id, count=50)
            teachers = self.data_generator.generate_teacher_data(tenant_id, count=5)
            
            dataset["users"].extend(users)
            dataset["members"].extend(members)
            dataset["teachers"].extend(teachers)
            
            # 为每个租户的教师生成课程
            teacher_ids = list(range(len(dataset["teachers"]) - 4, len(dataset["teachers"]) + 1))
            courses = self.data_generator.generate_course_data(teacher_ids, count=20)
            dataset["courses"].extend(courses)
            
            if (i + 1) % 10 == 0:
                print(f"   进度: {i + 1}/{tenant_count} 租户")
        
        print(f"✅ 数据集生成完成:")
        print(f"   租户: {len(dataset['tenants'])}")
        print(f"   用户: {len(dataset['users'])}")
        print(f"   会员: {len(dataset['members'])}")
        print(f"   教师: {len(dataset['teachers'])}")
        print(f"   课程: {len(dataset['courses'])}")
        
        return dataset


# 预定义的测试场景数据
PERFORMANCE_TEST_SCENARIOS = {
    "light_load": {
        "tenants": 10,
        "users_per_tenant": 5,
        "members_per_tenant": 20,
        "teachers_per_tenant": 3,
        "courses_per_teacher": 10
    },
    "medium_load": {
        "tenants": 50,
        "users_per_tenant": 10,
        "members_per_tenant": 50,
        "teachers_per_tenant": 5,
        "courses_per_teacher": 20
    },
    "heavy_load": {
        "tenants": 100,
        "users_per_tenant": 20,
        "members_per_tenant": 100,
        "teachers_per_tenant": 10,
        "courses_per_teacher": 50
    },
    "stress_test": {
        "tenants": 200,
        "users_per_tenant": 50,
        "members_per_tenant": 200,
        "teachers_per_tenant": 20,
        "courses_per_teacher": 100
    }
}
