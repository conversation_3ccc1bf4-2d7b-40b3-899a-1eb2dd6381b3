"""
性能测试工具类

提供性能测试的通用工具函数和装饰器
"""

import time
import threading
import statistics
import uuid
from typing import List, Dict, Any, Callable, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import psutil
import logging
from sqlmodel import Session

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    min_time: float
    max_time: float
    avg_time: float
    median_time: float
    p95_time: float
    p99_time: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    requests_per_second: float
    cpu_usage: float
    memory_usage: float


class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        
    def __enter__(self):
        self.start_time = time.perf_counter()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.perf_counter()
        
    @property
    def elapsed_time(self) -> float:
        """获取经过的时间（秒）"""
        if self.start_time is None or self.end_time is None:
            return 0.0
        return self.end_time - self.start_time


class ConcurrentTester:
    """并发测试器"""
    
    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.results: List[Dict[str, Any]] = []
        
    def run_concurrent_test(
        self,
        test_function: Callable,
        test_args_list: List[tuple],
        timeout: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """
        运行并发测试
        
        Args:
            test_function: 要测试的函数
            test_args_list: 测试参数列表，每个元素是一个参数元组
            timeout: 超时时间（秒）
            
        Returns:
            测试结果列表
        """
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_args = {
                executor.submit(self._run_single_test, test_function, args): args
                for args in test_args_list
            }
            
            # 收集结果
            for future in as_completed(future_to_args, timeout=timeout):
                args = future_to_args[future]
                try:
                    result = future.result()
                    results.append({
                        'args': args,
                        'success': True,
                        'result': result,
                        'error': None
                    })
                except Exception as e:
                    results.append({
                        'args': args,
                        'success': False,
                        'result': None,
                        'error': str(e)
                    })
                    
        return results
    
    def _run_single_test(self, test_function: Callable, args: tuple) -> Any:
        """运行单个测试"""
        with PerformanceTimer() as timer:
            result = test_function(*args)
        
        return {
            'result': result,
            'execution_time': timer.elapsed_time,
            'thread_id': threading.current_thread().ident
        }


class PerformanceAnalyzer:
    """性能分析器"""
    
    @staticmethod
    def analyze_response_times(response_times: List[float]) -> PerformanceMetrics:
        """
        分析响应时间数据
        
        Args:
            response_times: 响应时间列表（秒）
            
        Returns:
            性能指标
        """
        if not response_times:
            return PerformanceMetrics(
                min_time=0, max_time=0, avg_time=0, median_time=0,
                p95_time=0, p99_time=0, total_requests=0,
                successful_requests=0, failed_requests=0,
                requests_per_second=0, cpu_usage=0, memory_usage=0
            )
        
        sorted_times = sorted(response_times)
        total_requests = len(response_times)
        
        # 计算百分位数
        p95_index = int(0.95 * total_requests)
        p99_index = int(0.99 * total_requests)
        
        # 获取系统资源使用情况
        cpu_usage = psutil.cpu_percent()
        memory_usage = psutil.virtual_memory().percent
        
        return PerformanceMetrics(
            min_time=min(response_times),
            max_time=max(response_times),
            avg_time=statistics.mean(response_times),
            median_time=statistics.median(response_times),
            p95_time=sorted_times[p95_index] if p95_index < total_requests else sorted_times[-1],
            p99_time=sorted_times[p99_index] if p99_index < total_requests else sorted_times[-1],
            total_requests=total_requests,
            successful_requests=total_requests,  # 这里假设所有请求都成功，实际使用时需要传入成功/失败计数
            failed_requests=0,
            requests_per_second=total_requests / sum(response_times) if sum(response_times) > 0 else 0,
            cpu_usage=cpu_usage,
            memory_usage=memory_usage
        )
    
    @staticmethod
    def print_performance_report(metrics: PerformanceMetrics, test_name: str = "性能测试"):
        """打印性能报告"""
        print(f"\n{'='*60}")
        print(f"📊 {test_name} - 性能报告")
        print(f"{'='*60}")
        print(f"📈 响应时间统计:")
        print(f"   最小值: {metrics.min_time*1000:.2f}ms")
        print(f"   最大值: {metrics.max_time*1000:.2f}ms")
        print(f"   平均值: {metrics.avg_time*1000:.2f}ms")
        print(f"   中位数: {metrics.median_time*1000:.2f}ms")
        print(f"   95%分位: {metrics.p95_time*1000:.2f}ms")
        print(f"   99%分位: {metrics.p99_time*1000:.2f}ms")
        print(f"")
        print(f"📊 请求统计:")
        print(f"   总请求数: {metrics.total_requests}")
        print(f"   成功请求: {metrics.successful_requests}")
        print(f"   失败请求: {metrics.failed_requests}")
        print(f"   成功率: {(metrics.successful_requests/metrics.total_requests*100):.2f}%")
        print(f"   QPS: {metrics.requests_per_second:.2f}")
        print(f"")
        print(f"💻 系统资源:")
        print(f"   CPU使用率: {metrics.cpu_usage:.2f}%")
        print(f"   内存使用率: {metrics.memory_usage:.2f}%")
        print(f"{'='*60}")


def benchmark_function(iterations: int = 100):
    """
    性能基准测试装饰器
    
    Args:
        iterations: 测试迭代次数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            response_times = []
            
            print(f"\n🚀 开始性能基准测试: {func.__name__}")
            print(f"📊 测试迭代次数: {iterations}")
            
            for i in range(iterations):
                with PerformanceTimer() as timer:
                    result = func(*args, **kwargs)
                response_times.append(timer.elapsed_time)
                
                if (i + 1) % 10 == 0:
                    print(f"   进度: {i + 1}/{iterations}")
            
            # 分析结果
            metrics = PerformanceAnalyzer.analyze_response_times(response_times)
            PerformanceAnalyzer.print_performance_report(metrics, func.__name__)
            
            return result
            
        return wrapper
    return decorator


def memory_profile(func):
    """内存使用分析装饰器"""
    def wrapper(*args, **kwargs):
        import tracemalloc
        
        # 开始内存跟踪
        tracemalloc.start()
        
        try:
            result = func(*args, **kwargs)
        finally:
            # 获取内存使用情况
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc.stop()
            
            print(f"\n💾 {func.__name__} 内存使用情况:")
            print(f"   当前内存: {current / 1024 / 1024:.2f} MB")
            print(f"   峰值内存: {peak / 1024 / 1024:.2f} MB")
        
        return result
    return wrapper


class PerformanceDataGenerator:
    """性能测试数据生成器"""

    def __init__(self, session: Session):
        self.session = session

    def create_test_tenants(self, count: int = 10):
        """创建测试租户"""
        from app.features.tenants.models import Tenant, TenantStatus

        tenants = []
        # 使用UUID确保唯一性
        unique_suffix = str(uuid.uuid4())[:8]

        for i in range(count):
            tenant = Tenant(
                name=f"性能测试租户{i+1}",
                code=f"perf_tenant_{i+1}_{unique_suffix}_{i}",  # 添加索引确保唯一性
                contact_email=f"perf_tenant_{i+1}_{unique_suffix}@test.com",
                contact_phone=f"139{i+1:08d}",
                address=f"性能测试地址{i+1}",
                status=TenantStatus.ACTIVE,
                created_by=1
            )
            self.session.add(tenant)
            tenants.append(tenant)

        self.session.commit()
        # 刷新以获取ID
        for tenant in tenants:
            self.session.refresh(tenant)
        return tenants

    def create_test_users(self, tenant_id: int, count: int = 5):
        """创建测试用户"""
        from app.features.users.models import User, UserRole, UserStatus
        from app.utils.security import get_password_hash

        users = []
        unique_suffix = str(uuid.uuid4())[:8]

        for i in range(count):
            user = User(
                username=f"perf_user_{i+1}_{unique_suffix}",
                email=f"perf_user_{i+1}_{unique_suffix}@test.com",
                password_hash=get_password_hash("password123"),
                real_name=f"性能测试用户{i+1}",
                phone=f"138{i+1:08d}",
                role=UserRole.ADMIN,
                status=UserStatus.ACTIVE,
                tenant_id=tenant_id,
                created_by=1
            )
            self.session.add(user)
            users.append(user)

        self.session.commit()
        for user in users:
            self.session.refresh(user)
        return users

    def create_test_members(self, tenant_id: int, count: int = 10):
        """创建测试会员"""
        from app.features.members.models import Member, MemberStatus, Gender

        members = []
        unique_suffix = str(uuid.uuid4())[:8]

        for i in range(count):
            member = Member(
                name=f"性能测试会员{i+1}",
                phone=f"137{i+1:08d}",
                email=f"perf_member_{i+1}_{unique_suffix}@test.com",
                gender=Gender.MALE if i % 2 == 0 else Gender.FEMALE,
                status=MemberStatus.ACTIVE,
                tenant_id=tenant_id,
                created_by=1
            )
            self.session.add(member)
            members.append(member)

        self.session.commit()
        for member in members:
            self.session.refresh(member)
        return members

    def create_test_teachers(self, tenant_id: int, count: int = 5):
        """创建测试教师"""
        from app.features.teachers.models import Teacher, TeacherStatus, TeacherCategory, TeacherRegion, Gender

        teachers = []
        unique_suffix = str(uuid.uuid4())[:8]

        # 教师分类和区域的循环列表
        categories = [TeacherCategory.EUROPEAN, TeacherCategory.FILIPINO, TeacherCategory.CHINESE]
        regions = [TeacherRegion.EUROPE, TeacherRegion.PHILIPPINES, TeacherRegion.CHINA]

        for i in range(count):
            teacher = Teacher(
                name=f"性能测试教师{i+1}",
                phone=f"136{i+1:08d}",
                email=f"perf_teacher_{i+1}_{unique_suffix}@test.com",
                gender=Gender.MALE if i % 2 == 0 else Gender.FEMALE,
                teacher_category=categories[i % len(categories)],  # 添加必填字段
                region=regions[i % len(regions)],  # 添加必填字段
                status=TeacherStatus.ACTIVE,
                tenant_id=tenant_id,
                created_by=1
            )
            self.session.add(teacher)
            teachers.append(teacher)

        self.session.commit()
        for teacher in teachers:
            self.session.refresh(teacher)
        return teachers
