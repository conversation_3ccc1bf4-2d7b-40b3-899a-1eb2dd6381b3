"""
性能测试fixtures

提供稳定的性能测试基础设施，支持API和数据库性能测试
"""

import pytest
import os
import time
from sqlmodel import Session, create_engine, text
from fastapi.testclient import TestClient
from tests.performance.utils.performance_utils import PerformanceDataGenerator, ConcurrentTester


@pytest.fixture(scope="session")
def fixed_performance_engine():
    """性能测试数据库引擎"""
    # 使用与主应用相同的数据库配置，确保一致性
    from app.core.config import settings

    # 解析DATABASE_URL
    import re
    db_url = settings.DATABASE_URL
    match = re.match(r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
    if not match:
        raise ValueError(f"无法解析DATABASE_URL: {db_url}")

    user, password, host, port, db_name = match.groups()

    # 创建性能测试专用数据库
    test_db_name = f"{db_name}_perf_test"

    # 连接到默认数据库创建测试数据库
    admin_url = f"postgresql://{user}:{password}@{host}:{port}/postgres"
    admin_engine = create_engine(admin_url)

    with admin_engine.connect() as conn:
        conn.execute(text("COMMIT"))  # 结束当前事务
        try:
            conn.execute(text(f"DROP DATABASE IF EXISTS {test_db_name}"))
            conn.execute(text(f"CREATE DATABASE {test_db_name}"))
        except Exception as e:
            print(f"数据库创建警告: {e}")

    # 创建测试数据库引擎
    test_url = f"postgresql://{user}:{password}@{host}:{port}/{test_db_name}"
    engine = create_engine(test_url, echo=False)
    
    # 创建表结构
    from sqlmodel import SQLModel
    # 导入所有模型以确保表被创建
    import app.models
    SQLModel.metadata.create_all(engine)
    
    yield engine
    
    # 清理
    engine.dispose()
    with admin_engine.connect() as conn:
        conn.execute(text("COMMIT"))
        try:
            conn.execute(text(f"DROP DATABASE IF EXISTS {test_db_name}"))
        except Exception as e:
            print(f"数据库清理警告: {e}")
    admin_engine.dispose()


@pytest.fixture
def fixed_performance_session(fixed_performance_engine):
    """性能测试数据库会话"""
    with Session(fixed_performance_engine) as session:
        # 设置RLS上下文
        try:
            session.exec(text("SET app.current_tenant_id = '1'"))
        except Exception:
            pass  # 如果RLS未启用，忽略错误
        
        yield session
        
        # 清理会话
        session.rollback()


@pytest.fixture
def fixed_performance_client(fixed_performance_engine):
    """性能测试API客户端"""
    # 临时替换应用的数据库引擎
    from app.db.session import engine as original_engine
    from app.db import session as db_session

    # 保存原始引擎
    original = db_session.engine

    try:
        # 替换为测试引擎
        db_session.engine = fixed_performance_engine

        # 重新导入应用以使用新的数据库引擎
        from app.main import app

        # 创建测试客户端
        client = TestClient(app)

        yield client

    finally:
        # 恢复原始引擎
        db_session.engine = original


@pytest.fixture
def performance_data_generator(fixed_performance_session):
    """性能测试数据生成器"""
    return PerformanceDataGenerator(fixed_performance_session)


@pytest.fixture
def realistic_test_data(performance_data_generator):
    """创建真实的测试数据集"""
    
    # 创建测试租户
    tenants = performance_data_generator.create_test_tenants(3)
    
    # 为每个租户创建用户和其他数据
    test_data = {}
    
    for i, tenant in enumerate(tenants):
        # 创建管理员用户
        admin_users = performance_data_generator.create_test_users(tenant.id, 2)
        
        # 创建会员
        members = performance_data_generator.create_test_members(tenant.id, 10)
        
        # 创建教师
        teachers = performance_data_generator.create_test_teachers(tenant.id, 5)
        
        test_data[f"tenant_{i}"] = {
            "tenant": tenant,
            "admin_users": admin_users,
            "members": members,
            "teachers": teachers
        }
    
    return test_data


@pytest.fixture
def authenticated_client(fixed_performance_client, realistic_test_data):
    """已认证的API客户端"""
    
    # 获取第一个租户的管理员用户
    tenant_data = realistic_test_data["tenant_0"]
    tenant = tenant_data["tenant"]
    admin_user = tenant_data["admin_users"][0]
    
    # 执行登录
    login_data = {
        "email": admin_user.email,
        "password": "password123",
        "tenant_code": tenant.code
    }
    
    response = fixed_performance_client.post("/api/v1/auth/admin/login", json=login_data)
    
    if response.status_code != 200:
        pytest.fail(f"登录失败: {response.status_code} - {response.text}")
    
    token_data = response.json()
    access_token = token_data["data"]["access_token"]
    
    # 设置认证头
    fixed_performance_client.headers.update({
        "Authorization": f"Bearer {access_token}"
    })
    
    return {
        "client": fixed_performance_client,
        "token": access_token,
        "tenant": tenant,
        "user": admin_user,
        "test_data": realistic_test_data
    }


@pytest.fixture
def concurrent_tester():
    """并发测试器"""
    return ConcurrentTester()


@pytest.fixture
def performance_thresholds():
    """性能阈值配置"""
    return {
        "api_response_time_ms": 200,
        "database_query_time_ms": 100,
        "concurrent_success_rate": 0.95,
        "min_qps": 100
    }


class ThreadSafeDataGenerator:
    """线程安全的测试数据生成器，用于并发测试"""
    
    def __init__(self, engine):
        """初始化线程安全的数据生成器
        
        Args:
            engine: SQLAlchemy引擎
        """
        self.engine = engine
        self._id_counter = 0
    
    def get_unique_suffix(self):
        """生成唯一后缀，用于创建唯一实体"""
        import time
        import threading
        timestamp = int(time.time() * 1000)
        thread_id = threading.get_ident() % 10000
        self._id_counter += 1
        return f"{timestamp}_{thread_id}_{self._id_counter}"
    
    def create_test_tenant(self):
        """创建线程安全的测试租户"""
        from app.features.tenants.models import Tenant
        
        with Session(self.engine) as session:
            suffix = self.get_unique_suffix()
            tenant = Tenant(
                name=f"Test Tenant {suffix}",
                code=f"TT{suffix}",
                status="ACTIVE"
            )
            session.add(tenant)
            session.commit()
            session.refresh(tenant)
            return tenant
    
    def create_test_user(self, tenant_id: int):
        """创建线程安全的测试用户"""
        from app.features.users.models import User
        from app.utils.security import get_password_hash
        
        with Session(self.engine) as session:
            suffix = self.get_unique_suffix()
            user = User(
                tenant_id=tenant_id,
                email=f"user{suffix}@example.com",
                username=f"user{suffix}",
                full_name=f"Test User {suffix}",
                hashed_password=get_password_hash("password123"),
                role="ADMIN",
                status="ACTIVE"
            )
            session.add(user)
            session.commit()
            session.refresh(user)
            return user


@pytest.fixture
def thread_safe_data_generator(fixed_performance_engine):
    """创建线程安全的测试数据生成器"""
    return ThreadSafeDataGenerator(fixed_performance_engine)


@pytest.fixture
def performance_test_config():
    """性能测试配置"""
    return {
        "small": {
            "tenants": 1,
            "users_per_tenant": 5,
            "members_per_tenant": 10,
            "teachers_per_tenant": 5
        },
        "medium": {
            "tenants": 2,
            "users_per_tenant": 10,
            "members_per_tenant": 50,
            "teachers_per_tenant": 15
        },
        "large": {
            "tenants": 5,
            "users_per_tenant": 20,
            "members_per_tenant": 200,
            "teachers_per_tenant": 50
        }
    } 