"""
性能测试模块的配置文件
"""

import pytest
import os
from sqlmodel import create_engine, Session
from tests.performance.utils.benchmark_fixtures import (
    fixed_performance_engine, 
    fixed_performance_session, 
    fixed_performance_client,
    performance_data_generator,
    realistic_test_data,
    authenticated_client,
    thread_safe_data_generator,
    performance_thresholds
)

# 重新导出fixtures让它们在整个性能测试模块中可用
__all__ = [
    'fixed_performance_engine',
    'fixed_performance_session',
    'fixed_performance_client',
    'performance_data_generator',
    'realistic_test_data',
    'authenticated_client',
    'thread_safe_data_generator',
    'performance_thresholds'
]


def pytest_addoption(parser):
    """添加性能测试专用命令行选项"""
    parser.addoption(
        "--benchmark-iterations",
        action="store",
        default=100,
        type=int,
        help="性能基准测试的迭代次数"
    )
    parser.addoption(
        "--concurrent-users",
        action="store",
        default=10,
        type=int,
        help="并发测试的用户数量"
    )
    parser.addoption(
        "--performance-timeout",
        action="store",
        default=60,
        type=int,
        help="性能测试的超时时间（秒）"
    )
    parser.addoption(
        "--skip-slow-performance",
        action="store_true",
        default=False,
        help="跳过慢速性能测试"
    )
    parser.addoption(
        "--performance-report",
        action="store_true",
        default=False,
        help="生成详细的性能测试报告"
    )


def pytest_configure(config):
    """性能测试配置"""
    # 设置性能测试环境变量
    os.environ["PERFORMANCE_TESTING"] = "true"
    os.environ["TESTING"] = "true"
    
    # 添加性能测试标记
    config.addinivalue_line("markers", "slow_performance: 慢速性能测试")
    config.addinivalue_line("markers", "memory_intensive: 内存密集型测试")
    config.addinivalue_line("markers", "cpu_intensive: CPU密集型测试")


def pytest_collection_modifyitems(config, items):
    """修改性能测试收集项"""
    skip_slow = config.getoption("--skip-slow-performance")
    
    for item in items:
        # 跳过慢速性能测试
        if skip_slow and "slow_performance" in item.keywords:
            item.add_marker(pytest.mark.skip(reason="跳过慢速性能测试"))


@pytest.fixture(scope="session")
def performance_config(request):
    """性能测试配置fixture"""
    return {
        "benchmark_iterations": request.config.getoption("--benchmark-iterations"),
        "concurrent_users": request.config.getoption("--concurrent-users"),
        "performance_timeout": request.config.getoption("--performance-timeout"),
        "skip_slow_performance": request.config.getoption("--skip-slow-performance"),
        "performance_report": request.config.getoption("--performance-report"),
    }


@pytest.fixture(autouse=True)
def performance_test_setup(request):
    """性能测试自动设置"""
    # 在每个性能测试前的设置
    test_name = request.node.name
    
    # 设置测试特定的环境变量
    if "concurrent" in test_name.lower():
        os.environ["MAX_CONCURRENT_CONNECTIONS"] = "100"
    elif "benchmark" in test_name.lower():
        os.environ["BENCHMARK_MODE"] = "true"
    
    yield
    
    # 清理环境变量
    for env_var in ["MAX_CONCURRENT_CONNECTIONS", "BENCHMARK_MODE"]:
        if env_var in os.environ:
            del os.environ[env_var]


# 性能测试常量
PERFORMANCE_THRESHOLDS = {
    "api_response_time_ms": 200,  # API响应时间阈值（毫秒）
    "database_query_time_ms": 100,  # 数据库查询时间阈值（毫秒）
    "concurrent_success_rate": 0.95,  # 并发测试成功率阈值
    "memory_usage_mb": 500,  # 内存使用阈值（MB）
    "cpu_usage_percent": 80,  # CPU使用率阈值（%）
    "min_qps": 20,  # 最小QPS阈值
}


@pytest.fixture
def performance_thresholds():
    """性能阈值fixture"""
    return PERFORMANCE_THRESHOLDS.copy()
