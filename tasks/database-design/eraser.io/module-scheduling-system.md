# 课程排课模块 (Scheduling System)

## 概述

固定时间占位、课程安排、锁定管理、自动排课系统。

## eraser.io 代码

```
// === 课程排课模块 ===

course_system_configs [icon: settings] {
  id int pk
  tenant_id int
  default_slot_duration_minutes int
  auto_schedule_enabled boolean
  auto_schedule_day int
  created_by int
}

teacher_fixed_slots [icon: calendar] {
  id int pk
  tenant_id int
  teacher_id int
  weekday int
  start_time time
  is_available boolean
}

member_fixed_slot_locks [icon: lock] {
  id int pk
  tenant_id int
  member_id int
  teacher_fixed_slot_id int
  status enum
}

scheduled_classes [icon: clock] {
  id int pk
  tenant_id int
  teacher_id int
  member_id int
  class_datetime timestamp
  class_type enum
  price int
  member_card_id int
  status enum
}

schedule_tasks [icon: tasks] {
  id int pk
  tenant_id int
  task_name string
  task_type enum
  status enum
  total_classes int
  created_by int
}

schedule_logs [icon: file-text] {
  id int pk
  tenant_id int
  task_id int
  log_level string
  teacher_id int
  member_id int
}

// === 关系 ===
course_system_configs.tenant_id > tenants.id
course_system_configs.created_by > users.id
teacher_fixed_slots.tenant_id > tenants.id
teacher_fixed_slots.teacher_id > teachers.id
teacher_fixed_slots.created_by > users.id
member_fixed_slot_locks.tenant_id > tenants.id
member_fixed_slot_locks.member_id > members.id
member_fixed_slot_locks.teacher_fixed_slot_id > teacher_fixed_slots.id
member_fixed_slot_locks.created_by > users.id
scheduled_classes.tenant_id > tenants.id
scheduled_classes.teacher_id > teachers.id
scheduled_classes.member_id > members.id
scheduled_classes.member_card_id > member_cards.id
scheduled_classes.created_by > users.id
schedule_tasks.tenant_id > tenants.id
schedule_tasks.created_by > users.id
schedule_logs.tenant_id > tenants.id
schedule_logs.task_id > schedule_tasks.id
schedule_logs.teacher_id > teachers.id
schedule_logs.member_id > members.id

scheduled_class_operation_logs [icon: clipboard] {
  id int pk
  tenant_id int
  scheduled_class_id int
  member_id int
  teacher_id int
  operation_type enum
  operated_by int
  operator_type enum
}

teacher_fixed_slot_operation_logs [icon: edit] {
  id int pk
  tenant_id int
  teacher_fixed_slot_id int
  teacher_id int
  operation_type enum
  operated_by int
  operator_type enum
}

member_fixed_lock_operation_logs [icon: user-check] {
  id int pk
  tenant_id int
  member_fixed_slot_lock_id int
  teacher_fixed_slot_id int
  member_id int
  teacher_id int
  operation_type enum
  operated_by int
  operator_type enum
}

// === 操作记录关系 ===
scheduled_class_operation_logs.tenant_id > tenants.id
scheduled_class_operation_logs.scheduled_class_id > scheduled_classes.id
scheduled_class_operation_logs.member_id > members.id
scheduled_class_operation_logs.teacher_id > teachers.id
scheduled_class_operation_logs.operated_by > users.id

teacher_fixed_slot_operation_logs.tenant_id > tenants.id
teacher_fixed_slot_operation_logs.teacher_fixed_slot_id > teacher_fixed_slots.id
teacher_fixed_slot_operation_logs.teacher_id > teachers.id
teacher_fixed_slot_operation_logs.operated_by > users.id

member_fixed_lock_operation_logs.tenant_id > tenants.id
member_fixed_lock_operation_logs.member_fixed_slot_lock_id > member_fixed_slot_locks.id
member_fixed_lock_operation_logs.teacher_fixed_slot_id > teacher_fixed_slots.id
member_fixed_lock_operation_logs.member_id > members.id
member_fixed_lock_operation_logs.teacher_id > teachers.id
member_fixed_lock_operation_logs.operated_by > users.id
```

## 字段说明

### teacher_fixed_slots 表

- **id**: 主键
- **tenant_id**: 租户 ID
- **teacher_id**: 教师 ID
- **weekday**: 星期几（1-7，1 为星期一）
- **time_slot**: 时间段（HH:MM 格式，30 分钟间隔）
- **is_available**: 是否可用

### scheduled_classes 表

- **id**: 主键
- **tenant_id**: 租户 ID
- **teacher_id**: 教师 ID
- **member_id**: 会员 ID
- **course_template_id**: 课程模板 ID
- **class_datetime**: 课程时间（精确到分钟）
- **class_type**: 课程类型
  - `fixed`: 固定课程
  - `direct`: 直接约课课程
- **status**: 课程状态
  - `scheduled`: 已排课
  - `completed`: 已完成
  - `cancelled`: 已取消
  - `no_show`: 缺席

### member_fixed_slot_locks 表

- **id**: 主键
- **tenant_id**: 租户 ID
- **member_id**: 会员 ID
- **teacher_id**: 教师 ID
- **weekday**: 星期几（1-7）
- **time_slot**: 时间段（HH:MM）
- **status**: 锁定状态
  - `active`: 激活
  - `paused`: 暂停
  - `cancelled`: 取消

### course_templates 表

- **id**: 主键
- **tenant_id**: 租户 ID
- **name**: 课程名称
- **duration_minutes**: 课程时长（分钟）
- **price**: 课程价格
- **category**: 课程分类
- **level**: 课程级别
- **is_active**: 是否激活
- **created_by**: 创建者 ID

## 业务规则

### 固定时间占位

- 教师设置周一到周日的固定时间表
- 时间段以 30 分钟为单位（00:00-23:30）
- 每个时间段可开放/关闭

### 固定课位锁定

- 会员可锁定教师的固定时间段
- 锁定后自动生成固定课程
- 支持暂停和取消锁定

### 自动排课

- 每月 22 日下午 14:00 自动执行
- 按教师优先级排课（欧美/南非 > 菲律宾）
- 生成未来 4 周的课程安排

### 课程管理

- 标准课程时长 25 分钟，休息间隔 5 分钟
- 支持固定课和临时课
- 课程状态全生命周期管理

## 扩展字段（完整版本包含）

- **scheduled_classes**: price, booking_note, is_cancellable
- **course_templates**: description, objectives, materials, applicable_ages

## 索引设计

- `teacher_fixed_slots(teacher_id, weekday)` - 教师时间表
- `teacher_fixed_slots(tenant_id, is_available)` - 可用时间段
- `scheduled_classes(tenant_id, class_datetime)` - 课程时间查询
- `scheduled_classes(teacher_id, class_datetime)` - 教师课程
- `scheduled_classes(member_id, class_datetime)` - 会员课程
- `member_fixed_slot_locks(member_id, status)` - 会员锁定
- `member_fixed_slot_locks(teacher_id, weekday)` - 教师锁定
