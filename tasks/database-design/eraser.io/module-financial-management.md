# 财务管理模块 (Financial Management)

## 概述

会员卡管理、充值记录、消费记录、财务统计模块。

## eraser.io 代码

```
// === 财务管理模块 ===

member_cards [icon: wallet] {
  id int pk
  tenant_id int
  member_id int
  template_id int
  card_type enum
  balance int
  status enum
}

member_card_templates [icon: credit-card] {
  id int pk
  tenant_id int
  name string
  card_type enum
  sale_price int
  created_by int
}

recharge_records [icon: plus-circle] {
  id int pk
  tenant_id int
  member_id int
  member_card_id int
  amount int
  payment_method enum
  operated_by int
}

consumption_records [icon: minus-circle] {
  id int pk
  tenant_id int
  member_id int
  member_card_id int
  scheduled_class_id int
  amount int
  consumption_type enum
}

// === 关系 ===
member_cards.tenant_id > tenants.id
member_cards.member_id > members.id
member_cards.template_id > member_card_templates.id
member_card_templates.tenant_id > tenants.id
member_card_templates.created_by > users.id
recharge_records.tenant_id > tenants.id
recharge_records.member_id > members.id
recharge_records.member_card_id > member_cards.id
recharge_records.operated_by > users.id
consumption_records.tenant_id > tenants.id
consumption_records.member_id > members.id
consumption_records.member_card_id > member_cards.id
consumption_records.scheduled_class_id > scheduled_classes.id
```

## 字段说明

### member_card_templates 表

- **id**: 主键
- **tenant_id**: 租户 ID
- **name**: 卡片模板名称
- **card_type**: 卡片类型
  - `times_limited`: 次卡-有期限
  - `times_unlimited`: 次卡-无期限
  - `value_limited`: 储值卡-有期限
  - `value_unlimited`: 储值卡-无期限
- **sale_price**: 售卖价格
- **created_by**: 创建者 ID

### member_cards 表

- **id**: 主键
- **tenant_id**: 租户 ID
- **member_id**: 会员 ID
- **template_id**: 模板 ID
- **card_type**: 卡片类型
- **balance**: 当前余额
- **status**: 卡片状态
  - `active`: 激活
  - `frozen`: 冻结
  - `expired`: 过期
  - `cancelled`: 注销

### recharge_records 表

- **id**: 主键
- **tenant_id**: 租户 ID
- **member_id**: 会员 ID
- **member_card_id**: 会员卡 ID
- **amount**: 充值金额
- **operated_by**: 操作人 ID

### consumption_records 表

- **id**: 主键
- **tenant_id**: 租户 ID
- **member_id**: 会员 ID
- **member_card_id**: 会员卡 ID
- **scheduled_class_id**: 课程 ID
- **amount**: 消费金额

## 业务规则

### 会员卡模板

- 管理员创建卡片模板
- 设置售价、可用余额、有效期
- 支持线上购买、代理专售等配置

### 会员卡实例

- 新会员默认生成储值卡（余额 0，有效期 30 天）
- 每个会员仅有一个储值卡，可有多个次卡
- 支持卡片状态管理

### 充值管理

- 支持多种充值方式（微信支付、手动充值）
- 支持充值奖励和赠送金额
- 记录操作人和充值原因

### 消费管理

- 课程消费自动扣费
- 支持多种消费类型（课程费、材料费等）
- 消费记录与课程记录关联

### 财务统计

- 实时计算会员卡余额
- 统计充值和消费数据
- 支持财务报表生成

## 扩展字段（完整版本包含）

- **member_card_templates**: available_balance, validity_days, is_agent_exclusive
- **member_cards**: card_number, total_recharged, total_consumed, expires_at
- **recharge_records**: bonus_amount, payment_method, payment_status, transaction_id, notes
- **consumption_records**: consumption_type, description, status

## 索引设计

- `member_cards(tenant_id, member_id)` - 会员卡查询
- `member_cards(tenant_id, status)` - 状态筛选
- `recharge_records(tenant_id, member_id)` - 充值记录
- `recharge_records(operated_by)` - 操作人记录
- `consumption_records(tenant_id, member_id)` - 消费记录
- `consumption_records(scheduled_class_id)` - 课程消费
