# 核心关系图 - Markdown 版本

> 对应文件: `../core-relationships.md`

## 核心实体表

| 表名                  | 图标 | 说明     | 主要字段数 |
| --------------------- | ---- | -------- | ---------- |
| **tenants**           | 🏢   | 租户表   | 5          |
| **users**             | 👤   | 用户表   | 6          |
| **members**           | 👥   | 会员表   | 7          |
| **teachers**          | 🎓   | 教师表   | 6          |
| **scheduled_classes** | 🕐   | 已排课表 | 6          |
| **member_cards**      | 💳   | 会员卡表 | 5          |

## 详细字段定义

### tenants (租户表)

| 字段名     | 类型      | 约束             | 说明     |
| ---------- | --------- | ---------------- | -------- |
| id         | int       | PK               | 主键     |
| name       | string    | NOT NULL         | 租户名称 |
| code       | string    | UNIQUE, NOT NULL | 租户代码 |
| status     | enum      | NOT NULL         | 租户状态 |
| created_at | timestamp | NOT NULL         | 创建时间 |

**status** (租户状态):

- `trial`: 试用
- `active`: 激活
- `suspended`: 暂停
- `cancelled`: 注销

### users (用户表)

| 字段名     | 类型   | 约束             | 说明                          |
| ---------- | ------ | ---------------- | ----------------------------- |
| id         | int    | PK               | 主键                          |
| tenant_id  | int    | FK               | 租户 ID (super_admin 为 null) |
| username   | string | UNIQUE, NOT NULL | 用户名                        |
| role       | enum   | NOT NULL         | 用户角色                      |
| status     | enum   | NOT NULL         | 用户状态                      |
| created_by | int    | FK               | 创建者 ID                     |

**role** (用户角色):

- `super_admin`: 超级管理员
- `admin`: 租户管理员
- `agent`: 代理人员
- `sale`: 销售人员

**status** (用户状态):

- `active`: 激活
- `inactive`: 停用
- `locked`: 锁定

### members (会员表)

| 字段名        | 类型   | 约束         | 说明        |
| ------------- | ------ | ------------ | ----------- |
| id            | int    | PK           | 主键        |
| tenant_id     | int    | FK, NOT NULL | 租户 ID     |
| name          | string | NOT NULL     | 会员姓名    |
| phone         | string | NOT NULL     | 手机号      |
| member_status | enum   | NOT NULL     | 会员状态    |
| sales_id      | int    | FK           | 销售人员 ID |
| agent_id      | int    | FK           | 代理人员 ID |
| created_by    | int    | FK           | 创建者 ID   |

**member_status** (会员状态):

- `active`: 活跃
- `silent`: 沉默
- `frozen`: 冻结
- `cancelled`: 注销

### teachers (教师表)

| 字段名           | 类型   | 约束         | 说明           |
| ---------------- | ------ | ------------ | -------------- |
| id               | int    | PK           | 主键           |
| tenant_id        | int    | FK, NOT NULL | 租户 ID        |
| name             | string | NOT NULL     | 教师姓名       |
| teacher_category | enum   | NOT NULL     | 教师分类       |
| price_per_class  | int    | DEFAULT 0    | 单节课价格(元) |
| status           | enum   | NOT NULL     | 教师状态       |
| created_by       | int    | FK           | 创建者 ID      |

**teacher_category** (教师分类):

- `european`: 欧美教师
- `south_african`: 南非教师
- `filipino`: 菲律宾教师
- `chinese`: 中教
- `other`: 其他

**status** (教师状态):

- `pending`: 待审核
- `active`: 激活
- `inactive`: 停用
- `on_leave`: 请假

### scheduled_classes (已排课表)

| 字段名               | 类型      | 约束         | 说明              |
| -------------------- | --------- | ------------ | ----------------- |
| id                   | int       | PK           | 主键              |
| tenant_id            | int       | FK, NOT NULL | 租户 ID           |
| teacher_id           | int       | FK, NOT NULL | 教师 ID           |
| member_id            | int       | FK           | 会员 ID（可为空） |
| class_datetime       | timestamp | NOT NULL     | 课程时间          |
| class_type           | enum      | NOT NULL     | 课程类型          |
| price                | int       | NOT NULL     | 课程价格（元）    |
| member_card_id       | int       | FK           | 会员卡 ID         |
| status               | enum      | NOT NULL     | 课程状态          |
| is_visible_to_member | boolean   | NOT NULL     | 是否对会员可见    |

**class_type** (课程类型):

- `fixed`: 固定课程
- `direct`: 直接约课

**status** (课程状态):

- `available`: 可预约（空课）
- `booked`: 已预约
- `completed`: 已完成
- `teacher_no_show`: 教师缺席
- `member_no_show`: 会员缺席

### member_cards (会员卡表)

| 字段名    | 类型 | 约束         | 说明     |
| --------- | ---- | ------------ | -------- |
| id        | int  | PK           | 主键     |
| tenant_id | int  | FK, NOT NULL | 租户 ID  |
| member_id | int  | FK, NOT NULL | 会员 ID  |
| card_type | enum | NOT NULL     | 卡片类型 |
| balance   | int  | DEFAULT 0    | 卡片余额 |
| status    | enum | NOT NULL     | 卡片状态 |

**card_type** (卡片类型):

- `times_limited`: 次卡-有期限
- `times_unlimited`: 次卡-无期限
- `value_limited`: 储值卡-有期限
- `value_unlimited`: 储值卡-无期限

**status** (卡片状态):

- `active`: 激活
- `frozen`: 冻结
- `expired`: 过期
- `cancelled`: 注销

## 关系定义

### 主要关系

- `users.tenant_id` → `tenants.id` (N:1) - 用户归属租户
- `users.created_by` → `users.id` (N:1) - 用户创建者
- `members.tenant_id` → `tenants.id` (N:1) - 会员归属租户
- `members.sales_id` → `users.id` (N:1) - 会员销售人员
- `members.agent_id` → `users.id` (N:1) - 会员代理人员
- `members.created_by` → `users.id` (N:1) - 会员创建者
- `teachers.tenant_id` → `tenants.id` (N:1) - 教师归属租户
- `teachers.created_by` → `users.id` (N:1) - 教师创建者
- `scheduled_classes.tenant_id` → `tenants.id` (N:1) - 课程归属租户
- `scheduled_classes.teacher_id` → `teachers.id` (N:1) - 课程教师
- `scheduled_classes.member_id` → `members.id` (N:1) - 课程会员
- `scheduled_classes.member_card_id` → `member_cards.id` (N:1) - 课程会员卡
- `member_cards.tenant_id` → `tenants.id` (N:1) - 会员卡归属租户
- `member_cards.member_id` → `members.id` (N:1) - 会员卡归属会员

### 关系图 (ASCII)

```
        tenants (租户)
           │
    ┌──────┼──────┐
    │      │      │
  users  members teachers
    │      │      │
    └──────┼──────┘
           │
    scheduled_classes
           │
      member_cards
```
