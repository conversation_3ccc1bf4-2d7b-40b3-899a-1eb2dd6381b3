# 全局管理模块 - Markdown 版本

> 对应文件: `../module-global-management.md`

## 模块概述

租户管理、系统配置、使用统计、系统管理员模块。

## 表结构定义

### tenants (租户表)

| 字段名        | 类型      | 约束             | 默认值            | 说明         |
| ------------- | --------- | ---------------- | ----------------- | ------------ |
| id            | int       | PK               | AUTO_INCREMENT    | 主键         |
| name          | string    | NOT NULL         | -                 | 机构名称     |
| code          | string    | UNIQUE, NOT NULL | -                 | 机构代码     |
| status        | enum      | NOT NULL         | 'trial'           | 租户状态     |
| plan_type     | enum      | NOT NULL         | 'trial'           | 套餐类型     |
| max_teachers  | int       | NOT NULL         | 5                 | 最大教师数量 |
| max_members   | int       | NOT NULL         | 50                | 最大会员数量 |
| total_users   | int       | NOT NULL         | 0                 | 总用户数     |
| total_members | int       | NOT NULL         | 0                 | 总会员数     |
| total_classes | int       | NOT NULL         | 0                 | 总课程数     |
| total_revenue | int       | NOT NULL         | 0.00              | 总收入       |
| created_at    | timestamp | NOT NULL         | CURRENT_TIMESTAMP | 创建时间     |
| created_by    | string    | -                | NULL              | 创建者       |

**枚举定义**:

**status** (租户状态):

- `trial`: 试用
- `active`: 激活
- `suspended`: 暂停
- `cancelled`: 注销

**plan_type** (套餐类型):

- `trial`: 试用版
- `basic`: 基础版
- `standard`: 标准版
- `premium`: 高级版

### tenant_plan_templates (租户套餐模板表)

| 字段名         | 类型    | 约束             | 默认值         | 说明             |
| -------------- | ------- | ---------------- | -------------- | ---------------- |
| id             | int     | PK               | AUTO_INCREMENT | 主键             |
| plan_code      | string  | UNIQUE, NOT NULL | -              | 套餐代码         |
| plan_name      | string  | NOT NULL         | -              | 套餐名称         |
| max_teachers   | int     | NOT NULL         | -              | 最大教师数       |
| max_members    | int     | NOT NULL         | -              | 最大会员数       |
| max_storage_gb | int     | NOT NULL         | -              | 最大存储空间(GB) |
| monthly_price  | int | NOT NULL         | 0           | 月付价格         |
| yearly_price   | int | NOT NULL         | 0           | 年付价格         |
| is_active      | boolean | NOT NULL         | true           | 是否激活         |

### system_configs (系统配置表)

| 字段名       | 类型    | 约束             | 默认值         | 说明                        |
| ------------ | ------- | ---------------- | -------------- | --------------------------- |
| id           | int     | PK               | AUTO_INCREMENT | 主键                        |
| tenant_id    | int     | FK               | NULL           | 租户 ID (NULL 表示全局配置) |
| config_key   | string  | UNIQUE, NOT NULL | -              | 配置键                      |
| config_value | string  | NOT NULL         | -              | 配置值                      |
| config_type  | enum    | NOT NULL         | 'string'       | 配置类型                    |
| is_global    | boolean | NOT NULL         | false          | 是否全局配置                |

**外键关系**:

- `tenant_id` → `tenants.id`

**config_type** (配置类型):

- `string`: 字符串
- `number`: 数字
- `boolean`: 布尔值
- `json`: JSON 对象

### system_admins (系统管理员表)

| 字段名        | 类型      | 约束             | 默认值         | 说明         |
| ------------- | --------- | ---------------- | -------------- | ------------ |
| id            | int       | PK               | AUTO_INCREMENT | 主键         |
| username      | string    | UNIQUE, NOT NULL | -              | 用户名       |
| email         | string    | UNIQUE, NOT NULL | -              | 邮箱         |
| role          | enum      | NOT NULL         | -              | 管理员角色   |
| status        | enum      | NOT NULL         | 'active'       | 管理员状态   |
| last_login_at | timestamp | -                | NULL           | 最后登录时间 |
| created_by    | int       | FK               | NULL           | 创建者 ID    |

**外键关系**:

- `created_by` → `system_admins.id` (自引用)

**role** (管理员角色):

- `super_admin`: 超级管理员
- `admin`: 管理员
- `billing_admin`: 计费管理员
- `support`: 技术支持

**status** (管理员状态):

- `active`: 激活
- `inactive`: 停用
- `locked`: 锁定

### tenant_usage_stats (租户使用统计表)

| 字段名            | 类型    | 约束         | 默认值         | 说明       |
| ----------------- | ------- | ------------ | -------------- | ---------- |
| id                | int     | PK           | AUTO_INCREMENT | 主键       |
| tenant_id         | int     | FK, NOT NULL | -              | 租户 ID    |
| stat_date         | date    | NOT NULL     | -              | 统计日期   |
| total_classes     | int     | NOT NULL     | 0              | 总课程数   |
| completed_classes | int     | NOT NULL     | 0              | 完成课程数 |
| active_teachers   | int     | NOT NULL     | 0              | 活跃教师数 |
| active_members    | int     | NOT NULL     | 0              | 活跃会员数 |
| total_revenue     | int | NOT NULL     | 0           | 总收入     |

**外键关系**:

- `tenant_id` → `tenants.id`

**唯一约束**:

- `(tenant_id, stat_date)` - 租户日期组合唯一

## 业务规则

### 租户管理规则

1. **租户创建**:

   - 租户代码全局唯一
   - 新租户默认为试用状态
   - 根据套餐类型设置资源限制

2. **状态管理**:

   - 试用期管理：`trial_expires_at` 字段
   - 状态转换：trial → active → suspended → cancelled
   - 暂停状态保留数据，注销状态软删除

3. **资源限制**:
   - 教师数量限制：`max_teachers`
   - 会员数量限制：`max_members`
   - 存储空间限制：`max_storage_gb`
   - 超限时禁止新增

### 套餐管理规则

1. **套餐模板**:

   - 预定义套餐模板
   - 支持月付/年付定价
   - 套餐升级/降级

2. **资源配置**:

   - 不同套餐提供不同资源限制
   - 支持自定义套餐配置
   - 套餐变更时自动调整限制

3. **计费管理**:
   - 基于套餐的自动计费
   - 支持试用期免费
   - 支持优惠和折扣

### 系统配置规则

1. **配置层级**:

   - 全局配置：适用于所有租户
   - 租户配置：覆盖全局配置
   - 配置继承：租户配置优先

2. **配置类型**:

   - 字符串配置：如系统名称、邮件模板
   - 数字配置：如超时时间、限制数量
   - 布尔配置：如功能开关
   - JSON 配置：如复杂配置对象

3. **配置管理**:
   - 支持动态配置更新
   - 配置变更日志记录
   - 配置验证和回滚

### 使用统计规则

1. **统计维度**:

   - 按日统计租户使用量
   - 支持多维度统计（课程、用户、收入）
   - 统计数据用于计费和监控

2. **数据收集**:

   - 定时任务自动收集统计数据
   - 实时更新关键指标
   - 支持手动触发统计

3. **数据用途**:
   - 计费数据生成
   - 性能监控
   - 业务分析
   - 容量规划

## 扩展字段说明

### tenants 表完整版本

```sql
display_name           varchar(100)   -- 显示名称
description            text           -- 机构描述
domain                 varchar(100)   -- 自定义域名
subdomain              varchar(50)    -- 子域名
logo_url               varchar(500)   -- 机构logo
favicon_url            varchar(500)   -- 网站图标
contact_name           varchar(50)    -- 联系人姓名
contact_phone          varchar(20)    -- 联系电话
contact_email          varchar(100)   -- 联系邮箱
address                text           -- 机构地址
trial_expires_at       timestamp      -- 试用期结束时间
settings               json           -- 机构个性化配置
features               json           -- 功能开关配置
branding               json           -- 品牌定制配置
database_schema        varchar(50)    -- 对应的数据库schema
api_key                varchar(100)   -- API访问密钥
webhook_url            varchar(500)   -- 回调地址
last_login_at          timestamp      -- 最后登录时间
```

### system_configs 表完整版本

```sql
description            varchar(200)   -- 配置描述
```

### system_admins 表完整版本

```sql
real_name              varchar(50)    -- 真实姓名
password_hash          varchar(255)   -- 密码哈希
permissions            json           -- 权限列表
login_count            int            -- 登录次数
failed_login_attempts  int            -- 失败登录次数
locked_until           timestamp      -- 锁定到期时间
```

### tenant_usage_stats 表完整版本

```sql
stat_month             varchar(7)     -- 统计月份 YYYY-MM
stat_year              int            -- 统计年份
cancelled_classes      int            -- 取消课程数
no_show_classes        int            -- 缺席课程数
new_members            int            -- 新增会员数
new_teachers           int            -- 新增教师数
api_calls              int            -- API调用次数
storage_used_mb        int            -- 存储使用量(MB)
login_sessions         int            -- 登录会话数
```

## 索引设计

### 主要索引

```sql
-- 租户表
CREATE UNIQUE INDEX idx_tenants_code ON tenants(code);
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_plan_type ON tenants(plan_type);

-- 套餐模板表
CREATE UNIQUE INDEX idx_plan_templates_code ON tenant_plan_templates(plan_code);
CREATE INDEX idx_plan_templates_active ON tenant_plan_templates(is_active);

-- 系统配置表
CREATE UNIQUE INDEX idx_system_configs_key ON system_configs(config_key);
CREATE INDEX idx_system_configs_tenant ON system_configs(tenant_id, config_key);
CREATE INDEX idx_system_configs_global ON system_configs(is_global);

-- 系统管理员表
CREATE UNIQUE INDEX idx_system_admins_username ON system_admins(username);
CREATE UNIQUE INDEX idx_system_admins_email ON system_admins(email);
CREATE INDEX idx_system_admins_role ON system_admins(role);
CREATE INDEX idx_system_admins_status ON system_admins(status);

-- 使用统计表
CREATE UNIQUE INDEX idx_usage_stats_tenant_date ON tenant_usage_stats(tenant_id, stat_date);
CREATE INDEX idx_usage_stats_date ON tenant_usage_stats(stat_date);
```

## 常用查询模式

### 租户管理

```sql
-- 获取活跃租户列表
SELECT id, name, code, plan_type, total_users, total_members
FROM tenants
WHERE status = 'active'
ORDER BY created_at DESC;

-- 检查租户资源使用情况
SELECT t.name, t.max_teachers, t.max_members,
       COUNT(DISTINCT u.id) as current_users,
       COUNT(DISTINCT m.id) as current_members
FROM tenants t
LEFT JOIN users u ON t.id = u.tenant_id
LEFT JOIN members m ON t.id = m.tenant_id
WHERE t.id = ?
GROUP BY t.id;
```

### 系统配置

```sql
-- 获取租户配置（含继承）
SELECT config_key, config_value, config_type
FROM system_configs
WHERE (tenant_id = ? OR tenant_id IS NULL)
ORDER BY tenant_id DESC; -- 租户配置优先

-- 获取全局配置
SELECT config_key, config_value, config_type
FROM system_configs
WHERE is_global = true;
```

### 使用统计

```sql
-- 租户月度统计
SELECT stat_date, total_classes, completed_classes,
       active_teachers, active_members, total_revenue
FROM tenant_usage_stats
WHERE tenant_id = ?
  AND stat_date BETWEEN ? AND ?
ORDER BY stat_date;

-- 系统整体统计
SELECT stat_date,
       SUM(total_classes) as total_classes,
       SUM(active_teachers) as total_teachers,
       SUM(active_members) as total_members,
       SUM(total_revenue) as total_revenue
FROM tenant_usage_stats
WHERE stat_date BETWEEN ? AND ?
GROUP BY stat_date
ORDER BY stat_date;
```
