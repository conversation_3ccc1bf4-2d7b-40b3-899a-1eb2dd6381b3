# 会员管理模块 - Markdown 版本

> 对应文件: `../module-member-management.md`

## 模块概述

会员信息管理、归属关系、统计数据模块。

## 表结构定义

### members (会员表)

| 字段名        | 类型      | 约束         | 默认值            | 说明        |
| ------------- | --------- | ------------ | ----------------- | ----------- |
| id            | int       | PK           | AUTO_INCREMENT    | 主键        |
| tenant_id     | int       | FK, NOT NULL | -                 | 租户 ID     |
| name          | string    | NOT NULL     | -                 | 会员姓名    |
| phone         | string    | NOT NULL     | -                 | 手机号      |
| member_status | enum      | NOT NULL     | 'active'          | 会员状态    |
| agent_id      | int       | FK           | NULL              | 代理人员 ID |
| total_classes | int       | NOT NULL     | 0                 | 总上课数    |
| total_spent   | int       | NOT NULL     | 0                 | 总消费金额  |
| created_by    | int       | FK           | NULL              | 创建者 ID   |
| created_at    | timestamp | NOT NULL     | CURRENT_TIMESTAMP | 创建时间    |

**外键关系**:

- `tenant_id` → `tenants.id`
- `agent_id` → `users.id`
- `created_by` → `users.id`

**枚举定义**:

**member_status** (会员状态):

- `active`: 活跃 - 可正常使用所有功能
- `silent`: 沉默 - 限制部分功能
- `frozen`: 冻结 - 暂停所有服务
- `cancelled`: 注销 - 保留历史数据

## 业务规则

### 会员注册规则

1. **基本信息**:

   - 姓名和手机号为必填项
   - 支持同手机号多账户（不同租户）
   - 新会员默认为试用会员状态

2. **归属分配**:

   - 可分配给销售人员 (`sales_id`)
   - 可分配给代理人员 (`agent_id`)
   - 支持归属关系变更
   - 归属关系可为空

3. **数据完整性**:
   - 租户 ID 必须有效
   - 创建者必须是有效用户
   - 手机号在租户内唯一

### 状态管理规则

1. **状态转换**:

   ```
   active ←→ silent ←→ frozen → cancelled
   ```

   - `active`: 正常状态，可使用所有功能
   - `silent`: 沉默状态，限制预约等功能
   - `frozen`: 冻结状态，暂停所有服务
   - `cancelled`: 注销状态，不可恢复

2. **状态权限**:
   - 只有管理员可以修改会员状态
   - 状态变更需要记录操作日志
   - 注销会员保留历史数据

### 归属关系规则

1. **销售人员关系**:

   - 每个会员最多分配一个销售人员
   - 销售人员负责会员的销售服务
   - 支持销售人员变更

2. **代理人员关系**:

   - 每个会员最多分配一个代理人员
   - 代理人员负责会员的日常服务
   - 支持代理人员变更

3. **权限控制**:
   - 销售/代理人员只能查看自己的会员
   - 管理员可以查看所有会员
   - 支持批量分配和调整

## 扩展字段说明

### 完整版本包含的额外字段

```sql
-- 基础信息扩展
email               varchar(100)    -- 邮箱
gender              enum           -- 性别 (male/female/other)
birthday            date           -- 生日
avatar_url          varchar(500)   -- 头像URL

-- 微信信息
wechat_openid       varchar(100)   -- 微信OpenID (唯一)
wechat_unionid      varchar(100)   -- 微信UnionID
wechat_nickname     varchar(100)   -- 微信昵称
wechat_avatar       varchar(500)   -- 微信头像

-- 会员属性
member_type         enum           -- 会员类型 (trial/formal/vip)
source_channel      varchar(50)    -- 来源渠道

-- 地址信息
address             text           -- 详细地址
city                varchar(50)    -- 城市
province            varchar(50)    -- 省份
country             varchar(50)    -- 国家 (默认China)
postal_code         varchar(20)    -- 邮编

-- 学习信息
level               varchar(20)    -- 学习级别
learning_goals      text           -- 学习目标
preferred_time      json           -- 偏好时间段

-- 备注和标签
notes               text           -- 备注
tags                json           -- 标签数组

-- 统计信息扩展
completed_classes   int            -- 完成上课数
cancelled_classes   int            -- 取消上课数
no_show_classes     int            -- 缺席上课数

-- 评价信息
avg_rating          decimal(3,2)   -- 平均评分
rating_count        int            -- 评价次数

-- 时间信息
last_class_at       timestamp      -- 最后上课时间
last_login_at       timestamp      -- 最后登录时间
registered_at       timestamp      -- 注册时间
```

## 索引设计

### 主要索引

```sql
-- 会员表索引
CREATE INDEX idx_members_tenant_id ON members(tenant_id, id);
CREATE INDEX idx_members_phone ON members(tenant_id, phone);
CREATE INDEX idx_members_status ON members(tenant_id, member_status);
CREATE INDEX idx_members_sales ON members(sales_id);
CREATE INDEX idx_members_agent ON members(agent_id);
CREATE INDEX idx_members_created_by ON members(created_by);

-- 复合索引 - 优化常用查询
CREATE INDEX idx_members_sales_status ON members(sales_id, member_status);
CREATE INDEX idx_members_agent_status ON members(agent_id, member_status);
CREATE INDEX idx_members_tenant_status ON members(tenant_id, member_status, id);
```

### 索引说明

- `idx_members_tenant_id`: 租户会员查询优化
- `idx_members_phone`: 手机号查询优化
- `idx_members_status`: 状态筛选优化
- `idx_members_sales/agent`: 归属关系查询优化
- 复合索引: 多维度筛选查询优化

## 常用查询模式

### 会员查询

```sql
-- 按租户查询会员列表
SELECT id, name, phone, member_status, total_classes, total_spent
FROM members
WHERE tenant_id = ?
ORDER BY created_at DESC;

-- 按销售人员查询会员
SELECT id, name, phone, member_status, total_classes
FROM members
WHERE sales_id = ? AND member_status = 'active';

-- 按手机号查询会员
SELECT id, name, member_status, sales_id, agent_id
FROM members
WHERE tenant_id = ? AND phone = ?;
```

### 统计查询

```sql
-- 会员状态统计
SELECT member_status, COUNT(*) as count
FROM members
WHERE tenant_id = ?
GROUP BY member_status;

-- 销售人员会员统计
SELECT u.real_name, COUNT(m.id) as member_count,
       SUM(m.total_spent) as total_revenue
FROM users u
LEFT JOIN members m ON u.id = m.sales_id
WHERE u.tenant_id = ? AND u.role = 'sale'
GROUP BY u.id, u.real_name;
```

### 业务操作

```sql
-- 分配销售人员
UPDATE members
SET sales_id = ?, updated_at = NOW()
WHERE id = ? AND tenant_id = ?;

-- 更新会员状态
UPDATE members
SET member_status = ?, updated_at = NOW()
WHERE id = ? AND tenant_id = ?;

-- 更新统计信息
UPDATE members
SET total_classes = total_classes + 1,
    total_spent = total_spent + ?,
    updated_at = NOW()
WHERE id = ?;
```
