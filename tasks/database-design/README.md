# 数据库设计文档

## 📋 概述

本目录包含了 KS English Admin Backend 项目的完整数据库设计文档，采用双格式支持，既有可视化的 eraser.io 格式，也有便于开发的 Markdown 格式。

## 📁 目录结构

```
database-design/
├── README.md                              # 本文件 - 总览说明
├── current-database-design.md             # 当前数据库设计完整分析
├── future-database-design.md              # 第二/三阶段数据库设计
├── database-design-issues-and-recommendations.md # 问题分析和修复建议
├── eraser.io/                             # eraser.io 格式文件
│   ├── README.md                          # eraser.io 版本说明
│   ├── core-relationships.md              # 核心关系图
│   └── module-*.md                        # 各业务模块设计
└── markdown/                              # Markdown 格式文件
    ├── README.md                          # Markdown 版本说明
    ├── core-relationships-md.md           # 核心关系图
    └── module-*-md.md                     # 各业务模块设计
```

## 审计字段说明

### 标准审计字段

所有业务表都包含以下审计字段：

| 字段名     | 类型      | 说明                                   |
| ---------- | --------- | -------------------------------------- |
| created_at | TIMESTAMP | 创建时间，默认当前时间                 |
| updated_at | TIMESTAMP | 更新时间，可为空                       |
| created_by | INTEGER   | 创建者 ID，外键关联 users,members 表等 |

### 软删除字段（可选）

部分表可能需要软删除功能：

| 字段名     | 类型      | 说明                 |
| ---------- | --------- | -------------------- |
| is_deleted | BOOLEAN   | 是否删除，默认 false |
| deleted_at | TIMESTAMP | 删除时间             |
| deleted_by | INTEGER   | 删除者 ID            |

## 🎯 双格式设计理念

### eraser.io 格式 ✨

**适用场景**：

- 架构设计和讨论
- 团队协作和分享
- 项目演示和汇报
- 可视化关系展示

**特点**：

- 图形化界面直观
- 在线协作便利
- 关系展示清晰
- 适合非技术人员理解

### Markdown 格式 📝

**适用场景**：

- 日常开发参考
- 字段定义查询
- 业务规则确认
- 代码实现指导

**特点**：

- 详细的字段定义和约束
- 完整的业务规则说明
- 索引设计和查询示例
- 版本控制友好
- 搜索和定位便利

## 🏗️ 数据库架构概览

### 多租户架构

- **架构模式**: 共享数据库 + 行级安全策略(RLS)
- **数据隔离**: 通过 `tenant_id` 字段实现租户间数据隔离
- **技术栈**: PostgreSQL + SQLModel + FastAPI

### 核心模块

1. **全局管理模块**: 租户管理、系统配置、使用统计
2. **用户权限模块**: 用户管理、认证、会话管理
3. **会员管理模块**: 会员信息、归属关系、统计数据
4. **教师管理模块**: 教师信息、标签系统、分类管理
5. **课程排课模块**: 固定时间占位、课程安排、自动排课
6. **财务管理模块**: 会员卡管理、充值记录、消费记录

## 🔧 使用指南

### 设计阶段

1. 查看 `eraser.io/core-relationships.md` 了解整体架构
2. 使用 eraser.io 在线工具进行可视化设计
3. 团队讨论时分享 eraser.io 格式文件

### 开发阶段

1. 参考 `markdown/` 目录下的详细文档
2. 查找字段定义、约束和索引设计
3. 参考业务规则和查询示例进行编码

### 维护更新

1. 设计变更时同步更新两种格式
2. 保持字段定义和关系的一致性
3. 及时更新业务规则和查询示例

## 📊 核心实体关系

```
tenants (租户) - 多租户架构核心
├── users (用户) - 权限管理
├── members (会员) - 业务核心
├── teachers (教师) - 业务核心
├── scheduled_classes (已排课) - 业务流程
└── member_cards (会员卡) - 财务管理
```

## 🔄 主要业务流程

### 排课流程

1. 教师设置固定时间占位 → `teacher_fixed_slots`
2. 会员锁定固定课位 → `member_fixed_slot_locks`
3. 系统自动排课 → `scheduled_classes`
4. 课程消费扣费 → `consumption_records`

### 会员卡流程

1. 创建卡片模板 → `member_card_templates`
2. 发放会员卡 → `member_cards`
3. 充值操作 → `recharge_records`
4. 课程消费 → `consumption_records`

## 📈 设计原则

### 字段选择原则

- **必须包含**: 主键、外键、核心业务字段
- **适当简化**: 省略过多的描述和统计字段
- **保留关键**: 状态字段、分类字段、金额字段

### 关系设计原则

- **数据完整性**: 外键约束确保数据一致性
- **审计追踪**: 重要操作记录创建者和时间
- **多租户隔离**: 所有业务表包含 tenant_id

### 性能优化原则

- **索引设计**: 基于常用查询模式设计索引
- **查询优化**: 提供常用查询的最佳实践
- **数据分区**: 为大数据量表考虑分区策略

---

## 📋 新增文档说明

### 当前数据库设计分析 (current-database-design.md)

**用途**: 全面分析当前已实现的数据库设计
**内容包括**:
- 21张表的完整结构分析
- 字段类型、约束、索引详细说明
- RLS策略配置分析
- 设计问题识别和影响评估
- 性能考虑和优化建议

### 未来数据库设计 (future-database-design.md)

**用途**: 第二阶段和第三阶段的数据库设计规划
**内容包括**:
- 智能排课系统相关表设计
- 支付系统和消息通知表设计
- 文件管理和教材管理表设计
- 评价系统和统计报表表设计
- 与现有设计的一致性保证

### 问题分析和修复建议 (database-design-issues-and-recommendations.md)

**用途**: 详细的问题分析和具体修复方案
**内容包括**:
- 按优先级分类的问题清单
- 具体的SQL修复脚本
- 实施步骤和风险控制
- 性能监控和维护建议

---

**创建时间**: 2025-06-28
**维护人**: 开发团队
**版本**: v3.0 (完整设计文档)
**最后更新**: 2025-07-10
