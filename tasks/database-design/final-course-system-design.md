# 核心课程模块 - 完整表结构设计

## 📋 设计概述

基于详细需求分析，课程系统包含以下核心概念：

- **课节(slot)**: 时间段单位，默认 25 分钟，间隔 5 分钟
- **直接约课**: 教师/管理员开放具体时间段，会员直接预约
- **固定课表约课**: 会员锁定固定时间位，系统批量排课

## 1. 系统配置表

```sql
-- 课程系统配置表
CREATE TABLE course_system_configs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 课节基础配置
    default_slot_duration_minutes INTEGER DEFAULT 25, -- 默认课节时长(分钟)
    default_slot_interval_minutes INTEGER DEFAULT 5,  -- 默认课节间隔(分钟)

    -- 直接约课配置
    direct_booking_enabled BOOLEAN DEFAULT TRUE, -- 是否启用直接约课
    max_advance_days INTEGER, -- 会员最多可预约x天后的课程
    booking_deadline_hours INTEGER, -- 预约截止时间：上课前x小时
    cancel_deadline_hours INTEGER, -- 取消截止时间：上课前x小时
    booking_time_from TIME, -- 预约操作时间限制：从
    booking_time_to TIME,   -- 预约操作时间限制：到
    require_material BOOLEAN DEFAULT TRUE, -- 会员预约时是否必须选教材

    -- 教师权限配置
    teacher_can_add_slots BOOLEAN DEFAULT TRUE, -- 教师是否可自主增加课时
    teacher_can_delete_empty_slots BOOLEAN DEFAULT TRUE, -- 教师是否可删除空课时
    teacher_can_cancel_booking BOOLEAN DEFAULT FALSE, -- 教师是否可取消学生预约
    teacher_need_confirm BOOLEAN DEFAULT FALSE, -- 学生预约后是否需要教师确认

    -- 固定课表配置
    fixed_booking_enabled BOOLEAN DEFAULT TRUE, -- 是否启用固定课表约课
    auto_schedule_enabled BOOLEAN DEFAULT TRUE, -- 是否启用自动排课
    auto_schedule_day INTEGER DEFAULT 22, -- 自动排课日期(每月几号)
    auto_schedule_time TIME DEFAULT '14:00', -- 自动排课时间

    -- 排课配置
    default_schedule_weeks INTEGER DEFAULT 4, -- 默认排课周数
    interrupt_on_conflict BOOLEAN DEFAULT TRUE, -- 遇到重复时间是否终止
    skip_insufficient_balance BOOLEAN DEFAULT TRUE, -- 是否跳过余额不足的学生
    remove_insufficient_locks BOOLEAN DEFAULT TRUE, -- 是否移除余额不足学生的固定位

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);
```

## 2. 教师固定时间占位表

```sql
-- 教师固定时间占位表（教师开放的周期性时间段）
CREATE TABLE teacher_fixed_slots (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    teacher_id INTEGER NOT NULL REFERENCES teachers(id),

    -- 时间信息
    weekday INTEGER NOT NULL, -- 1-7 (周一到周日)
    start_time TIME NOT NULL, -- 开始时间 "18:00"
    duration_minutes INTEGER, -- 课节时长(NULL时使用系统默认值)

    -- 状态
    is_available BOOLEAN DEFAULT TRUE, -- 是否开放给会员锁定
    is_visible_to_members BOOLEAN DEFAULT TRUE, -- 是否对会员可见

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),

    -- 唯一约束
    UNIQUE(tenant_id,teacher_id, weekday, start_time)
);
```

## 3. 会员固定课位锁定表

```sql
-- 会员固定课位锁定表（会员锁定的固定时间位）
CREATE TABLE member_fixed_slot_locks (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    member_id INTEGER NOT NULL REFERENCES members(id),

    -- 直接关联教师固定时间段
    teacher_fixed_slot_id INTEGER NOT NULL REFERENCES teacher_fixed_slots(id),

    -- 冗余字段（便于查询和显示，避免总是需要JOIN）
    teacher_id INTEGER NOT NULL REFERENCES teachers(id),
    weekday INTEGER NOT NULL, -- 1-7
    start_time TIME NOT NULL, -- 开始时间

    -- 状态
    status VARCHAR(20) DEFAULT 'active',
    -- active: 激活锁定，参与排课
    -- paused: 暂停锁定，跳过排课
    -- cancelled: 取消锁定,只能是老师或者管理员取消了该固定位，会员自己取消会删除数据

    -- 锁定时间
    locked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),

    -- 唯一约束：同一时间段只能被一个会员锁定
    UNIQUE(teacher_fixed_slot_id),

    -- 检查约束：确保冗余字段与关联表一致
    CONSTRAINT check_teacher_consistency
    CHECK (teacher_id = (SELECT teacher_id FROM teacher_fixed_slots WHERE id = teacher_fixed_slot_id))
);
```

## 4. 已排课表（核心表）

```sql
-- 已排课表（所有具体的课程安排，包括固定约课和直接约课生成的课程）
CREATE TABLE scheduled_classes (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 基础信息
    teacher_id INTEGER NOT NULL REFERENCES teachers(id),
    member_id INTEGER REFERENCES members(id), -- 可为空（未预约状态）

    -- 时间信息
    class_datetime TIMESTAMP NOT NULL, -- 精确到分钟的上课时间
    duration_minutes INTEGER DEFAULT 25, -- 课程时长

    -- 课程信息
    class_type VARCHAR(20) DEFAULT 'direct', -- fixed: 固定约课, direct: 直接约课
    price INTEGER, -- 教师单价（整数，单位：元）

    -- 会员卡信息
    member_card_id INTEGER REFERENCES member_cards(id),
    member_card_name VARCHAR(100), -- 冗余字段，便于显示

    -- 预约信息
    booking_remark TEXT, -- 预约备注
    member_no_cancel BOOLEAN DEFAULT FALSE, -- 会员是否可取消

    -- 教材信息（第一期使用字符串字段）
    material_id INTEGER, -- 预留教材ID（第二期使用）
    material_name VARCHAR(100), -- 教材名称（第一期主要使用此字段）

    -- 状态信息
    status VARCHAR(20) DEFAULT 'available',
    -- available: 可预约（空课）
    -- booked: 已预约
    -- completed: 已完成
    -- teacher_no_show: 教师缺席
    -- member_no_show: 会员缺席
    -- 注意：没有cancelled状态，取消后变回available

    is_deleted BOOLEAN DEFAULT FALSE, -- 是否删除

    -- 可见性控制
    is_visible_to_member BOOLEAN DEFAULT TRUE, -- 是否对会员可见

    -- 操作信息
    created_by INTEGER REFERENCES users(id), -- 创建者
    operator_name VARCHAR(50), -- 操作人显示名

    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 5. 固定约课排课任务记录表

```sql
-- 固定约课排课任务记录表
CREATE TABLE fixed_schedule_tasks (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 任务信息
    task_name VARCHAR(100), -- 任务名称
    task_type VARCHAR(20) DEFAULT 'manual', -- manual: 手动, auto: 自动

    -- 排课参数
    teacher_ids JSON, -- 参与排课的教师ID列表
    from_date DATE, -- 开始排课日期（周一）
    schedule_weeks INTEGER, -- 排课周数
    interrupt_on_conflict BOOLEAN, -- 遇到冲突是否终止
    skip_insufficient_balance BOOLEAN, -- 是否跳过余额不足
    remove_insufficient_locks BOOLEAN, -- 是否移除余额不足的锁定

    -- 执行状态
    status VARCHAR(20) DEFAULT 'pending',
    -- pending: 待执行, running: 执行中, completed: 已完成, failed: 失败

    -- 执行结果
    total_teachers INTEGER DEFAULT 0, -- 总教师数
    success_teachers INTEGER DEFAULT 0, -- 成功教师数
    failed_teachers INTEGER DEFAULT 0, -- 失败教师数
    total_classes INTEGER DEFAULT 0, -- 总生成课程数

    -- 时间信息
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),

    -- 备注
    remark TEXT
);
```

## 6. 固定约课排课日志表

```sql
-- 固定约课排课详细日志表
CREATE TABLE fixed_schedule_logs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),
    task_id INTEGER NOT NULL REFERENCES schedule_tasks(id),

    -- 日志信息
    log_level VARCHAR(10), -- INFO, WARN, ERROR
    message TEXT, -- 日志内容

    -- 关联信息
    teacher_id INTEGER REFERENCES teachers(id),
    member_id INTEGER REFERENCES members(id),
    class_datetime TIMESTAMP, -- 相关课程时间

    -- 异常信息
    error_code VARCHAR(50), -- 错误代码
    error_detail TEXT, -- 错误详情

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 7. 操作记录表

### 7.1 已排课表操作记录

```sql
-- 已排课表操作记录（预约、取消等）
CREATE TABLE scheduled_class_operation_logs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 关联信息
    scheduled_class_id INTEGER NOT NULL REFERENCES scheduled_classes(id),
    member_id INTEGER REFERENCES members(id),
    teacher_id INTEGER NOT NULL REFERENCES teachers(id),

    -- 操作信息
    operation_type VARCHAR(20) NOT NULL,
    -- create_slot: 创建课时（教师/管理员开放）
    -- book: 预约
    -- cancel: 取消预约
    -- reschedule: 改期
    -- complete: 完成
    -- no_show_member: 会员缺席
    -- no_show_teacher: 教师缺席
    -- delete: 软删除

    -- 状态变更
    old_status VARCHAR(20), -- 操作前状态
    new_status VARCHAR(20), -- 操作后状态

    -- 课程信息
    class_datetime TIMESTAMP, -- 课程时间
    class_type VARCHAR(20), -- 课程类型

    -- 会员卡信息
    member_card_id INTEGER REFERENCES member_cards(id),
    member_card_name VARCHAR(100),
    deducted_amount INTEGER, -- 扣除金额（元）
    deducted_count int DEFAULT 0, -- 扣除次数

    -- 教材信息
    material_id INTEGER,
    material_name VARCHAR(100),

    -- 操作详情
    operation_reason TEXT, -- 操作原因
    remark TEXT, -- 备注

    -- 操作人信息
    operated_by INTEGER REFERENCES users(id), -- 操作人ID（NULL表示会员自己操作）
    operator_name VARCHAR(50), -- 操作人姓名
    operator_type VARCHAR(20), -- member, teacher, admin

    -- 时间信息
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7.2 教师固定时间占位表操作记录

```sql
-- 教师固定时间占位表操作记录
CREATE TABLE teacher_fixed_slot_operation_logs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 关联信息
    teacher_fixed_slot_id INTEGER REFERENCES teacher_fixed_slots(id), -- 可能为空（删除操作）
    teacher_id INTEGER NOT NULL REFERENCES teachers(id),

    -- 时间信息
    weekday INTEGER NOT NULL,
    start_time TIME NOT NULL,
    duration_minutes INTEGER,

    -- 操作信息
    operation_type VARCHAR(20) NOT NULL,
    -- create: 创建时间段
    -- update: 更新时间段
    -- enable: 开放时间段
    -- disable: 关闭时间段
    -- delete: 删除时间段
    -- visibility_change: 修改可见性

    -- 状态变更
    old_is_available BOOLEAN,
    new_is_available BOOLEAN,
    old_is_visible BOOLEAN,
    new_is_visible BOOLEAN,

    -- 操作详情
    operation_reason TEXT, -- 操作原因
    remark TEXT, -- 备注

    -- 操作人信息
    operated_by INTEGER REFERENCES users(id), -- 操作人ID
    operator_name VARCHAR(50), -- 操作人姓名
    operator_type VARCHAR(20), -- teacher, admin

    -- 时间信息
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7.3 会员固定位锁定表操作记录

```sql
-- 会员固定位锁定表操作记录
CREATE TABLE member_fixed_lock_operation_logs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id),

    -- 关联信息
    member_fixed_slot_lock_id INTEGER REFERENCES member_fixed_slot_locks(id), -- 可能为空（删除操作）
    teacher_fixed_slot_id INTEGER NOT NULL REFERENCES teacher_fixed_slots(id), -- 新增关联
    member_id INTEGER NOT NULL REFERENCES members(id),
    teacher_id INTEGER NOT NULL REFERENCES teachers(id),

    -- 时间信息（冗余字段）
    weekday INTEGER NOT NULL,
    start_time TIME NOT NULL,

    -- 操作信息
    operation_type VARCHAR(20) NOT NULL,
    -- lock: 会员锁定时间位
    -- unlock: 会员取消锁定
    -- pause: 暂停锁定
    -- resume: 恢复锁定
    -- admin_remove: 管理员移除锁定（如余额不足）
    -- system_remove: 系统自动移除锁定
    -- batch_remove: 批量移除（排课时）

    -- 状态变更
    old_status VARCHAR(20), -- 操作前状态
    new_status VARCHAR(20), -- 操作后状态

    -- 会员相关信息
    member_balance int, -- 操作时会员余额（用于分析）

    -- 操作详情
    operation_reason TEXT, -- 操作原因
    remark TEXT, -- 备注

    -- 操作人信息
    operated_by INTEGER REFERENCES users(id), -- 操作人ID（NULL表示会员自己操作）
    operator_name VARCHAR(50), -- 操作人姓名
    operator_type VARCHAR(20), -- member, admin, system

    -- 时间信息
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 8. 索引设计

```sql
-- 教师固定时间占位表索引
CREATE INDEX idx_teacher_fixed_slots_teacher ON teacher_fixed_slots(teacher_id);
CREATE INDEX idx_teacher_fixed_slots_available ON teacher_fixed_slots(teacher_id, is_available, is_visible_to_members);
CREATE INDEX idx_teacher_fixed_slots_weekday ON teacher_fixed_slots(weekday, start_time);

-- 会员固定课位锁定表索引
CREATE INDEX idx_member_locks_member ON member_fixed_slot_locks(member_id, status);
CREATE INDEX idx_member_locks_teacher_slot ON member_fixed_slot_locks(teacher_fixed_slot_id);
CREATE INDEX idx_member_locks_teacher_time ON member_fixed_slot_locks(teacher_id, weekday, start_time); -- 冗余字段索引
CREATE INDEX idx_member_locks_status ON member_fixed_slot_locks(tenant_id, status);

-- 已排课表索引
CREATE INDEX idx_scheduled_classes_teacher_datetime ON scheduled_classes(teacher_id, class_datetime);
CREATE INDEX idx_scheduled_classes_member_datetime ON scheduled_classes(member_id, class_datetime);
CREATE INDEX idx_scheduled_classes_status ON scheduled_classes(tenant_id, status);
CREATE INDEX idx_scheduled_classes_visible ON scheduled_classes(is_visible_to_member, status);
CREATE INDEX idx_scheduled_classes_datetime ON scheduled_classes(class_datetime);
CREATE INDEX idx_scheduled_classes_type ON scheduled_classes(class_type, status);

-- 操作记录表索引
CREATE INDEX idx_scheduled_class_logs_class ON scheduled_class_operation_logs(scheduled_class_id);
CREATE INDEX idx_scheduled_class_logs_member ON scheduled_class_operation_logs(member_id, operation_time);
CREATE INDEX idx_scheduled_class_logs_operation ON scheduled_class_operation_logs(operation_type, operation_time);

-- 教师固定时间占位操作记录索引
CREATE INDEX idx_teacher_slot_logs_teacher ON teacher_fixed_slot_operation_logs(teacher_id, operation_time);
CREATE INDEX idx_teacher_slot_logs_operation ON teacher_fixed_slot_operation_logs(operation_type, operation_time);
CREATE INDEX idx_teacher_slot_logs_time_slot ON teacher_fixed_slot_operation_logs(teacher_id, weekday, start_time);

-- 会员固定位锁定操作记录索引
CREATE INDEX idx_member_lock_logs_member ON member_fixed_lock_operation_logs(member_id, operation_time);
CREATE INDEX idx_member_lock_logs_teacher ON member_fixed_lock_operation_logs(teacher_id, weekday, start_time);
CREATE INDEX idx_member_lock_logs_operation ON member_fixed_lock_operation_logs(operation_type, operation_time);

-- 排课任务相关索引
CREATE INDEX idx_schedule_tasks_status ON schedule_tasks(status, created_at);
CREATE INDEX idx_schedule_logs_task ON schedule_logs(task_id, created_at);
```

## 9. 常用查询示例

```sql
-- 会员查看自己锁定的固定课（优化版）
SELECT mfsl.status, tfs.weekday, tfs.start_time, t.name as teacher_name
FROM member_fixed_slot_locks mfsl
JOIN teacher_fixed_slots tfs ON mfsl.teacher_fixed_slot_id = tfs.id
JOIN teachers t ON tfs.teacher_id = t.id
WHERE mfsl.member_id = ? AND mfsl.status = 'active'
ORDER BY tfs.weekday, tfs.start_time;

-- 会员查看某教师可锁定的时间段（优化版）
SELECT tfs.id, tfs.weekday, tfs.start_time
FROM teacher_fixed_slots tfs
LEFT JOIN member_fixed_slot_locks mfsl ON tfs.id = mfsl.teacher_fixed_slot_id AND mfsl.status = 'active'
WHERE tfs.teacher_id = ?
  AND tfs.is_available = TRUE
  AND tfs.is_visible_to_members = TRUE
  AND mfsl.id IS NULL -- 未被锁定
ORDER BY tfs.weekday, tfs.start_time;

-- 教师查看自己时间段的锁定情况（优化版）
SELECT tfs.weekday, tfs.start_time, tfs.is_available,
       mfsl.member_id, m.name as member_name, mfsl.status as lock_status
FROM teacher_fixed_slots tfs
LEFT JOIN member_fixed_slot_locks mfsl ON tfs.id = mfsl.teacher_fixed_slot_id AND mfsl.status = 'active'
LEFT JOIN members m ON mfsl.member_id = m.id
WHERE tfs.teacher_id = ?
ORDER BY tfs.weekday, tfs.start_time;

-- 会员端筛选：查找晚上18:00-22:00有空课的教师
SELECT DISTINCT t.id, t.name, t.teacher_category
FROM teachers t
JOIN scheduled_classes sc ON t.id = sc.teacher_id
WHERE t.tenant_id = ?
  AND t.status = 'active'
  AND sc.status = 'available'
  AND sc.is_visible_to_member = TRUE
  AND EXTRACT(HOUR FROM sc.class_datetime) BETWEEN 18 AND 22
  AND sc.class_datetime >= NOW()
ORDER BY t.teacher_category, t.name;

-- 排课时查询会员锁定的固定位（优化版）
SELECT mfsl.member_id, tfs.weekday, tfs.start_time,
       m.name as member_name, mc.balance
FROM member_fixed_slot_locks mfsl
JOIN teacher_fixed_slots tfs ON mfsl.teacher_fixed_slot_id = tfs.id
JOIN members m ON mfsl.member_id = m.id
JOIN member_cards mc ON m.id = mc.member_id
WHERE tfs.teacher_id = ?
  AND mfsl.status = 'active'
  AND mc.status = 'active'
ORDER BY tfs.weekday, tfs.start_time;

-- 查询教师的时间段操作历史
SELECT operation_type, operator_name, operation_time, operation_reason
FROM teacher_fixed_slot_operation_logs
WHERE teacher_id = ?
ORDER BY operation_time DESC;

-- 查询会员的锁定操作历史
SELECT operation_type, operator_name, operation_time, operation_reason,
       old_status, new_status
FROM member_fixed_lock_operation_logs
WHERE member_id = ?
ORDER BY operation_time DESC;

-- 查询某个时间段的完整操作历史
SELECT 'teacher' as source, operation_type, operator_name, operation_time, operation_reason
FROM teacher_fixed_slot_operation_logs
WHERE teacher_id = ? AND weekday = ? AND start_time = ?
UNION ALL
SELECT 'member' as source, operation_type, operator_name, operation_time, operation_reason
FROM member_fixed_lock_operation_logs
WHERE teacher_id = ? AND weekday = ? AND start_time = ?
ORDER BY operation_time;
```

## 10. 设计说明

### 10.1 表关联关系优化

#### 问题背景

原设计中 `member_fixed_slot_locks` 通过多个字段与 `teacher_fixed_slots` 关联：

```sql
-- 原设计：通过多字段关联
member_fixed_slot_locks (teacher_id, weekday, start_time)
↔
teacher_fixed_slots (teacher_id, weekday, start_time)
```

#### 优化方案

新设计采用直接外键关联 + 冗余字段：

```sql
-- 新设计：直接外键 + 冗余字段
member_fixed_slot_locks.teacher_fixed_slot_id → teacher_fixed_slots.id
-- 同时保留 teacher_id, weekday, start_time 作为冗余字段
```

#### 优化优势

1. **数据一致性**: 外键约束确保数据完整性，避免"孤儿"数据
2. **查询简化**: 直接通过 ID 关联，避免多字段 JOIN
3. **业务逻辑清晰**: 明确表达依赖关系
4. **性能优化**: 冗余字段支持快速查询，避免频繁 JOIN
5. **级联处理**: 支持教师删除时间段时的级联操作

## 11. 业务逻辑说明

### 10.1 排课扣费逻辑

```sql
-- 排课扣费流程（以会员为单位，按教师分组）
-- 1. 计算该会员对某教师的所有待排课程总费用
SELECT COUNT(*) * t.price_per_class as total_cost
FROM member_fixed_slot_locks mfsl
JOIN teachers t ON mfsl.teacher_id = t.id
WHERE mfsl.member_id = ? AND mfsl.teacher_id = ? AND mfsl.status = 'active';

-- 2. 检查会员卡余额是否充足
SELECT balance FROM member_cards
WHERE member_id = ? AND status = 'active';

-- 3. 如果余额充足，批量生成课程并扣费
BEGIN;
-- 生成课程记录
INSERT INTO scheduled_classes (...) VALUES (...);
-- 扣除会员卡余额
UPDATE member_cards SET balance = balance - total_cost WHERE id = ?;
-- 记录消费记录
INSERT INTO consumption_records (...) VALUES (...);
COMMIT;
```

### 10.2 冲突检测逻辑

```sql
-- 1. 教师时间冲突检测
SELECT COUNT(*) FROM scheduled_classes
WHERE teacher_id = ?
  AND class_datetime = ?
  AND status IN ('booked', 'completed');

-- 2. 会员时间冲突检测
SELECT COUNT(*) FROM scheduled_classes
WHERE member_id = ?
  AND class_datetime = ?
  AND status IN ('booked', 'completed');

-- 3. 教师固定位开放状态检测
SELECT is_available FROM teacher_fixed_slots
WHERE teacher_id = ? AND weekday = ? AND start_time = ?;
```

### 10.3 排课算法流程

```python
def schedule_classes(teacher_ids, from_date, weeks):
    """
    排课算法流程
    """
    # 1. 按优先级排序教师（欧美/南非 > 菲教，编号从大到小）
    teachers = sort_teachers_by_priority(teacher_ids)

    for teacher in teachers:
        log(f"为【{teacher.code}-{teacher.name}】开始执行排课")

        # 2. 获取该教师被锁定的固定位
        locked_slots = get_member_locked_slots(teacher.id)

        # 3. 按会员分组处理
        for member_id, slots in group_by_member(locked_slots):
            member = get_member(member_id)

            # 4. 计算总费用
            total_cost = len(slots) * weeks * teacher.price_per_class

            # 5. 检查余额
            if not check_member_balance(member_id, total_cost):
                if config.skip_insufficient_balance:
                    log(f"会员【{member.name}】余额不足，跳过")
                    if config.remove_insufficient_locks:
                        remove_member_locks(member_id, teacher.id)
                    continue
                else:
                    log(f"会员【{member.name}】余额不足，终止排课")
                    break

            # 6. 生成具体课程
            for week in range(weeks):
                for slot in slots:
                    class_datetime = calculate_datetime(from_date, week, slot)

                    # 7. 冲突检测
                    if check_conflicts(teacher.id, member_id, class_datetime):
                        if config.interrupt_on_conflict:
                            log(f"【{class_datetime}】时间冲突，结束排课")
                            return
                        else:
                            log(f"【{class_datetime}】时间冲突，跳过")
                            continue

                    # 8. 创建课程记录
                    create_scheduled_class(teacher.id, member_id, class_datetime)

            # 9. 批量扣费
            deduct_member_balance(member_id, total_cost)

        log(f"为【{teacher.code}-{teacher.name}】执行排课完成")
```

### 10.4 操作记录归档策略

```sql
-- 操作记录归档表（可选，用于历史数据归档）
CREATE TABLE scheduled_class_operation_logs_archive (
    LIKE scheduled_class_operation_logs INCLUDING ALL
);

CREATE TABLE teacher_fixed_slot_operation_logs_archive (
    LIKE teacher_fixed_slot_operation_logs INCLUDING ALL
);

CREATE TABLE member_fixed_lock_operation_logs_archive (
    LIKE member_fixed_lock_operation_logs INCLUDING ALL
);

-- 归档脚本（定期执行）
-- 将2年前的记录移动到归档表
INSERT INTO scheduled_class_operation_logs_archive
SELECT * FROM scheduled_class_operation_logs
WHERE operation_time < NOW() - INTERVAL '2 years';

INSERT INTO teacher_fixed_slot_operation_logs_archive
SELECT * FROM teacher_fixed_slot_operation_logs
WHERE operation_time < NOW() - INTERVAL '2 years';

INSERT INTO member_fixed_lock_operation_logs_archive
SELECT * FROM member_fixed_lock_operation_logs
WHERE operation_time < NOW() - INTERVAL '2 years';

-- 删除已归档的记录
DELETE FROM scheduled_class_operation_logs
WHERE operation_time < NOW() - INTERVAL '2 years';

DELETE FROM teacher_fixed_slot_operation_logs
WHERE operation_time < NOW() - INTERVAL '2 years';

DELETE FROM member_fixed_lock_operation_logs
WHERE operation_time < NOW() - INTERVAL '2 years';
```

```

```
