# KS English Admin Backend - 开发任务列表

基于 PRD: `prd-ks-english-admin-backend-complete.md`

## 模块化架构 - 项目结构

```
ks-english-admin-backend/
├── app/
│   ├── main.py                 # FastAPI应用入口，生命周期管理
│   ├── config.py              # 配置管理（已弃用，使用core/config.py）
│   ├── core/                  # 核心组件
│   │   ├── config.py          # 配置管理
│   │   ├── dependencies.py    # 依赖注入
│   │   ├── security.py        # 安全工具
│   │   ├── logging.py         # 日志工具
│   │   └── context.py         # 上下文管理
│   ├── db/                    # 数据库层
│   │   ├── base.py            # 数据库基础配置和多租户支持
│   │   └── session.py         # 数据库会话管理
│   ├── features/              # 业务模块（垂直分层架构）
│   │   ├── auth/              # 认证模块
│   │   ├── users/             # 用户管理模块
│   │   ├── tenants/           # 租户管理模块
│   │   ├── members/           # 会员管理模块
│   │   ├── teachers/          # 教师管理模块
│   │   ├── tags/              # 标签管理模块
│   │   ├── courses/           # 课程系统模块
│   │   └── shared/            # 共享模块
│   │       ├── models.py      # 全局共享模型
│   │       ├── schemas.py     # 共享数据模式
│   │       └── exceptions.py  # 共享异常定义
│   ├── models/                # 数据模型
│   │   └── shared/            # 全局共享数据模型
│   ├── api/                   # API路由
│   │   ├── common/            # 通用API组件
│   │   │   ├── responses.py   # 统一响应格式
│   │   │   ├── exceptions.py  # 异常处理
│   │   │   ├── pagination.py  # 分页工具
│   │   │   └── docs.py        # API文档配置
│   │   └── v1/                # API v1
│   │       ├── api.py         # 路由聚合
│   │       ├── admin/         # 管理端API
│   │       ├── member/        # 会员端API
│   │       └── public/        # 公共API
│   └── utils/                 # 工具函数
│       ├── security.py        # 安全工具
│       └── json_encoder.py    # JSON编码器
├── tests/                     # 测试目录
│   ├── fixtures/              # 测试夹具
│   │   ├── database.py        # 数据库fixtures
│   │   ├── client.py          # API客户端fixtures
│   │   └── business/          # 业务数据fixtures
│   ├── unit/                  # 单元测试
│   │   ├── features/          # 业务模块单元测试
│   │   └── utils/             # 工具函数测试
│   ├── integration/           # 集成测试
│   │   └──  api/v1/            # API集成测试
│   ├── e2e/                   # 端到端测试
│   └── performance/           # 性能测试
├── scripts/                   # 工具脚本
│   ├── test.py                # 统一测试脚本
│   ├── init_database.py       # 数据库初始化
│   └── test_enhanced.py       # 增强测试脚本
├── docs/                      # 项目文档
│   ├── 数据库架构/             # 数据库设计文档
│   └── 权限系统/              # 权限系统文档
├── tasks/                     # 任务和设计文档
│   ├── database-design/       # 数据库设计
│   ├── development-tips/      # 开发经验
│   └── *.md                   # 需求和任务文档
├── conftest.py                # pytest全局配置
├── pytest.ini                # pytest运行配置
├── requirements.txt           # 项目依赖
├── run.py                     # 生产环境启动脚本
├── quick_start.py             # 开发环境快速启动
├── .env.example              # 环境变量示例
└── README.md
```

## 第一期开发任务（教师管理和基础课程管理）

- [x] 1.0 标签管理系统开发

  - [x] 1.1 创建标签数据模型和数据库表结构
    - [x] 1.1.1 设计标签表(tags)：id, tenant_id, name, category, description, status, created_at, updated_at
    - [x] 1.1.2 设计标签分类表(tag_categories)：id, tenant_id, name, description, sort_order, created_at, updated_at
    - [x] 1.1.3 创建数据库迁移脚本 (通过 SQLModel 创建表)
  - [x] 1.2 实现标签管理的业务逻辑层
    - [x] 1.2.1 创建标签分类的 CRUD 服务(TagCategoryService)
    - [x] 1.2.2 创建标签的 CRUD 服务(TagService)
    - [x] 1.2.3 实现标签的批量操作功能
  - [x] 1.3 开发标签管理 API 接口
    - [x] 1.3.1 实现标签分类管理接口：创建、查询、更新、删除
    - [x] 1.3.2 实现标签管理接口：创建、查询、更新、删除、按分类查询
    - [x] 1.3.3 添加 API 文档和响应模型
  - [x] 1.4 编写标签管理的测试用例
    - [x] 1.4.1 编写标签服务层单元测试
    - [x] 1.4.2 编写标签 API 集成测试
    - [x] 1.4.3 创建标签相关的测试 fixtures

- [x] 2.0 教师管理模块开发

  - [x] 2.1 创建教师数据模型和数据库表结构
    - [x] 2.1.1 设计教师表(teachers)：id, tenant_id, name, gender, avatar, phone, email, price_per_class, teacher_category, tags(JSONB), region, wechat_bound, show_to_members, status, created_at, updated_at
    - [x] 2.1.2 设计教师标签关联表(teacher_tags)：teacher_id, tag_id（多对多关系）
    - [x] 2.1.3 创建相关索引优化查询性能
    - [x] 2.1.4 创建数据库迁移脚本 (通过 SQLModel 创建表)
  - [x] 2.2 实现教师管理的业务逻辑层
    - [x] 2.2.1 创建教师 CRUD 服务(TeacherService)
    - [x] 2.2.2 实现教师多维度筛选功能（按标签、区域、价格等）
    - [x] 2.2.3 实现教师标签关联管理
    - [x] 2.2.4 实现教师状态管理（激活/停用）
    - [x] 2.2.5 实现对应的单元测试，并创建相关的测试 fixtures
  - [x] 2.3 开发教师管理 API 接口
    - [x] 2.3.1 实现教师基础 CRUD 接口
    - [x] 2.3.2 实现教师列表查询接口（支持分页、筛选、排序）
    - [x] 2.3.3 实现教师标签管理接口
    - [x] 2.3.4 实现教师头像上传接口（基础版本）
    - [x] 2.3.5 添加完整的 API 文档和错误处理
  - [x] 2.4 完善教师管理的测试用例
    - [x] 2.4.1 确保教师服务层单元测试通过
    - [x] 2.4.2 编写教师 API 集成测试
    - [x] 2.4.3 完善教师相关的测试 fixtures
    - [x] 2.4.4 测试多维度筛选功能

- [x] 3.0 教师固定时间占位表功能开发（重新实现）

  - [x] 3.1 创建固定时间占位表数据模型
    - [x] 3.1.1 设计固定时间占位表(teacher_fixed_slots)：id, tenant_id, teacher_id, weekday(1-7), start_time(TIME), duration_minutes, is_available, is_visible_to_members, created_by, created_at, updated_at
    - [x] 3.1.2 设计时间验证规则（TIME 类型，支持 5 分钟间隔）
    - [x] 3.1.3 创建唯一约束防止重复时间段(teacher_id, weekday, start_time)
    - [x] 3.1.4 创建数据库索引优化查询性能
    - [x] 3.1.5 创建时间处理工具函数（时间验证、格式转换、时长计算）
  - [x] 3.2 实现固定时间占位表 Schema 层
    - [x] 3.2.1 创建请求/响应 Schema 模型（支持 TIME 类型）
    - [x] 3.2.2 实现字段验证器（时间格式、时长验证）
    - [x] 3.2.3 创建查询参数 Schema（支持时间范围查询）
    - [x] 3.2.4 创建批量操作 Schema 模型
  - [x] 3.3 实现固定时间占位表业务逻辑
    - [x] 3.3.1 创建固定时间占位表服务(TeacherFixedSlotService)
    - [x] 3.3.2 实现 CRUD 操作（创建、查询、更新、删除）
    - [x] 3.3.3 实现时间段冲突检测和可用性检查
    - [x] 3.3.4 实现批量操作功能（批量创建、更新、删除）
    - [x] 3.3.5 实现按教师查询可用时间段（支持可见性过滤）
    - [x] 3.3.6 创建测试 fixtures（教师固定时间段数据）
    - [x] 3.3.7 编写时间段服务层单元测试并测试通过
  - [x] 3.4 开发固定时间占位表 API 接口
    - [x] 3.4.1 实现教师设置固定时间段接口
    - [x] 3.4.2 实现查询教师固定时间表接口
    - [x] 3.4.3 实现时间段可用性管理接口
    - [x] 3.4.4 实现批量操作接口
    - [x] 3.4.5 集成到主路由配置
    - [x] 3.4.6 编写时间段 API 集成测试并测试通过
  - [x] 3.5 再次验证测试用例并完善
    - [x] 3.5.1 测试时间段冲突检测功能
    - [x] 3.5.2 测试批量操作功能
    - [x] 3.5.3 测试时间处理工具函数

- [x] 4.0 课程系统配置模块开发

  - [x] 4.1 创建课程系统配置数据模型
    - [x] 4.1.1 设计课程系统配置表(course_system_configs)：id, tenant_id, default_slot_duration_minutes, default_slot_interval_minutes, temp_booking_enabled, fixed_booking_enabled, auto_schedule_enabled, auto_schedule_day, auto_schedule_time, max_advance_days, booking_deadline_hours, cancel_deadline_hours, teacher_can_add_slots, teacher_can_delete_empty_slots, default_schedule_weeks, created_by, created_at, updated_at
    - [x] 4.1.2 创建配置验证规则和默认值
    - [x] 4.1.3 创建数据库索引和约束
  - [x] 4.2 实现课程系统配置 Schema 层
    - [x] 4.2.1 创建配置请求/响应 Schema 模型
    - [x] 4.2.2 实现配置字段验证器
    - [x] 4.2.3 创建配置查询和更新 Schema
  - [x] 4.3 实现课程系统配置业务逻辑
    - [x] 4.3.1 创建课程系统配置服务(CourseSystemConfigService)
    - [x] 4.3.2 实现配置 CRUD 操作，不需要 删除操作
    - [x] 4.3.3 创建对应的测试需要的 fixtures，通常是单元测试用
    - [x] 4.3.4 编写对应的服务层单元测试，并通过测试
  - [x] 4.4 开发课程系统配置 API 接口
    - [x] 4.4.1 实现配置查询接口
    - [x] 4.4.2 实现配置更新接口
    - [x] 4.4.3 实现配置重置接口
    - [x] 4.4.4 编写对应的 API 集成测试
  - [x] 4.5 再次 check 课程系统配置的测试用例
    - [x] 4.5.1 验证课程系统配置对应的单元测试和 API 集成测试
    - [x] 4.5.2 验证所有的单元测试和 API 集成测试

- [x] 5.0 会员固定课位锁定模块开发

  - [x] 5.1 创建会员固定课位锁定数据模型
    - [x] 5.1.1 设计会员固定课位锁定表(member_fixed_slot_locks)：id, tenant_id, member_id, teacher_fixed_slot_id, teacher_id, weekday, start_time, status, locked_at, created_by, created_at, updated_at
    - [x] 5.1.2 创建唯一约束(teacher_fixed_slot_id)和检查约束
    - [x] 5.1.3 创建数据库索引优化查询性能
  - [x] 5.2 实现会员固定课位锁定 Schema 层
    - [x] 5.2.1 创建锁定请求/响应 Schema 模型
    - [x] 5.2.2 实现锁定状态验证器
    - [x] 5.2.3 创建查询参数 Schema
    - [x] 5.2.4 创建批量操作 Schema 模型
  - [x] 5.3 实现会员固定课位锁定业务逻辑
    - [x] 5.3.1 创建会员固定课位锁定服务(MemberFixedSlotLockService)
    - [x] 5.3.2 实现锁定 CRUD 操作
    - [x] 5.3.3 实现锁定冲突检测和可用性检查
    - [x] 5.3.4 实现批量锁定/解锁功能
    - [x] 5.3.5 实现锁定状态管理（激活/暂停/取消）
    - [x] 5.3.6 创建相关测试 fixtures
    - [x] 5.3.7 编写对应服务层单元测试
  - [x] 5.4 开发会员固定课位锁定 API 接口
    - [x] 5.4.1 实现会员锁定时间段接口
    - [x] 5.4.2 实现查询会员锁定列表接口
    - [x] 5.4.3 实现锁定状态管理接口
    - [x] 5.4.4 实现批量锁定操作接口
    - [x] 5.4.5 编写锁定 API 集成测试
  - [x] 5.5 整体单元测试和 API 集成测试

- [x] 6.0 已排课表模块开发

  - [x] 6.1 创建已排课表数据模型
    - [x] 6.1.1 设计已排课表(scheduled_classes)：id, tenant_id, teacher_id, member_id, class_datetime, duration_minutes, class_type, price, member_card_id, member_card_name, booking_remark, material_name, status, is_visible_to_member, operator_name, created_by, created_at, updated_at
    - [x] 6.1.2 创建数据库索引和约束
  - [x] 6.2 实现已排课表 Schema 层
    - [x] 6.2.1 创建课程请求/响应 Schema 模型
    - [x] 6.2.2 实现最简单的课程状态验证器
    - [x] 6.2.3 创建查询参数 Schema（支持多维度查询）
    - [x] 6.2.4 创建临时课程创建 Schema
  - [x] 6.3 实现已排课表业务逻辑
    - [x] 6.3.1 创建已排课表服务(ScheduledClassService)
    - [x] 6.3.2 实现课程 CRUD 操作
    - [x] 6.3.3 实现临时课程创建功能
    - [x] 6.3.4 实现课程预约和取消功能
    - [x] 6.3.5 实现课程时间冲突检测
    - [x] 6.3.6 创建相关测试 fixtures
    - [x] 6.3.7 编写对应服务层单元测试
  - [x] 6.4 开发已排课表 API 接口
    - [x] 6.4.1 实现临时课程创建接口
    - [x] 6.4.2 实现课程查询接口（按教师、会员、时间范围查询）
    - [x] 6.4.3 实现课程预约和取消接口
    - [x] 6.4.4 实现课程状态更新接口
    - [x] 6.4.5 实现课程统计接口
    - [x] 6.4.6 编写对应的 API 集成测试
  - [x] 6.5 整体单元测试和 API 集成测试

- [x] 7.0 会员卡模块实现（第一期补充）

  - [x] 7.1 会员卡数据模型设计与实现
    - [x] 7.1.1 实现 MemberCardTemplate 模型（会员卡模板）
    - [x] 7.1.2 实现 MemberCard 模型（会员卡实例）
    - [x] 7.1.3 实现 MemberCardOperation 模型（会员卡各种操作记录和各种消费记录）
  - [x] 7.2 会员卡 Schema 设计与实现
    - [x] 7.2.1 创建会员卡模板相关 Schema（创建、更新、查询、响应）
    - [x] 7.2.2 创建会员卡实例相关 Schema（创建、更新、查询、响应）
    - [x] 7.2.3 创建充值相关 Schema（充值请求、充值记录响应）
    - [x] 7.2.4 创建消费相关 Schema（消费记录查询、响应）
    - [x] 7.2.5 创建查询参数 Schema（分页、筛选、排序）
  - [x] 7.3 会员卡服务层实现
    - [x] 7.3.1 会员卡模板管理服务（CRUD 操作）
    - [x] 7.3.2 会员卡实例管理服务（创建、查询、状态管理）
    - [x] 7.3.3 充值服务（余额增加、记录管理）
    - [x] 7.3.4 扣费服务（余额验证、扣除、消费记录）
    - [x] 7.3.5 创建会员卡服务层测试 fixtures
    - [x] 7.3.6 会员卡服务层单元测试 (扣费流程, 余额验证逻辑)
  - [ ] 7.4 会员卡集成到现有业务
    - [x] 7.4.1 会员注册时默认卡片创建
    - [x] 7.4.2 课程预约时的余额验证逻辑和自动扣费逻辑
    - [x] 7.4.3 课程取消预约时返还对应扣除掉的费用的逻辑
    - [x] 7.4.4 扣费逻辑和返还扣费逻辑都要同步到记录表里 MemberCardOperation，且是和实际扣费逻辑或者取消预约逻辑一次提交的，避免记录丢失
    - [x] 7.4.5 实现（或者验证）对应服务层单元测试，并通过 (预约和取消预约, 扣费，返还扣费，余额验证逻辑)
    - [x] 7.4.6 更新现有课程相关接口（如果需要）
    - [x] 7.4.7 实现（或者验证）对应课程相关接口 API 集成测试
  - [x] 7.5 会员卡 API 接口实现
    - [x] 7.5.1 会员卡模板管理 API
    - [x] 7.5.2 会员卡管理 API
    - [x] 7.5.3 充值管理 API
    - [x] 7.5.4 消费记录查询 API
  - [x] 7.6 会员卡模块测试
    - [x] 7.6.1 会员卡 API 集成测试

- [ ] 8.0 第一期集成测试与交付准备
  - [x] 8.1 核心业务流程集成测试
    - [x] 8.1.1 直接约课流程测试
      - [x] 教师直接开放课表 → 会员预约 → 扣费完成
      - [x] 管理员生成课程 → 会员预约 → 扣费完成
    - [x] 8.1.2 固定位管理流程测试
      - [x] 教师开放固定位 → 会员锁定固定位
      - [x] 教师开放多个固定位 → 会员批量锁定固定位
      - [x] 教师关闭固定位 → 会员锁定失效处理
      - [x] 会员 1 取消固定位锁定 → 位置释放 → 会员 2 锁定该固定位
  - [x] 8.2 跨模块数据一致性测试
    - [x] 8.2.1 多租户数据隔离验证
    - [x] 8.2.2 并发操作数据一致性测试
    - [x] 8.2.3 时间冲突检测准确性测试
  - [ ] 8.3 性能与稳定性测试
    - [ ] 8.3.1 查询性能基准测试
    - [ ] 8.3.2 并发预约压力测试
    - [ ] 8.3.3 大数据量场景测试
  - [ ] 8.4 第一期交付准备
    - [ ] 8.4.1 ~~完善 API 文档~~
    - [ ] 8.4.2 创建完整演示数据脚本
    - [ ] 8.4.3 前端（admin）功能演示准备

## 相关文件

### 新增文件

```
app/features/tags/
├── models.py              # 标签和标签分类数据模型
├── schemas.py             # 标签相关API数据模式
├── service.py             # 标签管理业务逻辑
└── router.py              # 标签管理API路由

app/features/teachers/
├── models.py              # 教师数据模型
├── schemas.py             # 教师相关API数据模式
├── service.py             # 教师管理业务逻辑
├── router.py              # 教师管理API路由
├── fixed_slots_models.py  # 教师固定时间占位表模型
├── fixed_slots_schemas.py # 固定时间占位表API数据模式
├── fixed_slots_service.py # 固定时间占位表业务逻辑
└── fixed_slots_router.py  # 固定时间占位表API路由

app/features/courses/
├── config_models.py       # 课程系统配置模型 (已完成)
├── config_exceptions.py   # 系统配置异常处理 (已完成)
├── config_utils.py        # 系统配置验证工具 (已完成)
├── config_schemas.py      # 系统配置API数据模式 (已完成)
├── config_service.py      # 系统配置业务逻辑
├── config_router.py       # 系统配置API路由(已转移到app/api目录下)
├── scheduled_models.py    # 已排课表模型
├── scheduled_schemas.py   # 已排课表API数据模式
├── scheduled_service.py   # 已排课表业务逻辑
└── scheduled_router.py    # 已排课表API路由

app/features/members/
├── fixed_lock_models.py   # 会员固定课位锁定模型
├── fixed_lock_schemas.py  # 固定课位锁定API数据模式
├── fixed_lock_service.py  # 固定课位锁定业务逻辑
├── fixed_lock_router.py   # 固定课位锁定API路由
├── member_cards_models.py # 会员卡相关数据模型
├── member_cards_schemas.py# 会员卡API数据模式
├── member_cards_service.py# 会员卡业务逻辑
├── member_cards_router.py # 会员卡API路由
└── member_cards_exceptions.py # 会员卡异常处理

tests/fixtures/business/
├── teacher.py             # 教师相关测试fixtures
├── tag.py                 # 标签相关测试fixtures
├── teacher_fixed_slot.py  # 教师固定时间段测试fixtures
├── member_fixed_lock.py   # 会员固定课位锁定测试fixtures
├── course_config.py       # 课程系统配置测试fixtures
├── scheduled_class.py     # 已排课表测试fixtures
├── member_card_template.py# 会员卡模板测试fixtures
├── member_card.py         # 会员卡测试fixtures
├── recharge_record.py     # 充值记录测试fixtures
└── consumption_record.py  # 消费记录测试fixtures

tests/unit/features/teachers/
├── test_teacher_service.py      # 教师服务层测试
├── test_teacher_fixed_slot_service.py # 固定时间段服务层测试
└── test_teacher_fixed_slot_models.py  # 固定时间段模型测试

tests/unit/features/courses/
├── test_config_service.py       # 系统配置服务层测试
└── test_scheduled_service.py    # 已排课表服务层测试

tests/unit/features/members/
├── test_fixed_lock_service.py   # 固定课位锁定服务层测试
├── test_member_cards_service.py # 会员卡服务层测试
├── test_recharge_service.py     # 充值服务层测试
└── test_consumption_service.py  # 消费服务层测试

tests/integration/api/v1/
├── test_teacher_api.py          # 教师API集成测试
├── test_tag_api.py              # 标签API集成测试
├── test_teacher_fixed_slots.py  # 固定时间段API集成测试
├── test_course_config.py        # 系统配置API集成测试
├── test_scheduled_classes.py    # 已排课表API集成测试
├── test_member_fixed_locks.py   # 固定课位锁定API集成测试
├── test_member_cards_api.py     # 会员卡API集成测试
├── test_recharge_api.py         # 充值API集成测试
└── test_consumption_api.py      # 消费记录API集成测试
```

### 需要修改的文件

```
app/api/v1/api.py          # 添加新模块路由
app/db/base.py             # 导入新的数据模型
migrations/                # 新增数据库迁移脚本
tests/conftest.py          # 可能需要添加新的全局fixtures
```

## 重要变更说明

### 3.0 教师固定时间占位表重新实现

**原因**: 原有实现使用 time_slot 枚举，不符合最新数据库设计要求
**变更内容**:

- 字段变更：`time_slot: TimeSlot` → `start_time: TIME`
- 新增字段：`duration_minutes`, `is_visible_to_members`, `created_by`
- 时间处理：从枚举改为 TIME 类型，支持更灵活的时间间隔
- 业务逻辑：重新设计冲突检测、可用性检查等核心功能

### 新增模块说明

- **课程系统配置模块**: 统一管理课程相关的系统配置
- **会员固定课位锁定模块**: 管理会员对固定时间段的锁定
- **已排课表模块**: 管理实际的课程安排和状态

## 开发注意事项

1. **数据库设计**：确保所有表都包含 tenant_id 字段，支持多租户数据隔离
2. **时间处理**：统一使用 TIME 类型和 UTC 时间，注意时区转换
3. **标签系统**：设计灵活的标签分类体系，支持未来扩展
4. **性能优化**：为常用查询字段添加索引，特别是筛选条件
5. **测试覆盖**：确保核心业务逻辑有完整的测试覆盖
6. **API 设计**：遵循项目现有的 API 设计规范和响应格式
7. **错误处理**：使用统一的错误处理机制和错误码体系
8. **模块化设计**：按功能模块组织代码，保持模块间的低耦合
9. **时间段管理**：重点关注时间冲突检测和可用性管理的准确性

### 数据库标准审计字段

所有业务表都包含以下审计字段：

| 字段名     | 类型      | 说明                                   |
| ---------- | --------- | -------------------------------------- |
| created_at | TIMESTAMP | 创建时间，默认当前时间                 |
| updated_at | TIMESTAMP | 更新时间，可为空                       |
| created_by | INTEGER   | 创建者 ID，外键关联 users,members 表等 |

### 软删除字段（可选）

部分表可能需要软删除功能：

| 字段名     | 类型      | 说明                 |
| ---------- | --------- | -------------------- |
| is_deleted | BOOLEAN   | 是否删除，默认 false |
| deleted_at | TIMESTAMP | 删除时间             |
| deleted_by | INTEGER   | 删除者 ID            |

## 验收标准

### 第一期功能验收

- [ ] 所有单元测试和集成测试通过
- [ ] API 文档完整且准确
- [ ] 多租户数据隔离验证通过
- [ ] 教师管理核心功能演示成功
- [ ] 教师固定时间占位表功能演示成功（新设计）
- [ ] 会员固定课位锁定功能演示成功
- [ ] 已排课表基础功能演示成功
- [ ] 课程系统配置功能演示成功
- [ ] 跨模块业务流程验证通过（教师设置时间段 → 会员锁定 → 课程创建）
- [ ] 性能测试满足基本要求
