## 目标

指导 AI 助手基于现有产品需求文档（PRD）创建详细的、分步骤的 Markdown 格式任务列表。该任务列表应引导开发人员基于现有技术栈完成实现工作。

## 输出

- **格式**：Markdown（`.md`）
- **位置**：`/tasks/`
- **文件名**：`tasks-[prd-file-name].md`（例如：`tasks-prd-user-profile-editing.md`）

## 技术栈与项目规范

- **Web 框架**: FastAPI
- **ORM**: SQLModel (SQLAlchemy + Pydantic)
- **数据库**: PostgreSQL
- **认证**: JWT (JSON Web Tokens)
- **密码加密**: bcrypt
- **数据验证**: Pydantic
- **API 文档**: OpenAPI/Swagger (自动生成)

## 流程

1. **接收 PRD 引用**：用户向 AI 指定特定的 PRD 文件
2. **分析 PRD**：AI 读取并分析指定 PRD 的功能需求、用户故事及其他部分
3. **阶段 1：生成父任务**：基于 PRD 分析，创建文件并生成实现该功能所需的主要高级任务。自行判断使用多少个高级任务，可能约为 5 - 10 个。以指定格式向用户展示这些任务（暂不包含子任务）。告知用户："我已根据 PRD 生成高级任务。准备好生成子任务了吗？回复 'Go' 继续。"
4. **等待确认**：暂停并等待用户回复 "Go"
5. **阶段 2：生成子任务**：用户确认后，将每个父任务分解为更小的、可执行的子任务，这些子任务是完成父任务所必需的。确保子任务与父任务逻辑连贯，并涵盖 PRD 中隐含的实现细节
   注意：如任务复杂，则可以对子任务再次拆分一层子任务，以保证任务的可执行性，涵盖核心和必要的细节
6. **识别相关文件**：根据任务和 PRD，识别需要创建或修改的潜在文件。在`相关文件`部分列出这些文件，若适用则包括相应的测试文件
7. **生成最终输出**：将父任务、子任务、相关文件和注释合并为最终的 Markdown 结构
8. **保存任务列表**：将生成的文档保存在`/tasks/`目录中，文件名为`tasks-[prd-file-name].md`，其中`[prd-file-name]`与输入 PRD 文件的基名匹配

## 输出格式

生成的任务列表尽量遵循以下结构：

````markdown
## 📁 项目结构

```
ks-english-admin-backend/
├── app/
│   ├── main.py                 # FastAPI应用入口，生命周期管理
│   ├── config.py              # 配置管理（已弃用，使用core/config.py）
│   ├── core/                  # 核心组件
│   │   ├── config.py          # 配置管理
│   │   ├── dependencies.py    # 依赖注入
│   │   ├── security.py        # 安全工具
│   │   ├── logging.py         # 日志工具
│   │   └── context.py         # 上下文管理
│   ├── db/                    # 数据库层
│   │   ├── base.py            # 数据库基础配置和多租户支持
│   │   └── session.py         # 数据库会话管理
│   ├── features/              # 业务模块（垂直分层架构）
│   │   ├── auth/              # 认证模块
│   │   ├── users/             # 用户管理模块
│   │   ├── tenants/           # 租户管理模块
│   │   ├── members/           # 会员管理模块
│   │   ├── teachers/          # 教师管理模块
│   │   ├── tags/              # 标签管理模块
│   │   ├── courses/           # 课程系统模块
│   │   └── shared/            # 共享模块
│   │       ├── models.py      # 全局共享模型
│   │       ├── schemas.py     # 共享数据模式
│   │       └── exceptions.py  # 共享异常定义
│   ├── models/                # 数据模型
│   │   └── shared/            # 全局共享数据模型
│   ├── api/                   # API路由
│   │   ├── common/            # 通用API组件
│   │   │   ├── responses.py   # 统一响应格式
│   │   │   ├── exceptions.py  # 异常处理
│   │   │   ├── pagination.py  # 分页工具
│   │   │   └── docs.py        # API文档配置
│   │   └── v1/                # API v1
│   │       ├── api.py         # 路由聚合
│   │       ├── admin/         # 管理端API
│   │       ├── member/        # 会员端API
│   │       └── public/        # 公共API
│   └── utils/                 # 工具函数
│       ├── security.py        # 安全工具
│       └── json_encoder.py    # JSON编码器
├── tests/                     # 测试目录
│   ├── fixtures/              # 测试夹具
│   │   ├── database.py        # 数据库fixtures
│   │   ├── client.py          # API客户端fixtures
│   │   └── business/          # 业务数据fixtures
│   ├── unit/                  # 单元测试
│   │   ├── features/          # 业务模块单元测试
│   │   └── utils/             # 工具函数测试
│   ├── integration/           # 集成测试
│   │   └──  api/v1/            # API集成测试
│   ├── e2e/                   # 端到端测试
│   └── performance/           # 性能测试
├── scripts/                   # 工具脚本
│   ├── test.py                # 统一测试脚本
│   ├── init_database.py       # 数据库初始化
│   └── test_enhanced.py       # 增强测试脚本
├── docs/                      # 项目文档
│   ├── 数据库架构/             # 数据库设计文档
│   └── 权限系统/              # 权限系统文档
├── tasks/                     # 任务和设计文档
│   ├── database-design/       # 数据库设计
│   ├── development-tips/      # 开发经验
│   └── *.md                   # 需求和任务文档
├── conftest.py                # pytest全局配置
├── pytest.ini                # pytest运行配置
├── requirements.txt           # 项目依赖
├── run.py                     # 生产环境启动脚本
├── quick_start.py             # 开发环境快速启动
├── .env.example              # 环境变量示例
└── README.md
```

## 任务

- [ ] 1.0 父任务标题
  - [ ] 1.1 [子任务描述 1.1]
  - [ ] 1.2 [子任务描述 1.2]
- [ ] 2.0 父任务标题
  - [ ] 2.1 [子任务描述 2.1]
- [ ] 3.0 父任务标题（若纯为结构性或配置性任务，可能不需要子任务）
- [ ] 4.0 父任务标题（若任务很复杂，可以考虑拆分三级任务）
  - [ ] 4.1 [子任务描述 4.1]
    - [ ] 4.11 [子任务描述 4.11]
````

## 注意事项

1. 创建或修改数据库模型时，确保使用 SQLModel
2. API 路由应遵循 RESTful 设计原则，但注意本项目永远不要使用 PUT 和 DELETE，总是用 POST 来替代
3. 始终考虑多租户支持，确保在 service 层使用 UserContext 依赖
4. 使用枚举而非字符串表示状态和角色
5. 为所有功能编写测试用例，至少包括单元测试

## 交互模型

该流程明确要求在生成父任务后暂停，以获取用户确认（"Go"），然后再继续生成详细的子任务。这确保在深入细节之前，高级计划与用户期望一致。

## 目标受众

假定任务列表的主要读者是将实现该功能的**初级开发人员**。
