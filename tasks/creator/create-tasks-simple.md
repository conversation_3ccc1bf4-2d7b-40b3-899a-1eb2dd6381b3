## 目标

指导 AI 助手生成详细的分步任务列表，指引开发人员实现功能。

## 技术栈与规范概要

- **核心技术**: FastAPI, SQLModel, PostgreSQL, JWT
- **项目规范**:
  - API 规则: 只用 POST 请求，不用 PUT/DELETE
  - 多租户支持: 使用 UserContext 依赖而非 tenant_id 参数
  - 使用枚举表示状态和角色
  - 按功能模块组织代码

## 流程

1. **分析 PRD**: 读取并理解 PRD 功能需求
2. **生成父任务**: 创建 5-10 个高级任务，暂停等待确认
3. **用户确认**: 用户回复"Go"继续
4. **生成子任务**: 将父任务分解为具体可执行子任务
5. **识别相关文件**: 列出需要创建/修改的文件
6. **最终输出**: 整合为完整任务列表，包含父任务、子任务和相关文件

## 输出格式

```markdown
## 任务

- [ ] 1.0 父任务标题
  - [ ] 1.1 子任务描述
  - [ ] 1.2 子任务描述
- [ ] 2.0 父任务标题
  - [ ] 2.1 子任务描述

## 相关文件

- `app/features/[模块名]/models.py` - 数据模型
- `app/features/[模块名]/service.py` - 业务逻辑
- `app/features/[模块名]/router.py` - API 路由
- `app/api/v1/[端点组]/[功能名].py` - API 端点
- `tests/unit/features/[模块名]/test_[功能名]_service.py` - 测试
```

## 注意事项

- 遵循现有代码模式和命名约定
- 确保新功能支持多租户隔离
- 为新代码编写适当测试
- 使用 UserContext 依赖获取上下文信息
