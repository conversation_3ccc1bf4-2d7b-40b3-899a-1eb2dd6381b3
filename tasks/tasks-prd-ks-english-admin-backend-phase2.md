# KS English Admin Backend - 第二期开发任务列表

基于 PRD: `prd-ks-english-admin-backend-complete.md` 第二期功能需求

## 📁 项目结构

```
ks-english-admin-backend/
├── app/
│   ├── features/              # 业务模块（垂直分层架构）
│   │   ├── courses/           # 课程系统模块
│   │   │   ├── scheduling/    # 固定课排课子模块
│   │   │   │   ├── models.py      # 固定课排课相关数据模型
│   │   │   │   ├── schemas.py     # 固定课排课API数据模式
│   │   │   │   ├── service.py     # 固定课排课业务逻辑
│   │   │   │   ├── router.py      # 固定课排课API路由
│   │   │   │   ├── algorithm.py   # 固定课排课算法核心
│   │   │   │   ├── exceptions.py  # 固定课排课异常定义
│   │   │   │   └── utils.py       # 固定课排课工具函数
│   │   │   └── operations/    # 操作记录子模块
│   │   │       ├── models.py      # 操作记录数据模型
│   │   │       ├── schemas.py     # 操作记录API数据模式
│   │   │       ├── service.py     # 操作记录业务逻辑
│   │   │       └── router.py      # 操作记录API路由
│   │   └── statistics/        # 统计分析模块
│   │       ├── models.py          # 统计相关数据模型
│   │       ├── schemas.py         # 统计API数据模式
│   │       ├── service.py         # 统计业务逻辑
│   │       └── router.py          # 统计API路由
│   ├── api/                   # API路由
│   │   └── v1/                # API v1
│   │       └── admin/         # 管理端API
│   │           ├── scheduling.py  # 固定课排课管理API
│   │           ├── operations.py  # 操作记录管理API
│   │           └── statistics.py  # 统计分析API
├── tests/                     # 测试目录
│   ├── fixtures/              # 测试夹具
│   │   └── business/          # 业务数据fixtures
│   │       ├── scheduling.py      # 固定课排课测试fixtures
│   │       ├── operations.py      # 操作记录测试fixtures
│   │       └── statistics.py      # 统计分析测试fixtures
│   ├── unit/                  # 单元测试
│   │   └── features/          # 业务模块单元测试
│   │       ├── courses/
│   │       │   ├── test_scheduling_service.py    # 固定课排课服务测试
│   │       │   ├── test_scheduling_algorithm.py  # 固定课排课算法测试
│   │       │   └── test_operations_service.py    # 操作记录服务测试
│   │       └── statistics/
│   │           └── test_statistics_service.py    # 统计服务测试
│   ├── integration/           # 集成测试
│   │   └── api/v1/admin/      # API集成测试
│   │       ├── test_scheduling_api.py     # 固定课排课API测试
│   │       ├── test_operations_api.py     # 操作记录API测试
│   │       └── test_statistics_api.py     # 统计分析API测试
│   └── e2e/                   # 端到端测试
│       └── test_scheduling_workflow.py    # 固定课排课完整流程测试
└── scripts/                   # 工具脚本
    └── scheduling/            # 固定课排课相关脚本
        ├── manual_schedule.py     # 手动触发固定课排课脚本
        └── schedule_monitor.py    # 固定课排课监控脚本
```

## 任务

- [x] 1.0 固定课排课数据模型设计与实现

  - [ ] 1.1 设计固定课排课任务表(fixed_schedule_tasks)
  - [ ] 1.2 设计固定课排课任务日志表(fixed_schedule_task_logs)
  - [ ] 1.3 创建固定课排课相关数据模型和 Schema
  - [ ] 1.4 创建数据库索引和约束优化

- [x] 2.0 固定课排课核心算法开发

  - [x] 2.1 实现固定课排课算法核心逻辑
  - [x] 2.2 实现教师优先级排序算法
  - [x] 2.3 实现时间冲突检测算法
  - [x] 2.4 实现会员余额验证和扣费逻辑
  - [x] 2.5 实现固定课排课结果统计功能

- [x] 3.0 固定课排课任务管理系统

  - [x] 3.1 实现固定课排课任务创建和管理服务
  - [x] 3.2 实现固定课排课任务执行引擎
  - [x] 3.3 实现固定课排课任务状态管理
  - [x] 3.4 实现固定课排课任务日志记录系统
  - [x] 3.5 实现固定课排课任务失败重试机制

- [x] 4.0 操作记录和审计系统

  - [x] 4.1 设计课程操作记录表(scheduled_class_operation_logs)
  - [x] 4.2 设计教师固定时间段操作记录表(teacher_fixed_slot_operation_logs)
  - [x] 4.3 设计会员固定位锁定操作记录表(member_fixed_lock_operation_logs)
  - [x] 4.4 实现操作记录服务和 API 接口
  - [x] 4.5 实现操作记录查询和统计功能

- [ ] 5.0 高级数据统计和报表系统

  - [ ] 5.1 实现收入统计报表服务
  - [ ] 5.2 实现课程统计报表服务
  - [ ] 5.3 实现会员消费统计服务
  - [ ] 5.4 实现教师工作量统计服务
  - [ ] 5.5 实现统计数据导出功能 6

- [x] 6.0 固定课排课 API 接口开发

  - [x] 6.1 实现手动触发固定课排课接口
  - [x] 6.2 实现固定课排课任务查询接口
  - [x] 6.3 实现固定课排课日志查询接口
  - [x] 6.4 实现固定课排课结果统计接口
  - [x] 6.5 集成固定课排课 API 到主路由

- [ ] 7.0 第二期测试开发

  - [x] 7.1 编写固定课排课算法单元测试
  - [x] 7.2 编写固定课排课服务层单元测试
  - [x] 7.3 编写操作记录服务单元测试
  - [x] 7.4 编写统计服务单元测试
  - [x] 7.5 编写固定课排课 API 集成测试（已完成 15 个测试用例，全部通过）
  - [x] 7.6 编写固定课排课端到端测试

- [x] 8.0 第二期集成测试与交付准备
  - [x] 8.1 固定课排课完整流程集成测试
  - [x] 8.2 固定课排课性能测试和优化
  - [ ] 8.3 固定课排课数据一致性验证
  - [ ] 8.4 第二期功能演示准备
  - [ ] 8.5 第二期文档完善和交付

## 相关文件

### 新增文件

```
app/features/courses/scheduling/
├── models.py              # 固定课排课任务和日志数据模型
├── schemas.py             # 固定课排课API数据模式
├── service.py             # 固定课排课业务逻辑
├── router.py              # 固定课排课API路由
├── algorithm.py           # 固定课排课算法核心
├── exceptions.py          # 固定课排课异常定义
└── utils.py               # 固定课排课工具函数

app/features/courses/operations/
├── models.py              # 操作记录数据模型
├── schemas.py             # 操作记录API数据模式
├── service.py             # 操作记录业务逻辑
└── router.py              # 操作记录API路由

app/features/statistics/
├── models.py              # 统计相关数据模型
├── schemas.py             # 统计API数据模式
├── service.py             # 统计业务逻辑
└── router.py              # 统计API路由

app/api/v1/admin/
├── scheduling.py          # 固定课排课管理API
├── operations.py          # 操作记录管理API
└── statistics.py          # 统计分析API

tests/fixtures/business/
├── scheduling.py          # 固定课排课测试fixtures
├── operations.py          # 操作记录测试fixtures
└── statistics.py          # 统计分析测试fixtures

tests/unit/features/courses/
├── test_scheduling_service.py    # 固定课排课服务测试
├── test_scheduling_algorithm.py  # 固定课排课算法测试
└── test_operations_service.py    # 操作记录服务测试

tests/unit/features/statistics/
└── test_statistics_service.py    # 统计服务测试

tests/integration/api/v1/admin/
├── test_scheduling_api.py         # 固定课排课API测试
├── test_operations_api.py         # 操作记录API测试
└── test_statistics_api.py         # 统计分析API测试

tests/e2e/
└── test_scheduling_workflow.py   # 固定课排课完整流程测试

scripts/scheduling/
├── manual_schedule.py             # 手动触发固定课排课脚本
└── schedule_monitor.py            # 固定课排课监控脚本
```

### 需要修改的文件

```
app/api/v1/api.py          # 添加新模块路由
app/db/base.py             # 导入新的数据模型
```

## 重要变更说明

### 第二期核心功能

**固定课排课系统**：

- 实现基于会员固定课位锁定的排课算法
- 暂时只支持手动触发
- 完整的排课任务管理和日志记录系统
- 排课过程中的冲突检测和余额验证

**操作记录系统**：

- 分离设计的三张操作记录表，分别记录课程、教师时间段、会员锁定的操作历史
- 完整的操作审计，支持纠纷处理和数据分析
- 操作记录归档策略，支持长期数据保存

**高级统计报表**：

- 收入统计：充值收入、课程消费收入分析
- 课程统计：出勤率、取消率、教师工作量分析
- 会员统计：消费行为、活跃度、留存分析
- 支持数据导出功能

### 排课算法核心规则

1. **排课维度和优先级**：

   - 以教师为主维度进行排课
   - 教师优先级：欧美和南非教师优先，其次菲教
   - 教师排序：按编号从大到小排序

2. **排课流程**：

   - 按会员分组处理，计算总费用
   - 检查会员卡余额是否充足
   - 生成课程记录的同时立即扣费
   - 余额不足时根据配置跳过或终止

3. **冲突检测**：

   - 教师时间冲突检测
   - 会员时间冲突检测
   - 教师固定位开放状态检测

4. **排课周期**：
   - 默认生成未来 4 周的课程安排
   - 支持自定义排课周数
   - 从指定日期开始排课，但该日期必须为周一

### 数据库设计要点

#### 固定课排课任务表(fixed_schedule_tasks)

- id, tenant_id, task_name, start_date, weeks_count
- teacher_priority_rule, balance_insufficient_action
- status(pending/running/completed/failed), created_by
- total_teachers, successful_teachers, failed_teachers, total_classes

#### 固定课排课任务日志表(fixed_schedule_task_logs)

- id, task_id, log_level(INFO/WARN/ERROR), message
- teacher_id, member_id, class_datetime, operation_type
- created_at

#### 操作记录表设计

- **scheduled_class_operation_logs**：课程操作记录
- **teacher_fixed_slot_operation_logs**：教师时间段操作记录
- **member_fixed_lock_operation_logs**：会员锁定操作记录

每张表包含：

- 操作类型、操作前后状态、操作人信息
- 业务对象 ID、操作时间、操作原因
- 详细的状态变更记录

## 开发注意事项

1. **排课算法设计**：

   - 确保排课算法的幂等性，避免重复排课
   - 实现完整的事务管理，确保排课过程的数据一致性
   - 支持排课失败的回滚机制

2. **性能优化**：

   - 排课算法需要处理大量数据，注意查询优化
   - 使用批量操作减少数据库交互次数
   - 考虑排课过程的内存使用优化

3. **错误处理**：

   - 完整的异常处理机制，记录详细的错误信息
   - 支持排课失败的重试机制
   - 异常情况的告警和通知

4. **监控和日志**：

   - 详细的排课过程日志记录
   - 排课性能监控和统计
   - 排课结果的验证和检查

5. **测试策略**：
   - 排课算法的单元测试，覆盖各种边界情况
   - 排课流程的集成测试，验证完整业务流程
   - 性能测试，确保排课效率满足要求

## 验收标准

### 第二期功能验收

- [ ] 固定课排课算法正确实现，排课成功率>95%
- [ ] 排课任务管理系统完整实现，暂时只支持手动触发
- [ ] 操作记录系统完整实现，支持完整的操作审计
- [ ] 统计报表系统实现，提供丰富的数据分析功能
- [ ] 所有单元测试和集成测试通过
- [ ] 排课性能满足要求，支持大数据量场景
- [ ] 排课数据一致性验证通过
- [ ] 完整的排课流程演示成功

### 技术验收标准

- [ ] 代码质量符合项目规范
- [ ] API 文档完整且准确
- [ ] 测试覆盖率达到要求
- [ ] 性能指标满足预期
- [ ] 错误处理机制完善
- [ ] 监控和日志系统完整
