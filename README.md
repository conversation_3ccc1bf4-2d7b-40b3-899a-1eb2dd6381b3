# 在线网约课系统 SaaS 后端

基于 **FastAPI + SQLModel + PostgreSQL** 的现代化在线网约课系统 SaaS 后端，采用多租户架构为外教课机构提供完整的管理解决方案。

## 🚀 快速开始

### 环境要求

- Python 3.9+
- PostgreSQL 12+
- Git

### 安装步骤

```bash
# 1. 克隆项目
git clone <repository-url>
cd ks-english-admin-backend

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息

# 5. 创建数据库
createdb course_booking_db

# 6. 启动应用
uvicorn app.main:app --host 0.0.0.0 --port 8012 --reload
uvicorn app.main:app --host 0.0.0.0 --port 3000 --reload
# 或
python quick_start.py  # 开发环境
# 或
python run.py         # 生产环境
```

### 访问应用

- **API 文档**: http://localhost:8012/docs
- **ReDoc**: http://localhost:8012/redoc
- **健康检查**: http://localhost:8012/health

## 🏗️ 技术架构

### 技术栈

- **Web 框架**: FastAPI
- **ORM**: SQLModel (SQLAlchemy + Pydantic)
- **数据库**: PostgreSQL (多 Schema 租户架构)
- **认证**: JWT (JSON Web Tokens)
- **密码加密**: bcrypt
- **数据验证**: Pydantic
- **API 文档**: OpenAPI/Swagger (自动生成)
- **测试框架**: pytest + httpx + TestClient

### 核心特性

- ✅ **多租户 SaaS 架构**: 共享数据库+RLS 行级安全，数据完全隔离
- ✅ **现代化异步 API**: 高性能 FastAPI 框架，支持高并发
- ✅ **类型安全**: SQLModel 提供完整类型检查和验证
- ✅ **自动生成文档**: OpenAPI/Swagger 文档，支持在线调试
- ✅ **JWT 身份认证**: 无状态认证机制，支持分布式部署
- ✅ **完整测试体系**: 单元测试、集成测试、端到端测试
- ✅ **垂直分层架构**: 模块化设计，每个业务模块独立完整
- ✅ **统一异常处理**: 全局异常捕获，统一错误响应格式
- ✅ **统一 API 响应**: http_code + business_code 双码设计
- ✅ **租户生命周期管理**: 注册、激活、暂停、计费
- ✅ **套餐模板系统**: 灵活的订阅计划配置
- ✅ **教师管理系统**: 教师信息、固定时间占位管理
- ✅ **标签分类系统**: 灵活的标签分类和管理
- ✅ **课程系统配置**: 课程参数和调度配置
- ✅ **审计日志**: 完整的操作追踪和记录
- ✅ **密码安全存储**: bcrypt 加密，安全可靠

## 🏗️ 多租户架构

### 数据库设计

- 共享数据库，共享 Schema (Shared Database, Shared Schema with Tenant ID)
- 租户 tenant, 用户 user（租户下属管理员），会员 member（租户发展的客户）

### 租户管理

- 自动租户注册和 Schema 创建
- 套餐模板管理（试用版、基础版、标准版、高级版、企业版）
- 租户状态管理（试用、活跃、暂停、终止、过期）
- API 密钥和自定义域名支持

## 📁 项目结构

```
ks-english-admin-backend/
├── app/
│   ├── main.py                 # FastAPI应用入口，生命周期管理
│   ├── config.py              # 配置管理（已弃用，使用core/config.py）
│   ├── core/                  # 核心组件
│   │   ├── config.py          # 配置管理
│   │   ├── dependencies.py    # 依赖注入
│   │   ├── security.py        # 安全工具
│   │   ├── logging.py         # 日志工具
│   │   └── context.py         # 上下文管理
│   ├── db/                    # 数据库层
│   │   ├── base.py            # 数据库基础配置和多租户支持
│   │   └── session.py         # 数据库会话管理
│   ├── features/              # 业务模块（垂直分层架构）
│   │   ├── auth/              # 认证模块
│   │   ├── users/             # 用户管理模块
│   │   ├── tenants/           # 租户管理模块
│   │   ├── members/           # 会员管理模块
│   │   ├── teachers/          # 教师管理模块
│   │   ├── tags/              # 标签管理模块
│   │   ├── courses/           # 课程系统模块
│   │   └── shared/            # 共享模块
│   │       ├── models.py      # 全局共享模型
│   │       ├── schemas.py     # 共享数据模式
│   │       └── exceptions.py  # 共享异常定义
│   ├── models/                # 数据模型
│   │   └── shared/            # 全局共享数据模型
│   ├── api/                   # API路由
│   │   ├── common/            # 通用API组件
│   │   │   ├── responses.py   # 统一响应格式
│   │   │   ├── exceptions.py  # 异常处理
│   │   │   ├── pagination.py  # 分页工具
│   │   │   └── docs.py        # API文档配置
│   │   └── v1/                # API v1
│   │       ├── api.py         # 路由聚合
│   │       ├── admin/         # 管理端API
│   │       ├── member/        # 会员端API
│   │       └── public/        # 公共API
│   └── utils/                 # 工具函数
│       ├── security.py        # 安全工具
│       └── json_encoder.py    # JSON编码器
├── tests/                     # 测试目录
│   ├── fixtures/              # 测试夹具
│   │   ├── database.py        # 数据库fixtures
│   │   ├── client.py          # API客户端fixtures
│   │   └── business/          # 业务数据fixtures
│   ├── unit/                  # 单元测试
│   │   ├── features/          # 业务模块单元测试
│   │   └── utils/             # 工具函数测试
│   ├── integration/           # 集成测试
│   │   └──  api/v1/            # API集成测试
│   ├── e2e/                   # 端到端测试
│   └── performance/           # 性能测试
├── scripts/                   # 工具脚本
│   ├── test.py                # 统一测试脚本
│   ├── init_database.py       # 数据库初始化
│   └── test_enhanced.py       # 增强测试脚本
├── docs/                      # 项目文档
│   ├── 数据库架构/             # 数据库设计文档
│   └── 权限系统/              # 权限系统文档
├── tasks/                     # 任务和设计文档
│   ├── database-design/       # 数据库设计
│   ├── development-tips/      # 开发经验
│   └── *.md                   # 需求和任务文档
├── conftest.py                # pytest全局配置
├── pytest.ini                # pytest运行配置
├── requirements.txt           # 项目依赖
├── run.py                     # 生产环境启动脚本
├── quick_start.py             # 开发环境快速启动
├── .env.example              # 环境变量示例
└── README.md
```

## 🧪 测试

### 运行测试

```bash
# 使用统一测试脚本（推荐）
python scripts/test.py unit          # 运行单元测试
python scripts/test.py api           # 运行API集成测试
python scripts/test.py all           # 运行所有测试

# 运行特定模块测试
python scripts/test.py unit -k "teacher"
python scripts/test.py api -k "tag"

# 生成覆盖率报告
python scripts/test.py unit --cov

# 直接使用pytest（不推荐，建议用上面的脚本）
pytest tests/unit/ -v
pytest tests/integration/api/ -v
```

### 测试架构

- **单元测试** (`tests/unit/`): 业务逻辑和服务层测试
- **集成测试** (`tests/integration/`): API 端点和数据库集成测试
- **端到端测试** (`tests/e2e/`): 完整业务流程测试
- **性能测试** (`tests/performance/`): 性能基准测试

### 测试覆盖

- ✅ **用户模块**: 完整的 service + API 测试
- ✅ **租户模块**: 完整的 service + API 测试
- ✅ **会员模块**: 完整的 service + API 测试
- ✅ **教师模块**: 完整的 service + API 测试
- ✅ **标签模块**: 完整的 service + API 测试
- ✅ **课程配置**: 完整的 service + API 测试
- ✅ **认证系统**: JWT 认证和权限测试

## 🔧 开发指南

### 添加新功能模块

采用**垂直分层架构**，每个业务模块包含完整的功能：

1. **创建模块目录** (`app/features/new_module/`)
2. **定义数据模型** (`models.py`) - 数据库模型
3. **定义数据模式** (`schemas.py`) - API 输入输出模型
4. **编写业务逻辑** (`service.py`) - 核心业务逻辑
5. **定义异常处理** (`exceptions.py`) - 模块特定异常
6. **实现 API 端点** (`router.py`) - RESTful API
7. **编写测试用例** (`tests/unit/features/new_module/`)

### 模块结构示例

```
app/features/example/
├── __init__.py
├── models.py          # SQLModel数据库模型
├── schemas.py         # Pydantic API模型
├── service.py         # 业务逻辑服务
├── exceptions.py      # 业务异常定义
└── router.py          # FastAPI路由
```

### 代码规范

- 遵循 PEP 8 代码风格
- 使用完整的类型提示
- 编写详细的文档字符串
- 保持函数单一职责
- 异常处理使用统一的业务异常类
- API 响应使用统一的响应模型

### 环境变量配置

```bash
# .env 文件示例
DATABASE_URL=postgresql://user:password@localhost:5432/course_booking_db
SECRET_KEY=your-secret-key-here
DEBUG=True
```

## 📚 API 使用

### 认证

```bash
# 获取 Token
curl -X POST "http://localhost:8012//api/v1/auth/admin/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password"}'

# 使用 Token
curl -H "Authorization: Bearer <token>" \
  "http://localhost:8012/api/v1/users/me"
```

### 租户管理

```bash
# 创建租户
curl -X POST "http://localhost:8012/api/v1/tenants/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "示例机构",
    "code": "demo_org",
    "contact_email": "<EMAIL>"
  }'

# 获取租户列表
curl "http://localhost:8012/api/v1/tenants/"

# 应用套餐
curl -X POST "http://localhost:8012/api/v1/tenants/1/apply-plan/basic"
```

## 🔒 安全说明

### 生产环境配置

1. **更换密钥**: 修改 `SECRET_KEY` 为强密码
2. **数据库安全**: 使用强密码，限制访问权限
3. **HTTPS**: 生产环境启用 HTTPS
4. **CORS**: 配置正确的跨域设置

### 认证机制

- JWT Token 认证
- bcrypt 密码加密
- API 密钥管理
- 租户级别访问控制

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**

   ```bash
   # 检查 PostgreSQL 服务
   brew services start postgresql  # macOS
   sudo systemctl start postgresql # Linux
   ```

2. **依赖安装失败**

   ```bash
   # 升级 pip
   pip install --upgrade pip
   # 清理缓存
   pip cache purge
   ```

3. **测试失败**
   ```bash
   # 检查测试数据库
   createdb course_booking_test_db
   ```

## 📝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -m 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建 Pull Request

### 提交规范

```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 重构代码
test: 添加测试
chore: 构建过程或辅助工具的变动
```

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🔗 相关链接

- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [SQLModel 文档](https://sqlmodel.tiangolo.com/)
- [PostgreSQL 文档](https://www.postgresql.org/docs/)
- [pytest 文档](https://docs.pytest.org/)
