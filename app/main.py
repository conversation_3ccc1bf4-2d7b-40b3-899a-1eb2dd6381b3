from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from app.core.config import settings
from app.core.logging import setup_logging, get_logger
from app.db.base import create_db_and_tables, init_global_data
from app.api.v1.api import api_router
from fastapi.openapi.utils import get_openapi
from app.api.common.exceptions import (
    APIException,
    api_exception_handler,
    validation_exception_handler,
    general_exception_handler
)
from app.api.common.docs import get_openapi_config

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    print("🚀 FastAPI应用启动中...")

    # 初始化日志系统 - 智能检测环境
    import os

    # 检查是否在测试环境中，如果是，保持现有的日志配置
    if os.getenv("TESTING") == "true":
        # 测试环境：不重新初始化日志系统，使用测试的配置
        logger = get_logger("app")
        logger.info("检测到测试环境，保持现有日志配置")
    else:
        # 生产/开发环境：正常初始化日志系统
        logger = setup_logging()
        logger.info("日志系统初始化完成")

    create_db_and_tables()
    init_global_data()
    print("✅ 数据库初始化完成")
    logger.info("数据库初始化完成")

    yield

    # 关闭时清理（如果需要）
    print("👋 FastAPI应用关闭")
    logger.info("FastAPI应用关闭")


# 创建FastAPI应用实例
openapi_config = get_openapi_config()
app = FastAPI(
    title=openapi_config["title"],
    version=openapi_config["version"],
    description=openapi_config["description"],
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    # 生成默认 OpenAPI 文档
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=f"{settings.OPENAPI_TOP_DESCRIPTION}\n\n" + (app.description or ""),
        routes=app.routes,
    )

    # 可选：最小化常见错误响应（保持文档简短），可通过环境变量关闭
    if settings.OPENAPI_MINIMAL_RESPONSES:
        status_codes_to_remove = [400, 401, 403, 404, 422]
        for path in openapi_schema.get("paths", {}).values():
            for method in path.values():
                if "responses" in method:
                    for code in status_codes_to_remove:
                        method["responses"].pop(str(code), None)

    app.openapi_schema = openapi_schema
    return openapi_schema

# 覆盖默认的 OpenAPI 生成函数
app.openapi = custom_openapi

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # //TODO: 生产环境应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加路由中间件
@app.middleware("http")
async def log_route(request: Request, call_next):
    # 检查是否启用路由日志记录
    if not settings.ENABLE_ROUTE_LOGGING:
        return await call_next(request)
    
    # 先处理请求，这样路由匹配就会完成
    response = await call_next(request)
    
    try:
        # 获取实际匹配的路由信息
        route_info = "Unknown"
        route_name = "Unknown"
        
        # 从request.scope中获取路由信息
        if 'route' in request.scope and request.scope['route']:
            route = request.scope['route']
            
            # 获取路由路径模式
            if hasattr(route, 'path'):
                route_info = route.path
            elif hasattr(route, 'path_regex'):
                route_info = str(route.path_regex.pattern)
            
            # 获取路由名称（通常是函数名）
            if hasattr(route, 'name'):
                route_name = route.name
            elif hasattr(route, 'endpoint') and hasattr(route.endpoint, '__name__'):
                route_name = route.endpoint.__name__
        
        # 输出详细的路由匹配信息
        print(f"请求路径: {request.url.path}")
        print(f"匹配的路由模式: {route_info}")
        print(f"路由函数名: {route_name}")
        print(f"HTTP方法: {request.method}")
        print(f"响应状态码: {response.status_code}")
        print("-" * 50)
        
    except Exception as e:
        print(f"获取路由信息失败: {e}")
        print(f"请求路径: {request.url.path} (方法: {request.method})")
        print("-" * 50)
    
    return response


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用 {settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": settings.APP_NAME}


# 注册异常处理器
app.add_exception_handler(APIException, api_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8012,
        reload=settings.DEBUG
    ) 