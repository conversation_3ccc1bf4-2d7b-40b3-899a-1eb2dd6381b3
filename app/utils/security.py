from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
from app.core.config import settings
import os


def get_bcrypt_rounds() -> int:
    """获取bcrypt rounds配置"""
    # 从环境变量读取，默认使用性能优化的配置
    env_rounds = os.getenv("BCRYPT_ROUNDS")
    if env_rounds:
        return int(env_rounds)

    # 根据环境选择合适的rounds
    if os.getenv("ENVIRONMENT") == "production":
        print("get_bcrypt_rounds 生产环境")
        return 12  # 生产环境：严格，登录qps可以允许比较低
    elif os.getenv("TESTING") == "true":
        print("get_bcrypt_rounds 测试环境")
        return 4   # 测试环境：最快配置
    else:
        print("get_bcrypt_rounds 开发环境")
        return 8   # 开发环境：高性能配置


# 优化的密码加密上下文
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=get_bcrypt_rounds()
)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: Dict[Any, Any], expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now() + expires_delta
    else:
        expire = datetime.now() + timedelta(minutes=15)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return payload
    except JWTError:
        return None


def create_verification_code() -> str:
    """生成验证码"""
    import random
    return str(random.randint(1000, 9999)) 