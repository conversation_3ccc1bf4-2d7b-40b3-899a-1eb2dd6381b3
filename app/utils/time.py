"""时间工具：统一全局时间策略

- 内部一律使用 UTC-aware datetime（timezone.utc）
- 对外展示/序列化按需转换
"""
from __future__ import annotations

from datetime import datetime, timezone
from typing import Optional

try:
    from zoneinfo import ZoneInfo  # Python 3.9+
except Exception:  # pragma: no cover
    ZoneInfo = None  # type: ignore


def now_utc() -> datetime:
    """返回当前 UTC 时间（timezone-aware）。"""
    return datetime.now(timezone.utc)


def now_local(tz: str = "Asia/Shanghai") -> datetime:
    """返回指定时区的当前时间（timezone-aware）。

    默认 Asia/Shanghai。如果系统不支持 zoneinfo，该函数将退化为本地时间（naive）。
    """
    if ZoneInfo is None:
        # 退化：返回本地时间（naive）
        return datetime.now()
    return datetime.now(ZoneInfo(tz))

