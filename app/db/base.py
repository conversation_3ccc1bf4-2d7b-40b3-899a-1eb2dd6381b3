"""
数据库基础配置和初始化
"""
import logging
from sqlmodel import Session, text
from app.core.config import settings

logger = logging.getLogger(__name__)


def create_database_if_not_exists():
    """创建数据库（如果不存在）"""
    import psycopg2
    from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
    from urllib.parse import urlparse

    # 解析数据库URL
    parsed = urlparse(settings.DATABASE_URL)

    # 连接到默认的postgres数据库
    conn = psycopg2.connect(
        host=parsed.hostname,
        port=parsed.port,
        user=parsed.username,
        password=parsed.password,
        database="postgres"
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)

    cursor = conn.cursor()

    # 获取数据库名称（去掉开头的斜杠）
    db_name = parsed.path.lstrip('/')

    # 检查数据库是否存在
    cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{db_name}'")
    exists = cursor.fetchone()

    if not exists:
        cursor.execute(f"CREATE DATABASE {db_name}")
        logger.info(f"Database {db_name} created")
    else:
        logger.info(f"Database {db_name} already exists")

    cursor.close()
    conn.close()


def setup_row_level_security():
    """设置行级安全策略 - 使用RLS v2实现"""
    from app.db.rls_v2 import setup_rls_policies_v2

    logger.info("Setting up Row Level Security using RLS v2...")

    setup_rls_policies_v2()

    logger.info("Row Level Security setup completed using RLS v2")


def init_global_data():
    """初始化全局数据"""
    from app.db.session import engine

    with Session(engine) as session:
        # 重置租户上下文
        session.exec(text("RESET app.current_tenant_id"))

        # 检查是否已有租户计划模板
        try:
            existing_templates = session.exec(text("SELECT count(*) FROM tenant_plan_templates")).first()
            if existing_templates == 0:
                logger.info("No tenant plan templates found, skipping creation")
        except Exception as e:
            logger.warning(f"Could not check tenant plan templates: {e}")

        # 检查是否已有系统配置
        try:
            existing_configs = session.exec(text("SELECT count(*) FROM system_configs")).first()
            if existing_configs == 0:
                logger.info("No system configs found, skipping creation")
        except Exception as e:
            logger.warning(f"Could not check system configs: {e}")

        # 检查是否已有超级管理员
        try:
            super_admin = session.exec(text("SELECT * FROM users WHERE username = 'kenshin'")).first()
            if not super_admin:
                logger.info("No super admin found, skipping creation")
        except Exception as e:
            logger.warning(f"Could not check super admin: {e}")

        session.commit()
        logger.info("Global data initialization completed")


def create_db_and_tables():
    """创建数据库和表"""
    logger.info("Starting database initialization...")

    # 1. 创建数据库（如果不存在）
    create_database_if_not_exists()

    # 2. 创建表结构
    from app.db.session import engine
    from sqlmodel import SQLModel

    # 导入所有模型以确保表被创建
    import app.models

    SQLModel.metadata.create_all(engine)
    logger.info("Database tables created")

    # 3. 设置行级安全
    setup_row_level_security()

    logger.info("✅ 数据库初始化完成")