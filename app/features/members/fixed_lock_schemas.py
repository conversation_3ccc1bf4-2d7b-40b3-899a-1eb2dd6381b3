from sqlmodel import SQLModel, Field
from typing import Optional, List
from datetime import datetime, time
from pydantic import field_validator

from .fixed_lock_models import (
    get_weekday_display,
    format_time_slot
)


# 基础Schema模型
class MemberFixedSlotLockBase(SQLModel):
    """会员固定课位锁定基础模型"""
    member_id: int = Field(description="会员ID")
    teacher_fixed_slot_id: int = Field(description="教师固定时间段ID")


# API请求模型
class MemberFixedSlotLockCreate(MemberFixedSlotLockBase):
    """创建会员固定课位锁定请求模型"""
    created_by: int = Field(description="创建者ID")


# 批量操作模型
class MemberFixedSlotLockBatchCreate(SQLModel):
    """批量创建会员固定课位锁定请求模型"""
    member_id: int = Field(description="会员ID")
    teacher_fixed_slot_ids: List[int] = Field(description="教师固定时间段ID列表")

    @field_validator('teacher_fixed_slot_ids')
    @classmethod
    def validate_slot_ids(cls, v):
        """验证时间段ID列表"""
        if not v:
            raise ValueError('时间段ID列表不能为空')

        if len(v) > 20:  # 限制批量创建数量
            raise ValueError('单次批量锁定不能超过20个时间段')

        # 检查是否有重复的ID
        if len(set(v)) != len(v):
            raise ValueError('时间段ID列表中存在重复项')

        return v

class MemberFixedSlotLockBatchDelete(SQLModel):
    """批量删除会员固定课位锁定请求模型"""
    lock_ids: List[int] = Field(description="要删除的锁定记录ID列表")
    reason: Optional[str] = Field(default=None, max_length=500, description="删除原因")

    @field_validator('lock_ids')
    @classmethod
    def validate_lock_ids(cls, v):
        """验证锁定ID列表"""
        if not v:
            raise ValueError('锁定ID列表不能为空')

        if len(v) > 50:  # 限制批量删除数量
            raise ValueError('单次批量删除不能超过50个锁定记录')

        # 检查是否有重复的ID
        if len(set(v)) != len(v):
            raise ValueError('锁定ID列表中存在重复项')

        return v


# 查询参数模型
class MemberFixedSlotLockQuery(SQLModel):
    """查询会员固定课位锁定请求模型"""
    member_id: Optional[int] = Field(default=None, description="会员ID")
    teacher_id: Optional[int] = Field(default=None, description="教师ID")
    teacher_fixed_slot_id: Optional[int] = Field(default=None, description="教师固定时间段ID")
    weekday: Optional[int] = Field(default=None, ge=1, le=7, description="星期几（1-7）")
    start_time_from: Optional[time] = Field(default=None, description="开始时间范围-起始")
    start_time_to: Optional[time] = Field(default=None, description="开始时间范围-结束")
    locked_at_from: Optional[datetime] = Field(default=None, description="锁定时间范围-起始")
    locked_at_to: Optional[datetime] = Field(default=None, description="锁定时间范围-结束")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    sort_by: str = Field(default="weekday,start_time", description="排序字段")
    sort_order: str = Field(default="asc", description="排序方向")
    
    @field_validator('sort_order')
    @classmethod
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方向必须是 asc 或 desc')
        return v


class AvailableSlotQuery(SQLModel):
    """可锁定时间段查询参数"""
    teacher_id: Optional[int] = Field(default=None, description="教师ID")
    weekdays: Optional[List[int]] = Field(default=None, description="星期列表（1-7）")
    start_time_from: Optional[time] = Field(default=None, description="开始时间范围-起始")
    start_time_to: Optional[time] = Field(default=None, description="开始时间范围-结束")
    only_available: bool = Field(default=True, description="仅返回可用时间段")
    only_visible: bool = Field(default=True, description="仅返回对会员可见的时间段")
    exclude_locked: bool = Field(default=True, description="排除已被锁定的时间段")
    
    @field_validator('weekdays')
    @classmethod
    def validate_weekdays(cls, v):
        """验证星期列表"""
        if v is not None:
            for weekday in v:
                if not (1 <= weekday <= 7):
                    raise ValueError('星期数字必须在1-7之间')
        return v


# API响应模型
class MemberFixedSlotLockResponse(SQLModel):
    """会员固定课位锁定响应模型"""
    id: int = Field(description="主键ID")
    member_id: int = Field(description="会员ID")
    member_name: Optional[str] = Field(default=None,description="会员姓名")
    teacher_fixed_slot_id: int = Field(description="教师固定时间段ID")
    teacher_id: int = Field(description="教师ID")
    teacher_name: Optional[str] = Field(default=None,description="教师姓名")
    weekday: int = Field(description="星期几（1-7）")
    weekday_name: str = Field(description="星期名称")
    start_time: time = Field(description="开始时间")
    time_slot_display: str = Field(description="时间段显示")
    locked_at: datetime = Field(description="锁定时间")
    created_by: Optional[int] = Field(description="创建者ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")


class MemberFixedSlotLockList(SQLModel):
    """会员固定课位锁定列表响应模型"""
    id: int = Field(description="主键ID")
    member_id: int = Field(description="会员ID")
    member_name: Optional[str] = Field(default=None, description="会员姓名")
    member_phone: Optional[str] = Field(default=None,description="会员手机号")
    teacher_id: int = Field(description="教师ID")
    teacher_name: Optional[str] = Field(default=None,description="教师姓名")
    weekday: int = Field(description="星期几")
    weekday_name: str = Field(description="星期名称")
    start_time: time = Field(description="开始时间")
    time_slot_display: str = Field(description="时间段显示")

    locked_at: datetime = Field(description="锁定时间")


class AvailableSlotResponse(SQLModel):
    """可锁定时间段响应模型"""
    teacher_fixed_slot_id: int = Field(description="教师固定时间段ID")
    teacher_id: int = Field(description="教师ID")
    teacher_name: Optional[str] = Field(default=None,description="教师姓名")
    weekday: int = Field(description="星期几（1-7）")
    weekday_name: str = Field(description="星期名称")
    start_time: time = Field(description="开始时间")
    duration_minutes: Optional[int] = Field(default=None,description="课程时长（分钟）")
    time_slot_display: str = Field(description="时间段显示")
    is_available: bool = Field(description="是否可用")
    is_visible_to_members: bool = Field(description="是否对会员可见")


# 冲突检测模型
class LockConflictCheck(SQLModel):
    """锁定冲突检测参数"""
    member_id: int = Field(description="会员ID")
    teacher_fixed_slot_id: int = Field(description="教师固定时间段ID")
    exclude_lock_id: Optional[int] = Field(default=None, description="排除的锁定记录ID（用于更新时检测）")


class LockConflictResult(SQLModel):
    """锁定冲突检测结果"""
    has_conflict: bool = Field(description="是否存在冲突")
    conflict_type: Optional[str] = Field(description="冲突类型")
    conflict_message: Optional[str] = Field(description="冲突详情")
    conflicting_lock_id: Optional[int] = Field(description="冲突的锁定记录ID")


# 操作结果模型
class LockOperationResult(SQLModel):
    """锁定操作结果"""
    success: bool = Field(description="操作是否成功")
    message: str = Field(description="操作结果消息")
    lock_id: Optional[int] = Field(description="锁定记录ID")
    affected_count: Optional[int] = Field(description="影响的记录数")


class BatchLockOperationResult(SQLModel):
    """批量锁定操作结果"""
    total_count: int = Field(description="总操作数")
    success_count: int = Field(description="成功数")
    failed_count: int = Field(description="失败数")
    success_ids: List[int] = Field(description="成功的ID列表")
    failed_items: List[dict] = Field(description="失败的项目详情")
    message: str = Field(description="操作结果消息")


