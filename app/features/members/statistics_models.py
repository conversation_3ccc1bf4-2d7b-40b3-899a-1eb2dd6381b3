"""会员统计数据模型"""

from sqlmodel import SQLModel, Field, Index, UniqueConstraint
from typing import Optional
from datetime import datetime
from decimal import Decimal


class MemberStatisticsBase(SQLModel):
    """会员统计基础模型"""
    # 课程统计
    total_classes: int = Field(default=0, description="总上课数")
    completed_classes: int = Field(default=0, description="完成上课数")
    cancelled_classes: int = Field(default=0, description="取消上课数")
    no_show_classes: int = Field(default=0, description="缺席上课数")
    
    # 消费统计
    total_spent: int = Field(default=0, description="总消费金额（元）")
    current_month_spent: int = Field(default=0, description="当月消费金额（元）")
    total_recharged: int = Field(default=0, description="总充值金额（元）")
    
    # 评价统计
    avg_rating: Decimal = Field(default=Decimal("0.0"), max_digits=3, decimal_places=1, description="平均评分")
    rating_count: int = Field(default=0, description="评价次数")
    
    # 时间信息
    last_class_at: Optional[datetime] = Field(default=None, description="最后上课时间")
    last_login_at: Optional[datetime] = Field(default=None, description="最后登录时间")


class MemberStatistics(MemberStatisticsBase, table=True):
    """会员统计数据库模型"""
    __tablename__ = "member_statistics"
    
    # 主键使用member_id
    member_id: int = Field(primary_key=True, foreign_key="members.id", description="会员ID")
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    
    __table_args__ = (
        # 索引
        Index('idx_member_stats_tenant', 'tenant_id'),
        Index('idx_member_stats_total_spent', 'tenant_id', 'total_spent'),
        Index('idx_member_stats_rating', 'tenant_id', 'avg_rating'),
        Index('idx_member_stats_last_class', 'tenant_id', 'last_class_at'),
        Index('idx_member_stats_updated', 'tenant_id', 'updated_at'),
    )


# API响应模型
class MemberStatisticsRead(MemberStatisticsBase):
    """会员统计信息读取模型"""
    member_id: int
    tenant_id: int
    created_at: datetime
    updated_at: Optional[datetime]


# API请求模型
class MemberStatisticsUpdate(SQLModel):
    """会员统计信息更新模型"""
    total_classes: Optional[int] = None
    completed_classes: Optional[int] = None
    cancelled_classes: Optional[int] = None
    no_show_classes: Optional[int] = None
    total_spent: Optional[int] = None
    current_month_spent: Optional[int] = None
    total_recharged: Optional[int] = None
    avg_rating: Optional[Decimal] = None
    rating_count: Optional[int] = None
    last_class_at: Optional[datetime] = None
    last_login_at: Optional[datetime] = None


class MemberStatisticsCreate(MemberStatisticsBase):
    """创建会员统计信息模型"""
    member_id: int
    tenant_id: int


# 统计增量更新模型
class MemberStatisticsIncrement(SQLModel):
    """会员统计增量更新模型"""
    class_completed: bool = False
    class_cancelled: bool = False
    class_no_show: bool = False
    amount_spent: int = 0
    amount_recharged: int = 0
    rating_added: Optional[Decimal] = None  # 新增评分
    login_time_updated: bool = False


# 聚合统计模型
class MemberStatisticsAggregation(SQLModel):
    """会员统计聚合信息"""
    total_members: int = Field(description="总会员数")
    active_members: int = Field(description="活跃会员数（有上课记录）")
    total_classes: int = Field(description="总课程数")
    total_revenue: int = Field(description="总收入（元）")
    avg_classes_per_member: float = Field(description="平均每会员上课数")
    avg_spent_per_member: int = Field(description="平均每会员消费（元）")
    completion_rate: float = Field(description="课程完成率")
    cancellation_rate: float = Field(description="课程取消率")
    no_show_rate: float = Field(description="缺席率")
