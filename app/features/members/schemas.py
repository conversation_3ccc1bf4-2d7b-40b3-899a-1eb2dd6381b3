from sqlmodel import SQLModel, Field, UniqueConstraint, Index, ForeignKeyConstraint
from typing import Optional, List, Dict, Any, TYPE_CHECKING
from datetime import datetime, timezone, date
from sqlalchemy import String, Column, JSON
from enum import Enum
from app.features.users.models import Gender
from .models import MemberType, MemberStatus, MemberBase
from .statistics_models import MemberStatisticsBase

# 导入会员卡相关类型
if TYPE_CHECKING:
    from app.features.member_cards.schemas import MemberCardSummary

# API请求模型
class MemberCreate(SQLModel):
    """创建会员请求模型"""
    name: str = Field(min_length=1, max_length=50)
    phone: str = Field(min_length=11, max_length=20)
    email: Optional[str] = None
    gender: Optional[Gender] = None
    birthday: Optional[date] = None
    member_type: MemberType = MemberType.TRIAL
    source_channel: Optional[str] = None
    agent_id: Optional[int] = None
    notes: Optional[str] = None


class MemberUpdate(SQLModel):
    """更新会员请求模型"""
    name: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    gender: Optional[Gender] = None
    birthday: Optional[date] = None
    avatar_url: Optional[str] = None
    # 微信信息
    wechat_openid: Optional[str] = None
    wechat_unionid: Optional[str] = None
    wechat_nickname: Optional[str] = None
    wechat_avatar: Optional[str] = None
    # 会员属性
    member_type: Optional[MemberType] = None
    member_status: Optional[MemberStatus] = None
    source_channel: Optional[str] = None
    agent_id: Optional[int] = None
    address: Optional[str] = None
    city: Optional[str] = None
    province: Optional[str] = None
    country: Optional[str] = None
    postal_code: Optional[str] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = None


# API 响应模型
class MemberRead(MemberBase, MemberStatisticsBase):
    """会员响应模型（包含统计信息）"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    registered_at: datetime

    # 会员卡信息（可选）
    cards: Optional[List["MemberCardSummary"]] = Field(default=None, description="会员卡列表")


class MemberBasicRead(MemberBase):
    """会员基础响应模型（不包含统计信息）"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    registered_at: datetime


class MemberLogin(SQLModel):
    """会员登录请求模型"""
    phone: str = Field(min_length=11, max_length=20)
    verification_code: str = Field(min_length=4, max_length=6)
    tenant_code: str = Field(min_length=1, max_length=50)


# 延迟解析前向引用
def _resolve_forward_refs():
    """解析前向引用"""
    try:
        from app.features.member_cards.schemas import MemberCardSummary
        MemberRead.model_rebuild()
    except ImportError:
        # 如果导入失败，忽略（可能是在测试环境或模块加载顺序问题）
        pass

# 在模块加载时尝试解析
_resolve_forward_refs()


