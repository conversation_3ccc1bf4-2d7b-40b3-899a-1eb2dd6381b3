from sqlmodel import SQLModel, Field, UniqueConstraint, Index, ForeignKeyConstraint
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone, date
from decimal import Decimal
from sqlalchemy import String, Column, JSON
from enum import Enum
from app.features.users.models import Gender


class MemberType(str, Enum):
    """会员类型枚举"""
    TRIAL = "trial"      # 试用会员
    FORMAL = "formal"    # 正式会员
    VIP = "vip"         # VIP会员


class MemberStatus(str, Enum):
    """会员状态枚举"""
    ACTIVE = "active"        # 活跃
    SILENT = "silent"        # 沉默
    FROZEN = "frozen"        # 冻结
    CANCELLED = "cancelled"  # 注销


class MemberBase(SQLModel):
    """会员基础模型"""
    # tenant_id: int = Field(description="租户ID")
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    # 基础信息
    name: str = Field(max_length=50, description="姓名")
    phone: str = Field(max_length=20, description="手机号")
    email: Optional[str] = Field(default=None, max_length=100, description="邮箱")
    gender: Optional[Gender] = Field(default=None, description="性别")
    birthday: Optional[date] = Field(default=None, description="生日")
    avatar_url: Optional[str] = Field(default=None, max_length=500, description="头像URL")
    
    # 微信信息
    wechat_openid: Optional[str] = Field(default=None, max_length=100, description="微信OpenID")
    wechat_unionid: Optional[str] = Field(default=None, max_length=100, description="微信UnionID")
    wechat_nickname: Optional[str] = Field(default=None, max_length=100, description="微信昵称")
    wechat_avatar: Optional[str] = Field(default=None, max_length=500, description="微信头像")
    
    # 会员属性
    member_type: MemberType = Field(default=MemberType.TRIAL, description="会员类型")
    member_status: MemberStatus = Field(default=MemberStatus.ACTIVE, description="会员状态")
    source_channel: Optional[str] = Field(default=None, max_length=50, description="来源渠道")
    
    # 归属关系
    agent_id: Optional[int] = Field(default=None, foreign_key="users.id", description="代理人员ID")
    
    # 地址信息
    address: Optional[str] = Field(default=None, description="地址")
    city: Optional[str] = Field(default=None, max_length=50, description="城市")
    province: Optional[str] = Field(default=None, max_length=50, description="省份")
    country: str = Field(default="China", max_length=50, description="国家")
    postal_code: Optional[str] = Field(default=None, max_length=20, description="邮编")
    
    # 备注和标签
    notes: Optional[str] = Field(default=None, description="备注")
    tags: List[str] = Field(default_factory=list, sa_column=Column(JSON), description="标签")

    # 会员卡信息（冗余字段，便于查询）
    primary_member_card_id: Optional[int] = Field(default=None, foreign_key="member_cards.id", description="主要会员卡ID")
    primary_member_card_name: Optional[str] = Field(default=None, max_length=100, description="主要会员卡名称")


class Member(MemberBase, table=True):
    """会员数据库模型"""
    __tablename__ = "members"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    updated_at: Optional[datetime] = Field(default=None)
    registered_at: datetime = Field(default_factory=lambda: datetime.now(), description="注册时间")
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者")
    
    __table_args__ = (
        # 租户内手机号可重复（支持同手机号多账户）
        Index('idx_tenant_member_phone', 'tenant_id', 'phone'),
        # 租户内邮箱唯一
        UniqueConstraint('tenant_id', 'email', name='uq_tenant_member_email'),
        # 微信OpenID全局唯一
        UniqueConstraint('wechat_openid', name='uq_wechat_openid'),
        # 索引
        Index('idx_tenant_members', 'tenant_id', 'id'),
        Index('idx_member_status', 'tenant_id', 'member_status'),
        Index('idx_member_type', 'tenant_id', 'member_type'),
        Index('idx_member_created_by', 'tenant_id', 'created_by'),
        Index('idx_member_agent', 'tenant_id', 'agent_id'),
        # 复合索引
        Index('idx_members_status_type', 'tenant_id', 'member_status', 'member_type'),
    )
