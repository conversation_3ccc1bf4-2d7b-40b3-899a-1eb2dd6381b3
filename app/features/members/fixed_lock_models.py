from sqlmodel import SQLModel, Field, UniqueConstraint, Index, CheckConstraint
from typing import Optional
from datetime import datetime, timezone, time
from sqlalchemy import text


class MemberFixedSlotLockBase(SQLModel):
    """会员固定课位锁定基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    member_id: int = Field(foreign_key="members.id", description="会员ID")

    # 直接关联教师固定时间段
    teacher_fixed_slot_id: int = Field(foreign_key="teacher_fixed_slots.id", description="教师固定时间段ID")

    # 冗余字段（便于查询和显示，避免总是需要JOIN）
    teacher_id: int = Field(foreign_key="teachers.id", description="教师ID")
    weekday: int = Field(ge=1, le=7, description="星期几（1-7，1为星期一）")
    start_time: time = Field(description="开始时间（HH:MM格式）")

    # 会员信息冗余字段
    member_name: Optional[str] = Field(default=None, max_length=50, description="会员姓名")
    member_phone: Optional[str] = Field(default=None, max_length=20, description="会员手机号")

    # 锁定时间
    locked_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        description="锁定时间"
    )


class MemberFixedSlotLock(MemberFixedSlotLockBase, table=True):
    """会员固定课位锁定表"""
    __tablename__ = "member_fixed_slot_locks"

    id: Optional[int] = Field(default=None, primary_key=True, description="主键ID")

    # 审计字段
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者ID")
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        description="创建时间"
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        description="更新时间"
    )

    __table_args__ = (
        # ==================== 唯一约束 ====================
        # 同一时间段只能有一个锁定记录
        Index(
            "idx_unique_lock_per_slot",
            "teacher_fixed_slot_id",
            unique=True
        ),

        # 检查约束：确保weekday在有效范围内
        CheckConstraint(
            "weekday >= 1 AND weekday <= 7",
            name="ck_weekday_range"
        ),

        # ==================== 基础索引 ====================
        Index("idx_member_locks_member",  "tenant_id", "member_id"),
        Index("idx_member_locks_teacher_slot",  "tenant_id", "teacher_fixed_slot_id"),

        # ==================== 业务查询优化索引 ====================

        # 1. 会员课表查询（会员端核心需求）
        # 支持: WHERE member_id = ? ORDER BY weekday, start_time
        Index("idx_member_locks_schedule", "tenant_id",
              "member_id", "weekday", "start_time"),

        # 2. 教师时段锁定状态查询（管理端核心需求）
        # 支持: WHERE teacher_fixed_slot_id = ?
        Index("idx_member_locks_slot_status", "tenant_id",
              "teacher_fixed_slot_id"),

        # 3. 按教师查询所有锁定（管理端需求）
        # 支持: WHERE teacher_id = ?
        Index("idx_member_locks_teacher_active", "tenant_id",
              "teacher_id", "weekday", "start_time"),

        # 4. 时间范围锁定查询
        # 支持: WHERE weekday IN (...) AND start_time BETWEEN ...
        Index("idx_member_locks_time_range", "tenant_id",
              "weekday", "start_time"),

        # ==================== 覆盖索引（优化JOIN查询）====================

        # 5. 会员课表完整信息覆盖索引
        # 只包含最核心的关联字段
        Index("idx_member_locks_schedule_full", "tenant_id",
              "member_id", "weekday", "start_time",
              postgresql_include=["teacher_id", "teacher_fixed_slot_id"]),

        # 6. 教师时段锁定信息覆盖索引
        # 支持管理端查询教师时段占用状态，包含会员信息
        Index("idx_member_locks_teacher_slot_full", "tenant_id",
              "teacher_fixed_slot_id",
              postgresql_include=["member_id", "member_name", "member_phone", "locked_at"]),

        # ==================== 辅助索引 ====================
        Index("idx_member_locks_locked_at", "locked_at"),  # 用于时间范围查询和统计
    )


# 工具函数


def get_weekday_display(weekday: int) -> str:
    """获取星期的中文显示名称"""
    weekday_names = {
        1: "星期一",
        2: "星期二",
        3: "星期三",
        4: "星期四",
        5: "星期五",
        6: "星期六",
        7: "星期日"
    }
    return weekday_names.get(weekday, "未知")


def format_time_slot(weekday: int, start_time: time) -> str:
    """格式化时间段显示"""
    weekday_display = get_weekday_display(weekday)
    time_display = start_time.strftime("%H:%M")
    return f"{weekday_display} {time_display}" 