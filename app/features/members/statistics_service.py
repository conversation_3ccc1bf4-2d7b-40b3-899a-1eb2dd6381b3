"""会员统计数据服务"""

from sqlmodel import Session, select, func
from sqlalchemy import and_, text
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal

from app.features.base.base_service import TenantAwareService
from .statistics_models import (
    MemberStatistics, 
    MemberStatisticsCreate, 
    MemberStatisticsUpdate,
    MemberStatisticsIncrement,
    MemberStatisticsAggregation
)
from .models import Member
from app.api.common.exceptions import BusinessException


class MemberStatisticsService(TenantAwareService[MemberStatistics]):
    """会员统计数据服务"""
    
    @property
    def model_class(self):
        return MemberStatistics
    
    def get_or_create_statistics(self, member_id: int) -> MemberStatistics:
        """获取或创建会员统计记录"""
        stats = self.session.get(MemberStatistics, member_id)
        if not stats:
            # 创建新的统计记录
            stats_data = MemberStatisticsCreate(
                member_id=member_id,
                tenant_id=self.tenant_id
            )
            stats = MemberStatistics(**stats_data.model_dump())
            stats.created_at = datetime.now()
            self.session.add(stats)
            self.session.commit()
            self.session.refresh(stats)
        return stats
    
    def get_statistics(self, member_id: int) -> Optional[MemberStatistics]:
        """获取会员统计信息"""
        return self.session.get(MemberStatistics, member_id)
    
    def update_statistics(self, member_id: int, update_data: MemberStatisticsUpdate) -> MemberStatistics:
        """更新会员统计信息"""
        stats = self.get_or_create_statistics(member_id)
        
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            if value is not None:
                setattr(stats, field, value)
        
        stats.updated_at = datetime.now()
        self.session.add(stats)
        self.session.commit()
        self.session.refresh(stats)
        
        return stats
    
    def increment_statistics(self, member_id: int, increment_data: MemberStatisticsIncrement) -> MemberStatistics:
        """增量更新会员统计信息"""
        stats = self.get_or_create_statistics(member_id)
        
        # 更新课程统计
        if any([increment_data.class_completed, increment_data.class_cancelled, increment_data.class_no_show]):
            stats.total_classes += 1
            
            if increment_data.class_completed:
                stats.completed_classes += 1
                stats.last_class_at = datetime.now()
            elif increment_data.class_cancelled:
                stats.cancelled_classes += 1
            elif increment_data.class_no_show:
                stats.no_show_classes += 1
        
        # 更新消费统计
        if increment_data.amount_spent > 0:
            stats.total_spent += increment_data.amount_spent
            # 更新当月消费（简化实现，实际可能需要更复杂的逻辑）
            stats.current_month_spent += increment_data.amount_spent
        
        if increment_data.amount_recharged > 0:
            stats.total_recharged += increment_data.amount_recharged
        
        # 更新评分统计
        if increment_data.rating_added is not None:
            if stats.rating_count == 0:
                stats.avg_rating = increment_data.rating_added
                stats.rating_count = 1
            else:
                # 计算新的平均评分
                total_rating = stats.avg_rating * stats.rating_count + increment_data.rating_added
                stats.rating_count += 1
                stats.avg_rating = total_rating / stats.rating_count
        
        # 更新登录时间
        if increment_data.login_time_updated:
            stats.last_login_at = datetime.now()
        
        stats.updated_at = datetime.now()
        self.session.add(stats)
        self.session.commit()
        self.session.refresh(stats)
        
        return stats
    
    def update_login_time(self, member_id: int) -> MemberStatistics:
        """更新会员最后登录时间"""
        increment_data = MemberStatisticsIncrement(login_time_updated=True)
        return self.increment_statistics(member_id, increment_data)
    
    def get_member_statistics_list(self, 
                                   member_ids: List[int] = None,
                                   skip: int = 0, 
                                   limit: int = 100) -> List[MemberStatistics]:
        """获取会员统计列表"""
        statement = select(MemberStatistics)
        
        if member_ids:
            statement = statement.where(MemberStatistics.member_id.in_(member_ids))
        
        statement = statement.offset(skip).limit(limit)
        return self.session.exec(statement).all()
    
    def get_tenant_aggregation(self) -> MemberStatisticsAggregation:
        """获取租户级别的聚合统计"""
        # 总会员数
        total_members = self.session.exec(
            select(func.count(Member.id))
        ).first() or 0
        
        # 活跃会员数（有上课记录的）
        active_members = self.session.exec(
            select(func.count(MemberStatistics.member_id)).where(
                MemberStatistics.total_classes > 0
            )
        ).first() or 0
        
        # 统计汇总
        stats_summary = self.session.exec(
            select(
                func.sum(MemberStatistics.total_classes).label('total_classes'),
                func.sum(MemberStatistics.total_spent).label('total_revenue'),
                func.sum(MemberStatistics.completed_classes).label('completed_classes'),
                func.sum(MemberStatistics.cancelled_classes).label('cancelled_classes'),
                func.sum(MemberStatistics.no_show_classes).label('no_show_classes')
            )
        ).first()
        
        total_classes = stats_summary.total_classes or 0
        total_revenue = stats_summary.total_revenue or 0
        completed_classes = stats_summary.completed_classes or 0
        cancelled_classes = stats_summary.cancelled_classes or 0
        no_show_classes = stats_summary.no_show_classes or 0
        
        # 计算比率
        avg_classes_per_member = total_classes / total_members if total_members > 0 else 0
        avg_spent_per_member = total_revenue // total_members if total_members > 0 else 0
        completion_rate = completed_classes / total_classes if total_classes > 0 else 0
        cancellation_rate = cancelled_classes / total_classes if total_classes > 0 else 0
        no_show_rate = no_show_classes / total_classes if total_classes > 0 else 0
        
        return MemberStatisticsAggregation(
            total_members=total_members,
            active_members=active_members,
            total_classes=total_classes,
            total_revenue=total_revenue,
            avg_classes_per_member=round(avg_classes_per_member, 2),
            avg_spent_per_member=avg_spent_per_member,
            completion_rate=round(completion_rate, 4),
            cancellation_rate=round(cancellation_rate, 4),
            no_show_rate=round(no_show_rate, 4)
        )
    
    def reset_monthly_statistics(self) -> int:
        """重置月度统计（每月初调用）"""
        statement = text("""
            UPDATE member_statistics 
            SET current_month_spent = 0, updated_at = :now 
            WHERE tenant_id = :tenant_id
        """)
        
        result = self.session.exec(
            statement, 
            {"now": datetime.now(), "tenant_id": self.tenant_id}
        )
        self.session.commit()
        return result.rowcount
    
    def migrate_from_member_table(self) -> Dict[str, int]:
        """从members表迁移统计数据到member_statistics表"""
        # 查询所有会员的统计数据
        members_with_stats = self.session.exec(
            select(
                Member.id,
                Member.tenant_id,
                Member.total_classes,
                Member.completed_classes,
                Member.cancelled_classes,
                Member.no_show_classes,
                Member.total_spent,
                Member.avg_rating,
                Member.rating_count,
                Member.last_class_at,
                Member.last_login_at
            )
        ).all()
        
        migrated_count = 0
        skipped_count = 0
        
        for member in members_with_stats:
            # 检查是否已存在统计记录
            existing_stats = self.session.get(MemberStatistics, member.id)
            if existing_stats:
                skipped_count += 1
                continue
            
            # 创建新的统计记录
            stats = MemberStatistics(
                member_id=member.id,
                tenant_id=member.tenant_id,
                total_classes=member.total_classes or 0,
                completed_classes=member.completed_classes or 0,
                cancelled_classes=member.cancelled_classes or 0,
                no_show_classes=member.no_show_classes or 0,
                total_spent=member.total_spent or 0,
                avg_rating=member.avg_rating or Decimal("0.0"),
                rating_count=member.rating_count or 0,
                last_class_at=member.last_class_at,
                last_login_at=member.last_login_at,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.session.add(stats)
            migrated_count += 1
        
        self.session.commit()
        
        return {
            "migrated_count": migrated_count,
            "skipped_count": skipped_count,
            "total_processed": len(members_with_stats)
        }


def get_member_statistics_service(session: Session, tenant_id: int) -> MemberStatisticsService:
    """获取会员统计服务实例"""
    return MemberStatisticsService(session, tenant_id)
