"""会员模块异常处理"""

from app.api.common.exceptions import BusinessException, NotFoundError, ErrorLevel, BaseErrorCode
from typing import Optional, Dict


class MemberErrorCode(BaseErrorCode):
    """会员模块错误码"""
    PHONE_EXISTS = "MEMBER_PHONE_EXISTS"
    EMAIL_EXISTS = "MEMBER_EMAIL_EXISTS"
    ACCOUNT_FROZEN = "MEMBER_ACCOUNT_FROZEN"
    STATUS_INVALID = "MEMBER_STATUS_INVALID"
    INSUFFICIENT_BALANCE = "MEMBER_INSUFFICIENT_BALANCE"


class MemberNotFoundError(NotFoundError):
    """会员不存在异常"""
    def __init__(self, phone: Optional[str] = None):
        if phone:
            super().__init__(f"手机号 {phone} 对应的会员")
        else:
            super().__init__("会员")


class MemberBusinessException(BusinessException):
    """会员业务异常"""
    
    @classmethod
    def phone_already_exists(cls, phone: str):
        """手机号已存在"""
        return cls(
            f"手机号 {phone} 已存在",
            MemberErrorCode.PHONE_EXISTS,  # 使用枚举
            ErrorLevel.WARNING
        )
    
    @classmethod
    def email_already_exists(cls, email: str):
        """邮箱已存在"""
        return cls(
            f"邮箱 {email} 已存在",
            MemberErrorCode.EMAIL_EXISTS,
            ErrorLevel.WARNING
        )
    
    @classmethod
    def status_invalid(cls, status: str):
        """会员状态无效"""
        return cls(f"会员状态 {status} 无效")  # 使用默认错误码
    
    @classmethod
    def account_frozen(cls):
        """账户被冻结 - 客户端需要特殊处理"""
        return cls(
            "会员账户已被禁用，请联系管理员",
            MemberErrorCode.ACCOUNT_FROZEN,
            ErrorLevel.ERROR
        )
    
    @classmethod
    def insufficient_balance(cls, required: int, current: int):
        """余额不足 - 客户端可能需要跳转充值页面"""
        return cls(
            f"余额不足，需要 {required} 元，当前余额 {current} 元",
            MemberErrorCode.INSUFFICIENT_BALANCE,
            ErrorLevel.WARNING,
            {"required": required, "current": current}
        )
    
    @classmethod
    def general_error(cls, message: str):
        """通用会员业务错误"""
        return cls(message)  # 使用默认错误码和级别 