from typing import List, Optional, Tuple
from sqlmodel import Session, select, text, func, and_
from datetime import datetime, timezone

from .fixed_lock_models import (
    MemberFixedSlotLock
)
from .fixed_lock_schemas import (
    MemberFixedSlotLockCreate, MemberFixedSlotLockQuery,
    MemberFixedSlotLockBatchCreate, MemberFixedSlotLockBatchDelete,
    AvailableSlotQuery, LockConflictCheck, LockConflictResult
)
from .models import Member
from ..teachers.fixed_slots_models import TeacherFixedSlot
from .fixed_lock_exceptions import (
    MemberFixedSlotLockNotFoundError, MemberFixedSlotLockBusinessException
)
from app.features.courses.operations.operation_logger import get_operation_logger
from app.features.base.base_service import TenantAwareService


class MemberFixedSlotLockService(TenantAwareService[MemberFixedSlotLock]):
    """会员固定课位锁定服务"""
    
    @property
    def model_class(self):
        return MemberFixedSlotLock

    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)
        self.operation_logger = get_operation_logger(session, tenant_id)

    # ==================== CRUD操作 ====================

    def create_lock(self, lock_data: MemberFixedSlotLockCreate, created_by: int, operator_name: str) -> MemberFixedSlotLock:
        """创建固定课位锁定"""
        # 验证会员是否存在
        member = self.session.get(Member, lock_data.member_id)
        if not member:
            raise MemberFixedSlotLockBusinessException.member_not_found(lock_data.member_id)

        # 验证教师固定时间段是否存在
        teacher_slot = self.session.get(TeacherFixedSlot, lock_data.teacher_fixed_slot_id)
        if not teacher_slot:
            raise MemberFixedSlotLockBusinessException.teacher_slot_not_found(lock_data.teacher_fixed_slot_id)

        # 检查时间段是否可用
        if not teacher_slot.is_available:
            raise MemberFixedSlotLockBusinessException.slot_not_available(
                lock_data.teacher_fixed_slot_id
            )

        # 检查时间段是否对会员可见
        if not teacher_slot.is_visible_to_members:
            raise MemberFixedSlotLockBusinessException.slot_not_visible(
                lock_data.teacher_fixed_slot_id
            )

        # 检查时间段是否已被锁定
        existing_lock = self._get_active_lock_by_slot(lock_data.teacher_fixed_slot_id)
        if existing_lock:
            raise MemberFixedSlotLockBusinessException.slot_already_locked(
                teacher_slot.teacher_id, teacher_slot.weekday, teacher_slot.start_time, existing_lock.member_id
            )

        # 创建锁定记录
        lock_dict = lock_data.model_dump(exclude={'created_by'})
        lock_dict['tenant_id'] = self.tenant_id
        lock_dict['created_by'] = created_by
        
        # 设置冗余字段
        lock_dict['teacher_id'] = teacher_slot.teacher_id
        lock_dict['weekday'] = teacher_slot.weekday
        lock_dict['start_time'] = teacher_slot.start_time
        lock_dict['member_name'] = member.name
        lock_dict['member_phone'] = member.phone

        lock = MemberFixedSlotLock(**lock_dict)
        now = datetime.now()
        lock.created_at = now
        lock.updated_at = now
        lock.locked_at = now

        self.session.add(lock)
        self.session.commit()
        self.session.refresh(lock)

        # 记录操作日志
        self.operation_logger.log_member_lock_create(
            lock=lock,
            operator_id=created_by,
            operator_name=operator_name,
            operator_type="admin"
        )

        return lock

    def get_lock(self, lock_id: int) -> Optional[MemberFixedSlotLock]:
        """根据ID获取锁定记录"""
        return self.session.get(MemberFixedSlotLock, lock_id)

    def get_lock_by_slot(self, teacher_fixed_slot_id: int) -> Optional[MemberFixedSlotLock]:
        """根据教师固定时间段ID获取锁定记录"""
        statement = select(MemberFixedSlotLock).where(
            MemberFixedSlotLock.teacher_fixed_slot_id == teacher_fixed_slot_id
        )
        return self.session.exec(statement).first()

    def _get_active_lock_by_slot(self, teacher_fixed_slot_id: int) -> Optional[MemberFixedSlotLock]:
        """根据教师固定时间段ID获取激活状态的锁定记录"""
        statement = select(MemberFixedSlotLock).where(
            MemberFixedSlotLock.teacher_fixed_slot_id == teacher_fixed_slot_id
        )
        return self.session.exec(statement).first()

    def get_locks(self, query_params: MemberFixedSlotLockQuery) -> Tuple[List[MemberFixedSlotLock], int]:
        """获取锁定记录列表"""
        statement = select(MemberFixedSlotLock)

        # 筛选条件
        conditions = []
        
        if query_params.member_id:
            conditions.append(MemberFixedSlotLock.member_id == query_params.member_id)
        
        if query_params.teacher_id:
            conditions.append(MemberFixedSlotLock.teacher_id == query_params.teacher_id)
        
        if query_params.teacher_fixed_slot_id:
            conditions.append(MemberFixedSlotLock.teacher_fixed_slot_id == query_params.teacher_fixed_slot_id)
        
        if query_params.weekday:
            conditions.append(MemberFixedSlotLock.weekday == query_params.weekday)
        
        if query_params.start_time_from:
            conditions.append(MemberFixedSlotLock.start_time >= query_params.start_time_from)
        
        if query_params.start_time_to:
            conditions.append(MemberFixedSlotLock.start_time <= query_params.start_time_to)
        
        if query_params.locked_at_from:
            conditions.append(MemberFixedSlotLock.locked_at >= query_params.locked_at_from)
        
        if query_params.locked_at_to:
            conditions.append(MemberFixedSlotLock.locked_at <= query_params.locked_at_to)

        if conditions:
            statement = statement.where(and_(*conditions))

        # 计算总数
        count_statement = select(func.count()).select_from(statement.subquery())
        total = self.session.exec(count_statement).one()

        # 排序
        statement = statement.order_by(
            MemberFixedSlotLock.weekday.asc(),
            MemberFixedSlotLock.start_time.asc(),
            MemberFixedSlotLock.locked_at.desc()
        )

        # 分页
        if query_params.page and query_params.size:
            offset = (query_params.page - 1) * query_params.size
            statement = statement.offset(offset).limit(query_params.size)

        locks = self.session.exec(statement).all()
        return locks, total

    def delete_lock(self, lock_id: int, operator_id: Optional[int] = None, operator_name: Optional[str] = None, is_member_operation: bool = False, reason: Optional[str] = None) -> bool:
        """删除锁定记录"""
        lock = self.session.get(MemberFixedSlotLock, lock_id)
        if not lock:
            return False

        # 记录操作日志（在删除前记录）
        if operator_id and operator_name:
            self.operation_logger.log_member_lock_delete(
                lock=lock,
                operator_id=operator_id,
                operator_name=operator_name,
                operator_type="member" if is_member_operation else "admin",
                is_member_operation=is_member_operation,
                reason=reason
            )

        self.session.delete(lock)
        self.session.commit()
        return True

    # ==================== 冲突检测和可用性检查 ====================

    def check_lock_conflict(self, conflict_check: LockConflictCheck) -> LockConflictResult:
        """检查锁定冲突"""
        # 检查教师固定时间段是否存在
        teacher_slot = self.session.get(TeacherFixedSlot, conflict_check.teacher_fixed_slot_id)
        if not teacher_slot:
            return LockConflictResult(
                has_conflict=True,
                conflict_type="slot_not_found",
                conflict_message="教师固定时间段不存在"
            )

        # 检查时间段是否已被锁定
        statement = select(MemberFixedSlotLock).where(
            MemberFixedSlotLock.teacher_fixed_slot_id == conflict_check.teacher_fixed_slot_id
        )

        if conflict_check.exclude_lock_id:
            statement = statement.where(MemberFixedSlotLock.id != conflict_check.exclude_lock_id)

        existing_lock = self.session.exec(statement).first()

        if existing_lock:
            return LockConflictResult(
                has_conflict=True,
                conflict_type="slot_already_locked",
                conflict_message=f"时间段已被会员 {existing_lock.member_id} 锁定",
                conflicting_lock_id=existing_lock.id
            )

        return LockConflictResult(
            has_conflict=False,
            conflict_type=None,
            conflict_message=None,
            conflicting_lock_id=None
        )

    def get_available_slots(self, query: AvailableSlotQuery) -> List[TeacherFixedSlot]:
        """获取可锁定的时间段"""
        statement = select(TeacherFixedSlot)

        conditions = []

        if query.teacher_id:
            conditions.append(TeacherFixedSlot.teacher_id == query.teacher_id)

        if query.only_available:
            conditions.append(TeacherFixedSlot.is_available == True)

        if query.only_visible:
            conditions.append(TeacherFixedSlot.is_visible_to_members == True)

        if query.weekdays:
            # 将整数列表转换为Weekday枚举列表
            from app.features.teachers.fixed_slots_models import Weekday
            weekday_enums = [Weekday(weekday) for weekday in query.weekdays]
            conditions.append(TeacherFixedSlot.weekday.in_(weekday_enums))

        if query.start_time_from:
            conditions.append(TeacherFixedSlot.start_time >= query.start_time_from)

        if query.start_time_to:
            conditions.append(TeacherFixedSlot.start_time <= query.start_time_to)

        if conditions:
            statement = statement.where(and_(*conditions))

        # 如果需要排除已锁定的时间段
        if query.exclude_locked:
            # 使用LEFT JOIN排除已被激活锁定的时间段
            locked_slots_subquery = select(MemberFixedSlotLock.teacher_fixed_slot_id)
            statement = statement.where(
                TeacherFixedSlot.id.not_in(locked_slots_subquery)
            )

        # 排序
        statement = statement.order_by(
            TeacherFixedSlot.weekday.asc(),
            TeacherFixedSlot.start_time.asc()
        )

        return self.session.exec(statement).all()

    def get_member_locks(self, member_id: int) -> List[MemberFixedSlotLock]:
        """获取会员的锁定记录"""
        statement = select(MemberFixedSlotLock).where(
            and_(
                MemberFixedSlotLock.tenant_id == self.tenant_id,
                MemberFixedSlotLock.member_id == member_id
            )
        )

        statement = statement.order_by(
            MemberFixedSlotLock.weekday.asc(),
            MemberFixedSlotLock.start_time.asc()
        )

        return self.session.exec(statement).all()

    def get_teacher_slot_locks(self, teacher_id: int) -> List[MemberFixedSlotLock]:
        """获取教师时间段的锁定情况"""
        statement = select(MemberFixedSlotLock).where(
            MemberFixedSlotLock.teacher_id == teacher_id
        )

        statement = statement.order_by(
            MemberFixedSlotLock.weekday.asc(),
            MemberFixedSlotLock.start_time.asc()
        )

        return self.session.exec(statement).all()

    # ==================== 批量操作 ====================

    def batch_create_locks(self, batch_data: MemberFixedSlotLockBatchCreate, created_by: int, operator_name: str) -> List[MemberFixedSlotLock]:
        """批量创建锁定记录"""
        # 获取会员信息
        member = self.session.get(Member, batch_data.member_id)
        if not member:
            raise MemberFixedSlotLockBusinessException.member_not_found(batch_data.member_id)

        created_locks = []
        now = datetime.now()

        for slot_id in batch_data.teacher_fixed_slot_ids:
            try:
                # 验证教师固定时间段是否存在
                teacher_slot = self.session.get(TeacherFixedSlot, slot_id)
                if not teacher_slot:
                    continue  # 跳过不存在的时间段

                # 检查时间段是否可用
                if not teacher_slot.is_available or not teacher_slot.is_visible_to_members:
                    continue  # 跳过不可用的时间段

                # 检查时间段是否已被锁定
                existing_lock = self._get_active_lock_by_slot(slot_id)
                if existing_lock:
                    continue  # 跳过已被锁定的时间段

                # 创建锁定记录
                lock = MemberFixedSlotLock(
                    tenant_id=self.tenant_id,
                    member_id=batch_data.member_id,
                    teacher_fixed_slot_id=slot_id,
                    teacher_id=teacher_slot.teacher_id,
                    weekday=teacher_slot.weekday,
                    start_time=teacher_slot.start_time,
                    member_name=member.name,
                    member_phone=member.phone,
                    created_by=created_by,
                    created_at=now,
                    updated_at=now,
                    locked_at=now
                )

                self.session.add(lock)
                created_locks.append(lock)

            except Exception:
                # 跳过出错的记录，继续处理其他记录
                continue

        if created_locks:
            self.session.commit()
            for lock in created_locks:
                self.session.refresh(lock)

            # 记录批量创建操作日志
            self.operation_logger.log_member_locks_batch_create(
                locks=created_locks,
                operator_id=created_by,
                operator_name=operator_name,
                operator_type="admin"
            )

        return created_locks

    def batch_delete_locks(self, batch_data: MemberFixedSlotLockBatchDelete) -> int:
        """批量删除锁定记录"""
        deleted_count = 0

        for lock_id in batch_data.lock_ids:
            lock = self.session.get(MemberFixedSlotLock, lock_id)
            if lock:
                self.session.delete(lock)
                deleted_count += 1

        if deleted_count > 0:
            self.session.commit()

        return deleted_count

    # ==================== 状态管理 ====================

    def cancel_lock(self, lock_id: int) -> None:
        """取消锁定（会员自己取消会删除数据）"""
        lock = self.session.get(MemberFixedSlotLock, lock_id)
        if not lock:
            raise MemberFixedSlotLockNotFoundError(lock_id)

        # 会员自己取消锁定时，直接删除记录
        # TODO:操作记录到日志表
        self.session.delete(lock)
        self.session.commit()

    def admin_cancel_lock(self, lock_id: int) -> Optional[MemberFixedSlotLock]:
        """管理员取消锁定（保留记录，状态改为CANCELLED）"""
        lock = self.session.get(MemberFixedSlotLock, lock_id)
        if not lock:
            raise MemberFixedSlotLockNotFoundError(lock_id)

        # 直接删除记录
        # TODO:操作记录到日志表
        self.session.delete(lock)
        self.session.commit()

        return lock

    def check_and_fix_data_consistency(self) -> dict:
        """检查并修复冗余字段数据一致性

        Returns:
            dict: 包含检查结果和修复统计的字典
        """
        # 查询所有活跃的锁定记录及其对应的教师时间段
        statement = select(
            MemberFixedSlotLock,
            TeacherFixedSlot
        ).join(
            TeacherFixedSlot,
            MemberFixedSlotLock.teacher_fixed_slot_id == TeacherFixedSlot.id
        )

        results = self.session.exec(statement).all()

        inconsistent_records = []
        fixed_count = 0

        for lock, teacher_slot in results:
            # 检查冗余字段是否与主表一致
            inconsistencies = []

            if lock.teacher_id != teacher_slot.teacher_id:
                inconsistencies.append(f"teacher_id: {lock.teacher_id} -> {teacher_slot.teacher_id}")
                lock.teacher_id = teacher_slot.teacher_id

            if lock.weekday != teacher_slot.weekday:
                inconsistencies.append(f"weekday: {lock.weekday} -> {teacher_slot.weekday}")
                lock.weekday = teacher_slot.weekday

            if lock.start_time != teacher_slot.start_time:
                inconsistencies.append(f"start_time: {lock.start_time} -> {teacher_slot.start_time}")
                lock.start_time = teacher_slot.start_time

            if inconsistencies:
                inconsistent_records.append({
                    "lock_id": lock.id,
                    "teacher_fixed_slot_id": lock.teacher_fixed_slot_id,
                    "member_id": lock.member_id,
                    "inconsistencies": inconsistencies
                })

                # 修复数据
                lock.updated_at = datetime.now()
                self.session.add(lock)
                fixed_count += 1

        # 提交修复
        if fixed_count > 0:
            self.session.commit()

        return {
            "total_checked": len(results),
            "inconsistent_count": len(inconsistent_records),
            "fixed_count": fixed_count,
            "inconsistent_records": inconsistent_records
        }

    def validate_data_consistency(self, lock_id: Optional[int] = None) -> dict:
        """验证数据一致性（只检查不修复）

        Args:
            lock_id: 可选，指定检查特定锁定记录的ID

        Returns:
            dict: 包含验证结果的字典
        """
        if lock_id:
            # 检查特定记录
            statement = select(
                MemberFixedSlotLock,
                TeacherFixedSlot
            ).join(
                TeacherFixedSlot,
                MemberFixedSlotLock.teacher_fixed_slot_id == TeacherFixedSlot.id
            ).where(
                MemberFixedSlotLock.id == lock_id
            )

            result = self.session.exec(statement).first()
            if not result:
                return {"error": f"Lock record {lock_id} not found"}

            lock, teacher_slot = result
            inconsistencies = []

            if lock.teacher_id != teacher_slot.teacher_id:
                inconsistencies.append(f"teacher_id: {lock.teacher_id} != {teacher_slot.teacher_id}")

            if lock.weekday != teacher_slot.weekday:
                inconsistencies.append(f"weekday: {lock.weekday} != {teacher_slot.weekday}")

            if lock.start_time != teacher_slot.start_time:
                inconsistencies.append(f"start_time: {lock.start_time} != {teacher_slot.start_time}")

            return {
                "lock_id": lock_id,
                "is_consistent": len(inconsistencies) == 0,
                "inconsistencies": inconsistencies
            }
        else:
            # 检查所有记录
            return self.check_and_fix_data_consistency()

    # ==================== 数据同步方法 ====================

    def sync_member_info(self, member_id: int) -> int:
        """同步会员信息到锁定记录"""
        # 获取会员信息
        member = self.session.get(Member, member_id)
        if not member:
            return 0

        # 更新该会员的所有锁定记录
        statement = select(MemberFixedSlotLock).where(
            and_(
                MemberFixedSlotLock.tenant_id == self.tenant_id,
                MemberFixedSlotLock.member_id == member_id
            )
        )

        locks = self.session.exec(statement).all()
        updated_count = 0

        for lock in locks:
            if lock.member_name != member.name or lock.member_phone != member.phone:
                lock.member_name = member.name
                lock.member_phone = member.phone
                lock.updated_at = datetime.now()
                updated_count += 1

        if updated_count > 0:
            self.session.commit()

        return updated_count


def get_member_fixed_slot_lock_service(session: Session, tenant_id: int) -> MemberFixedSlotLockService:
    """获取会员固定课位锁定服务实例"""
    return MemberFixedSlotLockService(session, tenant_id)
