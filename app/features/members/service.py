from typing import List, Optional, Dict
from sqlmodel import Session, select, text
from datetime import datetime, timezone, timedelta

from app.features.base.base_service import TenantAwareService
from .models import Member, MemberType, MemberStatus
from .schemas import MemberCreate, MemberUpdate, MemberRead

# 导入业务异常和便捷函数
from app.api.common.exceptions import BusinessException
from .exceptions import MemberBusinessException, MemberNotFoundError

# 导入统计服务
from .statistics_service import MemberStatisticsService, get_member_statistics_service
from .statistics_models import MemberStatisticsIncrement

# 导入会员卡相关服务和模型
from ..member_cards.card_service import MemberCardService
from ..member_cards.template_service import MemberCardTemplateService
from ..member_cards.models import CardType, MemberCard
from ..member_cards.schemas import MemberCardCreate, MemberCardTemplateCreate, MemberCardSummary

class MemberService(TenantAwareService[Member]):
    """会员管理服务"""

    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)
        self._stats_service = None

    @property
    def model_class(self):
        return Member

    @property
    def stats_service(self) -> MemberStatisticsService:
        """获取统计服务实例"""
        if self._stats_service is None:
            self._stats_service = get_member_statistics_service(self.session, self.tenant_id)
        return self._stats_service
    
    def create_member(self, member_data: MemberCreate, created_by: Optional[int] = None) -> Member:
        """创建新会员"""
        # 检查手机号是否已存在（租户内可重复，但需要明确处理）
        existing_member = self.get_member_by_phone(member_data.phone)
        if existing_member:
            raise MemberBusinessException.phone_already_exists(member_data.phone)
        
        # 检查邮箱是否已存在（租户内唯一）
        if member_data.email:
            existing_email = self.get_member_by_email(member_data.email)
            if existing_email:
                raise MemberBusinessException.email_already_exists(member_data.email)
        
        member_dict = member_data.model_dump()
        member_dict['tenant_id'] = self.tenant_id
        member_dict['created_by'] = created_by
        
        member = Member(**member_dict)
        # 设置创建时间和更新时间
        now = datetime.now()
        member.created_at = now
        member.updated_at = now
        member.registered_at = now

        try:
            # 在同一个事务中创建会员、默认模板和默认储值卡
            self.session.add(member)
            self.session.flush()  # 刷新以获取member.id，但不提交事务

            # 为新会员创建默认模板和储值卡
            default_card = self._create_default_template_and_card(member.id, created_by)

            # 更新会员的会员卡信息
            member.primary_member_card_id = default_card.id
            member.primary_member_card_name = f"默认储值卡-{member.name}"

            # 一起提交事务
            self.session.commit()
            self.session.refresh(member)

        except Exception as e:
            # 回滚事务
            self.session.rollback()
            # 使用统一异常处理
            raise MemberBusinessException.general_error(f"会员创建失败：{str(e)}")

        return member
    
    def get_member(self, member_id: int) -> Optional[Member]:
        """根据ID获取会员（RLS自动过滤租户）"""
        return self.session.get(Member, member_id)
    
    def get_member_by_phone(self, phone: str) -> Optional[Member]:
        """根据手机号获取会员（RLS自动过滤租户）"""
        statement = select(Member).where(Member.phone == phone)
        return self.session.exec(statement).first()
    
    def get_member_by_email(self, email: str) -> Optional[Member]:
        """根据邮箱获取会员（RLS自动过滤租户）"""
        statement = select(Member).where(Member.email == email)
        return self.session.exec(statement).first()
    
    def get_member_by_wechat(self, wechat_openid: str) -> Optional[Member]:
        """根据微信OpenID获取会员（RLS自动过滤租户）"""
        statement = select(Member).where(Member.wechat_openid == wechat_openid)
        return self.session.exec(statement).first()
    
    def get_members(self, 
                   skip: int = 0, 
                   limit: int = 100,
                   member_type: Optional[MemberType] = None,
                   member_status: Optional[MemberStatus] = None,
                   agent_id: Optional[int] = None,
                   search_keyword: Optional[str] = None) -> List[Member]:
        """获取会员列表（RLS自动过滤租户）"""
        statement = select(Member)
        
        if member_type:
            statement = statement.where(Member.member_type == member_type)
        if member_status:
            statement = statement.where(Member.member_status == member_status)
        if agent_id:
            statement = statement.where(Member.agent_id == agent_id)
        if search_keyword:
            # 支持按姓名、手机号、邮箱搜索
            from sqlalchemy import or_
            search_conditions = [
                Member.name.contains(search_keyword),
                Member.phone.contains(search_keyword)
            ]
            # 只有当email不为None时才添加邮箱搜索条件
            search_conditions.append(
                Member.email.contains(search_keyword)
            )
            statement = statement.where(or_(*search_conditions))
            
        statement = statement.offset(skip).limit(limit).order_by(Member.created_at.desc())
        return self.session.exec(statement).all()
    
    def count_members(self,
                     member_type: Optional[MemberType] = None,
                     member_status: Optional[MemberStatus] = None,
                     agent_id: Optional[int] = None) -> int:
        """统计会员数量（RLS自动过滤租户）"""
        statement = select(Member)
        
        if member_type:
            statement = statement.where(Member.member_type == member_type)
        if member_status:
            statement = statement.where(Member.member_status == member_status)
        if agent_id:
            statement = statement.where(Member.agent_id == agent_id)
            
        result = self.session.exec(statement)
        return len(result.all())
    
    def update_member(self, member_id: int, member_data: MemberUpdate) -> Optional[Member]:
        """更新会员信息"""
        member = self.session.get(Member, member_id)
        if not member:
            return None
        
        update_data = member_data.model_dump(exclude_unset=True)
        
        # 检查邮箱唯一性（如果要更新邮箱）
        if 'email' in update_data and update_data['email']:
            existing_email = self.get_member_by_email(update_data['email'])
            if existing_email and existing_email.id != member_id:
                raise MemberBusinessException.email_already_exists(update_data['email'])
        
        # 检查手机号（如果要更新手机号）
        if 'phone' in update_data:
            existing_phone = self.get_member_by_phone(update_data['phone'])
            if existing_phone and existing_phone.id != member_id:
                raise MemberBusinessException.phone_already_exists(update_data['phone'])
        
        for field, value in update_data.items():
            setattr(member, field, value)
        
        member.updated_at = datetime.now()
        
        self.session.add(member)
        self.session.commit()
        self.session.refresh(member)
        
        return member
    
    def update_member_status(self, member_id: int, status: MemberStatus) -> Optional[Member]:
        """更新会员状态"""
        member = self.session.get(Member, member_id)
        if not member:
            return None
        
        member.member_status = status
        member.updated_at = datetime.now()
        
        self.session.add(member)
        self.session.commit()
        self.session.refresh(member)
        
        return member
    
    def update_member_stats(self, member_id: int,
                           class_completed: bool = False,
                           class_cancelled: bool = False,
                           class_no_show: bool = False,
                           amount_spent: int = 0) -> Optional[Member]:
        """更新会员统计信息（现在使用独立的统计表）"""
        member = self.session.get(Member, member_id)
        if not member:
            return None

        # 使用统计服务更新统计信息
        increment_data = MemberStatisticsIncrement(
            class_completed=class_completed,
            class_cancelled=class_cancelled,
            class_no_show=class_no_show,
            amount_spent=amount_spent
        )

        self.stats_service.increment_statistics(member_id, increment_data)

        # 更新会员表的更新时间
        member.updated_at = datetime.now()
        self.session.add(member)
        self.session.commit()
        self.session.refresh(member)

        return member
    
    def update_login_time(self, member_id: int) -> Optional[Member]:
        """更新会员最后登录时间（现在使用独立的统计表）"""
        member = self.session.get(Member, member_id)
        if not member:
            return None

        # 使用统计服务更新登录时间
        self.stats_service.update_login_time(member_id)

        # 更新会员表的更新时间
        member.updated_at = datetime.now()
        self.session.add(member)
        self.session.commit()
        self.session.refresh(member)

        return member

    def get_member_with_stats(self, member_id: int) -> Optional[MemberRead]:
        """获取包含统计信息的会员数据"""
        member = self.session.get(Member, member_id)
        if not member:
            return None

        # 获取统计信息
        stats = self.stats_service.get_statistics(member_id)

        # 构建包含统计信息的响应模型
        member_dict = member.model_dump()
        if stats:
            member_dict.update({
                'total_classes': stats.total_classes,
                'completed_classes': stats.completed_classes,
                'cancelled_classes': stats.cancelled_classes,
                'no_show_classes': stats.no_show_classes,
                'total_spent': stats.total_spent,
                'avg_rating': stats.avg_rating,
                'rating_count': stats.rating_count,
                'last_class_at': stats.last_class_at,
                'last_login_at': stats.last_login_at,
            })
        else:
            # 如果没有统计数据，使用默认值
            member_dict.update({
                'total_classes': 0,
                'completed_classes': 0,
                'cancelled_classes': 0,
                'no_show_classes': 0,
                'total_spent': 0,
                'avg_rating': 0.0,
                'rating_count': 0,
                'last_class_at': None,
                'last_login_at': None,
            })

        return MemberRead(**member_dict)

    def get_members_with_stats(self,
                              skip: int = 0,
                              limit: int = 100,
                              member_type: Optional[MemberType] = None,
                              member_status: Optional[MemberStatus] = None,
                              agent_id: Optional[int] = None,
                              search_keyword: Optional[str] = None) -> List[MemberRead]:
        """获取包含统计信息的会员列表"""
        # 先获取基础会员数据
        members = self.get_members(skip, limit, member_type, member_status, agent_id, search_keyword)

        if not members:
            return []

        # 批量获取统计信息
        member_ids = [m.id for m in members]
        stats_list = self.stats_service.get_member_statistics_list(member_ids)
        stats_dict = {stats.member_id: stats for stats in stats_list}

        # 批量获取会员卡信息（避免N+1查询）
        cards_dict = self._get_members_cards_batch(member_ids)

        # 构建响应数据
        result = []
        for member in members:
            member_dict = member.model_dump()
            stats = stats_dict.get(member.id)

            if stats:
                member_dict.update({
                    'total_classes': stats.total_classes,
                    'completed_classes': stats.completed_classes,
                    'cancelled_classes': stats.cancelled_classes,
                    'no_show_classes': stats.no_show_classes,
                    'total_spent': stats.total_spent,
                    'avg_rating': stats.avg_rating,
                    'rating_count': stats.rating_count,
                    'last_class_at': stats.last_class_at,
                    'last_login_at': stats.last_login_at,
                })
            else:
                # 默认值
                member_dict.update({
                    'total_classes': 0,
                    'completed_classes': 0,
                    'cancelled_classes': 0,
                    'no_show_classes': 0,
                    'total_spent': 0,
                    'avg_rating': 0.0,
                    'rating_count': 0,
                    'last_class_at': None,
                    'last_login_at': None,
                })
            
            # 添加会员卡信息
            member_dict['cards'] = cards_dict.get(member.id, [])

            result.append(MemberRead(**member_dict))

        return result

    def _get_members_cards_batch(self, member_ids: List[int]) -> Dict[int, List[MemberCardSummary]]:
        """批量获取会员的会员卡信息（避免N+1查询）"""
        if not member_ids:
            return {}

        # 一次查询获取所有相关的会员卡
        statement = select(MemberCard).where(
            MemberCard.member_id.in_(member_ids)
        ).order_by(MemberCard.member_id, MemberCard.created_at.desc())

        cards = self.session.exec(statement).all()

        # 按会员ID组织会员卡数据
        cards_dict = {}
        for card in cards:
            if card.member_id not in cards_dict:
                cards_dict[card.member_id] = []

            # 转换为MemberCardSummary对象
            card_summary = MemberCardSummary(
                id=card.id,
                name=card.name,
                card_type=card.card_type,
                balance=card.balance,
                status=card.status,
                expires_at=card.expires_at,
                last_used_at=card.last_used_at
            )
            cards_dict[card.member_id].append(card_summary)

        return cards_dict
    
    def delete_member(self, member_id: int) -> bool:
        """删除会员及其关联的会员卡"""
        member = self.session.get(Member, member_id)
        if not member:
            return False
        
        try:
            # 先更新会员表的主卡id为空
            member.primary_member_card_id = None
            member.primary_member_card_name = None
            self.session.add(member)

            # 先删除该会员的所有会员卡
            from sqlmodel import select
            from ..member_cards.models import MemberCard
            
            # 查询该会员的所有会员卡
            member_cards_statement = select(MemberCard).where(MemberCard.member_id == member_id)
            member_cards = self.session.exec(member_cards_statement).all()
            
            # 删除所有会员卡
            for card in member_cards:
                self.session.delete(card)
            
            # 删除会员
            self.session.delete(member)
            self.session.commit()
            return True
            
        except Exception as e:
            # 回滚事务
            self.session.rollback()
            raise BusinessException(f"删除会员失败：{str(e)}")

    def _create_default_template_and_card(self, member_id: int, created_by: Optional[int] = None) -> 'MemberCard':
        """为新会员创建默认模板和储值卡"""
        # 创建会员卡模板服务实例
        template_service = MemberCardTemplateService(self.session, self.tenant_id)
        card_service = MemberCardService(self.session, self.tenant_id)

        # 1. 创建默认模板
        default_template_data = MemberCardTemplateCreate(
            name=f"默认储值卡模板-{member_id}",
            card_type=CardType.VALUE_LIMITED,
            sale_price=1000,
            available_balance=1000,  # 为测试方便设定可用余额为1000元
            validity_days=30,  # 30天有效期
            description="系统为新会员自动创建的默认储值卡模板"
        )

        default_template = template_service.create_template(default_template_data, created_by)

        # 2. 基于模板创建默认储值卡
        default_card_data = MemberCardCreate(
            member_id=member_id,
            template_id=default_template.id
        )

        # 创建默认储值卡
        default_card = card_service.create_card(default_card_data, created_by)

        return default_card


def get_member_service(session: Session, tenant_id: int) -> MemberService:
    """获取会员服务实例"""
    return MemberService(session, tenant_id)