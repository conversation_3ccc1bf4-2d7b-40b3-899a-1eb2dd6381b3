from typing import List, Optional
from sqlmodel import Session, select, text
from datetime import datetime

from app.features.base.base_service import BaseService
from .models import User, UserRole, UserStatus
from .schemas import UserCreate, UserUpdate, UserSearchParams, UserListResponse
from app.utils.security import get_password_hash, verify_password
from app.utils.time import now_utc

# 导入业务异常和便捷函数
from app.api.common.exceptions import BusinessException
from .exceptions import UserBusinessException, UserNotFoundError


class UserService(BaseService[User]):
    """用户管理服务"""

    @property
    def model_class(self):
        return User

    def create_user(self, user_data: UserCreate, created_by: Optional[int] = None) -> User:
        """创建新用户"""
        # 检查用户名是否已存在
        existing_user = self.get_user_by_username(user_data.username)
        if existing_user:
            raise UserBusinessException.username_already_exists(user_data.username)

        # 检查邮箱是否已存在（在当前租户内）
        if user_data.email:
            existing_email_user = self.get_user_by_email(user_data.email)
            if existing_email_user:
                raise UserBusinessException.email_already_exists(user_data.email)

        user_dict = user_data.model_dump()
        user_dict['password_hash'] = get_password_hash(user_dict.pop('password'))
        user_dict['tenant_id'] = self.tenant_id
        user_dict['created_by'] = created_by

        user = User(**user_dict)
        # 设置创建时间和更新时间（统一UTC）
        now = now_utc()
        user.created_at = now
        user.updated_at = now

        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)
        
        # 验证对象是否真的存在于数据库
        db_user = self.session.get(User, user.id)
        if not db_user:
            raise UserBusinessException.general_error("用户创建失败：无法从数据库检索新创建的用户")

        return user

    def get_user(self, user_id: int) -> Optional[User]:
        """根据ID获取用户（RLS自动过滤租户）"""
        return self.session.get(User, user_id)

    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        statement = select(User).where(User.username == username)
        return self.session.exec(statement).first()

    def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户（RLS自动过滤租户）"""
        statement = select(User).where(User.email == email)
        return self.session.exec(statement).first()

    def get_users(self,
                  skip: int = 0,
                  limit: int = 100,
                  role: Optional[UserRole] = None,
                  status: Optional[UserStatus] = None,
                  search: Optional[str] = None) -> List[User]:
        """获取用户列表（RLS自动过滤租户）"""
        statement = select(User)

        if role:
            statement = statement.where(User.role == role)
        if status:
            statement = statement.where(User.status == status)
        if search:
            # 搜索用户名、邮箱、真实姓名
            search_filter = (
                User.username.ilike(f"%{search}%") |
                User.email.ilike(f"%{search}%") |
                User.real_name.ilike(f"%{search}%")
            )
            statement = statement.where(search_filter)

        statement = statement.offset(skip).limit(limit)
        return self.session.exec(statement).all()

    def get_users_with_pagination(self, params: UserSearchParams) -> UserListResponse:
        """获取用户列表（带分页信息）"""
        # 获取总数
        count_statement = select(User)
        if params.role:
            count_statement = count_statement.where(User.role == params.role)
        if params.status:
            count_statement = count_statement.where(User.status == params.status)
        if params.search:
            search_filter = (
                User.username.ilike(f"%{params.search}%") |
                User.email.ilike(f"%{params.search}%") |
                User.real_name.ilike(f"%{params.search}%")
            )
            count_statement = count_statement.where(search_filter)

        from sqlalchemy import func
        total = self.session.exec(select(func.count()).select_from(count_statement.subquery())).one()

        # 获取数据
        users = self.get_users(
            skip=params.skip,
            limit=params.limit,
            role=params.role,
            status=params.status,
            search=params.search
        )

        return UserListResponse(
            items=users,
            total=total,
            skip=params.skip,
            limit=params.limit
        )

    def update_user(self, user_id: int, user_data: UserUpdate) -> User:
        """更新用户信息"""
        user = self.session.get(User, user_id)
        if not user:
            raise UserNotFoundError()

        update_data = user_data.model_dump(exclude_unset=True)

        # 如果更新邮箱，检查是否已存在
        if 'email' in update_data and update_data['email']:
            existing_email_user = self.get_user_by_email(update_data['email'])
            if existing_email_user and existing_email_user.id != user_id:
                raise UserBusinessException.email_already_exists(update_data['email'])

        for field, value in update_data.items():
            setattr(user, field, value)

        user.updated_at = now_utc()

        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)

        return user

    def change_password(self, user_id: int, old_password: str, new_password: str) -> User:
        """修改密码"""
        user = self.session.get(User, user_id)
        if not user:
            raise UserNotFoundError()

        if not verify_password(old_password, user.password_hash):
            raise UserBusinessException.invalid_password()

        user.password_hash = get_password_hash(new_password)
        user.updated_at = now_utc()

        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)

        return user

    def reset_password(self, user_id: int, new_password: str) -> User:
        """重置密码（管理员操作）"""
        user = self.session.get(User, user_id)
        if not user:
            raise UserNotFoundError()

        user.password_hash = get_password_hash(new_password)
        user.updated_at = now_utc()

        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)

        return user

    def activate_user(self, user_id: int) -> User:
        """激活用户"""
        user = self.session.get(User, user_id)
        if not user:
            raise UserNotFoundError()

        user.status = UserStatus.ACTIVE
        user.updated_at = datetime.now()

        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)

        return user

    def deactivate_user(self, user_id: int) -> User:
        """停用用户"""
        user = self.session.get(User, user_id)
        if not user:
            raise UserNotFoundError()

        user.status = UserStatus.INACTIVE
        user.updated_at = datetime.now()

        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)

        return user

    def lock_user(self, user_id: int, lock_until: Optional[datetime] = None) -> User:
        """锁定用户"""
        user = self.session.get(User, user_id)
        if not user:
            raise UserNotFoundError()

        user.status = UserStatus.LOCKED
        user.locked_until = lock_until
        user.updated_at = datetime.now()

        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)

        return user

    def delete_user(self, user_id: int) -> None:
        """删除用户（硬删除）"""
        user = self.session.get(User, user_id)
        if not user:
            raise UserNotFoundError()

        self.session.delete(user)
        self.session.commit()

    def get_salesmen(self) -> List[User]:
        """获取所有销售人员（角色为AGENT，状态为ACTIVE的用户）"""
        statement = select(User).where(
            (User.role.in_([UserRole.AGENT])) &
            (User.status == UserStatus.ACTIVE)
        )
        return self.session.exec(statement).all()


def get_user_service(session: Session, tenant_id: Optional[int] = None) -> UserService:
    """获取用户服务实例"""
    return UserService(session, tenant_id)
