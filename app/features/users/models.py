from sqlmodel import SQLModel, Field, UniqueConstraint, Index, ForeignKeyConstraint
from typing import Optional, Dict, Any
from datetime import datetime, timezone, date
from pydantic import EmailStr
from sqlalchemy import String, Column, JSON
from enum import Enum


class UserRole(str, Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"  # 超级管理员（跨租户）
    ADMIN = "admin"              # 租户管理员
    AGENT = "agent"              # 代理人员


class UserStatus(str, Enum):
    """用户状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    LOCKED = "locked"


class Gender(str, Enum):
    """性别枚举"""
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"


class UserBase(SQLModel):
    """用户基础模型"""
    tenant_id: Optional[int] = Field(default=None, foreign_key="tenants.id", description="租户ID，super_admin为null")
    # 登录信息
    username: str = Field(max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(default=None, sa_type=String(100), description="邮箱")
    phone: Optional[str] = Field(default=None, max_length=20, description="手机号")
    password_hash: str = Field(max_length=255, description="密码哈希")

    # 基础信息
    real_name: Optional[str] = Field(default=None, max_length=50, description="真实姓名")
    avatar_url: Optional[str] = Field(default=None, max_length=500, description="头像URL")
    gender: Optional[Gender] = Field(default=None, description="性别")
    birthday: Optional[date] = Field(default=None, description="生日")

    # 角色和状态
    role: UserRole = Field(description="用户角色")
    status: UserStatus = Field(default=UserStatus.ACTIVE, description="用户状态")

    # 登录信息
    last_login_at: Optional[datetime] = Field(default=None, description="最后登录时间")
    login_count: int = Field(default=0, description="登录次数")
    failed_login_attempts: int = Field(default=0, description="失败登录尝试次数")
    locked_until: Optional[datetime] = Field(default=None, description="锁定至")


class User(UserBase, table=True):
    """用户数据库模型"""
    __tablename__ = "users"

    id: Optional[int] = Field(default=None, primary_key=True)

    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    updated_at: Optional[datetime] = Field(default=None)
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者")
    # created_by: Optional[int] = Field(default=None, description="创建者")

    __table_args__ = (
        # 全局用户名唯一
        UniqueConstraint('username', name='uq_username'),
        # 租户内邮箱唯一（允许跨租户重复）
        UniqueConstraint('tenant_id', 'email', name='uq_tenant_email'),
        # 租户内手机号唯一
        UniqueConstraint('tenant_id', 'phone', name='uq_tenant_phone'),
        # 索引
        Index('idx_tenant_users', 'tenant_id', 'id'),
        Index('idx_user_role', 'tenant_id', 'role'),
        Index('idx_user_status', 'tenant_id', 'status'),
    )


class UserSessionBase(SQLModel):
    """用户会话基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    user_id: int = Field(foreign_key="users.id", description="用户ID")

    # 会话信息
    session_token: str = Field(max_length=255, unique=True, description="会话令牌")
    refresh_token: Optional[str] = Field(default=None, max_length=255, unique=True, description="刷新令牌")
    expires_at: datetime = Field(description="过期时间")

    # 设备信息
    device_type: Optional[str] = Field(default=None, max_length=20, description="设备类型")
    device_info: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="设备信息")
    ip_address: Optional[str] = Field(default=None, description="IP地址")
    user_agent: Optional[str] = Field(default=None, description="用户代理")

    # 状态
    is_active: bool = Field(default=True, description="是否激活")
    last_activity_at: datetime = Field(default_factory=lambda: datetime.now(), description="最后活跃时间")


class UserSession(UserSessionBase, table=True):
    """用户会话数据库模型"""
    __tablename__ = "user_sessions"

    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now())

    __table_args__ = (
        Index('idx_user_sessions', 'user_id'),
        Index('idx_session_token', 'session_token'),
        Index('idx_session_active', 'is_active', 'expires_at'),
    )
