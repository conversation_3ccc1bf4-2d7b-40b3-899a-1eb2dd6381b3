from sqlmodel import SQLModel, Field
from pydantic import EmailStr
from typing import Optional
from datetime import datetime
from .models import TenantBase, TenantPlanTemplateBase, PlanType, TenantStatus


# Tenant API Schemas
class TenantCreate(TenantBase):
    """创建租户请求模型"""
    # 重新定义必填字段以确保验证
    name: str = Field(min_length=1, max_length=100, description="机构名称")
    code: str = Field(min_length=1, max_length=50, description="机构代码")
    contact_email: Optional[EmailStr] = Field(default=None, description="联系邮箱")
    # contact_email: Optional[str] = Field(default=None, pattern=r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', description="联系邮箱")


class TenantUpdate(SQLModel):
    """更新租户请求模型"""
    name: Optional[str] = None
    display_name: Optional[str] = None
    description: Optional[str] = None
    contact_name: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None
    address: Optional[str] = None
    plan_type: Optional[PlanType] = None
    status: Optional[TenantStatus] = None


class TenantRead(TenantBase):
    """租户响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    database_schema: Optional[str] = None
    api_key: Optional[str] = None 
    trial_expires_at: Optional[datetime] = None


# Tenant Plan Template API Schemas
class TenantPlanTemplateCreate(TenantPlanTemplateBase):
    """创建租户套餐模板请求模型"""
    pass


class TenantPlanTemplateUpdate(SQLModel):
    """更新租户套餐模板请求模型"""
    plan_name: Optional[str] = None
    description: Optional[str] = None
    max_teachers: Optional[int] = None
    max_members: Optional[int] = None
    max_storage_gb: Optional[int] = None
    monthly_price: Optional[int] = None
    yearly_price: Optional[int] = None
    setup_fee: Optional[int] = None
    price_per_class: Optional[int] = None
    is_active: Optional[bool] = None
    sort_order: Optional[int] = None


class TenantPlanTemplateRead(TenantPlanTemplateBase):
    """租户套餐模板响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
