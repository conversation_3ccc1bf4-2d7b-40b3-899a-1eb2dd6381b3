from sqlmodel import SQLModel, Field
from typing import Optional, List
from datetime import datetime
from .models import TagCategoryBase, TagBase, TagStatus


# 标签分类相关Schema
class TagCategoryCreate(SQLModel):
    """创建标签分类请求模型"""
    name: str = Field(min_length=1, max_length=50, description="分类名称")
    description: Optional[str] = Field(default=None, max_length=200, description="分类描述")
    sort_order: int = Field(default=0, description="排序顺序")


class TagCategoryUpdate(SQLModel):
    """更新标签分类请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=50, description="分类名称")
    description: Optional[str] = Field(default=None, max_length=200, description="分类描述")
    sort_order: Optional[int] = Field(default=None, description="排序顺序")


class TagCategoryRead(TagCategoryBase):
    """标签分类响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None


class TagCategoryList(SQLModel):
    """标签分类列表响应模型"""
    id: int
    name: str
    description: Optional[str] = None
    sort_order: int
    tag_count: int = Field(default=0, description="该分类下的标签数量")
    created_at: datetime
    updated_at: Optional[datetime] = None


# 标签相关Schema
class TagCreate(SQLModel):
    """创建标签请求模型"""
    name: str = Field(min_length=1, max_length=50, description="标签名称")
    category_id: int = Field(gt=0, description="标签分类ID")
    description: Optional[str] = Field(default=None, max_length=200, description="标签描述")
    status: TagStatus = Field(default=TagStatus.ACTIVE, description="标签状态")

    # 注意：category_name 不在创建请求中，会在service层自动填充


class TagUpdate(SQLModel):
    """更新标签请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=50, description="标签名称")
    category_id: Optional[int] = Field(default=None, gt=0, description="标签分类ID")
    description: Optional[str] = Field(default=None, max_length=200, description="标签描述")
    status: Optional[TagStatus] = Field(default=None, description="标签状态")

    # 注意：category_name 不在更新请求中，当category_id变更时会在service层自动更新


class TagRead(TagBase):
    """标签响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None


class TagList(SQLModel):
    """标签列表响应模型"""
    id: int
    name: str
    description: Optional[str] = None
    status: TagStatus
    category_id: int
    category_name: str = Field(description="分类名称")


class TagWithTeacherCount(TagRead):
    """带教师数量的标签响应模型"""
    teacher_count: int = Field(default=0, description="使用该标签的教师数量")

# 批量操作Schema
class TagBatchCreate(SQLModel):
    """批量创建标签请求模型"""
    category_id: int = Field(gt=0, description="标签分类ID")
    tags: List[str] = Field(min_items=1, max_items=50, description="标签名称列表")


class TagBatchUpdate(SQLModel):
    """批量更新标签请求模型"""
    tag_ids: List[int] = Field(min_items=1, description="标签ID列表")
    status: Optional[TagStatus] = Field(default=None, description="标签状态")
    category_id: Optional[int] = Field(default=None, gt=0, description="标签分类ID")


# 查询参数Schema
class TagCategoryQuery(SQLModel):
    """标签分类查询参数"""
    name: Optional[str] = Field(default=None, description="分类名称模糊查询")


class TagQuery(SQLModel):
    """标签查询参数"""
    name: Optional[str] = Field(default=None, description="标签名称模糊查询")
    category_id: Optional[int] = Field(default=None, description="标签分类ID")
    status: Optional[TagStatus] = Field(default=None, description="标签状态")
