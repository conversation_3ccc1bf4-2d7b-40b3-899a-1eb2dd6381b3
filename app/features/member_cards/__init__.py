"""
会员卡模块

包含会员卡模板、会员卡实例、交易记录、操作日志等功能
"""

from .models import (
    # 枚举
    CardType,
    CardStatus,
    PaymentMethod,
    MemberCardOperationType,

    # 模型
    MemberCardTemplate,
    MemberCardTemplateBase,
    MemberCard,
    MemberCardBase,
    MemberCardOperation,
    MemberCardOperationBase,
)

# 导出Schema
from .schemas import (
    # 会员卡模板Schema
    MemberCardTemplateCreate,
    MemberCardTemplateUpdate,
    MemberCardTemplateRead,
    MemberCardTemplateList,

    # 会员卡实例Schema
    MemberCardCreate,
    MemberCardUpdate,
    MemberCardRead,
    MemberCardList,
    MemberCardSummary,

    # 充值相关Schema
    RechargeRequest,
    RechargeResponse,

    # 消费相关Schema
    ConsumptionRequest,
    ConsumptionResponse,

    # 操作记录Schema
    MemberCardOperationRead,
    MemberCardOperationList,

    # 查询参数Schema
    MemberCardQuery,
    MemberCardOperationQuery,

    # 余额验证Schema
    BalanceCheckRequest,
    BalanceCheckResponse,

    # 统计相关Schema
    MemberCardStatistics,
    RechargeStatistics,
    ConsumptionStatistics,

    # 批量操作Schema
    BatchRechargeRequest,
    BatchRechargeResponse,
    BatchCardStatusUpdate,

    # 课程预约集成Schema
    CourseBookingBalanceCheck,
    CourseBookingBalanceResponse,
    CourseBookingDeduction,

    # 异常处理Schema
    MemberCardError,
)

# 导出服务
from .template_service import MemberCardTemplateService, get_member_card_template_service
from .card_service import MemberCardService, get_member_card_service
from .recharge_service import RechargeService, get_recharge_service
from .consumption_service import ConsumptionService, get_consumption_service

# 导出异常
from .exceptions import (
    MemberCardErrorCode,
    MemberCardTemplateNotFoundError,
    MemberCardTemplateBusinessException,
    MemberCardNotFoundError,
    MemberCardBusinessException,
    RechargeBusinessException,
    ConsumptionBusinessException,
    MemberCardOperationNotFoundError,
    MemberCardOperationBusinessException,
    MemberCardGeneralException,
)

__all__ = [
    # 枚举
    "CardType",
    "CardStatus",
    "PaymentMethod",
    "MemberCardOperationType",

    # 模型
    "MemberCardTemplate",
    "MemberCardTemplateBase",
    "MemberCard",
    "MemberCardBase",
    "MemberCardOperation",
    "MemberCardOperationBase",

    # 会员卡模板Schema
    "MemberCardTemplateCreate",
    "MemberCardTemplateUpdate",
    "MemberCardTemplateRead",
    "MemberCardTemplateList",

    # 会员卡实例Schema
    "MemberCardCreate",
    "MemberCardUpdate",
    "MemberCardRead",
    "MemberCardList",
    "MemberCardSummary",

    # 充值相关Schema
    "RechargeRequest",
    "RechargeResponse",

    # 消费相关Schema
    "ConsumptionRequest",
    "ConsumptionResponse",

    # 操作记录Schema
    "MemberCardOperationRead",
    "MemberCardOperationList",

    # 查询参数Schema
    "MemberCardQuery",
    "MemberCardOperationQuery",

    # 余额验证Schema
    "BalanceCheckRequest",
    "BalanceCheckResponse",

    # 统计相关Schema
    "MemberCardStatistics",
    "RechargeStatistics",
    "ConsumptionStatistics",

    # 批量操作Schema
    "BatchRechargeRequest",
    "BatchRechargeResponse",
    "BatchCardStatusUpdate",

    # 课程预约集成Schema
    "CourseBookingBalanceCheck",
    "CourseBookingBalanceResponse",
    "CourseBookingDeduction",

    # 异常处理Schema
    "MemberCardError",

    # 服务类
    "MemberCardTemplateService",
    "get_member_card_template_service",
    "MemberCardService",
    "get_member_card_service",
    "RechargeService",
    "get_recharge_service",
    "ConsumptionService",
    "get_consumption_service",

    # 异常类
    "MemberCardErrorCode",
    "MemberCardTemplateNotFoundError",
    "MemberCardTemplateBusinessException",
    "MemberCardNotFoundError",
    "MemberCardBusinessException",
    "RechargeBusinessException",
    "ConsumptionBusinessException",
    "MemberCardOperationNotFoundError",
    "MemberCardOperationBusinessException",
    "MemberCardGeneralException",
]
