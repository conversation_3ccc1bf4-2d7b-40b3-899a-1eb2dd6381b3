"""
会员卡充值服务

提供会员卡充值相关的业务逻辑
"""

from typing import List, Optional
from sqlmodel import Session, select, and_, func
from datetime import datetime, timezone
import uuid

from app.features.base.base_service import TenantAwareService

from .models import (
    MemberCard, MemberCardOperation, CardStatus, PaymentMethod, 
    MemberCardOperationType
)
from .schemas import (
    RechargeRequest,
    RechargeResponse,
    BatchRechargeRequest,
    BatchRechargeResponse,
    DeductionRequest,
    DeductionResponse
)
from .exceptions import (
    MemberCardNotFoundError,
    MemberCardBusinessException,
    RechargeBusinessException
)


class RechargeService(TenantAwareService[MemberCardOperation]):
    """充值服务"""
    
    @property
    def model_class(self):
        return MemberCardOperation
    
    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)
        # 充值限制配置
        self.min_recharge_amount = 1  # 最小充值金额（元）
        self.max_recharge_amount = 50000  # 最大充值金额（元）
        self.max_bonus_rate = 0.5  # 最大赠送比例（50%）
    
    def recharge(self, recharge_data: RechargeRequest, operator_id: Optional[int] = None) -> RechargeResponse:
        """执行充值操作"""
        
        # 验证会员卡
        card = self.session.get(MemberCard, recharge_data.member_card_id)
        if not card:
            raise MemberCardNotFoundError(recharge_data.member_card_id)
        
        # 验证卡片状态
        self._validate_card_for_recharge(card)
        
        # 验证充值金额
        self._validate_recharge_amount(recharge_data.amount, recharge_data.bonus_amount)
        
        # 验证支付方式
        self._validate_payment_method(recharge_data.payment_method)
        
        # 记录充值前余额
        balance_before = card.balance
        
        # 计算充值后余额
        total_amount = recharge_data.amount + recharge_data.bonus_amount
        balance_after = balance_before + total_amount
        
        # 生成交易ID
        transaction_id = self._generate_transaction_id()
        
        try:
            # 开始事务
            # 使用原子操作更新会员卡余额和统计信息
            # 这样可以避免并发情况下的余额计算错误
            from sqlmodel import update
            update_values = {
                'balance': MemberCard.balance + total_amount,
                'total_recharged': MemberCard.total_recharged + total_amount,
                'last_used_at': datetime.now(),
                'updated_at': datetime.now()
            }
            
            # 处理有效期延长
            operation_description = f"充值 {recharge_data.amount}元"
            if recharge_data.bonus_amount > 0:
                operation_description += f"，赠送 {recharge_data.bonus_amount}元"
                
            if recharge_data.extend_validity_days and recharge_data.extend_validity_days > 0:
                # 如果卡片已有有效期且未过期，则在原有效期基础上延长
                if card.expires_at and card.expires_at > datetime.now():
                    from datetime import timedelta
                    new_expires_at = card.expires_at + timedelta(days=recharge_data.extend_validity_days)
                    update_values['expires_at'] = new_expires_at
                    operation_description += f"，延长有效期 {recharge_data.extend_validity_days} 天"
                # 如果卡片没有有效期或已过期，则从当前时间开始计算
                else:
                    from datetime import timedelta
                    new_expires_at = datetime.now() + timedelta(days=recharge_data.extend_validity_days)
                    update_values['expires_at'] = new_expires_at
                    operation_description += f"，设置有效期 {recharge_data.extend_validity_days} 天"
            
            update_stmt = update(MemberCard).where(
                MemberCard.id == recharge_data.member_card_id
            ).values(**update_values)

            result = self.session.exec(update_stmt)
            if result.rowcount == 0:
                # 更新失败，卡片可能不存在
                raise MemberCardNotFoundError(recharge_data.member_card_id)

            # 刷新卡片对象以获取最新数据
            self.session.refresh(card)
            balance_after = card.balance
            
            # 创建操作记录
            operation_data = {
                'tenant_id': self.tenant_id,
                'member_id': card.member_id,
                'member_card_id': card.id,
                'operation_type': MemberCardOperationType.RECHARGE,
                'operation_description': operation_description,
                'amount_change': total_amount,
                'balance_before': balance_before,
                'balance_after': balance_after,
                'payment_method': recharge_data.payment_method,
                'payment_amount': recharge_data.amount,
                'actual_amount': recharge_data.actual_amount,
                'bonus_amount': recharge_data.bonus_amount,
                'transaction_id': transaction_id,
                'notes': recharge_data.notes,
                'operator_id': operator_id,
                'status': 'completed'
            }
            
            operation = self.create(operation_data, operator_id, auto_commit=False)
            
            # 提交事务
            self.session.commit()
            self.session.refresh(card)
            self.session.refresh(operation)
            
            # 返回充值响应
            return RechargeResponse(
                operation_id=operation.id,
                member_card_id=card.id,
                amount=recharge_data.amount,
                bonus_amount=recharge_data.bonus_amount,
                total_amount=total_amount,
                balance_before=balance_before,
                balance_after=balance_after,
                payment_method=recharge_data.payment_method,
                transaction_id=transaction_id,
                created_at=operation.created_at,
                extend_validity_days=recharge_data.extend_validity_days if recharge_data.extend_validity_days and recharge_data.extend_validity_days > 0 else None,
                expires_at=card.expires_at
            )
            
        except Exception as e:
            self.session.rollback()
            raise RechargeBusinessException.recharge_failed(str(e))
    
    def batch_recharge(self, batch_request: BatchRechargeRequest, operator_id: Optional[int] = None) -> BatchRechargeResponse:
        """批量充值"""
        
        success_items = []
        failed_items = []
        
        for recharge_item in batch_request.recharge_items:
            try:
                result = self.recharge(recharge_item, operator_id)
                success_items.append(result)
            except Exception as e:
                failed_items.append({
                    "member_card_id": recharge_item.member_card_id,
                    "amount": recharge_item.amount,
                    "error": str(e)
                })
        
        return BatchRechargeResponse(
            success_count=len(success_items),
            failed_count=len(failed_items),
            success_items=success_items,
            failed_items=failed_items
        )
    
    def get_recharge_history(self, member_card_id: int, limit: int = 50) -> List[MemberCardOperation]:
        """获取充值历史记录"""
        
        statement = select(MemberCardOperation).where(
            and_(
                MemberCardOperation.member_card_id == member_card_id,
                MemberCardOperation.operation_type == MemberCardOperationType.RECHARGE
            )
        ).order_by(MemberCardOperation.created_at.desc()).limit(limit)
        
        return self.session.exec(statement).all()
    
    def get_member_recharge_statistics(self, member_id: int) -> dict:
        """获取会员充值统计"""
        
        # 总充值次数和金额
        recharge_stats = self.session.exec(
            select(
                func.count(MemberCardOperation.id).label('total_count'),
                func.sum(MemberCardOperation.actual_amount).label('total_amount'),
                func.sum(MemberCardOperation.bonus_amount).label('total_bonus')
            ).where(
                and_(
                    MemberCardOperation.member_id == member_id,
                    MemberCardOperation.operation_type == MemberCardOperationType.RECHARGE,
                    MemberCardOperation.status == 'completed'
                )
            )
        ).first()
        
        # 按支付方式统计
        payment_stats = {}
        for payment_method in PaymentMethod:
            stats = self.session.exec(
                select(
                    func.count(MemberCardOperation.id).label('count'),
                    func.sum(MemberCardOperation.actual_amount).label('amount')
                ).where(
                    and_(
                        MemberCardOperation.member_id == member_id,
                        MemberCardOperation.operation_type == MemberCardOperationType.RECHARGE,
                        MemberCardOperation.payment_method == payment_method,
                        MemberCardOperation.status == 'completed'
                    )
                )
            ).first()
            
            payment_stats[payment_method.value] = {
                'count': stats.count or 0,
                'amount': stats.amount or 0
            }
        
        return {
            'total_recharge_count': recharge_stats.total_count or 0,
            'total_recharge_amount': recharge_stats.total_amount or 0,
            'total_bonus_amount': recharge_stats.total_bonus or 0,
            'payment_method_stats': payment_stats
        }
    
    def get_daily_recharge_statistics(self, date_from: datetime, date_to: datetime) -> dict:
        """获取指定时间段的每日充值统计"""
        
        statement = select(
            func.date(MemberCardOperation.created_at).label('date'),
            func.count(MemberCardOperation.id).label('count'),
            func.sum(MemberCardOperation.actual_amount).label('amount'),
            func.sum(MemberCardOperation.bonus_amount).label('bonus')
        ).where(
            and_(
                MemberCardOperation.operation_type == MemberCardOperationType.RECHARGE,
                MemberCardOperation.status == 'completed',
                MemberCardOperation.created_at >= date_from,
                MemberCardOperation.created_at <= date_to
            )
        ).group_by(func.date(MemberCardOperation.created_at)).order_by('date')
        
        results = self.session.exec(statement).all()
        
        return {
            'daily_stats': [
                {
                    'date': str(result.date),
                    'count': result.count,
                    'amount': result.amount or 0,
                    'bonus': result.bonus or 0
                }
                for result in results
            ]
        }
    
    def deduct(self, deduction_data: DeductionRequest, operator_id: Optional[int] = None) -> DeductionResponse:
        """执行扣费操作"""
        
        # 验证会员卡
        card = self.session.get(MemberCard, deduction_data.member_card_id)
        if not card:
            raise MemberCardNotFoundError(deduction_data.member_card_id)
        
        # 验证卡片状态
        self._validate_card_for_deduction(card)
        
        # 验证扣费金额
        if deduction_data.amount <= 0:
            raise RechargeBusinessException.amount_too_small(deduction_data.amount, 1)
            
        # 验证余额是否充足
        if card.balance < deduction_data.amount:
            raise MemberCardBusinessException.insufficient_balance(card.id, card.balance, deduction_data.amount)
        
        # 记录扣费前余额
        balance_before = card.balance
        
        # 计算扣费后余额
        balance_after = balance_before - deduction_data.amount
        
        # 生成交易ID
        transaction_id = self._generate_transaction_id("DD")
        
        try:
            # 开始事务
            # 使用原子操作更新会员卡余额和统计信息
            from sqlmodel import update
            update_values = {
                'balance': MemberCard.balance - deduction_data.amount,
                'total_consumed': MemberCard.total_consumed + deduction_data.amount,
                'last_used_at': datetime.now(),
                'updated_at': datetime.now()
            }
            
            # 处理有效期减少
            operation_description = f"扣费 {deduction_data.amount}元"
                
            if deduction_data.reduce_validity_days and deduction_data.reduce_validity_days > 0:
                # 如果卡片已有有效期，则减少有效期
                if card.expires_at:
                    from datetime import timedelta
                    new_expires_at = card.expires_at - timedelta(days=deduction_data.reduce_validity_days)
                    # 确保有效期不会小于当前时间
                    if new_expires_at < datetime.now():
                        new_expires_at = datetime.now()
                    update_values['expires_at'] = new_expires_at
                    operation_description += f"，减少有效期 {deduction_data.reduce_validity_days} 天"
            
            update_stmt = update(MemberCard).where(
                MemberCard.id == deduction_data.member_card_id
            ).values(**update_values)

            result = self.session.exec(update_stmt)
            if result.rowcount == 0:
                # 更新失败，卡片可能不存在
                raise MemberCardNotFoundError(deduction_data.member_card_id)

            # 刷新卡片对象以获取最新数据
            self.session.refresh(card)
            balance_after = card.balance
            
            # 创建操作记录
            operation_data = {
                'tenant_id': self.tenant_id,
                'member_id': card.member_id,
                'member_card_id': card.id,
                'operation_type': MemberCardOperationType.MANUAL_DEDUCTION,
                'operation_description': operation_description,
                'amount_change': -deduction_data.amount,  # 负数表示减少
                'balance_before': balance_before,
                'balance_after': balance_after,
                'transaction_id': transaction_id,
                'notes': deduction_data.notes,
                'operator_id': operator_id,
                'status': 'completed'
            }
            
            operation = self.create(operation_data, operator_id, auto_commit=False)
            
            # 提交事务
            self.session.commit()
            self.session.refresh(card)
            self.session.refresh(operation)
            
            # 返回扣费响应
            return DeductionResponse(
                operation_id=operation.id,
                member_card_id=card.id,
                amount=deduction_data.amount,
                balance_before=balance_before,
                balance_after=balance_after,
                reduce_validity_days=deduction_data.reduce_validity_days if deduction_data.reduce_validity_days and deduction_data.reduce_validity_days > 0 else None,
                expires_at=card.expires_at,
                transaction_id=transaction_id,
                created_at=operation.created_at
            )
            
        except Exception as e:
            self.session.rollback()
            raise RechargeBusinessException.recharge_failed(f"扣费失败: {str(e)}")
    
    # 私有验证方法
    def _validate_card_for_recharge(self, card: MemberCard):
        """验证会员卡是否可以充值"""
        
        if card.status == CardStatus.CANCELLED:
            raise MemberCardBusinessException.card_cancelled(card.id, card.cancel_reason)
        
        if card.status == CardStatus.FROZEN:
            raise MemberCardBusinessException.card_frozen(card.id, card.freeze_reason)
        
        # 过期卡允许充值，充值后会更新有效期
        # if card.expires_at and card.expires_at <= datetime.now():
        #     raise MemberCardBusinessException.card_expired(card.id, card.expires_at.isoformat())
    
    def _validate_recharge_amount(self, amount: int, bonus_amount: int):
        """验证充值金额"""
        
        if amount < self.min_recharge_amount:
            raise RechargeBusinessException.amount_too_small(amount, self.min_recharge_amount)
        
        if amount > self.max_recharge_amount:
            raise RechargeBusinessException.amount_too_large(amount, self.max_recharge_amount)
        
        if bonus_amount < 0:
            raise MemberCardBusinessException.invalid_amount(bonus_amount, "赠送金额不能为负数")
        
        # 验证赠送比例
        if bonus_amount > amount * self.max_bonus_rate:
            raise MemberCardBusinessException.invalid_amount(
                bonus_amount, 
                f"赠送金额不能超过充值金额的{int(self.max_bonus_rate * 100)}%"
            )
    
    def _validate_payment_method(self, payment_method: PaymentMethod):
        """验证支付方式"""
        
        # 这里可以添加支付方式的业务验证
        # 例如：某些支付方式在特定时间不可用等
        valid_methods = [PaymentMethod.WECHAT, PaymentMethod.ALIPAY, PaymentMethod.MANUAL, PaymentMethod.BANK_TRANSFER]
        
        if payment_method not in valid_methods:
            raise RechargeBusinessException.invalid_payment_method(payment_method.value)
    
    def _generate_transaction_id(self, prefix: str = "RC") -> str:
        """生成交易ID"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = str(uuid.uuid4())[:8].upper()
        return f"{prefix}{timestamp}{random_suffix}"

    def _validate_card_for_deduction(self, card: MemberCard):
        """验证会员卡是否可以扣费"""
        
        if card.status == CardStatus.CANCELLED:
            raise MemberCardBusinessException.card_cancelled(card.id, card.cancel_reason)
        
        if card.status == CardStatus.FROZEN:
            raise MemberCardBusinessException.card_frozen(card.id, card.freeze_reason)
        
        # 过期卡不能扣费
        if card.expires_at and card.expires_at <= datetime.now():
            raise MemberCardBusinessException.card_expired(card.id, card.expires_at.isoformat())


def get_recharge_service(session: Session, tenant_id: int) -> RechargeService:
    """获取充值服务实例"""
    return RechargeService(session, tenant_id)
