"""
会员卡模块 Schema 定义

包含会员卡模板、会员卡实例、操作记录等相关的API请求和响应模型
"""

from sqlmodel import SQLModel, Field
from typing import Optional, List
from datetime import datetime
from pydantic import validator, field_validator

from .models import (
    CardType, CardStatus, PaymentMethod, MemberCardOperationType,
    MemberCardTemplateBase, MemberCardBase, MemberCardOperationBase
)


# ==================== 会员卡模板 Schema ====================

class MemberCardTemplateCreate(SQLModel):
    """创建会员卡模板请求模型"""
    name: str = Field(min_length=1, max_length=100, description="卡片模板名称")
    card_type: CardType = Field(description="卡片类型")
    sale_price: int = Field(ge=0, description="售卖价格（元）")
    
    # 扩展字段
    available_balance: Optional[int] = Field(default=None, ge=0, description="可用余额（元或次数）")
    validity_days: Optional[int] = Field(default=None, gt=0, description="有效期(天)")
    is_agent_exclusive: bool = Field(default=False, description="是否代理专售")
    allow_repeat_purchase: bool = Field(default=True, description="是否允许重复购买")
    allow_renewal: bool = Field(default=True, description="是否支持线上续费")
    description: Optional[str] = Field(default=None, max_length=500, description="模板描述")


class MemberCardTemplateUpdate(SQLModel):
    """更新会员卡模板请求模型"""
    name: Optional[str] = Field(default=None, min_length=1, max_length=100, description="卡片模板名称")
    card_type: Optional[CardType] = Field(default=None, description="卡片类型（不允许修改）")
    sale_price: Optional[int] = Field(default=None, ge=0, description="售卖价格（元）")
    
    # 扩展字段
    available_balance: Optional[int] = Field(default=None, ge=0, description="可用余额（元或次数）")
    validity_days: Optional[int] = Field(default=None, gt=0, description="有效期(天)")
    is_agent_exclusive: Optional[bool] = Field(default=None, description="是否代理专售")
    allow_repeat_purchase: Optional[bool] = Field(default=None, description="是否允许重复购买")
    allow_renewal: Optional[bool] = Field(default=None, description="是否支持线上续费")
    description: Optional[str] = Field(default=None, max_length=500, description="模板描述")
    is_active: Optional[bool] = Field(default=None, description="是否启用")


class MemberCardTemplateRead(MemberCardTemplateBase):
    """会员卡模板响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None


class MemberCardTemplateList(SQLModel):
    """会员卡模板列表项模型"""
    id: int
    name: str
    card_type: CardType
    sale_price: int
    available_balance: Optional[int] = None
    validity_days: Optional[int] = None
    is_agent_exclusive: bool
    is_active: bool
    created_at: datetime


# ==================== 会员卡实例 Schema ====================

class MemberCardCreate(SQLModel):
    """创建会员卡请求模型"""
    member_id: int = Field(gt=0, description="会员ID")
    template_id: int = Field(gt=0, description="模板ID")


class MemberCardUpdate(SQLModel):
    """更新会员卡请求模型"""
    status: Optional[CardStatus] = Field(default=None, description="卡片状态")
    balance: Optional[int] = Field(default=None, ge=0, description="余额（元或次数）")
    expires_at: Optional[datetime] = Field(default=None, description="过期时间")
    freeze_reason: Optional[str] = Field(default=None, max_length=200, description="冻结原因")
    cancel_reason: Optional[str] = Field(default=None, max_length=200, description="注销原因")


class MemberCardRead(MemberCardBase):
    """会员卡响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[int] = None


class MemberCardList(SQLModel):
    """会员卡列表项模型"""
    id: int
    member_id: int
    name: str
    card_type: CardType
    balance: int
    status: CardStatus
    card_number: Optional[str] = None
    expires_at: Optional[datetime] = None
    last_used_at: Optional[datetime] = None
    created_at: datetime


class MemberCardSummary(SQLModel):
    """会员卡摘要信息模型（用于会员详情页面）"""
    id: int
    name: str
    card_type: CardType
    balance: int
    status: CardStatus
    expires_at: Optional[datetime] = None
    last_used_at: Optional[datetime] = None


# ==================== 充值相关 Schema ====================

class RechargeRequest(SQLModel):
    """充值请求模型"""
    member_card_id: int = Field(gt=0, description="会员卡ID")
    amount: int = Field(gt=0, description="充值金额（元）")
    bonus_amount: Optional[int] = Field(default=0, ge=0, description="赠送金额（元）")
    actual_amount: int = Field(default=0, ge=0, description="实收金额（元）")
    payment_method: PaymentMethod = Field(description="支付方式")
    extend_validity_days: Optional[int] = Field(default=0, ge=0, description="延长有效期（天）")
    notes: Optional[str] = Field(default=None, max_length=500, description="备注信息")


class RechargeResponse(SQLModel):
    """充值响应模型"""
    operation_id: int = Field(description="操作记录ID")
    member_card_id: int = Field(description="会员卡ID")
    amount: int = Field(description="充值金额（元）")
    bonus_amount: int = Field(description="赠送金额（元）")
    total_amount: int = Field(description="总到账金额（元）")
    balance_before: int = Field(description="充值前余额（元）")
    balance_after: int = Field(description="充值后余额（元）")
    payment_method: PaymentMethod = Field(description="支付方式")
    transaction_id: Optional[str] = Field(description="交易ID")
    created_at: datetime = Field(description="充值时间")
    extend_validity_days: Optional[int] = Field(default=None, description="延长的有效期天数")
    expires_at: Optional[datetime] = Field(default=None, description="新的过期时间")


# ==================== 消费相关 Schema ====================

class ConsumptionRequest(SQLModel):
    """消费请求模型（用于手动扣费）"""
    member_card_id: int = Field(gt=0, description="会员卡ID")
    amount: int = Field(gt=0, description="消费金额（元）")
    scheduled_class_id: Optional[int] = Field(default=None, gt=0, description="关联课程ID")
    operation_description: str = Field(min_length=1, max_length=200, description="操作描述")
    reason: Optional[str] = Field(default=None, max_length=500, description="扣费原因")


class ConsumptionResponse(SQLModel):
    """消费响应模型"""
    operation_id: int = Field(description="操作记录ID")
    member_card_id: int = Field(description="会员卡ID")
    amount: int = Field(description="消费金额（元）")
    balance_before: int = Field(description="消费前余额（元）")
    balance_after: int = Field(description="消费后余额（元）")
    scheduled_class_id: Optional[int] = Field(description="关联课程ID")
    created_at: datetime = Field(description="消费时间")


# ==================== 操作记录 Schema ====================

class MemberCardOperationRead(MemberCardOperationBase):
    """会员卡操作记录响应模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None


class MemberCardOperationList(SQLModel):
    """会员卡操作记录列表项模型"""
    id: int
    operation_type: MemberCardOperationType
    operation_description: str
    amount_change: Optional[int] = None
    balance_before: Optional[int] = None
    balance_after: Optional[int] = None
    status_before: Optional[CardStatus] = None
    status_after: Optional[CardStatus] = None
    scheduled_class_id: Optional[int] = None
    operator_name: Optional[str] = None
    created_at: datetime


# ==================== 查询参数 Schema ====================

class MemberCardQuery(SQLModel):
    """会员卡查询参数"""
    member_id: Optional[int] = Field(default=None, gt=0, description="会员ID筛选")
    card_type: Optional[CardType] = Field(default=None, description="卡片类型筛选")
    status: Optional[CardStatus] = Field(default=None, description="卡片状态筛选")
    template_id: Optional[int] = Field(default=None, gt=0, description="模板ID筛选")
    search_keyword: Optional[str] = Field(default=None, max_length=100, description="搜索关键词（卡号、会员姓名）")
    
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    sort_by: Optional[str] = Field(default="created_at", description="排序字段")
    sort_order: Optional[str] = Field(default="desc", regex="^(asc|desc)$", description="排序方向")


class MemberCardOperationQuery(SQLModel):
    """会员卡操作记录查询参数"""
    member_id: Optional[int] = Field(default=None, gt=0, description="会员ID筛选")
    member_card_id: Optional[int] = Field(default=None, gt=0, description="会员卡ID筛选")
    operation_type: Optional[MemberCardOperationType] = Field(default=None, description="操作类型筛选")
    operation_types: Optional[List[MemberCardOperationType]] = Field(default=None, description="操作类型列表筛选")
    scheduled_class_id: Optional[int] = Field(default=None, gt=0, description="课程ID筛选")
    operator_id: Optional[int] = Field(default=None, gt=0, description="操作人ID筛选")
    
    # 时间范围筛选
    date_from: Optional[datetime] = Field(default=None, description="开始时间")
    date_to: Optional[datetime] = Field(default=None, description="结束时间")
    
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    sort_by: Optional[str] = Field(default="created_at", description="排序字段")
    sort_order: Optional[str] = Field(default="desc", regex="^(asc|desc)$", description="排序方向")


# ==================== 余额验证 Schema ====================

class BalanceCheckRequest(SQLModel):
    """余额验证请求模型"""
    member_card_id: int = Field(gt=0, description="会员卡ID")
    required_amount: int = Field(gt=0, description="所需金额（元）")


class BalanceCheckResponse(SQLModel):
    """余额验证响应模型"""
    member_card_id: int = Field(description="会员卡ID")
    current_balance: int = Field(description="当前余额（元）")
    required_amount: int = Field(description="所需金额（元）")
    is_sufficient: bool = Field(description="余额是否充足")
    shortage_amount: int = Field(description="不足金额（元，余额充足时为0）")


# ==================== 统计相关 Schema ====================

class MemberCardStatistics(SQLModel):
    """会员卡统计信息模型"""
    total_cards: int = Field(description="总卡片数")
    active_cards: int = Field(description="激活卡片数")
    frozen_cards: int = Field(description="冻结卡片数")
    expired_cards: int = Field(description="过期卡片数")
    cancelled_cards: int = Field(description="注销卡片数")

    total_balance: int = Field(description="总余额（元）")
    total_recharged: int = Field(description="总充值金额（元）")
    total_consumed: int = Field(description="总消费金额（元）")

    # 按卡片类型统计
    times_limited_count: int = Field(description="次卡-有期限数量")
    times_unlimited_count: int = Field(description="次卡-无期限数量")
    value_limited_count: int = Field(description="储值卡-有期限数量")
    value_unlimited_count: int = Field(description="储值卡-无期限数量")


class RechargeStatistics(SQLModel):
    """充值统计信息模型"""
    total_recharge_count: int = Field(description="总充值次数")
    total_recharge_amount: int = Field(description="总充值金额（元）")
    total_bonus_amount: int = Field(description="总赠送金额（元）")

    # 按支付方式统计
    wechat_count: int = Field(description="微信支付次数")
    wechat_amount: int = Field(description="微信支付金额（元）")
    alipay_count: int = Field(description="支付宝支付次数")
    alipay_amount: int = Field(description="支付宝支付金额（元）")
    manual_count: int = Field(description="手动充值次数")
    manual_amount: int = Field(description="手动充值金额（元）")


class ConsumptionStatistics(SQLModel):
    """消费统计信息模型"""
    total_consumption_count: int = Field(description="总消费次数")
    total_consumption_amount: int = Field(description="总消费金额（元）")

    # 按消费类型统计
    class_fee_count: int = Field(description="课程费用消费次数")
    class_fee_amount: int = Field(description="课程费用消费金额（元）")
    manual_deduction_count: int = Field(description="人工扣费次数")
    manual_deduction_amount: int = Field(description="人工扣费金额（元）")


# ==================== 批量操作 Schema ====================

class BatchRechargeRequest(SQLModel):
    """批量充值请求模型"""
    recharge_items: List[RechargeRequest] = Field(min_items=1, max_items=100, description="充值项目列表")


class BatchRechargeResponse(SQLModel):
    """批量充值响应模型"""
    success_count: int = Field(description="成功数量")
    failed_count: int = Field(description="失败数量")
    success_items: List[RechargeResponse] = Field(description="成功项目列表")
    failed_items: List[dict] = Field(description="失败项目列表（包含错误信息）")


class BatchCardStatusUpdate(SQLModel):
    """批量更新卡片状态请求模型"""
    card_ids: List[int] = Field(min_items=1, max_items=100, description="卡片ID列表")
    status: CardStatus = Field(description="目标状态")
    reason: Optional[str] = Field(default=None, max_length=200, description="操作原因")


# ==================== 课程预约集成 Schema ====================

class CourseBookingBalanceCheck(SQLModel):
    """课程预约余额检查请求模型"""
    member_id: int = Field(gt=0, description="会员ID")
    course_price: int = Field(gt=0, description="课程价格（元）")
    preferred_card_id: Optional[int] = Field(default=None, gt=0, description="优先使用的卡片ID")


class CourseBookingBalanceResponse(SQLModel):
    """课程预约余额检查响应模型"""
    can_book: bool = Field(description="是否可以预约")
    selected_card_id: Optional[int] = Field(description="选中的卡片ID")
    selected_card_type: Optional[CardType] = Field(description="选中的卡片类型")
    current_balance: Optional[int] = Field(description="当前余额（元）")
    required_amount: int = Field(description="所需金额（元）")
    shortage_amount: int = Field(description="不足金额（元，余额充足时为0）")
    available_cards: List[MemberCardSummary] = Field(description="可用卡片列表")


class CourseBookingDeduction(SQLModel):
    """课程预约扣费请求模型"""
    member_card_id: int = Field(gt=0, description="会员卡ID")
    scheduled_class_id: int = Field(gt=0, description="课程ID")
    amount: int = Field(gt=0, description="扣费金额（元）")
    operation_description: str = Field(description="操作描述")


# ==================== 扣费相关 Schema ====================

class DeductionRequest(SQLModel):
    """扣费请求模型"""
    member_card_id: int = Field(gt=0, description="会员卡ID")
    amount: int = Field(gt=0, description="扣费金额（元）")
    reduce_validity_days: Optional[int] = Field(default=0, ge=0, description="减少有效期（天）")
    notes: Optional[str] = Field(default=None, max_length=500, description="备注信息")


class DeductionResponse(SQLModel):
    """扣费响应模型"""
    operation_id: int = Field(description="操作记录ID")
    member_card_id: int = Field(description="会员卡ID")
    amount: int = Field(description="扣费金额（元）")
    balance_before: int = Field(description="扣费前余额（元）")
    balance_after: int = Field(description="扣费后余额（元）")
    reduce_validity_days: Optional[int] = Field(default=None, description="减少的有效期天数")
    expires_at: Optional[datetime] = Field(default=None, description="新的过期时间")
    transaction_id: Optional[str] = Field(description="交易ID")
    created_at: datetime = Field(description="扣费时间")


# ==================== 异常处理 Schema ====================

class MemberCardError(SQLModel):
    """会员卡错误信息模型"""
    error_code: str = Field(description="错误代码")
    error_message: str = Field(description="错误信息")
    details: Optional[dict] = Field(default=None, description="错误详情")
