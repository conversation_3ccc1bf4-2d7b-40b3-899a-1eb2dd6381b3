"""固定课排课算法核心"""

from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, date, timedelta
from sqlmodel import Session, select, and_, or_
from dataclasses import dataclass
import json

from app.features.teachers.models import Teacher, TeacherRegion
from app.features.members.models import Member
from app.features.members.fixed_lock_models import MemberFixedSlotLock
from app.features.teachers.fixed_slots_models import TeacherFixedSlot
from app.features.courses.scheduled_classes_models import ScheduledClass, ClassStatus, ClassType
from app.features.member_cards.models import MemberCard, CardStatus
from app.features.member_cards.consumption_service import ConsumptionService
from app.features.courses.scheduled_classes_service import ScheduledClassService

from .models import (
    TeacherPriorityRule, BalanceInsufficientAction, LogLevel, OperationType,
    FixedScheduleTask, FixedScheduleTaskLog
)
from .exceptions import SchedulingBusinessException


@dataclass
class SchedulingContext:
    """排课上下文"""
    task: FixedScheduleTask
    session: Session
    tenant_id: int
    start_date: date
    weeks_count: int
    teacher_ids: Optional[List[int]]
    teacher_priority_rule: TeacherPriorityRule
    balance_insufficient_action: BalanceInsufficientAction
    interrupt_on_conflict: bool


@dataclass
class TeacherSchedulingResult:
    """教师排课结果"""
    teacher_id: int
    teacher_name: str
    teacher_display_code: str
    success: bool
    total_members: int
    successful_members: int
    failed_members: int
    total_classes: int
    total_amount: int
    error_message: Optional[str] = None


@dataclass
class MemberSchedulingInfo:
    """会员排课信息"""
    member_id: int
    member_name: str
    member_card_id: int
    member_card_name: str
    locked_slots: List[MemberFixedSlotLock]
    total_cost: int
    current_balance: int


class FixedScheduleAlgorithm:
    """固定课排课算法核心类"""
    
    def __init__(self, context: SchedulingContext):
        self.context = context
        self.session = context.session
        self.tenant_id = context.tenant_id
        
        # 初始化服务
        self.consumption_service = ConsumptionService(self.session, self.tenant_id)
        self.class_service = ScheduledClassService(self.session, self.tenant_id)
        
        # 统计信息
        self.total_teachers = 0
        self.successful_teachers = 0
        self.failed_teachers = 0
        self.total_classes = 0
        self.total_amount = 0
    
    def execute_scheduling(self) -> Dict[str, Any]:
        """执行排课算法"""
        try:
            self._log_info("开始执行固定课排课算法")
            
            # 1. 获取并排序教师列表
            teachers = self._get_sorted_teachers()
            if not teachers:
                raise SchedulingBusinessException.no_available_teachers()
            
            self.total_teachers = len(teachers)
            self._log_info(f"共找到 {self.total_teachers} 位教师参与排课")
            
            # 2. 按教师维度执行排课
            teacher_results = []
            for teacher in teachers:
                try:
                    result = self._schedule_for_teacher(teacher)
                    teacher_results.append(result)
                    
                    if result.success:
                        self.successful_teachers += 1
                        self.total_classes += result.total_classes
                        self.total_amount += result.total_amount
                    else:
                        self.failed_teachers += 1
                        
                except Exception as e:
                    self.failed_teachers += 1
                    # 安全地获取教师信息，避免对象被删除的错误
                    try:
                        teacher_name = teacher.name
                        teacher_id = teacher.id
                    except:
                        teacher_name = "未知教师"
                        teacher_id = None

                    error_msg = f"教师 {teacher_name} 排课失败: {str(e)}"
                    self._log_error(error_msg, teacher_id=teacher_id)
                    
                    # 安全地创建失败结果
                    try:
                        teacher_id = teacher.id
                        teacher_name = teacher.name
                        teacher_display_code = teacher.display_code or str(teacher.id)
                    except:
                        teacher_id = None
                        teacher_name = "未知教师"
                        teacher_display_code = "未知"

                    teacher_results.append(TeacherSchedulingResult(
                        teacher_id=teacher_id or 0,
                        teacher_name=teacher_name,
                        teacher_display_code=teacher_display_code,
                        success=False,
                        total_members=0,
                        successful_members=0,
                        failed_members=0,
                        total_classes=0,
                        total_amount=0,
                        error_message=str(e)
                    ))
            
            # 3. 生成排课结果
            result = {
                "success": True,
                "total_teachers": self.total_teachers,
                "successful_teachers": self.successful_teachers,
                "failed_teachers": self.failed_teachers,
                "total_classes": self.total_classes,
                "total_amount": self.total_amount,
                "teacher_results": teacher_results
            }
            
            self._log_info(f"排课算法执行完成，成功 {self.successful_teachers}/{self.total_teachers} 位教师")
            return result
            
        except Exception as e:
            try:
                self._log_error(f"排课算法执行失败: {str(e)}")
            except:
                # 如果日志记录也失败，直接打印错误
                print(f"排课算法执行失败: {str(e)}")
            raise
    
    def _get_sorted_teachers(self) -> List[Teacher]:
        """获取并排序教师列表"""
        # 构建查询条件
        statement = select(Teacher).where(
            and_(
                Teacher.tenant_id == self.tenant_id,
                Teacher.status == "active",  # 只选择活跃教师
                Teacher.show_to_members == True  # 只选择对会员可见的教师
            )
        )
        
        # 如果指定了教师ID列表，则过滤
        if self.context.teacher_ids:
            statement = statement.where(Teacher.id.in_(self.context.teacher_ids))
        
        teachers = self.session.exec(statement).all()
        
        # 按优先级规则排序
        return self._sort_teachers_by_priority(teachers)
    
    def _sort_teachers_by_priority(self, teachers: List[Teacher]) -> List[Teacher]:
        """按优先级规则排序教师"""
        if self.context.teacher_priority_rule == TeacherPriorityRule.REGION_FIRST:
            # 按地区优先级排序：欧美 > 南非 > 菲教，然后按编号倒序
            region_priority = {
                TeacherRegion.EUROPE: 4,
                TeacherRegion.NORTH_AMERICA: 3,
                TeacherRegion.SOUTH_AFRICA: 2,
                TeacherRegion.PHILIPPINES: 1
            }
            
            return sorted(teachers, key=lambda t: (
                region_priority.get(t.region, 0),  # 地区优先级
                -(int(t.display_code) if t.display_code and t.display_code.isdigit() else t.id)  # 显示编号倒序
            ), reverse=True)
            
        elif self.context.teacher_priority_rule == TeacherPriorityRule.NUMBER_DESC:
            # 按显示编号倒序，如果没有显示编号则按ID倒序
            return sorted(teachers, key=lambda t: (
                -(int(t.display_code) if t.display_code and t.display_code.isdigit() else t.id)
            ), reverse=True)
            
        elif self.context.teacher_priority_rule == TeacherPriorityRule.PRICE_DESC:
            # 按价格倒序
            return sorted(teachers, key=lambda t: t.price_per_class, reverse=True)
            
        elif self.context.teacher_priority_rule == TeacherPriorityRule.RATING_DESC:
            # 按评分倒序（如果有评分字段的话）
            return sorted(teachers, key=lambda t: getattr(t, 'rating', 0), reverse=True)
            
        else:
            # 自定义排序或默认排序
            return teachers
    
    def _schedule_for_teacher(self, teacher: Teacher) -> TeacherSchedulingResult:
        """为单个教师执行排课"""
        display_info = f"{teacher.display_code}" if teacher.display_code else f"ID:{teacher.id}"
        self._log_info(f"开始为教师 {teacher.name}({display_info}) 执行排课", teacher_id=teacher.id)
        
        # 获取该教师的会员锁定信息
        member_infos = self._get_member_scheduling_infos(teacher.id)
        
        if not member_infos:
            self._log_warn(f"教师 {teacher.name} 没有会员锁定固定位", teacher_id=teacher.id)
            return TeacherSchedulingResult(
                teacher_id=teacher.id,
                teacher_name=teacher.name,
                teacher_display_code=teacher.display_code or str(teacher.id),
                success=True,
                total_members=0,
                successful_members=0,
                failed_members=0,
                total_classes=0,
                total_amount=0
            )
        
        # 按会员分组处理
        total_members = len(member_infos)
        successful_members = 0
        failed_members = 0
        total_classes = 0
        total_amount = 0
        
        for member_info in member_infos:
            try:
                # 检查会员余额
                if member_info.total_cost > member_info.current_balance:
                    self._handle_insufficient_balance(member_info, teacher)
                    failed_members += 1
                    continue
                
                # 为该会员生成课程
                classes_created = self._create_classes_for_member(member_info, teacher)
                
                if classes_created > 0:
                    successful_members += 1
                    total_classes += classes_created
                    total_amount += member_info.total_cost
                    
                    self._log_info(
                        f"为会员 {member_info.member_name} 成功生成 {classes_created} 节课程，扣费 {member_info.total_cost} 元",
                        teacher_id=teacher.id,
                        member_id=member_info.member_id
                    )
                else:
                    failed_members += 1
                    
            except Exception as e:
                failed_members += 1
                self._log_error(
                    f"为会员 {member_info.member_name} 排课失败: {str(e)}",
                    teacher_id=teacher.id,
                    member_id=member_info.member_id
                )
                
                if self.context.interrupt_on_conflict:
                    raise
        
        success = failed_members == 0 or successful_members > 0
        
        self._log_info(
            f"教师 {teacher.name} 排课完成，成功 {successful_members}/{total_members} 位会员，共生成 {total_classes} 节课程",
            teacher_id=teacher.id
        )
        
        return TeacherSchedulingResult(
            teacher_id=teacher.id,
            teacher_name=teacher.name,
            teacher_display_code=teacher.display_code or str(teacher.id),
            success=success,
            total_members=total_members,
            successful_members=successful_members,
            failed_members=failed_members,
            total_classes=total_classes,
            total_amount=total_amount
        )

    def _get_member_scheduling_infos(self, teacher_id: int) -> List[MemberSchedulingInfo]:
        """获取该教师的会员排课信息"""
        # 查询该教师的所有活跃锁定
        statement = select(MemberFixedSlotLock)

        locks = self.session.exec(statement).all()

        # 按会员分组
        member_locks_map = {}
        for lock in locks:
            if lock.member_id not in member_locks_map:
                member_locks_map[lock.member_id] = []
            member_locks_map[lock.member_id].append(lock)

        # 构建会员排课信息
        member_infos = []
        for member_id, member_locks in member_locks_map.items():
            try:
                member_info = self._build_member_scheduling_info(member_id, member_locks, teacher_id)
                if member_info:
                    member_infos.append(member_info)
            except Exception as e:
                self._log_error(f"构建会员 {member_id} 排课信息失败: {str(e)}", member_id=member_id)

        return member_infos

    def _build_member_scheduling_info(self, member_id: int, locks: List[MemberFixedSlotLock], teacher_id: int) -> Optional[MemberSchedulingInfo]:
        """构建会员排课信息"""
        # 获取会员信息
        member = self.session.get(Member, member_id)
        if not member:
            return None

        # 获取会员卡信息
        if not member.primary_member_card_id:
            self._log_warn(f"会员 {member.name} 没有主要会员卡", member_id=member_id)
            return None

        member_card = self.session.get(MemberCard, member.primary_member_card_id)
        if not member_card or member_card.status != CardStatus.ACTIVE:
            self._log_warn(f"会员 {member.name} 的会员卡不可用", member_id=member_id)
            return None

        # 获取教师价格
        teacher = self.session.get(Teacher, teacher_id)
        if not teacher:
            return None

        # 计算总费用
        total_slots = len(locks)
        total_cost = total_slots * self.context.weeks_count * teacher.price_per_class

        return MemberSchedulingInfo(
            member_id=member_id,
            member_name=member.name,
            member_card_id=member_card.id,
            member_card_name=member_card.card_number or f"卡片{member_card.id}",
            locked_slots=locks,
            total_cost=total_cost,
            current_balance=member_card.balance
        )

    def _handle_insufficient_balance(self, member_info: MemberSchedulingInfo, teacher: Teacher):
        """处理余额不足的情况"""
        if self.context.balance_insufficient_action == BalanceInsufficientAction.SKIP:
            self._log_warn(
                f"会员 {member_info.member_name} 余额不足，跳过排课。需要 {member_info.total_cost} 元，可用 {member_info.current_balance} 元",
                member_id=member_info.member_id,
                teacher_id=teacher.id
            )

        elif self.context.balance_insufficient_action == BalanceInsufficientAction.TERMINATE:
            raise SchedulingBusinessException.balance_insufficient(
                member_info.member_name, member_info.total_cost, member_info.current_balance
            )

        elif self.context.balance_insufficient_action == BalanceInsufficientAction.REMOVE_LOCK:
            # 移除该会员对该教师的所有锁定
            self._remove_member_locks(member_info.member_id, teacher.id)
            self._log_warn(
                f"会员 {member_info.member_name} 余额不足，已移除对教师 {teacher.name} 的固定位锁定",
                member_id=member_info.member_id,
                teacher_id=teacher.id
            )

    def _remove_member_locks(self, member_id: int, teacher_id: int):
        """移除会员对指定教师的所有锁定"""
        statement = select(MemberFixedSlotLock).where(
            and_(
                MemberFixedSlotLock.tenant_id == self.tenant_id,
                MemberFixedSlotLock.member_id == member_id,
                MemberFixedSlotLock.teacher_id == teacher_id
            )
        )

        locks = self.session.exec(statement).all()
        for lock in locks:
            self.session.delete(lock)

        self.session.commit()

    def _create_classes_for_member(self, member_info: MemberSchedulingInfo, teacher: Teacher) -> int:
        """为会员创建课程"""
        classes_created = 0

        try:
            # 开始事务
            for week in range(self.context.weeks_count):
                week_start_date = self.context.start_date + timedelta(weeks=week)

                for lock in member_info.locked_slots:
                    # 计算具体的上课时间
                    class_date = week_start_date + timedelta(days=lock.weekday - 1)  # weekday 1-7，转换为0-6
                    class_datetime = datetime.combine(class_date, lock.start_time)

                    # 检查时间冲突
                    if self._check_time_conflict(teacher.id, member_info.member_id, class_datetime):
                        if self.context.interrupt_on_conflict:
                            raise SchedulingBusinessException.time_conflict(
                                teacher.name, class_datetime.strftime('%Y-%m-%d %H:%M')
                            )
                        else:
                            self._log_warn(
                                f"时间冲突，跳过 {class_datetime.strftime('%Y-%m-%d %H:%M')} 的课程",
                                teacher_id=teacher.id,
                                member_id=member_info.member_id,
                                class_datetime=class_datetime
                            )
                            continue

                    # 创建课程记录
                    class_data = {
                        'tenant_id': self.tenant_id,
                        'teacher_id': teacher.id,
                        'member_id': member_info.member_id,
                        'class_datetime': class_datetime,
                        'duration_minutes': 25,  # 默认25分钟
                        'class_type': ClassType.FIXED,
                        'price': teacher.price_per_class,
                        'member_card_id': member_info.member_card_id,
                        'member_card_name': member_info.member_card_name,
                        'status': ClassStatus.BOOKED,
                        'created_at': datetime.now()
                    }

                    scheduled_class = ScheduledClass(**class_data)
                    self.session.add(scheduled_class)
                    classes_created += 1

            # 扣除会员卡余额
            if classes_created > 0:
                self._deduct_member_balance(member_info, teacher, classes_created)

            self.session.commit()

        except Exception as e:
            self.session.rollback()
            raise

        return classes_created

    def _check_time_conflict(self, teacher_id: int, member_id: int, class_datetime: datetime) -> bool:
        """检查时间冲突"""
        # 检查教师时间冲突
        teacher_conflict = self.session.exec(
            select(ScheduledClass).where(
                and_(
                    ScheduledClass.tenant_id == self.tenant_id,
                    ScheduledClass.teacher_id == teacher_id,
                    ScheduledClass.class_datetime == class_datetime,
                    ScheduledClass.status.in_([ClassStatus.BOOKED, ClassStatus.TEACHER_NO_SHOW, ClassStatus.MEMBER_NO_SHOW]),
                    ScheduledClass.is_deleted == False
                )
            )
        ).first()

        if teacher_conflict:
            return True

        # 检查会员时间冲突
        member_conflict = self.session.exec(
            select(ScheduledClass).where(
                and_(
                    ScheduledClass.tenant_id == self.tenant_id,
                    ScheduledClass.member_id == member_id,
                    ScheduledClass.class_datetime == class_datetime,
                    ScheduledClass.status.in_([ClassStatus.BOOKED, ClassStatus.TEACHER_NO_SHOW, ClassStatus.MEMBER_NO_SHOW]),
                    ScheduledClass.is_deleted == False
                )
            )
        ).first()

        return member_conflict is not None

    def _deduct_member_balance(self, member_info: MemberSchedulingInfo, teacher: Teacher, classes_count: int):
        """扣除会员卡余额"""
        from app.features.member_cards.schemas import ConsumptionRequest

        total_amount = classes_count * teacher.price_per_class

        consumption_data = ConsumptionRequest(
            member_card_id=member_info.member_card_id,
            amount=total_amount,
            operation_description=f"固定课排课扣费 - 教师{teacher.name}，{classes_count}节课",
            reason=f"固定课排课任务ID: {self.context.task.id}",
            scheduled_class_id=None  # 批量扣费，不关联具体课程
        )

        # 执行扣费（不提交事务，由外层统一管理）
        response, operation = self.consumption_service.consume_without_commit(consumption_data, operator_id=None)

        self._log_info(
            f"成功扣除会员 {member_info.member_name} 余额 {total_amount} 元",
            member_id=member_info.member_id,
            operation_type=OperationType.BALANCE_DEDUCT
        )

    # ==================== 日志记录方法 ====================

    def _log_info(self, message: str, teacher_id: Optional[int] = None, member_id: Optional[int] = None,
                  class_datetime: Optional[datetime] = None, operation_type: Optional[OperationType] = None):
        """记录信息日志"""
        self._create_log(LogLevel.INFO, message, teacher_id, member_id, class_datetime, operation_type)

    def _log_warn(self, message: str, teacher_id: Optional[int] = None, member_id: Optional[int] = None,
                  class_datetime: Optional[datetime] = None, operation_type: Optional[OperationType] = None):
        """记录警告日志"""
        self._create_log(LogLevel.WARN, message, teacher_id, member_id, class_datetime, operation_type)

    def _log_error(self, message: str, teacher_id: Optional[int] = None, member_id: Optional[int] = None,
                   class_datetime: Optional[datetime] = None, operation_type: Optional[OperationType] = None):
        """记录错误日志"""
        self._create_log(LogLevel.ERROR, message, teacher_id, member_id, class_datetime, operation_type)

    def _create_log(self, level: LogLevel, message: str, teacher_id: Optional[int] = None,
                    member_id: Optional[int] = None, class_datetime: Optional[datetime] = None,
                    operation_type: Optional[OperationType] = None, operation_data: Optional[dict] = None):
        """创建日志记录"""
        # 安全地获取任务ID
        try:
            task_id = self.context.task.id
        except:
            # 如果无法获取任务ID，直接返回，不记录日志
            print(f"警告：无法获取任务ID，跳过日志记录: {message}")
            return

        log_data = {
            'tenant_id': self.tenant_id,
            'task_id': task_id,
            'log_level': level,
            'message': message,
            'teacher_id': teacher_id,
            'member_id': member_id,
            'class_datetime': class_datetime,
            'operation_type': operation_type,
            'operation_data': json.dumps(operation_data, ensure_ascii=False) if operation_data else None,
            'created_at': datetime.now()
        }

        log = FixedScheduleTaskLog(**log_data)
        self.session.add(log)
        # 注意：这里不提交，让外层事务统一管理
