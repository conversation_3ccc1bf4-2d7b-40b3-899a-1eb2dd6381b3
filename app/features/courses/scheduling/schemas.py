"""固定课排课API数据模式"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List
from datetime import datetime, date
from .models import (
    ScheduleTaskStatus, TeacherPriorityRule, BalanceInsufficientAction,
    LogLevel, OperationType
)
from pydantic import ConfigDict
import json


class FixedScheduleTaskCreate(BaseModel):
    """创建固定课排课任务请求"""
    task_name: str = Field(..., min_length=1, max_length=100, description="任务名称")
    start_date: date = Field(..., description="开始排课日期（必须为周一）")
    weeks_count: int = Field(default=4, ge=1, le=12, description="排课周数")
    teacher_ids: Optional[List[int]] = Field(default=None, description="参与排课的教师ID列表，null表示全部教师")
    teacher_priority_rule: TeacherPriorityRule = Field(default=TeacherPriorityRule.REGION_FIRST, description="教师优先级规则")
    balance_insufficient_action: BalanceInsufficientAction = Field(default=BalanceInsufficientAction.SKIP, description="余额不足时的处理动作")
    interrupt_on_conflict: bool = Field(default=True, description="遇到冲突是否终止任务")
    remark: Optional[str] = Field(default=None, description="备注")

    @field_validator('start_date')
    @classmethod
    def validate_start_date(cls, v):
        """验证开始日期必须是周一"""
        if v.weekday() != 0:  # 0表示周一
            raise ValueError("开始日期必须是周一")
        return v

    @field_validator('teacher_ids')
    @classmethod
    def validate_teacher_ids(cls, v):
        """验证教师ID列表"""
        if v is not None and len(v) == 0:
            raise ValueError("教师ID列表不能为空")
        return v


class FixedScheduleTaskUpdate(BaseModel):
    """更新固定课排课任务请求"""
    task_name: Optional[str] = Field(None, min_length=1, max_length=100, description="任务名称")
    remark: Optional[str] = Field(None, description="备注")


class FixedScheduleTaskResponse(BaseModel):
    """固定课排课任务响应"""
    id: int
    tenant_id: int
    task_name: str
    start_date: datetime
    weeks_count: int
    teacher_ids: Optional[List[int]]
    teacher_priority_rule: TeacherPriorityRule
    balance_insufficient_action: BalanceInsufficientAction
    interrupt_on_conflict: bool
    status: ScheduleTaskStatus
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    total_teachers: int
    successful_teachers: int
    failed_teachers: int
    total_classes: int
    total_amount: int
    error_message: Optional[str]
    remark: Optional[str]
    created_at: datetime
    updated_at: Optional[datetime]
    created_by: Optional[int]

    @field_validator('teacher_ids', mode='before')
    @classmethod
    def parse_teacher_ids(cls, v):
        """解析教师ID列表"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return v
        return v

    model_config = ConfigDict(from_attributes=True)


class FixedScheduleTaskQuery(BaseModel):
    """固定课排课任务查询参数"""
    task_name: Optional[str] = Field(None, description="任务名称（模糊搜索）")
    status: Optional[ScheduleTaskStatus] = Field(None, description="任务状态")
    start_date_from: Optional[date] = Field(None, description="开始日期范围-起始")
    start_date_to: Optional[date] = Field(None, description="开始日期范围-结束")
    created_by: Optional[int] = Field(None, description="创建者ID")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")


class FixedScheduleTaskLogCreate(BaseModel):
    """创建排课任务日志请求"""
    task_id: int = Field(..., description="任务ID")
    log_level: LogLevel = Field(..., description="日志级别")
    message: str = Field(..., min_length=1, description="日志消息")
    teacher_id: Optional[int] = Field(None, description="相关教师ID")
    member_id: Optional[int] = Field(None, description="相关会员ID")
    class_datetime: Optional[datetime] = Field(None, description="相关课程时间")
    operation_type: Optional[OperationType] = Field(None, description="操作类型")
    operation_data: Optional[dict] = Field(None, description="操作相关数据")

    @field_validator('operation_data', mode='before')
    @classmethod
    def serialize_operation_data(cls, v):
        """序列化操作数据"""
        if v is not None and not isinstance(v, str):
            return json.dumps(v, ensure_ascii=False)
        return v


class FixedScheduleTaskLogResponse(BaseModel):
    """排课任务日志响应"""
    id: int
    tenant_id: int
    task_id: int
    log_level: LogLevel
    message: str
    teacher_id: Optional[int]
    member_id: Optional[int]
    class_datetime: Optional[datetime]
    operation_type: Optional[OperationType]
    operation_data: Optional[str]
    created_at: datetime

    @field_validator('operation_data', mode='before')
    @classmethod
    def parse_operation_data(cls, v):
        """解析操作数据"""
        if v is None:
            return None
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                return v
        return v

    model_config = ConfigDict(from_attributes=True)


class FixedScheduleTaskLogQuery(BaseModel):
    """排课任务日志查询参数"""
    task_id: Optional[int] = Field(None, description="任务ID")
    log_level: Optional[LogLevel] = Field(None, description="日志级别")
    teacher_id: Optional[int] = Field(None, description="教师ID")
    member_id: Optional[int] = Field(None, description="会员ID")
    operation_type: Optional[OperationType] = Field(None, description="操作类型")
    message: Optional[str] = Field(None, description="日志消息（模糊搜索）")
    created_from: Optional[datetime] = Field(None, description="创建时间范围-起始")
    created_to: Optional[datetime] = Field(None, description="创建时间范围-结束")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=50, ge=1, le=200, description="每页数量")
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")


class ScheduleTaskExecuteRequest(BaseModel):
    """执行排课任务请求"""
    task_id: int = Field(..., description="任务ID")
    force_restart: bool = Field(default=False, description="是否强制重新开始（忽略当前状态）")


class ScheduleTaskStatsResponse(BaseModel):
    """排课任务统计响应"""
    total_tasks: int = Field(description="总任务数")
    pending_tasks: int = Field(description="待执行任务数")
    running_tasks: int = Field(description="执行中任务数")
    completed_tasks: int = Field(description="已完成任务数")
    failed_tasks: int = Field(description="失败任务数")
    total_classes_generated: int = Field(description="总生成课程数")
    total_amount_deducted: int = Field(description="总扣费金额（元）")
