"""
固定课排课算法性能优化版本

优化策略：
1. 批量数据预加载：减少数据库查询次数
2. 内存缓存：缓存常用数据避免重复查询
3. 批量操作：批量创建课程和更新余额
4. 查询优化：使用JOIN减少N+1查询问题
5. 事务优化：减少事务提交次数

注意：遵循用户要求，不过度优化，保持代码可读性和维护性
"""

from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Optional, Set
from sqlmodel import Session, select, and_, or_
from collections import defaultdict

from app.features.courses.scheduling.algorithm import FixedScheduleAlgorithm
from app.features.courses.scheduling.context import SchedulingContext
from app.features.courses.scheduling.models import (
    MemberSchedulingInfo, TeacherSchedulingResult, SchedulingResult
)
from app.features.courses.scheduling.enums import BalanceInsufficientAction
from app.features.courses.scheduling.exceptions import SchedulingBusinessException

from app.features.teachers.models import Teacher
from app.features.members.models import Member, MemberCard
from app.features.members.fixed_lock_models import MemberFixedSlotLock
from app.features.courses.models import ScheduledClass, ClassStatus, ClassType
from app.features.members.card_models import CardStatus


class OptimizedFixedScheduleAlgorithm(FixedScheduleAlgorithm):
    """
    性能优化版本的固定课排课算法
    
    主要优化点：
    1. 批量预加载数据，减少数据库查询
    2. 使用内存缓存避免重复查询
    3. 批量创建课程记录
    4. 优化事务处理
    """
    
    def __init__(self, context: SchedulingContext):
        super().__init__(context)
        
        # 性能优化相关的缓存
        self._teachers_cache: Dict[int, Teacher] = {}
        self._members_cache: Dict[int, Member] = {}
        self._member_cards_cache: Dict[int, MemberCard] = {}
        self._existing_classes_cache: Set[str] = set()  # 存储已存在的课程时间键
        
        # 批量操作缓存
        self._classes_to_create: List[Dict] = []
        self._balance_updates: List[Dict] = []
        
        # 性能统计
        self._db_query_count = 0
        self._cache_hit_count = 0
    
    def execute_scheduling(self) -> Dict[str, Any]:
        """执行优化版排课算法"""
        try:
            self._log_info("开始执行优化版固定课排课算法")
            
            # 1. 预加载所有需要的数据
            self._preload_data()
            
            # 2. 获取并排序教师列表
            teachers = self._get_sorted_teachers()
            if not teachers:
                raise SchedulingBusinessException.no_available_teachers()
            
            self.total_teachers = len(teachers)
            self._log_info(f"共找到 {self.total_teachers} 位教师参与排课")
            
            # 3. 批量处理所有教师的排课
            teacher_results = self._batch_schedule_for_teachers(teachers)
            
            # 4. 批量执行数据库操作
            self._execute_batch_operations()
            
            # 5. 统计结果
            for result in teacher_results:
                if result.success:
                    self.successful_teachers += 1
                    self.total_classes += result.total_classes
                    self.total_amount += result.total_amount
                else:
                    self.failed_teachers += 1
            
            # 6. 记录性能统计
            self._log_performance_stats()
            
            self._log_info(f"排课算法执行完成，成功 {self.successful_teachers}/{self.total_teachers} 位教师")
            
            return SchedulingResult(
                total_teachers=self.total_teachers,
                successful_teachers=self.successful_teachers,
                failed_teachers=self.failed_teachers,
                total_classes=self.total_classes,
                total_amount=self.total_amount,
                teacher_results=teacher_results
            ).model_dump()
            
        except Exception as e:
            self._log_error(f"排课算法执行失败: {str(e)}")
            raise
    
    def _preload_data(self):
        """预加载所有需要的数据"""
        self._log_info("开始预加载数据")
        
        # 1. 预加载所有教师数据
        teacher_ids = self.context.teacher_ids or []
        if teacher_ids:
            teachers = self.session.exec(
                select(Teacher).where(
                    and_(
                        Teacher.tenant_id == self.tenant_id,
                        Teacher.id.in_(teacher_ids),
                        Teacher.status == "active"
                    )
                )
            ).all()
            self._teachers_cache = {t.id: t for t in teachers}
            self._db_query_count += 1
        
        # 2. 预加载所有相关的锁定数据
        locks = self.session.exec(
            select(MemberFixedSlotLock).where(
                and_(
                    MemberFixedSlotLock.tenant_id == self.tenant_id,
                    MemberFixedSlotLock.teacher_id.in_(teacher_ids) if teacher_ids else True
                )
            )
        ).all()
        self._db_query_count += 1
        
        # 3. 预加载所有相关的会员数据
        member_ids = list(set(lock.member_id for lock in locks))
        if member_ids:
            members = self.session.exec(
                select(Member).where(
                    and_(
                        Member.tenant_id == self.tenant_id,
                        Member.id.in_(member_ids)
                    )
                )
            ).all()
            self._members_cache = {m.id: m for m in members}
            self._db_query_count += 1
            
            # 4. 预加载所有相关的会员卡数据
            member_card_ids = [m.primary_member_card_id for m in members if m.primary_member_card_id]
            if member_card_ids:
                member_cards = self.session.exec(
                    select(MemberCard).where(
                        and_(
                            MemberCard.tenant_id == self.tenant_id,
                            MemberCard.id.in_(member_card_ids)
                        )
                    )
                ).all()
                self._member_cards_cache = {mc.id: mc for mc in member_cards}
                self._db_query_count += 1
        
        # 5. 预加载排课期间的现有课程（用于冲突检测）
        start_datetime = datetime.combine(self.context.start_date, datetime.min.time())
        end_datetime = start_datetime + timedelta(weeks=self.context.weeks_count)
        
        existing_classes = self.session.exec(
            select(ScheduledClass).where(
                and_(
                    ScheduledClass.tenant_id == self.tenant_id,
                    ScheduledClass.class_datetime >= start_datetime,
                    ScheduledClass.class_datetime < end_datetime,
                    ScheduledClass.status.in_([ClassStatus.BOOKED, ClassStatus.TEACHER_NO_SHOW, ClassStatus.MEMBER_NO_SHOW]),
                    ScheduledClass.is_deleted == False
                )
            )
        ).all()
        
        # 构建冲突检测缓存
        for cls in existing_classes:
            teacher_key = f"teacher_{cls.teacher_id}_{cls.class_datetime.isoformat()}"
            member_key = f"member_{cls.member_id}_{cls.class_datetime.isoformat()}"
            self._existing_classes_cache.add(teacher_key)
            self._existing_classes_cache.add(member_key)
        
        self._db_query_count += 1
        
        self._log_info(f"数据预加载完成，执行了 {self._db_query_count} 次数据库查询")
    
    def _batch_schedule_for_teachers(self, teachers: List[Teacher]) -> List[TeacherSchedulingResult]:
        """批量处理所有教师的排课"""
        teacher_results = []
        
        for teacher in teachers:
            try:
                result = self._schedule_for_teacher_optimized(teacher)
                teacher_results.append(result)
                
                self._log_info(
                    f"教师 {teacher.name} 排课完成: {result.total_classes}节课程, {result.total_amount}元",
                    teacher_id=teacher.id
                )
                
            except Exception as e:
                self._log_error(f"教师 {teacher.name} 排课失败: {str(e)}", teacher_id=teacher.id)
                teacher_results.append(TeacherSchedulingResult(
                    teacher_id=teacher.id,
                    teacher_name=teacher.name,
                    success=False,
                    error_message=str(e),
                    total_members=0,
                    successful_members=0,
                    failed_members=0,
                    total_classes=0,
                    total_amount=0
                ))
        
        return teacher_results
    
    def _schedule_for_teacher_optimized(self, teacher: Teacher) -> TeacherSchedulingResult:
        """优化版教师排课处理"""
        self._log_info(f"开始为教师 {teacher.name} 执行排课", teacher_id=teacher.id)
        
        # 获取该教师的会员排课信息（使用缓存）
        member_infos = self._get_member_scheduling_infos_optimized(teacher.id)
        
        if not member_infos:
            self._log_warn(f"教师 {teacher.name} 没有找到有效的会员锁定", teacher_id=teacher.id)
            return TeacherSchedulingResult(
                teacher_id=teacher.id,
                teacher_name=teacher.name,
                success=True,
                total_members=0,
                successful_members=0,
                failed_members=0,
                total_classes=0,
                total_amount=0
            )
        
        # 批量处理会员排课
        total_members = len(member_infos)
        successful_members = 0
        failed_members = 0
        total_classes = 0
        total_amount = 0
        
        for member_info in member_infos:
            try:
                # 检查会员余额
                if member_info.total_cost > member_info.current_balance:
                    self._handle_insufficient_balance(member_info, teacher)
                    failed_members += 1
                    continue
                
                # 为该会员准备课程数据（不立即插入数据库）
                classes_created = self._prepare_classes_for_member(member_info, teacher)
                
                if classes_created > 0:
                    successful_members += 1
                    total_classes += classes_created
                    total_amount += member_info.total_cost
                    
                    # 准备余额更新
                    self._prepare_balance_update(member_info, teacher, classes_created)
                    
                    self._log_info(
                        f"为会员 {member_info.member_name} 准备 {classes_created} 节课程，扣费 {member_info.total_cost} 元",
                        teacher_id=teacher.id,
                        member_id=member_info.member_id
                    )
                else:
                    failed_members += 1
                    
            except Exception as e:
                self._log_error(
                    f"为会员 {member_info.member_name} 排课失败: {str(e)}",
                    teacher_id=teacher.id,
                    member_id=member_info.member_id
                )
                failed_members += 1
        
        return TeacherSchedulingResult(
            teacher_id=teacher.id,
            teacher_name=teacher.name,
            success=True,
            total_members=total_members,
            successful_members=successful_members,
            failed_members=failed_members,
            total_classes=total_classes,
            total_amount=total_amount
        )
