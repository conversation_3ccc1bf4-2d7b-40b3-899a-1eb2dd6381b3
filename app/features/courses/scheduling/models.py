"""固定课排课任务模型"""

from sqlmodel import SQLModel, Field, Index, text
from typing import Optional, List
from datetime import datetime, date
from enum import Enum
import json


class ScheduleTaskStatus(str, Enum):
    """排课任务状态枚举"""
    PENDING = "pending"      # 待执行
    RUNNING = "running"      # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 执行失败


class TeacherPriorityRule(str, Enum):
    """教师优先级规则枚举"""
    REGION_FIRST = "region_first"    # 按地区优先（欧美/南非 > 菲律宾）
    ID_DESC = "id_desc"              # 按教师ID倒序
    RANDOM = "random"                # 随机排序
    PRICE_DESC = "price_desc"        # 按价格倒序
    NUMBER_DESC = "number_desc"      # 按显示编号倒序
    RATING_DESC = "rating_desc"      # 按评分倒序


class BalanceInsufficientAction(str, Enum):
    """余额不足处理动作枚举"""
    SKIP = "skip"                    # 跳过该会员
    REMOVE_LOCK = "remove_lock"      # 移除锁定
    NOTIFY_ONLY = "notify_only"      # 仅通知
    TERMINATE = "terminate"          # 终止任务


class LogLevel(str, Enum):
    """日志级别枚举"""
    INFO = "info"
    WARN = "warn"
    ERROR = "error"


class OperationType(str, Enum):
    """操作类型枚举"""
    TASK_START = "task_start"                # 任务开始
    TASK_COMPLETE = "task_complete"          # 任务完成
    TEACHER_PROCESS = "teacher_process"      # 处理教师
    MEMBER_PROCESS = "member_process"        # 处理会员
    CLASS_CREATE = "class_create"            # 创建课程
    BALANCE_CHECK = "balance_check"          # 余额检查
    BALANCE_DEDUCT = "balance_deduct"        # 余额扣除
    CONFLICT_DETECT = "conflict_detect"      # 冲突检测
    ERROR_HANDLE = "error_handle"            # 错误处理


class FixedScheduleTaskBase(SQLModel):
    """固定课排课任务基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    # 任务信息
    task_name: str = Field(max_length=100, description="任务名称")
    
    # 排课参数
    teacher_ids: Optional[str] = Field(default=None, description="参与排课的教师ID列表（JSON格式）")
    start_date: datetime = Field(description="开始排课日期（周一）")
    weeks_count: int = Field(default=4, ge=1, le=12, description="排课周数")
    teacher_priority_rule: TeacherPriorityRule = Field(
        default=TeacherPriorityRule.REGION_FIRST, 
        description="教师优先级规则"
    )
    balance_insufficient_action: BalanceInsufficientAction = Field(
        default=BalanceInsufficientAction.SKIP, 
        description="余额不足时的处理动作"
    )
    interrupt_on_conflict: bool = Field(default=True, description="遇到冲突是否终止任务")
    
    # 执行状态
    status: ScheduleTaskStatus = Field(default=ScheduleTaskStatus.PENDING, description="任务状态")
    
    # 统计信息
    total_teachers: int = Field(default=0, description="总教师数")
    successful_teachers: int = Field(default=0, description="成功处理教师数")
    failed_teachers: int = Field(default=0, description="失败教师数")
    total_classes: int = Field(default=0, description="总生成课程数")
    total_amount: int = Field(default=0, description="总扣费金额（元）")
    
    # 时间信息
    started_at: Optional[datetime] = Field(default=None, description="开始执行时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    
    # 备注和错误信息
    remark: Optional[str] = Field(default=None, description="备注")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class FixedScheduleTask(FixedScheduleTaskBase, table=True):
    """固定课排课任务表"""
    __tablename__ = "fixed_schedule_tasks"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者")
    
    __table_args__ = (
        # 基础索引
        Index("idx_fixed_schedule_tasks_tenant", "tenant_id"),
        Index("idx_fixed_schedule_tasks_status", "tenant_id", "status"),
        Index("idx_fixed_schedule_tasks_date", "tenant_id", "start_date"),
        Index("idx_fixed_schedule_tasks_created", "tenant_id", "created_at"),
    )


class FixedScheduleTaskLogBase(SQLModel):
    """固定课排课任务日志基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    task_id: int = Field(foreign_key="fixed_schedule_tasks.id", description="任务ID")
    
    # 日志信息
    log_level: LogLevel = Field(description="日志级别")
    message: str = Field(description="日志内容")
    
    # 关联信息
    teacher_id: Optional[int] = Field(default=None, foreign_key="teachers.id", description="相关教师ID")
    member_id: Optional[int] = Field(default=None, foreign_key="members.id", description="相关会员ID")
    class_datetime: Optional[datetime] = Field(default=None, description="相关课程时间")
    
    # 操作详情
    operation_type: Optional[OperationType] = Field(default=None, description="操作类型")
    operation_data: Optional[str] = Field(default=None, description="操作相关数据（JSON格式）")
    
    # 异常信息
    error_code: Optional[str] = Field(default=None, max_length=50, description="错误代码")
    error_detail: Optional[str] = Field(default=None, description="错误详情")


class FixedScheduleTaskLog(FixedScheduleTaskLogBase, table=True):
    """固定课排课任务日志表"""
    __tablename__ = "fixed_schedule_task_logs"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    
    __table_args__ = (
        # 基础索引
        Index("idx_fixed_schedule_logs_tenant", "tenant_id"),
        Index("idx_fixed_schedule_logs_task", "task_id"),
        Index("idx_fixed_schedule_logs_level", "tenant_id", "log_level"),
        Index("idx_fixed_schedule_logs_time", "tenant_id", "created_at"),
        Index("idx_fixed_schedule_logs_teacher", "teacher_id"),
        Index("idx_fixed_schedule_logs_member", "member_id"),
    )


# 数据类型转换工具函数
def serialize_teacher_ids(teacher_ids: Optional[List[int]]) -> Optional[str]:
    """序列化教师ID列表为JSON字符串"""
    if teacher_ids is None:
        return None
    return json.dumps(teacher_ids)


def deserialize_teacher_ids(teacher_ids_json: Optional[str]) -> Optional[List[int]]:
    """反序列化JSON字符串为教师ID列表"""
    if teacher_ids_json is None:
        return None
    try:
        return json.loads(teacher_ids_json)
    except (json.JSONDecodeError, TypeError):
        return None


def serialize_operation_data(data: Optional[dict]) -> Optional[str]:
    """序列化操作数据为JSON字符串"""
    if data is None:
        return None
    return json.dumps(data, ensure_ascii=False)


def deserialize_operation_data(data_json: Optional[str]) -> Optional[dict]:
    """反序列化JSON字符串为操作数据"""
    if data_json is None:
        return None
    try:
        return json.loads(data_json)
    except (json.JSONDecodeError, TypeError):
        return None
