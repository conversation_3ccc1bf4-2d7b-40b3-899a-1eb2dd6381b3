"""固定课排课任务管理服务"""

from typing import List, Optional, Tuple, Dict, Any
from sqlmodel import Session, select, and_, or_, func, desc
from datetime import datetime, date, timedelta
import json

from app.features.base.base_service import TenantAwareService
from app.features.base.query_utils import search_with_pagination

from .models import (
    FixedScheduleTask, FixedScheduleTaskLog, ScheduleTaskStatus,
    TeacherPriorityRule, BalanceInsufficientAction, LogLevel, OperationType
)
from .schemas import (
    FixedScheduleTaskCreate, FixedScheduleTaskUpdate, FixedScheduleTaskQuery,
    FixedScheduleTaskLogCreate, FixedScheduleTaskLogQuery,
    ScheduleTaskExecuteRequest, ScheduleTaskStatsResponse
)
from .exceptions import (
    SchedulingTaskNotFoundError, SchedulingBusinessException
)
from .algorithm import FixedScheduleAlgorithm, SchedulingContext
from .utils import validate_scheduling_params, format_scheduling_summary


class FixedScheduleTaskService(TenantAwareService[FixedScheduleTask]):
    """固定课排课任务服务"""
    
    @property
    def model_class(self):
        return FixedScheduleTask
    
    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)
    
    # ==================== CRUD操作 ====================
    
    def create_task(self, task_data: FixedScheduleTaskCreate, created_by: int) -> FixedScheduleTask:
        """创建排课任务"""
        # 验证排课参数
        teacher_ids = task_data.teacher_ids
        validation_errors = validate_scheduling_params(
            task_data.start_date, 
            task_data.weeks_count, 
            teacher_ids
        )
        
        if validation_errors:
            raise SchedulingBusinessException.invalid_task_status(
                "参数验证失败", "; ".join(validation_errors)
            )
        
        # 创建任务记录
        task_dict = task_data.model_dump()
        task_dict['tenant_id'] = self.tenant_id
        task_dict['created_by'] = created_by
        task_dict['start_date'] = datetime.combine(task_data.start_date, datetime.min.time())
        
        # 序列化教师ID列表
        if teacher_ids:
            task_dict['teacher_ids'] = json.dumps(teacher_ids)
        else:
            task_dict['teacher_ids'] = None
        
        task = FixedScheduleTask(**task_dict)
        now = datetime.now()
        task.created_at = now
        task.updated_at = now
        
        self.session.add(task)
        self.session.commit()
        self.session.refresh(task)
        
        # 记录创建日志
        self._create_task_log(
            task.id, LogLevel.INFO, f"创建排课任务: {task.task_name}",
            operation_type=OperationType.TASK_START
        )
        
        return task
    
    def get_task(self, task_id: int) -> FixedScheduleTask:
        """获取排课任务"""
        task = self.session.get(FixedScheduleTask, task_id)
        if not task or task.tenant_id != self.tenant_id:
            raise SchedulingTaskNotFoundError(task_id)
        return task
    
    def update_task(self, task_id: int, task_data: FixedScheduleTaskUpdate) -> FixedScheduleTask:
        """更新排课任务"""
        task = self.get_task(task_id)
        
        # 检查任务状态，只有待执行状态的任务可以更新
        if task.status != ScheduleTaskStatus.PENDING:
            raise SchedulingBusinessException.invalid_task_status(
                task.status.value, ScheduleTaskStatus.PENDING.value
            )
        
        # 更新字段
        update_data = task_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(task, field, value)
        
        task.updated_at = datetime.now()
        
        self.session.add(task)
        self.session.commit()
        self.session.refresh(task)
        
        return task
    
    def delete_task(self, task_id: int) -> None:
        """删除排课任务"""
        task = self.get_task(task_id)
        
        # 检查任务状态，只有待执行或失败状态的任务可以删除
        if task.status in [ScheduleTaskStatus.RUNNING]:
            raise SchedulingBusinessException.task_already_running(task_id)
        
        # 删除相关日志
        self.session.exec(
            select(FixedScheduleTaskLog).where(
                FixedScheduleTaskLog.task_id == task_id
            )
        ).all()
        
        for log in self.session.exec(
            select(FixedScheduleTaskLog).where(
                FixedScheduleTaskLog.task_id == task_id
            )
        ).all():
            self.session.delete(log)
        
        # 删除任务
        self.session.delete(task)
        self.session.commit()
    
    def query_tasks(self, query: FixedScheduleTaskQuery) -> Tuple[List[FixedScheduleTask], int]:
        """查询排课任务"""
        # 构建基础查询
        statement = select(FixedScheduleTask).where(
            FixedScheduleTask.tenant_id == self.tenant_id
        )
        
        # 添加筛选条件
        filters = []
        
        if query.task_name:
            filters.append(FixedScheduleTask.task_name.ilike(f"%{query.task_name}%"))
        
        if query.status:
            filters.append(FixedScheduleTask.status == query.status)
        
        if query.start_date_from:
            filters.append(FixedScheduleTask.start_date >= datetime.combine(query.start_date_from, datetime.min.time()))
        
        if query.start_date_to:
            filters.append(FixedScheduleTask.start_date <= datetime.combine(query.start_date_to, datetime.max.time()))
        
        if query.created_by:
            filters.append(FixedScheduleTask.created_by == query.created_by)
        
        # 应用过滤器
        for filter_condition in filters:
            statement = statement.where(filter_condition)

        # 应用排序
        sort_field = getattr(FixedScheduleTask, query.sort_by, FixedScheduleTask.created_at)
        if query.sort_order == "desc":
            statement = statement.order_by(sort_field.desc())
        else:
            statement = statement.order_by(sort_field.asc())

        # 获取总数
        count_statement = select(func.count()).select_from(statement.subquery())
        total = self.session.exec(count_statement).one()

        # 应用分页
        offset = (query.page - 1) * query.size
        statement = statement.offset(offset).limit(query.size)

        # 执行查询
        tasks = self.session.exec(statement).all()
        
        return tasks, total
    
    def get_task_statistics(self) -> ScheduleTaskStatsResponse:
        """获取任务统计信息"""
        # 简化统计查询，分别统计各状态的任务数量
        total_tasks = self.session.exec(
            select(func.count(FixedScheduleTask.id)).where(
                FixedScheduleTask.tenant_id == self.tenant_id
            )
        ).one()

        pending_tasks = self.session.exec(
            select(func.count(FixedScheduleTask.id)).where(
                and_(
                    FixedScheduleTask.tenant_id == self.tenant_id,
                    FixedScheduleTask.status == ScheduleTaskStatus.PENDING
                )
            )
        ).one()

        running_tasks = self.session.exec(
            select(func.count(FixedScheduleTask.id)).where(
                and_(
                    FixedScheduleTask.tenant_id == self.tenant_id,
                    FixedScheduleTask.status == ScheduleTaskStatus.RUNNING
                )
            )
        ).one()

        completed_tasks = self.session.exec(
            select(func.count(FixedScheduleTask.id)).where(
                and_(
                    FixedScheduleTask.tenant_id == self.tenant_id,
                    FixedScheduleTask.status == ScheduleTaskStatus.COMPLETED
                )
            )
        ).one()

        failed_tasks = self.session.exec(
            select(func.count(FixedScheduleTask.id)).where(
                and_(
                    FixedScheduleTask.tenant_id == self.tenant_id,
                    FixedScheduleTask.status == ScheduleTaskStatus.FAILED
                )
            )
        ).one()

        # 统计已完成任务的总课程数和总金额
        completed_stats = self.session.exec(
            select(
                func.coalesce(func.sum(FixedScheduleTask.total_classes), 0).label('total_classes'),
                func.coalesce(func.sum(FixedScheduleTask.total_amount), 0).label('total_amount')
            ).where(
                and_(
                    FixedScheduleTask.tenant_id == self.tenant_id,
                    FixedScheduleTask.status == ScheduleTaskStatus.COMPLETED
                )
            )
        ).one()

        return ScheduleTaskStatsResponse(
            total_tasks=total_tasks,
            pending_tasks=pending_tasks,
            running_tasks=running_tasks,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            total_classes_generated=completed_stats[0],
            total_amount_deducted=completed_stats[1]
        )
    
    # ==================== 状态管理 ====================
    
    def update_task_status(self, task_id: int, status: ScheduleTaskStatus, 
                          error_message: Optional[str] = None) -> FixedScheduleTask:
        """更新任务状态"""
        task = self.get_task(task_id)
        
        old_status = task.status
        task.status = status
        task.updated_at = datetime.now()
        
        if status == ScheduleTaskStatus.RUNNING:
            task.started_at = datetime.now()
        elif status in [ScheduleTaskStatus.COMPLETED, ScheduleTaskStatus.FAILED]:
            task.completed_at = datetime.now()
        
        if error_message:
            task.error_message = error_message
        
        self.session.add(task)
        self.session.commit()
        self.session.refresh(task)
        
        # 记录状态变更日志
        self._create_task_log(
            task_id, LogLevel.INFO, 
            f"任务状态从 {old_status.value} 变更为 {status.value}",
            operation_type=OperationType.TASK_COMPLETE if status == ScheduleTaskStatus.COMPLETED else None
        )
        
        return task
    
    def update_task_statistics(self, task_id: int, statistics: Dict[str, int]) -> FixedScheduleTask:
        """更新任务统计信息"""
        task = self.get_task(task_id)
        
        # 更新统计字段
        task.total_teachers = statistics.get('total_teachers', task.total_teachers)
        task.successful_teachers = statistics.get('successful_teachers', task.successful_teachers)
        task.failed_teachers = statistics.get('failed_teachers', task.failed_teachers)
        task.total_classes = statistics.get('total_classes', task.total_classes)
        task.total_amount = statistics.get('total_amount', task.total_amount)
        task.updated_at = datetime.now()
        
        self.session.add(task)
        self.session.commit()
        self.session.refresh(task)
        
        return task
    
    # ==================== 辅助方法 ====================
    
    def _create_task_log(self, task_id: int, level: LogLevel, message: str,
                        teacher_id: Optional[int] = None, member_id: Optional[int] = None,
                        class_datetime: Optional[datetime] = None,
                        operation_type: Optional[OperationType] = None,
                        operation_data: Optional[dict] = None):
        """创建任务日志"""
        log_data = {
            'tenant_id': self.tenant_id,
            'task_id': task_id,
            'log_level': level,
            'message': message,
            'teacher_id': teacher_id,
            'member_id': member_id,
            'class_datetime': class_datetime,
            'operation_type': operation_type,
            'operation_data': json.dumps(operation_data, ensure_ascii=False) if operation_data else None,
            'created_at': datetime.now()
        }
        
        log = FixedScheduleTaskLog(**log_data)
        self.session.add(log)
        self.session.commit()
        
        return log


class FixedScheduleTaskLogService(TenantAwareService[FixedScheduleTaskLog]):
    """固定课排课任务日志服务"""

    @property
    def model_class(self):
        return FixedScheduleTaskLog

    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)

    def create_log(self, log_data: FixedScheduleTaskLogCreate) -> FixedScheduleTaskLog:
        """创建任务日志"""
        log_dict = log_data.model_dump()
        log_dict['tenant_id'] = self.tenant_id
        log_dict['created_at'] = datetime.now()

        log = FixedScheduleTaskLog(**log_dict)
        self.session.add(log)
        self.session.commit()
        self.session.refresh(log)

        return log

    def get_log(self, log_id: int) -> FixedScheduleTaskLog:
        """获取日志记录"""
        log = self.session.get(FixedScheduleTaskLog, log_id)
        if not log or log.tenant_id != self.tenant_id:
            raise SchedulingTaskNotFoundError(log_id)
        return log

    def query_logs(self, query: FixedScheduleTaskLogQuery) -> Tuple[List[FixedScheduleTaskLog], int]:
        """查询任务日志"""
        # 构建基础查询
        statement = select(FixedScheduleTaskLog).where(
            FixedScheduleTaskLog.tenant_id == self.tenant_id
        )

        # 添加筛选条件
        filters = []

        if query.task_id:
            filters.append(FixedScheduleTaskLog.task_id == query.task_id)

        if query.log_level:
            filters.append(FixedScheduleTaskLog.log_level == query.log_level)

        if query.teacher_id:
            filters.append(FixedScheduleTaskLog.teacher_id == query.teacher_id)

        if query.member_id:
            filters.append(FixedScheduleTaskLog.member_id == query.member_id)

        if query.operation_type:
            filters.append(FixedScheduleTaskLog.operation_type == query.operation_type)

        if query.created_from:
            filters.append(FixedScheduleTaskLog.created_at >= query.created_from)

        if query.created_to:
            filters.append(FixedScheduleTaskLog.created_at <= query.created_to)

        # 应用过滤器
        for filter_condition in filters:
            statement = statement.where(filter_condition)

        # 应用消息搜索
        if query.message:
            statement = statement.where(FixedScheduleTaskLog.message.contains(query.message))

        # 应用排序
        sort_field = getattr(FixedScheduleTaskLog, query.sort_by, FixedScheduleTaskLog.created_at)
        if query.sort_order == "desc":
            statement = statement.order_by(sort_field.desc())
        else:
            statement = statement.order_by(sort_field.asc())

        # 获取总数
        count_statement = select(func.count()).select_from(statement.subquery())
        total = self.session.exec(count_statement).one()

        # 应用分页
        offset = (query.page - 1) * query.size
        statement = statement.offset(offset).limit(query.size)

        # 执行查询
        logs = self.session.exec(statement).all()

        return logs, total

    def get_task_logs(self, task_id: int, limit: int = 100) -> List[FixedScheduleTaskLog]:
        """获取指定任务的日志"""
        statement = select(FixedScheduleTaskLog).where(
            and_(
                FixedScheduleTaskLog.tenant_id == self.tenant_id,
                FixedScheduleTaskLog.task_id == task_id
            )
        ).order_by(desc(FixedScheduleTaskLog.created_at)).limit(limit)

        return self.session.exec(statement).all()

    def get_error_logs(self, task_id: Optional[int] = None, limit: int = 50) -> List[FixedScheduleTaskLog]:
        """获取错误日志"""
        statement = select(FixedScheduleTaskLog).where(
            and_(
                FixedScheduleTaskLog.tenant_id == self.tenant_id,
                FixedScheduleTaskLog.log_level == LogLevel.ERROR
            )
        )

        if task_id:
            statement = statement.where(FixedScheduleTaskLog.task_id == task_id)

        statement = statement.order_by(desc(FixedScheduleTaskLog.created_at)).limit(limit)

        return self.session.exec(statement).all()

    def delete_old_logs(self, days: int = 30) -> int:
        """删除旧日志"""
        cutoff_date = datetime.now() - timedelta(days=days)

        old_logs = self.session.exec(
            select(FixedScheduleTaskLog).where(
                and_(
                    FixedScheduleTaskLog.tenant_id == self.tenant_id,
                    FixedScheduleTaskLog.created_at < cutoff_date
                )
            )
        ).all()

        count = len(old_logs)
        for log in old_logs:
            self.session.delete(log)

        self.session.commit()
        return count


class FixedScheduleTaskExecutor:
    """固定课排课任务执行引擎"""

    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        self.task_service = FixedScheduleTaskService(session, tenant_id)
        self.log_service = FixedScheduleTaskLogService(session, tenant_id)

    def execute_task(self, task_id: int, force_restart: bool = False) -> Dict[str, Any]:
        """执行排课任务"""
        try:
            # 获取任务
            task = self.task_service.get_task(task_id)

            # 检查任务状态
            if not force_restart:
                if task.status == ScheduleTaskStatus.RUNNING:
                    raise SchedulingBusinessException.task_already_running(task_id)
                elif task.status == ScheduleTaskStatus.COMPLETED:
                    raise SchedulingBusinessException.task_already_completed(task_id)

            # 更新任务状态为执行中
            self.task_service.update_task_status(task_id, ScheduleTaskStatus.RUNNING)

            # 创建排课上下文
            context = self._create_scheduling_context(task)

            # 执行排课算法
            algorithm = FixedScheduleAlgorithm(context)
            result = algorithm.execute_scheduling()

            # 更新任务统计信息
            self.task_service.update_task_statistics(task_id, {
                'total_teachers': result['total_teachers'],
                'successful_teachers': result['successful_teachers'],
                'failed_teachers': result['failed_teachers'],
                'total_classes': result['total_classes'],
                'total_amount': result['total_amount']
            })

            # 更新任务状态为完成
            self.task_service.update_task_status(task_id, ScheduleTaskStatus.COMPLETED)

            # 记录完成日志
            summary = format_scheduling_summary(
                result['total_teachers'], result['successful_teachers'],
                result['total_classes'], result['total_amount']
            )
            self._create_execution_log(task_id, LogLevel.INFO, f"排课任务执行完成: {summary}")

            return {
                'success': True,
                'task_id': task_id,
                'result': result,
                'message': '排课任务执行成功'
            }

        except Exception as e:
            # 更新任务状态为失败
            error_message = str(e)
            self.task_service.update_task_status(
                task_id, ScheduleTaskStatus.FAILED, error_message
            )

            # 记录错误日志
            self._create_execution_log(task_id, LogLevel.ERROR, f"排课任务执行失败: {error_message}")

            return {
                'success': False,
                'task_id': task_id,
                'error': error_message,
                'message': '排课任务执行失败'
            }

    def retry_failed_task(self, task_id: int) -> Dict[str, Any]:
        """重试失败的任务"""
        task = self.task_service.get_task(task_id)

        if task.status != ScheduleTaskStatus.FAILED:
            raise SchedulingBusinessException.invalid_task_status(
                task.status.value, ScheduleTaskStatus.FAILED.value
            )

        # 清除之前的错误信息
        task.error_message = None
        self.session.add(task)
        self.session.commit()

        # 记录重试日志
        self._create_execution_log(task_id, LogLevel.INFO, "开始重试失败的排课任务")

        # 执行任务
        return self.execute_task(task_id, force_restart=True)

    def cancel_running_task(self, task_id: int) -> Dict[str, Any]:
        """取消正在执行的任务"""
        task = self.task_service.get_task(task_id)

        if task.status != ScheduleTaskStatus.RUNNING:
            raise SchedulingBusinessException.invalid_task_status(
                task.status.value, ScheduleTaskStatus.RUNNING.value
            )

        # 更新任务状态为失败（取消）
        self.task_service.update_task_status(
            task_id, ScheduleTaskStatus.FAILED, "任务被手动取消"
        )

        # 记录取消日志
        self._create_execution_log(task_id, LogLevel.WARN, "排课任务被手动取消")

        return {
            'success': True,
            'task_id': task_id,
            'message': '任务已取消'
        }

    def get_task_execution_status(self, task_id: int) -> Dict[str, Any]:
        """获取任务执行状态"""
        task = self.task_service.get_task(task_id)

        # 获取最近的日志
        recent_logs = self.log_service.get_task_logs(task_id, limit=10)

        # 计算执行进度（如果正在执行）
        progress = self._calculate_execution_progress(task)

        return {
            'task_id': task_id,
            'status': task.status.value,
            'started_at': task.started_at,
            'completed_at': task.completed_at,
            'progress': progress,
            'statistics': {
                'total_teachers': task.total_teachers,
                'successful_teachers': task.successful_teachers,
                'failed_teachers': task.failed_teachers,
                'total_classes': task.total_classes,
                'total_amount': task.total_amount
            },
            'error_message': task.error_message,
            'recent_logs': [
                {
                    'level': log.log_level.value,
                    'message': log.message,
                    'created_at': log.created_at
                }
                for log in recent_logs
            ]
        }

    def _create_scheduling_context(self, task: FixedScheduleTask) -> SchedulingContext:
        """创建排课上下文"""
        # 解析教师ID列表
        teacher_ids = None
        if task.teacher_ids:
            try:
                teacher_ids = json.loads(task.teacher_ids)
            except json.JSONDecodeError:
                teacher_ids = None

        return SchedulingContext(
            task=task,
            session=self.session,
            tenant_id=self.tenant_id,
            start_date=task.start_date.date(),
            weeks_count=task.weeks_count,
            teacher_ids=teacher_ids,
            teacher_priority_rule=task.teacher_priority_rule,
            balance_insufficient_action=task.balance_insufficient_action,
            interrupt_on_conflict=task.interrupt_on_conflict
        )

    def _calculate_execution_progress(self, task: FixedScheduleTask) -> Dict[str, Any]:
        """计算执行进度"""
        if task.status != ScheduleTaskStatus.RUNNING:
            return {'percentage': 0, 'current_step': '未开始'}

        # 简单的进度估算（实际项目中可以更精确）
        if task.total_teachers > 0:
            completed_teachers = task.successful_teachers + task.failed_teachers
            percentage = min(int(completed_teachers / task.total_teachers * 100), 99)
        else:
            percentage = 10  # 刚开始执行

        return {
            'percentage': percentage,
            'current_step': f'正在处理教师排课 ({task.successful_teachers + task.failed_teachers}/{task.total_teachers})'
        }

    def _create_execution_log(self, task_id: int, level: LogLevel, message: str):
        """创建执行日志"""
        log_data = FixedScheduleTaskLogCreate(
            task_id=task_id,
            log_level=level,
            message=message,
            operation_type=OperationType.TASK_START if '开始' in message else OperationType.TASK_COMPLETE
        )
        return self.log_service.create_log(log_data)
