"""固定课排课工具函数"""

from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime, date, time, timedelta
from sqlmodel import Session, select, and_

from app.features.teachers.models import Teacher, TeacherRegion
from app.features.members.models import Member
from app.features.members.fixed_lock_models import MemberFixedSlotLock
from app.features.courses.scheduled_classes_models import ScheduledClass, ClassStatus

from .models import TeacherPriorityRule


def validate_start_date(start_date: date) -> bool:
    """验证开始日期是否为周一"""
    return start_date.weekday() == 0


def calculate_end_date(start_date: date, weeks_count: int) -> date:
    """计算结束日期"""
    return start_date + timedelta(weeks=weeks_count)


def get_week_dates(start_date: date, weeks_count: int) -> List[Tuple[date, date]]:
    """获取每周的开始和结束日期"""
    week_dates = []
    for week in range(weeks_count):
        week_start = start_date + timedelta(weeks=week)
        week_end = week_start + timedelta(days=6)
        week_dates.append((week_start, week_end))
    return week_dates


def calculate_class_datetime(base_date: date, weekday: int, start_time: time) -> datetime:
    """计算具体的上课时间"""
    # weekday: 1-7 (1=Monday), 转换为 0-6 (0=Monday)
    days_offset = weekday - 1
    class_date = base_date + timedelta(days=days_offset)
    return datetime.combine(class_date, start_time)


def get_teacher_priority_score(teacher: Teacher, priority_rule: TeacherPriorityRule) -> Tuple[int, int]:
    """获取教师优先级评分"""
    if priority_rule == TeacherPriorityRule.REGION_FIRST:
        # 地区优先级
        region_priority = {
            TeacherRegion.EUROPE: 4,
            TeacherRegion.NORTH_AMERICA: 3,
            TeacherRegion.SOUTH_AFRICA: 2,
            TeacherRegion.PHILIPPINES: 1
        }
        region_score = region_priority.get(teacher.region, 0)
        
        # 使用显示编号倒序，如果没有显示编号则使用ID（数字越大优先级越高）
        number_score = int(teacher.display_code) if teacher.display_code and teacher.display_code.isdigit() else teacher.id
        
        return (region_score, number_score)
        
    elif priority_rule == TeacherPriorityRule.NUMBER_DESC:
        number_score = int(teacher.display_code) if teacher.display_code and teacher.display_code.isdigit() else teacher.id
        return (number_score, 0)
        
    elif priority_rule == TeacherPriorityRule.PRICE_DESC:
        return (teacher.price_per_class, 0)
        
    elif priority_rule == TeacherPriorityRule.RATING_DESC:
        rating = getattr(teacher, 'rating', 0)
        return (rating, 0)
        
    else:
        return (0, 0)


def sort_teachers_by_priority(teachers: List[Teacher], priority_rule: TeacherPriorityRule) -> List[Teacher]:
    """按优先级规则排序教师"""
    return sorted(teachers, key=lambda t: get_teacher_priority_score(t, priority_rule), reverse=True)


def group_locks_by_member(locks: List[MemberFixedSlotLock]) -> Dict[int, List[MemberFixedSlotLock]]:
    """按会员分组锁定记录"""
    member_locks = {}
    for lock in locks:
        if lock.member_id not in member_locks:
            member_locks[lock.member_id] = []
        member_locks[lock.member_id].append(lock)
    return member_locks


def calculate_total_cost(locks: List[MemberFixedSlotLock], weeks_count: int, price_per_class: int) -> int:
    """计算总费用"""
    return len(locks) * weeks_count * price_per_class


def check_teacher_availability(session: Session, tenant_id: int, teacher_id: int, 
                             class_datetime: datetime) -> bool:
    """检查教师在指定时间是否可用"""
    conflict = session.exec(
        select(ScheduledClass).where(
            and_(
                ScheduledClass.tenant_id == tenant_id,
                ScheduledClass.teacher_id == teacher_id,
                ScheduledClass.class_datetime == class_datetime,
                ScheduledClass.status.in_([ClassStatus.BOOKED]),
                ScheduledClass.is_deleted == False
            )
        )
    ).first()
    
    return conflict is None


def check_member_availability(session: Session, tenant_id: int, member_id: int,
                            class_datetime: datetime) -> bool:
    """检查会员在指定时间是否可用"""
    conflict = session.exec(
        select(ScheduledClass).where(
            and_(
                ScheduledClass.tenant_id == tenant_id,
                ScheduledClass.member_id == member_id,
                ScheduledClass.class_datetime == class_datetime,
                ScheduledClass.status.in_([ClassStatus.BOOKED]),
                ScheduledClass.is_deleted == False
            )
        )
    ).first()
    
    return conflict is None


def batch_check_time_conflicts(session: Session, tenant_id: int, 
                             class_schedules: List[Dict]) -> List[Dict]:
    """批量检查时间冲突
    
    Args:
        class_schedules: 包含 teacher_id, member_id, class_datetime 的字典列表
    
    Returns:
        包含冲突检查结果的字典列表
    """
    results = []
    
    for schedule in class_schedules:
        teacher_id = schedule['teacher_id']
        member_id = schedule['member_id']
        class_datetime = schedule['class_datetime']
        
        teacher_available = check_teacher_availability(session, tenant_id, teacher_id, class_datetime)
        member_available = check_member_availability(session, tenant_id, member_id, class_datetime)
        
        results.append({
            'teacher_id': teacher_id,
            'member_id': member_id,
            'class_datetime': class_datetime,
            'teacher_available': teacher_available,
            'member_available': member_available,
            'has_conflict': not (teacher_available and member_available)
        })
    
    return results


def get_active_locks_for_teacher(session: Session, tenant_id: int, teacher_id: int) -> List[MemberFixedSlotLock]:
    """获取教师的所有活跃锁定"""
    statement = select(MemberFixedSlotLock).where(
        and_(
            MemberFixedSlotLock.tenant_id == tenant_id,
            MemberFixedSlotLock.teacher_id == teacher_id,
        )
    )
    
    return session.exec(statement).all()


def get_active_locks_for_member(session: Session, tenant_id: int, member_id: int) -> List[MemberFixedSlotLock]:
    """获取会员的所有活跃锁定"""
    statement = select(MemberFixedSlotLock).where(
        and_(
            MemberFixedSlotLock.tenant_id == tenant_id,
            MemberFixedSlotLock.member_id == member_id,
        )
    )
    
    return session.exec(statement).all()


def format_scheduling_summary(total_teachers: int, successful_teachers: int, 
                            total_classes: int, total_amount: int) -> str:
    """格式化排课结果摘要"""
    success_rate = (successful_teachers / total_teachers * 100) if total_teachers > 0 else 0
    
    return (
        f"排课完成：{successful_teachers}/{total_teachers} 位教师成功排课 "
        f"(成功率 {success_rate:.1f}%)，共生成 {total_classes} 节课程，"
        f"总扣费 {total_amount} 元"
    )


def validate_scheduling_params(start_date: date, weeks_count: int, teacher_ids: Optional[List[int]]) -> List[str]:
    """验证排课参数"""
    errors = []
    
    # 验证开始日期
    if not validate_start_date(start_date):
        errors.append("开始日期必须是周一")
    
    # 验证开始日期不能是过去
    if start_date < date.today():
        errors.append("开始日期不能是过去的日期")
    
    # 验证周数
    if weeks_count < 1 or weeks_count > 12:
        errors.append("排课周数必须在 1-12 之间")
    
    # 验证教师ID列表
    if teacher_ids is not None and len(teacher_ids) == 0:
        errors.append("教师ID列表不能为空")
    
    return errors


def generate_class_schedules(locks: List[MemberFixedSlotLock], start_date: date, 
                           weeks_count: int) -> List[Dict]:
    """生成课程安排列表"""
    schedules = []
    
    for week in range(weeks_count):
        week_start_date = start_date + timedelta(weeks=week)
        
        for lock in locks:
            class_datetime = calculate_class_datetime(week_start_date, lock.weekday, lock.start_time)
            
            schedules.append({
                'teacher_id': lock.teacher_id,
                'member_id': lock.member_id,
                'class_datetime': class_datetime,
                'weekday': lock.weekday,
                'start_time': lock.start_time,
                'lock_id': lock.id
            })
    
    return schedules


def calculate_scheduling_statistics(teacher_results: List[Dict]) -> Dict[str, Any]:
    """计算排课统计信息"""
    total_teachers = len(teacher_results)
    successful_teachers = sum(1 for r in teacher_results if r.get('success', False))
    failed_teachers = total_teachers - successful_teachers

    total_members = sum(r.get('total_members', 0) for r in teacher_results)
    successful_members = sum(r.get('successful_members', 0) for r in teacher_results)
    failed_members = sum(r.get('failed_members', 0) for r in teacher_results)

    total_classes = sum(r.get('total_classes', 0) for r in teacher_results)
    total_amount = sum(r.get('total_amount', 0) for r in teacher_results)

    # 计算成功率
    teacher_success_rate = (successful_teachers / total_teachers * 100) if total_teachers > 0 else 0
    member_success_rate = (successful_members / total_members * 100) if total_members > 0 else 0

    # 统计失败原因
    failure_reasons = {}
    for result in teacher_results:
        if not result.get('success', False) and result.get('error_message'):
            reason = result['error_message']
            failure_reasons[reason] = failure_reasons.get(reason, 0) + 1

    return {
        'total_teachers': total_teachers,
        'successful_teachers': successful_teachers,
        'failed_teachers': failed_teachers,
        'teacher_success_rate': round(teacher_success_rate, 2),

        'total_members': total_members,
        'successful_members': successful_members,
        'failed_members': failed_members,
        'member_success_rate': round(member_success_rate, 2),

        'total_classes': total_classes,
        'total_amount': total_amount,
        'average_amount_per_teacher': round(total_amount / successful_teachers, 2) if successful_teachers > 0 else 0,
        'average_classes_per_teacher': round(total_classes / successful_teachers, 2) if successful_teachers > 0 else 0,

        'failure_reasons': failure_reasons
    }


def generate_scheduling_report(statistics: Dict[str, Any]) -> str:
    """生成排课报告"""
    report_lines = [
        "=== 固定课排课结果报告 ===",
        "",
        f"教师统计:",
        f"  总教师数: {statistics['total_teachers']}",
        f"  成功教师数: {statistics['successful_teachers']}",
        f"  失败教师数: {statistics['failed_teachers']}",
        f"  教师成功率: {statistics['teacher_success_rate']}%",
        "",
        f"会员统计:",
        f"  总会员数: {statistics['total_members']}",
        f"  成功会员数: {statistics['successful_members']}",
        f"  失败会员数: {statistics['failed_members']}",
        f"  会员成功率: {statistics['member_success_rate']}%",
        "",
        f"课程统计:",
        f"  总课程数: {statistics['total_classes']}",
        f"  总扣费金额: {statistics['total_amount']} 元",
        f"  平均每位教师课程数: {statistics['average_classes_per_teacher']}",
        f"  平均每位教师扣费: {statistics['average_amount_per_teacher']} 元",
    ]

    if statistics['failure_reasons']:
        report_lines.extend([
            "",
            "失败原因统计:",
        ])
        for reason, count in statistics['failure_reasons'].items():
            report_lines.append(f"  {reason}: {count} 次")

    return "\n".join(report_lines)


def export_scheduling_results_to_csv(teacher_results: List[Dict], file_path: str):
    """导出排课结果到CSV文件"""
    import csv

    headers = [
        '教师ID', '教师姓名', '教师编号', '是否成功',
        '总会员数', '成功会员数', '失败会员数',
        '总课程数', '总扣费金额', '错误信息'
    ]

    with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(headers)

        for result in teacher_results:
            writer.writerow([
                result.get('teacher_id', ''),
                result.get('teacher_name', ''),
                result.get('teacher_display_code', ''),
                '成功' if result.get('success', False) else '失败',
                result.get('total_members', 0),
                result.get('successful_members', 0),
                result.get('failed_members', 0),
                result.get('total_classes', 0),
                result.get('total_amount', 0),
                result.get('error_message', '')
            ])


def analyze_scheduling_performance(teacher_results: List[Dict]) -> Dict[str, Any]:
    """分析排课性能"""
    if not teacher_results:
        return {}

    # 按成功率分析
    success_rates = []
    for result in teacher_results:
        total = result.get('total_members', 0)
        successful = result.get('successful_members', 0)
        if total > 0:
            success_rates.append(successful / total * 100)

    # 按课程数分析
    class_counts = [r.get('total_classes', 0) for r in teacher_results]

    # 按扣费金额分析
    amounts = [r.get('total_amount', 0) for r in teacher_results]

    return {
        'success_rate_stats': {
            'min': min(success_rates) if success_rates else 0,
            'max': max(success_rates) if success_rates else 0,
            'avg': sum(success_rates) / len(success_rates) if success_rates else 0
        },
        'class_count_stats': {
            'min': min(class_counts) if class_counts else 0,
            'max': max(class_counts) if class_counts else 0,
            'avg': sum(class_counts) / len(class_counts) if class_counts else 0,
            'total': sum(class_counts)
        },
        'amount_stats': {
            'min': min(amounts) if amounts else 0,
            'max': max(amounts) if amounts else 0,
            'avg': sum(amounts) / len(amounts) if amounts else 0,
            'total': sum(amounts)
        }
    }
