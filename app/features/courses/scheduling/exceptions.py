"""固定课排课异常定义"""

from typing import Optional, Any, Dict
from datetime import datetime, date
from app.api.common.exceptions import BusinessException, NotFoundError, GlobalErrorCode, ErrorLevel, BaseErrorCode


class SchedulingErrorCode(BaseErrorCode):
    """排课模块错误码"""
    TASK_NOT_FOUND = "SCHEDULING_TASK_NOT_FOUND"
    INVALID_TASK_STATUS = "SCHEDULING_INVALID_TASK_STATUS"
    TASK_ALREADY_RUNNING = "SCHEDULING_TASK_ALREADY_RUNNING"
    INVALID_DATE_RANGE = "SCHEDULING_INVALID_DATE_RANGE"
    TEACHER_NOT_FOUND = "SCHEDULING_TEACHER_NOT_FOUND"
    MEMBER_NOT_FOUND = "SCHEDULING_MEMBER_NOT_FOUND"
    NO_AVAILABLE_TEACHERS = "SCHEDULING_NO_AVAILABLE_TEACHERS"
    INSUFFICIENT_BALANCE = "SCHEDULING_INSUFFICIENT_BALANCE"
    TIME_CONFLICT = "SCHEDULING_TIME_CONFLICT"
    NO_AVAILABLE_SLOTS = "SCHEDULING_NO_AVAILABLE_SLOTS"
    ALGORITHM_ERROR = "SCHEDULING_ALGORITHM_ERROR"
    DATABASE_ERROR = "SCHEDULING_DATABASE_ERROR"
    PARAMETER_VALIDATION_ERROR = "SCHEDULING_PARAMETER_VALIDATION_ERROR"


class SchedulingTaskNotFoundError(NotFoundError):
    """排课任务未找到异常"""
    
    def __init__(self, task_id: int):
        details = {"task_id": task_id}
        super().__init__(resource=f"排课任务 {task_id}", details=details)


class SchedulingBusinessException(BusinessException):
    """排课业务异常基类"""
    
    @classmethod
    def invalid_task_status(cls, task_id: int, current_status: str, expected_status: str = None):
        """任务状态无效"""
        if expected_status:
            message = f"任务 {task_id} 状态无效，当前状态: {current_status}，期望状态: {expected_status}"
        else:
            message = f"任务 {task_id} 状态无效: {current_status}"
        return cls(message, SchedulingErrorCode.INVALID_TASK_STATUS)
    
    @classmethod
    def task_already_running(cls, task_id: int):
        """任务已在运行中"""
        return cls(f"任务 {task_id} 已在运行中", SchedulingErrorCode.TASK_ALREADY_RUNNING)
    
    @classmethod
    def invalid_date_range(cls, start_date: date, weeks_count: int):
        """日期范围无效"""
        return cls(
            f"日期范围无效: 开始日期 {start_date}，周数 {weeks_count}",
            SchedulingErrorCode.INVALID_DATE_RANGE
        )
    
    @classmethod
    def teacher_not_found(cls, teacher_id: int):
        """教师未找到"""
        return cls(f"教师未找到: {teacher_id}", SchedulingErrorCode.TEACHER_NOT_FOUND)
    
    @classmethod
    def member_not_found(cls, member_id: int):
        """会员未找到"""
        return cls(f"会员未找到: {member_id}", SchedulingErrorCode.MEMBER_NOT_FOUND)
    
    @classmethod
    def no_available_teachers(cls):
        """没有可用的教师"""
        return cls("没有可用的教师", SchedulingErrorCode.NO_AVAILABLE_TEACHERS)
    
    @classmethod
    def insufficient_balance(cls, member_name: str, required_amount: int, current_balance: int):
        """余额不足"""
        return cls(
            f"会员 {member_name} 余额不足，需要 {required_amount} 元，当前余额 {current_balance} 元",
            SchedulingErrorCode.INSUFFICIENT_BALANCE
        )
    
    @classmethod
    def balance_insufficient(cls, member_name: str, required_amount: int, current_balance: int):
        """余额不足（别名，保持向后兼容性）"""
        return cls.insufficient_balance(member_name, required_amount, current_balance)
    
    @classmethod
    def time_conflict(cls, teacher_name: str, time_str: str):
        """时间冲突"""
        return cls(
            f"时间冲突: 教师 {teacher_name} 在 {time_str} 已有课程安排",
            SchedulingErrorCode.TIME_CONFLICT
        )
    
    @classmethod
    def no_available_slots(cls, teacher_id: int, weekday: int):
        """无可用时间槽"""
        return cls(
            f"教师 {teacher_id} 在星期 {weekday} 无可用时间槽",
            SchedulingErrorCode.NO_AVAILABLE_SLOTS
        )
    
    @classmethod
    def scheduling_algorithm_error(cls, error_message: str, context: Optional[Dict] = None):
        """排课算法错误"""
        return cls(
            f"排课算法错误: {error_message}",
            SchedulingErrorCode.ALGORITHM_ERROR
        )
    
    @classmethod
    def database_operation_error(cls, operation: str, error_message: str):
        """数据库操作错误"""
        return cls(
            f"数据库操作失败 ({operation}): {error_message}",
            SchedulingErrorCode.DATABASE_ERROR
        )
    
    @classmethod
    def parameter_validation_error(cls, parameter: str, value: Any, reason: str):
        """参数验证错误"""
        return cls(
            f"参数验证失败: {parameter} = {value}, 原因: {reason}",
            SchedulingErrorCode.PARAMETER_VALIDATION_ERROR
        )


class SchedulingAlgorithmException(SchedulingBusinessException):
    """排课算法专用异常"""
    
    @classmethod
    def teacher_processing_failed(cls, teacher_id: int, error_message: str):
        """教师处理失败"""
        return cls(
            f"教师 {teacher_id} 处理失败: {error_message}",
            SchedulingErrorCode.TEACHER_NOT_FOUND
        )
    
    @classmethod
    def member_processing_failed(cls, member_id: int, error_message: str):
        """会员处理失败"""
        return cls(
            f"会员 {member_id} 处理失败: {error_message}",
            SchedulingErrorCode.MEMBER_NOT_FOUND
        )
    
    @classmethod
    def class_creation_failed(cls, teacher_id: int, member_id: int, class_datetime: datetime, error_message: str):
        """课程创建失败"""
        return cls(
            f"课程创建失败: 教师 {teacher_id}，会员 {member_id}，时间 {class_datetime}，错误: {error_message}",
            SchedulingErrorCode.DATABASE_ERROR
        )


class SchedulingValidationException(SchedulingBusinessException):
    """排课验证异常"""
    
    @classmethod
    def invalid_start_date(cls, start_date: date, reason: str):
        """开始日期无效"""
        return cls(
            f"开始日期无效: {start_date}，原因: {reason}",
            SchedulingErrorCode.INVALID_DATE_RANGE
        )
    
    @classmethod
    def invalid_weeks_count(cls, weeks_count: int, min_weeks: int, max_weeks: int):
        """周数无效"""
        return cls(
            f"周数无效: {weeks_count}，有效范围: {min_weeks}-{max_weeks}",
            SchedulingErrorCode.INVALID_DATE_RANGE
        )
    
    @classmethod
    def invalid_teacher_list(cls, teacher_ids: list, reason: str):
        """教师列表无效"""
        return cls(
            f"教师列表无效: {teacher_ids}，原因: {reason}",
            SchedulingErrorCode.PARAMETER_VALIDATION_ERROR
        )
