"""操作记录数据模型"""

from sqlmodel import SQLModel, Field, Index, text
from sqlalchemy.orm import mapped_column
from sqlalchemy import ForeignKey

from typing import Optional
from datetime import datetime
from enum import Enum


class ClassOperationType(str, Enum):
    """课程操作类型枚举"""
    CREATE = "create"                    # 创建课程
    UPDATE = "update"                    # 更新课程信息
    BOOK = "book"                       # 会员预约课程
    CANCEL_BOOKING = "cancel_booking"    # 取消预约
    CANCEL_CLASS = "cancel_class"       # 取消课程
    DELETE = "delete"                   # 删除课程


class TeacherSlotOperationType(str, Enum):
    """教师固定时间段操作类型枚举"""
    CREATE = "create"                   # 创建时间段
    DELETE = "delete"                   # 删除时间段
    SET_VISIBLE = "set_visible"         # 设置对会员可见
    SET_INVISIBLE = "set_invisible"     # 设置对会员不可见


class MemberLockOperationType(str, Enum):
    """会员固定位锁定操作类型枚举"""
    CREATE = "create"                   # 创建锁定
    DELETE_BY_MEMBER = "delete_by_member"   # 删除锁定-会员
    DELETE_BY_ADMIN = "delete_by_admin"     # 删除锁定-管理员


class OperationStatus(str, Enum):
    """操作状态枚举"""
    SUCCESS = "success"                 # 操作成功
    FAILED = "failed"                   # 操作失败
    PENDING = "pending"                 # 操作待处理


# ==================== 课程操作记录表 ====================

class ScheduledClassOperationLogBase(SQLModel):
    """课程操作记录表基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    # 关联信息
    scheduled_class_id: int = Field(foreign_key="scheduled_classes.id", description="课程ID")
    
    # 操作信息
    operation_type: ClassOperationType = Field(description="操作类型")
    operation_status: OperationStatus = Field(default=OperationStatus.SUCCESS, description="操作状态")
    operation_description: str = Field(max_length=200, description="操作描述")
    
    # 操作人信息
    operator_id: Optional[int] = Field(default=None, foreign_key="users.id", description="操作人ID")
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人姓名")
    operator_type: str = Field(max_length=20, description="操作人类型（admin/teacher/member）")
    
    # 业务对象信息（冗余字段，便于查询）
    teacher_id: int = Field(foreign_key="teachers.id", description="教师ID")
    teacher_name: str = Field(max_length=50, description="教师姓名")
    member_id: Optional[int] = Field(default=None, foreign_key="members.id", description="会员ID")
    member_name: Optional[str] = Field(default=None, max_length=50, description="会员姓名")
    
    # 课程信息（冗余字段）
    class_datetime: datetime = Field(description="课程时间")
    
    # 备注信息
    reason: Optional[str] = Field(default=None, max_length=100, description="操作原因")


class ScheduledClassOperationLog(ScheduledClassOperationLogBase, table=True):
    """课程操作记录表数据库模型"""
    __tablename__ = "scheduled_class_operation_logs"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="操作时间")
    
    __table_args__ = (
        # 索引设计 - 所有索引以tenant_id开头（多租户架构要求）
        # 基础查询索引
        Index('idx_class_op_logs_tenant', 'tenant_id'),
        Index('idx_class_op_logs_class', 'tenant_id', 'scheduled_class_id'),
        Index('idx_class_op_logs_teacher', 'tenant_id', 'teacher_id'),
        Index('idx_class_op_logs_member', 'tenant_id', 'member_id'),
        Index('idx_class_op_logs_operator', 'tenant_id', 'operator_id'),
        Index('idx_class_op_logs_type', 'tenant_id', 'operation_type'),
        Index('idx_class_op_logs_status', 'tenant_id', 'operation_status'),
        Index('idx_class_op_logs_time', 'tenant_id', 'created_at'),
        Index('idx_class_op_logs_class_time', 'tenant_id', 'class_datetime'),
        
        # 复合索引 - 优化多维度筛选查询
        Index('idx_class_op_logs_tenant_type', 'tenant_id', 'operation_type'),
        Index('idx_class_op_logs_tenant_teacher', 'tenant_id', 'teacher_id'),
        Index('idx_class_op_logs_tenant_member', 'tenant_id', 'member_id'),
        Index('idx_class_op_logs_tenant_time', 'tenant_id', 'created_at'),
        Index('idx_class_op_logs_class_type', 'scheduled_class_id', 'operation_type'),
        Index('idx_class_op_logs_teacher_type', 'teacher_id', 'operation_type'),
        Index('idx_class_op_logs_member_type', 'member_id', 'operation_type'),
        
        # 排序优化索引
        Index('idx_class_op_logs_time_desc', 'tenant_id', 'created_at', 'id'),
        Index('idx_class_op_logs_class_time_desc', 'scheduled_class_id', 'created_at', 'id'),
    )


# ==================== 教师固定时间段操作记录表 ====================

class TeacherFixedSlotOperationLogBase(SQLModel):
    """教师固定时间段操作记录表基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    # 关联信息
    teacher_fixed_slot_id: int = Field(
        sa_column=mapped_column(
            ForeignKey("teacher_fixed_slots.id", ondelete="SET NULL"),
            index=True
        ),
        description="教师固定时间段ID"
    )

    # 操作信息
    operation_type: TeacherSlotOperationType = Field(description="操作类型")
    operation_status: OperationStatus = Field(default=OperationStatus.SUCCESS, description="操作状态")
    operation_description: str = Field(max_length=200, description="操作描述")
    
    # 操作人信息
    operator_id: Optional[int] = Field(default=None, foreign_key="users.id", description="操作人ID")
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人姓名")
    operator_type: str = Field(max_length=20, description="操作人类型（admin/teacher）")
    
    # 业务对象信息（冗余字段）
    teacher_id: int = Field(foreign_key="teachers.id", description="教师ID")
    teacher_name: str = Field(max_length=50, description="教师姓名")
    
    # 时间段信息（冗余字段）
    weekday: int = Field(ge=1, le=7, description="星期几（1-7）")
    start_time: str = Field(max_length=8, description="开始时间（HH:MM格式）")
    duration_minutes: Optional[int] = Field(default=None, description="时长（分钟）")
    
    # 备注信息
    reason: Optional[str] = Field(default=None, max_length=100, description="操作原因")


class TeacherFixedSlotOperationLog(TeacherFixedSlotOperationLogBase, table=True):
    """教师固定时间段操作记录表数据库模型"""
    __tablename__ = "teacher_fixed_slot_operation_logs"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="操作时间")
    
    __table_args__ = (
        # 索引设计 - 所有索引以tenant_id开头（多租户架构要求）
        # 基础查询索引
        Index('idx_teacher_slot_op_logs_tenant', 'tenant_id'),
        Index('idx_teacher_slot_op_logs_slot', 'tenant_id', 'teacher_fixed_slot_id'),
        Index('idx_teacher_slot_op_logs_teacher', 'tenant_id', 'teacher_id'),
        Index('idx_teacher_slot_op_logs_operator', 'tenant_id', 'operator_id'),
        Index('idx_teacher_slot_op_logs_type', 'tenant_id', 'operation_type'),
        Index('idx_teacher_slot_op_logs_status', 'tenant_id', 'operation_status'),
        Index('idx_teacher_slot_op_logs_time', 'tenant_id', 'created_at'),
        Index('idx_teacher_slot_op_logs_weekday', 'tenant_id', 'weekday'),
        
        # 复合索引
        Index('idx_teacher_slot_op_logs_tenant_type', 'tenant_id', 'operation_type'),
        Index('idx_teacher_slot_op_logs_tenant_teacher', 'tenant_id', 'teacher_id'),
        Index('idx_teacher_slot_op_logs_tenant_time', 'tenant_id', 'created_at'),
        Index('idx_teacher_slot_op_logs_teacher_type', 'teacher_id', 'operation_type'),
        Index('idx_teacher_slot_op_logs_slot_type', 'teacher_fixed_slot_id', 'operation_type'),
        
        # 排序优化索引
        Index('idx_teacher_slot_op_logs_time_desc', 'tenant_id', 'created_at', 'id'),
        Index('idx_teacher_slot_op_logs_teacher_time_desc', 'teacher_id', 'created_at', 'id'),
    )


# ==================== 会员固定位锁定操作记录表 ====================

class MemberFixedLockOperationLogBase(SQLModel):
    """会员固定位锁定操作记录表基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    # 关联信息
    member_fixed_slot_lock_id: int = Field(
        sa_column=mapped_column(
            ForeignKey("member_fixed_slot_locks.id", ondelete="SET NULL"),
            index=True
        ),
        description="会员固定位锁定ID"
    )

    # member_fixed_slot_lock_id: Optional[int] = Field(default=None, foreign_key="member_fixed_slot_locks.id", nullable=True, description="会员固定位锁定ID")
    
    # 操作信息
    operation_type: MemberLockOperationType = Field(description="操作类型")
    operation_status: OperationStatus = Field(default=OperationStatus.SUCCESS, description="操作状态")
    operation_description: str = Field(max_length=200, description="操作描述")
    
    # 操作人信息
    operator_id: Optional[int] = Field(default=None, foreign_key="users.id", description="操作人ID")
    operator_name: Optional[str] = Field(default=None, max_length=50, description="操作人姓名")
    operator_type: str = Field(max_length=20, description="操作人类型（admin/member）")
    
    # 业务对象信息（冗余字段）
    member_id: int = Field(foreign_key="members.id", description="会员ID")
    member_name: str = Field(max_length=50, description="会员姓名")
    member_phone: Optional[str] = Field(default=None, max_length=20, description="会员手机号")
    teacher_id: int = Field(foreign_key="teachers.id", description="教师ID")
    teacher_name: str = Field(max_length=50, description="教师姓名")
    
    # 时间段信息（冗余字段）
    weekday: int = Field(ge=1, le=7, description="星期几（1-7）")
    start_time: str = Field(max_length=8, description="开始时间（HH:MM格式）")
    
    # 备注信息
    reason: Optional[str] = Field(default=None, max_length=100, description="操作原因")


class MemberFixedLockOperationLog(MemberFixedLockOperationLogBase, table=True):
    """会员固定位锁定操作记录表数据库模型"""
    __tablename__ = "member_fixed_lock_operation_logs"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 时间信息
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="操作时间")
    
    __table_args__ = (
        # 索引设计 - 所有索引以tenant_id开头（多租户架构要求）
        # 基础查询索引
        Index('idx_member_lock_op_logs_tenant', 'tenant_id'),
        Index('idx_member_lock_op_logs_lock', 'tenant_id', 'member_fixed_slot_lock_id'),
        Index('idx_member_lock_op_logs_member', 'tenant_id', 'member_id'),
        Index('idx_member_lock_op_logs_teacher', 'tenant_id', 'teacher_id'),
        Index('idx_member_lock_op_logs_operator', 'tenant_id', 'operator_id'),
        Index('idx_member_lock_op_logs_type', 'tenant_id', 'operation_type'),
        Index('idx_member_lock_op_logs_status', 'tenant_id', 'operation_status'),
        Index('idx_member_lock_op_logs_time', 'tenant_id', 'created_at'),
        Index('idx_member_lock_op_logs_weekday', 'tenant_id', 'weekday'),
        
        # 复合索引
        Index('idx_member_lock_op_logs_tenant_type', 'tenant_id', 'operation_type'),
        Index('idx_member_lock_op_logs_tenant_member', 'tenant_id', 'member_id'),
        Index('idx_member_lock_op_logs_tenant_teacher', 'tenant_id', 'teacher_id'),
        Index('idx_member_lock_op_logs_tenant_time', 'tenant_id', 'created_at'),
        Index('idx_member_lock_op_logs_member_type', 'member_id', 'operation_type'),
        Index('idx_member_lock_op_logs_teacher_type', 'teacher_id', 'operation_type'),
        Index('idx_member_lock_op_logs_lock_type', 'member_fixed_slot_lock_id', 'operation_type'),
        
        # 排序优化索引
        Index('idx_member_lock_op_logs_time_desc', 'tenant_id', 'created_at', 'id'),
        Index('idx_member_lock_op_logs_member_time_desc', 'member_id', 'created_at', 'id'),
    )
