"""
操作日志记录器 - 统一的日志记录工具
"""
from typing import Optional, Dict, Any
from sqlmodel import Session
from datetime import datetime

from .models import (
    TeacherSlotOperationType, MemberLockOperationType, OperationStatus
)
from .service import TeacherFixedSlotOperationLogService, MemberFixedLockOperationLogService
from .schemas import TeacherFixedSlotOperationLogCreate, MemberFixedLockOperationLogCreate
from app.features.teachers.fixed_slots_models import TeacherFixedSlot
from app.features.members.fixed_lock_models import MemberFixedSlotLock
from app.features.teachers.models import Teacher
from app.features.members.models import Member


class OperationLogger:
    """操作日志记录器 - 提供统一的日志记录接口"""
    
    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        self.teacher_log_service = TeacherFixedSlotOperationLogService(session, tenant_id)
        self.member_log_service = MemberFixedLockOperationLogService(session, tenant_id)

    # ==================== 教师固定时段操作日志 ====================

    def log_teacher_slot_create(
        self,
        slot: TeacherFixedSlot,
        operator_id: int,
        operator_name: str,
        operator_type: str = "admin",
        reason: Optional[str] = None
    ) -> None:
        """记录教师时间段创建操作"""
        # 获取教师信息
        teacher = self.session.get(Teacher, slot.teacher_id)
        # 确保 teacher_name 是字符串类型
        teacher_name = str(teacher.name) if teacher else "未知教师"
        
        log_data = TeacherFixedSlotOperationLogCreate(
            teacher_fixed_slot_id=slot.id,
            operation_type=TeacherSlotOperationType.CREATE,
            operation_description=f"创建教师固定时间段：{self._format_weekday(slot.weekday)} {slot.start_time}",
            operator_id=operator_id,
            operator_name=operator_name,
            operator_type=operator_type,
            teacher_id=slot.teacher_id,
            teacher_name=teacher_name,
            weekday=slot.weekday,
            start_time=str(slot.start_time),
            duration_minutes=slot.duration_minutes,
            reason=reason
        )
        
        self.teacher_log_service.create_operation_log(log_data)

    def log_teacher_slot_delete(
        self,
        slot: TeacherFixedSlot,
        operator_id: int,
        operator_name: str,
        operator_type: str = "admin",
        reason: Optional[str] = None
    ) -> None:
        """记录教师时间段删除操作"""
        # 获取教师信息
        teacher = self.session.get(Teacher, slot.teacher_id)
        # 确保 teacher_name 是字符串类型
        teacher_name = str(teacher.name) if teacher else "未知教师"
        
        log_data = TeacherFixedSlotOperationLogCreate(
            teacher_fixed_slot_id=slot.id,
            operation_type=TeacherSlotOperationType.DELETE,
            operation_description=f"删除教师固定时间段：{self._format_weekday(slot.weekday)} {slot.start_time}",
            operator_id=operator_id,
            operator_name=operator_name,
            operator_type=operator_type,
            teacher_id=slot.teacher_id,
            teacher_name=teacher_name,
            weekday=slot.weekday,
            start_time=str(slot.start_time),
            duration_minutes=slot.duration_minutes,
            reason=reason
        )
        
        self.teacher_log_service.create_operation_log(log_data)

    def log_teacher_slot_visibility_change(
        self,
        slot: TeacherFixedSlot,
        old_visible: bool,
        new_visible: bool,
        operator_id: int,
        operator_name: str,
        operator_type: str = "admin",
        reason: Optional[str] = None
    ) -> None:
        """记录教师时间段可见性变更操作"""
        # 获取教师信息
        teacher = self.session.get(Teacher, slot.teacher_id)
        # 确保 teacher_name 是字符串类型
        teacher_name = str(teacher.name) if teacher else "未知教师"
        
        operation_type = TeacherSlotOperationType.SET_VISIBLE if new_visible else TeacherSlotOperationType.SET_INVISIBLE
        action = "设置对会员可见" if new_visible else "设置对会员不可见"
        
        log_data = TeacherFixedSlotOperationLogCreate(
            teacher_fixed_slot_id=slot.id,
            operation_type=operation_type,
            operation_description=f"{action}：{self._format_weekday(slot.weekday)} {slot.start_time}",
            operator_id=operator_id,
            operator_name=operator_name,
            operator_type=operator_type,
            teacher_id=slot.teacher_id,
            teacher_name=teacher_name,
            weekday=slot.weekday,
            start_time=str(slot.start_time),
            duration_minutes=slot.duration_minutes,
            reason=reason
        )
        
        self.teacher_log_service.create_operation_log(log_data)

    # ==================== 会员固定时段锁定操作日志 ====================

    def log_member_lock_create(
        self,
        lock: MemberFixedSlotLock,
        operator_id: int,
        operator_name: str,
        operator_type: str = "admin",
        reason: Optional[str] = None
    ) -> None:
        """记录会员锁定创建操作"""
        # 获取会员和教师信息
        member = self.session.get(Member, lock.member_id)
        teacher = self.session.get(Teacher, lock.teacher_id)
        
        # 确保 member_name 和 teacher_name 是字符串类型
        member_name = str(member.name) if member else "未知会员"
        teacher_name = str(teacher.name) if teacher else "未知教师"
        
        log_data = MemberFixedLockOperationLogCreate(
            member_fixed_slot_lock_id=lock.id,
            operation_type=MemberLockOperationType.CREATE,
            operation_description=f"会员锁定固定时间段：{self._format_weekday(lock.weekday)} {lock.start_time}",
            operator_id=operator_id,
            operator_name=operator_name,
            operator_type=operator_type,
            member_id=lock.member_id,
            member_name=member_name,
            member_phone=lock.member_phone,
            teacher_id=lock.teacher_id,
            teacher_name=teacher_name,
            weekday=lock.weekday,
            start_time=str(lock.start_time),
            reason=reason
        )
        
        self.member_log_service.create_operation_log(log_data)

    def log_member_lock_delete(
        self,
        lock: MemberFixedSlotLock,
        operator_id: int,
        operator_name: str,
        operator_type: str = "admin",
        is_member_operation: bool = False,
        reason: Optional[str] = None
    ) -> None:
        """记录会员锁定删除操作"""
        # 获取会员和教师信息
        member = self.session.get(Member, lock.member_id)
        teacher = self.session.get(Teacher, lock.teacher_id)
        
        # 确保 member_name 和 teacher_name 是字符串类型
        member_name = str(member.name) if member else "未知会员"
        teacher_name = str(teacher.name) if teacher else "未知教师"
        
        # 根据操作者类型选择操作类型
        operation_type = MemberLockOperationType.DELETE_BY_MEMBER if is_member_operation else MemberLockOperationType.DELETE_BY_ADMIN
        action = "会员取消锁定" if is_member_operation else "管理员删除锁定"
        
        log_data = MemberFixedLockOperationLogCreate(
            member_fixed_slot_lock_id=lock.id,
            operation_type=operation_type,
            operation_description=f"{action}：{self._format_weekday(lock.weekday)} {lock.start_time}",
            operator_id=operator_id,
            operator_name=operator_name,
            operator_type=operator_type,
            member_id=lock.member_id,
            member_name=member_name,
            member_phone=lock.member_phone,
            teacher_id=lock.teacher_id,
            teacher_name=teacher_name,
            weekday=lock.weekday,
            start_time=str(lock.start_time),
            reason=reason
        )
        
        self.member_log_service.create_operation_log(log_data)

    # ==================== 批量操作日志 ====================

    def log_teacher_slots_batch_create(
        self,
        slots: list[TeacherFixedSlot],
        operator_id: int,
        operator_name: str,
        operator_type: str = "admin",
        reason: Optional[str] = None
    ) -> None:
        """记录教师时间段批量创建操作"""
        for slot in slots:
            self.log_teacher_slot_create(slot, operator_id, operator_name, operator_type, reason)

    def log_teacher_slots_batch_delete(
        self,
        slots: list[TeacherFixedSlot],
        operator_id: int,
        operator_name: str,
        operator_type: str = "admin",
        reason: Optional[str] = None
    ) -> None:
        """记录教师时间段批量删除操作"""
        for slot in slots:
            self.log_teacher_slot_delete(slot, operator_id, operator_name, operator_type, reason)

    def log_member_locks_batch_create(
        self,
        locks: list[MemberFixedSlotLock],
        operator_id: int,
        operator_name: str,
        operator_type: str = "admin",
        reason: Optional[str] = None
    ) -> None:
        """记录会员锁定批量创建操作"""
        for lock in locks:
            self.log_member_lock_create(lock, operator_id, operator_name, operator_type, reason)

    def log_member_locks_batch_delete(
        self,
        locks: list[MemberFixedSlotLock],
        operator_id: int,
        operator_name: str,
        operator_type: str = "admin",
        is_member_operation: bool = False,
        reason: Optional[str] = None
    ) -> None:
        """记录会员锁定批量删除操作"""
        for lock in locks:
            self.log_member_lock_delete(lock, operator_id, operator_name, operator_type, is_member_operation, reason)

    # ==================== 辅助方法 ====================

    def _format_weekday(self, weekday: int) -> str:
        """格式化星期显示"""
        weekday_names = {
            1: "周一", 2: "周二", 3: "周三", 4: "周四",
            5: "周五", 6: "周六", 7: "周日"
        }
        return weekday_names.get(weekday, f"周{weekday}")


def get_operation_logger(session: Session, tenant_id: int) -> OperationLogger:
    """获取操作日志记录器实例"""
    return OperationLogger(session, tenant_id)
