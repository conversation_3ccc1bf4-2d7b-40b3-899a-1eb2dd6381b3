"""操作记录服务"""

from typing import List, Optional, Tuple, Dict, Any
from sqlmodel import Session, select, and_, or_, func, desc
from datetime import datetime, date
import json

from app.features.base.base_service import TenantAwareService
from app.features.base.query_utils import search_with_pagination

from .models import (
    ScheduledClassOperationLog, TeacherFixedSlotOperationLog, MemberFixedLockOperationLog,
    ClassOperationType, TeacherSlotOperationType, MemberLockOperationType,
    OperationStatus
)
from .schemas import (
    ScheduledClassOperationLogCreate, ScheduledClassOperationLogQuery,
    TeacherFixedSlotOperationLogCreate, TeacherFixedSlotOperationLogQuery,
    MemberFixedLockOperationLogCreate, MemberFixedLockOperationLogQuery
)


class ScheduledClassOperationLogService(TenantAwareService[ScheduledClassOperationLog]):
    """课程操作记录服务"""
    
    @property
    def model_class(self):
        return ScheduledClassOperationLog
    
    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)
    
    def create_operation_log(self, log_data: ScheduledClassOperationLogCreate) -> ScheduledClassOperationLog:
        """创建课程操作记录"""
        log_dict = log_data.model_dump()
        log_dict['tenant_id'] = self.tenant_id
        log_dict['operation_status'] = OperationStatus.SUCCESS
        log_dict['created_at'] = datetime.now()
        
        log = ScheduledClassOperationLog(**log_dict)
        self.session.add(log)
        self.session.commit()
        self.session.refresh(log)
        
        return log
    
    def get_operation_log(self, log_id: int) -> ScheduledClassOperationLog:
        """获取课程操作记录"""
        log = self.session.get(ScheduledClassOperationLog, log_id)
        if not log or log.tenant_id != self.tenant_id:
            raise ValueError(f"操作记录不存在: {log_id}")
        return log
    
    def query_operation_logs(self, query: ScheduledClassOperationLogQuery) -> Tuple[List[ScheduledClassOperationLog], int]:
        """查询课程操作记录"""
        # 构建筛选条件
        filters = {}
        
        if query.scheduled_class_id:
            filters['scheduled_class_id'] = query.scheduled_class_id
        
        if query.operation_type:
            filters['operation_type'] = query.operation_type
        
        if query.operation_status:
            filters['operation_status'] = query.operation_status
        
        if query.teacher_id:
            filters['teacher_id'] = query.teacher_id
        
        if query.member_id:
            filters['member_id'] = query.member_id
        
        if query.operator_id:
            filters['operator_id'] = query.operator_id
        
        if query.operator_type:
            filters['operator_type'] = query.operator_type
        
        # 添加租户过滤
        filters['tenant_id'] = self.tenant_id
        
        # 时间范围筛选需要特殊处理
        additional_filters = []
        
        if query.class_datetime_from:
            additional_filters.append(ScheduledClassOperationLog.class_datetime >= query.class_datetime_from)
        
        if query.class_datetime_to:
            additional_filters.append(ScheduledClassOperationLog.class_datetime <= query.class_datetime_to)
        
        if query.created_from:
            additional_filters.append(ScheduledClassOperationLog.created_at >= query.created_from)
        
        if query.created_to:
            additional_filters.append(ScheduledClassOperationLog.created_at <= query.created_to)
        
        # 使用分页搜索
        search_fields = ['operation_description'] if query.operation_description else []
        
        logs, total = search_with_pagination(
            session=self.session,
            model_class=ScheduledClassOperationLog,
            search_term=query.operation_description,
            search_fields=search_fields,
            filters=filters,
            page=query.page,
            size=query.size,
            sort_field=query.sort_by,
            sort_desc=(query.sort_order == "desc")
        )
        
        # 应用额外的时间范围筛选
        if additional_filters:
            statement = select(ScheduledClassOperationLog).where(and_(*additional_filters))
            for key, value in filters.items():
                if hasattr(ScheduledClassOperationLog, key):
                    field = getattr(ScheduledClassOperationLog, key)
                    statement = statement.where(field == value)
            
            if query.operation_description:
                statement = statement.where(
                    ScheduledClassOperationLog.operation_description.ilike(f"%{query.operation_description}%")
                )
            
            # 重新计算总数
            count_statement = select(func.count()).select_from(statement.subquery())
            total = self.session.exec(count_statement).one()
            
            # 应用排序和分页
            if query.sort_order == "desc":
                statement = statement.order_by(desc(getattr(ScheduledClassOperationLog, query.sort_by)))
            else:
                statement = statement.order_by(getattr(ScheduledClassOperationLog, query.sort_by))
            
            offset = (query.page - 1) * query.size
            statement = statement.offset(offset).limit(query.size)
            
            logs = self.session.exec(statement).all()
        
        return logs, total
    
    def get_class_operation_logs(self, class_id: int, limit: int = 50) -> List[ScheduledClassOperationLog]:
        """获取指定课程的操作记录"""
        statement = select(ScheduledClassOperationLog).where(
            and_(
                ScheduledClassOperationLog.tenant_id == self.tenant_id,
                ScheduledClassOperationLog.scheduled_class_id == class_id
            )
        ).order_by(desc(ScheduledClassOperationLog.created_at)).limit(limit)
        
        return self.session.exec(statement).all()
    
    def get_teacher_operation_logs(self, teacher_id: int, limit: int = 50) -> List[ScheduledClassOperationLog]:
        """获取指定教师的操作记录"""
        statement = select(ScheduledClassOperationLog).where(
            and_(
                ScheduledClassOperationLog.tenant_id == self.tenant_id,
                ScheduledClassOperationLog.teacher_id == teacher_id
            )
        ).order_by(desc(ScheduledClassOperationLog.created_at)).limit(limit)
        
        return self.session.exec(statement).all()
    
    def get_member_operation_logs(self, member_id: int, limit: int = 50) -> List[ScheduledClassOperationLog]:
        """获取指定会员的操作记录"""
        statement = select(ScheduledClassOperationLog).where(
            and_(
                ScheduledClassOperationLog.tenant_id == self.tenant_id,
                ScheduledClassOperationLog.member_id == member_id
            )
        ).order_by(desc(ScheduledClassOperationLog.created_at)).limit(limit)
        
        return self.session.exec(statement).all()


class TeacherFixedSlotOperationLogService(TenantAwareService[TeacherFixedSlotOperationLog]):
    """教师固定时间段操作记录服务"""
    
    @property
    def model_class(self):
        return TeacherFixedSlotOperationLog
    
    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)
    
    def create_operation_log(self, log_data: TeacherFixedSlotOperationLogCreate) -> TeacherFixedSlotOperationLog:
        """创建教师固定时间段操作记录"""
        log_dict = log_data.model_dump()
        log_dict['tenant_id'] = self.tenant_id
        log_dict['operation_status'] = OperationStatus.SUCCESS
        log_dict['created_at'] = datetime.now()
        
        log = TeacherFixedSlotOperationLog(**log_dict)
        self.session.add(log)
        self.session.commit()
        self.session.refresh(log)
        
        return log
    
    def get_operation_log(self, log_id: int) -> TeacherFixedSlotOperationLog:
        """获取教师固定时间段操作记录"""
        log = self.session.get(TeacherFixedSlotOperationLog, log_id)
        if not log or log.tenant_id != self.tenant_id:
            raise ValueError(f"操作记录不存在: {log_id}")
        return log
    
    def query_operation_logs(self, query: TeacherFixedSlotOperationLogQuery) -> Tuple[List[TeacherFixedSlotOperationLog], int]:
        """查询教师固定时间段操作记录"""
        # 构建筛选条件
        filters = {}
        
        if query.teacher_fixed_slot_id:
            filters['teacher_fixed_slot_id'] = query.teacher_fixed_slot_id
        
        if query.operation_type:
            filters['operation_type'] = query.operation_type
        
        if query.operation_status:
            filters['operation_status'] = query.operation_status
        
        if query.teacher_id:
            filters['teacher_id'] = query.teacher_id
        
        if query.operator_id:
            filters['operator_id'] = query.operator_id
        
        if query.operator_type:
            filters['operator_type'] = query.operator_type
        
        if query.weekday:
            filters['weekday'] = query.weekday
        
        # 添加租户过滤
        filters['tenant_id'] = self.tenant_id
        
        # 时间范围筛选需要特殊处理
        additional_filters = []
        
        if query.created_from:
            additional_filters.append(TeacherFixedSlotOperationLog.created_at >= query.created_from)
        
        if query.created_to:
            additional_filters.append(TeacherFixedSlotOperationLog.created_at <= query.created_to)
        
        # 使用分页搜索
        search_fields = ['operation_description'] if query.operation_description else []
        
        logs, total = search_with_pagination(
            session=self.session,
            model_class=TeacherFixedSlotOperationLog,
            search_term=query.operation_description,
            search_fields=search_fields,
            filters=filters,
            page=query.page,
            size=query.size,
            sort_field=query.sort_by,
            sort_desc=(query.sort_order == "desc")
        )
        
        # 应用额外的时间范围筛选
        if additional_filters:
            statement = select(TeacherFixedSlotOperationLog).where(and_(*additional_filters))
            for key, value in filters.items():
                if hasattr(TeacherFixedSlotOperationLog, key):
                    field = getattr(TeacherFixedSlotOperationLog, key)
                    statement = statement.where(field == value)
            
            if query.operation_description:
                statement = statement.where(
                    TeacherFixedSlotOperationLog.operation_description.ilike(f"%{query.operation_description}%")
                )
            
            # 重新计算总数
            count_statement = select(func.count()).select_from(statement.subquery())
            total = self.session.exec(count_statement).one()
            
            # 应用排序和分页
            if query.sort_order == "desc":
                statement = statement.order_by(desc(getattr(TeacherFixedSlotOperationLog, query.sort_by)))
            else:
                statement = statement.order_by(getattr(TeacherFixedSlotOperationLog, query.sort_by))
            
            offset = (query.page - 1) * query.size
            statement = statement.offset(offset).limit(query.size)
            
            logs = self.session.exec(statement).all()
        
        return logs, total
    
    def get_teacher_slot_operation_logs(self, slot_id: int, limit: int = 50) -> List[TeacherFixedSlotOperationLog]:
        """获取指定教师时间段的操作记录"""
        statement = select(TeacherFixedSlotOperationLog).where(
            and_(
                TeacherFixedSlotOperationLog.tenant_id == self.tenant_id,
                TeacherFixedSlotOperationLog.teacher_fixed_slot_id == slot_id
            )
        ).order_by(desc(TeacherFixedSlotOperationLog.created_at)).limit(limit)
        
        return self.session.exec(statement).all()
    
    def get_teacher_operation_logs(self, teacher_id: int, limit: int = 50) -> List[TeacherFixedSlotOperationLog]:
        """获取指定教师的时间段操作记录"""
        statement = select(TeacherFixedSlotOperationLog).where(
            and_(
                TeacherFixedSlotOperationLog.tenant_id == self.tenant_id,
                TeacherFixedSlotOperationLog.teacher_id == teacher_id
            )
        ).order_by(desc(TeacherFixedSlotOperationLog.created_at)).limit(limit)
        
        return self.session.exec(statement).all()


class MemberFixedLockOperationLogService(TenantAwareService[MemberFixedLockOperationLog]):
    """会员固定位锁定操作记录服务"""

    @property
    def model_class(self):
        return MemberFixedLockOperationLog

    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)

    def create_operation_log(self, log_data: MemberFixedLockOperationLogCreate) -> MemberFixedLockOperationLog:
        """创建会员固定位锁定操作记录"""
        log_dict = log_data.model_dump()
        log_dict['tenant_id'] = self.tenant_id
        log_dict['operation_status'] = OperationStatus.SUCCESS
        log_dict['created_at'] = datetime.now()

        log = MemberFixedLockOperationLog(**log_dict)
        self.session.add(log)
        self.session.commit()
        self.session.refresh(log)

        return log

    def get_operation_log(self, log_id: int) -> MemberFixedLockOperationLog:
        """获取会员固定位锁定操作记录"""
        log = self.session.get(MemberFixedLockOperationLog, log_id)
        if not log or log.tenant_id != self.tenant_id:
            raise ValueError(f"操作记录不存在: {log_id}")
        return log

    def query_operation_logs(self, query: MemberFixedLockOperationLogQuery) -> Tuple[List[MemberFixedLockOperationLog], int]:
        """查询会员固定位锁定操作记录"""
        # 构建筛选条件
        filters = {}

        if query.member_fixed_slot_lock_id:
            filters['member_fixed_slot_lock_id'] = query.member_fixed_slot_lock_id

        if query.operation_type:
            filters['operation_type'] = query.operation_type

        if query.operation_status:
            filters['operation_status'] = query.operation_status

        if query.member_id:
            filters['member_id'] = query.member_id

        if query.teacher_id:
            filters['teacher_id'] = query.teacher_id

        if query.operator_id:
            filters['operator_id'] = query.operator_id

        if query.operator_type:
            filters['operator_type'] = query.operator_type

        if query.weekday:
            filters['weekday'] = query.weekday

        # 添加租户过滤
        filters['tenant_id'] = self.tenant_id

        # 时间范围筛选需要特殊处理
        additional_filters = []

        if query.created_from:
            additional_filters.append(MemberFixedLockOperationLog.created_at >= query.created_from)

        if query.created_to:
            additional_filters.append(MemberFixedLockOperationLog.created_at <= query.created_to)

        # 使用分页搜索
        search_fields = ['operation_description'] if query.operation_description else []

        logs, total = search_with_pagination(
            session=self.session,
            model_class=MemberFixedLockOperationLog,
            search_term=query.operation_description,
            search_fields=search_fields,
            filters=filters,
            page=query.page,
            size=query.size,
            sort_field=query.sort_by,
            sort_desc=(query.sort_order == "desc")
        )

        # 应用额外的时间范围筛选
        if additional_filters:
            statement = select(MemberFixedLockOperationLog).where(and_(*additional_filters))
            for key, value in filters.items():
                if hasattr(MemberFixedLockOperationLog, key):
                    field = getattr(MemberFixedLockOperationLog, key)
                    statement = statement.where(field == value)

            if query.operation_description:
                statement = statement.where(
                    MemberFixedLockOperationLog.operation_description.ilike(f"%{query.operation_description}%")
                )

            # 重新计算总数
            count_statement = select(func.count()).select_from(statement.subquery())
            total = self.session.exec(count_statement).one()

            # 应用排序和分页
            if query.sort_order == "desc":
                statement = statement.order_by(desc(getattr(MemberFixedLockOperationLog, query.sort_by)))
            else:
                statement = statement.order_by(getattr(MemberFixedLockOperationLog, query.sort_by))

            offset = (query.page - 1) * query.size
            statement = statement.offset(offset).limit(query.size)

            logs = self.session.exec(statement).all()

        return logs, total

    def get_member_lock_operation_logs(self, lock_id: int, limit: int = 50) -> List[MemberFixedLockOperationLog]:
        """获取指定会员锁定的操作记录"""
        statement = select(MemberFixedLockOperationLog).where(
            and_(
                MemberFixedLockOperationLog.tenant_id == self.tenant_id,
                MemberFixedLockOperationLog.member_fixed_slot_lock_id == lock_id
            )
        ).order_by(desc(MemberFixedLockOperationLog.created_at)).limit(limit)

        return self.session.exec(statement).all()

    def get_member_operation_logs(self, member_id: int, limit: int = 50) -> List[MemberFixedLockOperationLog]:
        """获取指定会员的锁定操作记录"""
        statement = select(MemberFixedLockOperationLog).where(
            and_(
                MemberFixedLockOperationLog.tenant_id == self.tenant_id,
                MemberFixedLockOperationLog.member_id == member_id
            )
        ).order_by(desc(MemberFixedLockOperationLog.created_at)).limit(limit)

        return self.session.exec(statement).all()

    def get_teacher_lock_operation_logs(self, teacher_id: int, limit: int = 50) -> List[MemberFixedLockOperationLog]:
        """获取指定教师的会员锁定操作记录"""
        statement = select(MemberFixedLockOperationLog).where(
            and_(
                MemberFixedLockOperationLog.tenant_id == self.tenant_id,
                MemberFixedLockOperationLog.teacher_id == teacher_id
            )
        ).order_by(desc(MemberFixedLockOperationLog.created_at)).limit(limit)

        return self.session.exec(statement).all()


# ==================== 统计服务 ====================

class OperationLogStatisticsService:
    """操作记录统计服务"""

    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id

    def get_class_operation_statistics(self,
                                     start_date: Optional[datetime] = None,
                                     end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取课程操作统计"""
        # 构建基础查询
        statement = select(
            ScheduledClassOperationLog.operation_type,
            func.count(ScheduledClassOperationLog.id).label('count'),
            func.count(func.distinct(ScheduledClassOperationLog.scheduled_class_id)).label('unique_classes'),
            func.count(func.distinct(ScheduledClassOperationLog.teacher_id)).label('unique_teachers'),
            func.count(func.distinct(ScheduledClassOperationLog.member_id)).label('unique_members')
        ).where(ScheduledClassOperationLog.tenant_id == self.tenant_id)

        # 添加时间范围筛选
        if start_date:
            statement = statement.where(ScheduledClassOperationLog.created_at >= start_date)
        if end_date:
            statement = statement.where(ScheduledClassOperationLog.created_at <= end_date)

        statement = statement.group_by(ScheduledClassOperationLog.operation_type)

        results = self.session.exec(statement).all()

        # 处理统计结果
        statistics = {
            'total_operations': 0,
            'total_unique_classes': 0,
            'total_unique_teachers': 0,
            'total_unique_members': 0,
            'operation_type_stats': {},
            'period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }
        }

        unique_classes = set()
        unique_teachers = set()
        unique_members = set()

        for result in results:
            operation_type, count, classes, teachers, members = result
            statistics['total_operations'] += count
            statistics['operation_type_stats'][operation_type.value] = {
                'count': count,
                'unique_classes': classes,
                'unique_teachers': teachers,
                'unique_members': members
            }

        # 计算总的唯一数量（需要重新查询以避免重复计算）
        if start_date or end_date:
            unique_statement = select(
                func.count(func.distinct(ScheduledClassOperationLog.scheduled_class_id)).label('classes'),
                func.count(func.distinct(ScheduledClassOperationLog.teacher_id)).label('teachers'),
                func.count(func.distinct(ScheduledClassOperationLog.member_id)).label('members')
            ).where(ScheduledClassOperationLog.tenant_id == self.tenant_id)

            if start_date:
                unique_statement = unique_statement.where(ScheduledClassOperationLog.created_at >= start_date)
            if end_date:
                unique_statement = unique_statement.where(ScheduledClassOperationLog.created_at <= end_date)

            unique_result = self.session.exec(unique_statement).first()
            if unique_result:
                statistics['total_unique_classes'] = unique_result.classes
                statistics['total_unique_teachers'] = unique_result.teachers
                statistics['total_unique_members'] = unique_result.members

        return statistics

    def get_teacher_slot_operation_statistics(self,
                                            start_date: Optional[datetime] = None,
                                            end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取教师时间段操作统计"""
        # 构建基础查询
        statement = select(
            TeacherFixedSlotOperationLog.operation_type,
            func.count(TeacherFixedSlotOperationLog.id).label('count'),
            func.count(func.distinct(TeacherFixedSlotOperationLog.teacher_fixed_slot_id)).label('unique_slots'),
            func.count(func.distinct(TeacherFixedSlotOperationLog.teacher_id)).label('unique_teachers')
        ).where(TeacherFixedSlotOperationLog.tenant_id == self.tenant_id)

        # 添加时间范围筛选
        if start_date:
            statement = statement.where(TeacherFixedSlotOperationLog.created_at >= start_date)
        if end_date:
            statement = statement.where(TeacherFixedSlotOperationLog.created_at <= end_date)

        statement = statement.group_by(TeacherFixedSlotOperationLog.operation_type)

        results = self.session.exec(statement).all()

        # 处理统计结果
        statistics = {
            'total_operations': 0,
            'total_unique_slots': 0,
            'total_unique_teachers': 0,
            'operation_type_stats': {},
            'period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }
        }

        for result in results:
            operation_type, count, slots, teachers = result
            statistics['total_operations'] += count
            statistics['operation_type_stats'][operation_type.value] = {
                'count': count,
                'unique_slots': slots,
                'unique_teachers': teachers
            }

        # 计算总的唯一数量
        if start_date or end_date:
            unique_statement = select(
                func.count(func.distinct(TeacherFixedSlotOperationLog.teacher_fixed_slot_id)).label('slots'),
                func.count(func.distinct(TeacherFixedSlotOperationLog.teacher_id)).label('teachers')
            ).where(TeacherFixedSlotOperationLog.tenant_id == self.tenant_id)

            if start_date:
                unique_statement = unique_statement.where(TeacherFixedSlotOperationLog.created_at >= start_date)
            if end_date:
                unique_statement = unique_statement.where(TeacherFixedSlotOperationLog.created_at <= end_date)

            unique_result = self.session.exec(unique_statement).first()
            if unique_result:
                statistics['total_unique_slots'] = unique_result.slots
                statistics['total_unique_teachers'] = unique_result.teachers

        return statistics

    def get_member_lock_operation_statistics(self,
                                           start_date: Optional[datetime] = None,
                                           end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取会员锁定操作统计"""
        # 构建基础查询
        statement = select(
            MemberFixedLockOperationLog.operation_type,
            func.count(MemberFixedLockOperationLog.id).label('count'),
            func.count(func.distinct(MemberFixedLockOperationLog.member_fixed_slot_lock_id)).label('unique_locks'),
            func.count(func.distinct(MemberFixedLockOperationLog.member_id)).label('unique_members'),
            func.count(func.distinct(MemberFixedLockOperationLog.teacher_id)).label('unique_teachers')
        ).where(MemberFixedLockOperationLog.tenant_id == self.tenant_id)

        # 添加时间范围筛选
        if start_date:
            statement = statement.where(MemberFixedLockOperationLog.created_at >= start_date)
        if end_date:
            statement = statement.where(MemberFixedLockOperationLog.created_at <= end_date)

        statement = statement.group_by(MemberFixedLockOperationLog.operation_type)

        results = self.session.exec(statement).all()

        # 处理统计结果
        statistics = {
            'total_operations': 0,
            'total_unique_locks': 0,
            'total_unique_members': 0,
            'total_unique_teachers': 0,
            'operation_type_stats': {},
            'period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }
        }

        for result in results:
            operation_type, count, locks, members, teachers = result
            statistics['total_operations'] += count
            statistics['operation_type_stats'][operation_type.value] = {
                'count': count,
                'unique_locks': locks,
                'unique_members': members,
                'unique_teachers': teachers
            }

        # 计算总的唯一数量
        if start_date or end_date:
            unique_statement = select(
                func.count(func.distinct(MemberFixedLockOperationLog.member_fixed_slot_lock_id)).label('locks'),
                func.count(func.distinct(MemberFixedLockOperationLog.member_id)).label('members'),
                func.count(func.distinct(MemberFixedLockOperationLog.teacher_id)).label('teachers')
            ).where(MemberFixedLockOperationLog.tenant_id == self.tenant_id)

            if start_date:
                unique_statement = unique_statement.where(MemberFixedLockOperationLog.created_at >= start_date)
            if end_date:
                unique_statement = unique_statement.where(MemberFixedLockOperationLog.created_at <= end_date)

            unique_result = self.session.exec(unique_statement).first()
            if unique_result:
                statistics['total_unique_locks'] = unique_result.locks
                statistics['total_unique_members'] = unique_result.members
                statistics['total_unique_teachers'] = unique_result.teachers

        return statistics

    def get_comprehensive_statistics(self,
                                   start_date: Optional[datetime] = None,
                                   end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取综合操作统计"""
        class_stats = self.get_class_operation_statistics(start_date, end_date)
        teacher_slot_stats = self.get_teacher_slot_operation_statistics(start_date, end_date)
        member_lock_stats = self.get_member_lock_operation_statistics(start_date, end_date)

        return {
            'period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            },
            'summary': {
                'total_operations': (
                    class_stats['total_operations'] +
                    teacher_slot_stats['total_operations'] +
                    member_lock_stats['total_operations']
                ),
                'class_operations': class_stats['total_operations'],
                'teacher_slot_operations': teacher_slot_stats['total_operations'],
                'member_lock_operations': member_lock_stats['total_operations']
            },
            'class_operations': class_stats,
            'teacher_slot_operations': teacher_slot_stats,
            'member_lock_operations': member_lock_stats
        }

    def get_daily_operation_trends(self,
                                 start_date: datetime,
                                 end_date: datetime,
                                 operation_type: str = 'all') -> Dict[str, Any]:
        """获取每日操作趋势"""
        from sqlalchemy import func, cast, Date

        trends = {}

        if operation_type in ['all', 'class']:
            # 课程操作趋势
            statement = select(
                cast(ScheduledClassOperationLog.created_at, Date).label('date'),
                ScheduledClassOperationLog.operation_type,
                func.count(ScheduledClassOperationLog.id).label('count')
            ).where(
                and_(
                    ScheduledClassOperationLog.tenant_id == self.tenant_id,
                    ScheduledClassOperationLog.created_at >= start_date,
                    ScheduledClassOperationLog.created_at <= end_date
                )
            ).group_by(
                cast(ScheduledClassOperationLog.created_at, Date),
                ScheduledClassOperationLog.operation_type
            ).order_by(cast(ScheduledClassOperationLog.created_at, Date))

            class_trends = self.session.exec(statement).all()
            trends['class_operations'] = [
                {
                    'date': result.date.isoformat(),
                    'operation_type': result.operation_type.value,
                    'count': result.count
                }
                for result in class_trends
            ]

        if operation_type in ['all', 'teacher_slot']:
            # 教师时间段操作趋势
            statement = select(
                cast(TeacherFixedSlotOperationLog.created_at, Date).label('date'),
                TeacherFixedSlotOperationLog.operation_type,
                func.count(TeacherFixedSlotOperationLog.id).label('count')
            ).where(
                and_(
                    TeacherFixedSlotOperationLog.tenant_id == self.tenant_id,
                    TeacherFixedSlotOperationLog.created_at >= start_date,
                    TeacherFixedSlotOperationLog.created_at <= end_date
                )
            ).group_by(
                cast(TeacherFixedSlotOperationLog.created_at, Date),
                TeacherFixedSlotOperationLog.operation_type
            ).order_by(cast(TeacherFixedSlotOperationLog.created_at, Date))

            teacher_trends = self.session.exec(statement).all()
            trends['teacher_slot_operations'] = [
                {
                    'date': result.date.isoformat(),
                    'operation_type': result.operation_type.value,
                    'count': result.count
                }
                for result in teacher_trends
            ]

        if operation_type in ['all', 'member_lock']:
            # 会员锁定操作趋势
            statement = select(
                cast(MemberFixedLockOperationLog.created_at, Date).label('date'),
                MemberFixedLockOperationLog.operation_type,
                func.count(MemberFixedLockOperationLog.id).label('count')
            ).where(
                and_(
                    MemberFixedLockOperationLog.tenant_id == self.tenant_id,
                    MemberFixedLockOperationLog.created_at >= start_date,
                    MemberFixedLockOperationLog.created_at <= end_date
                )
            ).group_by(
                cast(MemberFixedLockOperationLog.created_at, Date),
                MemberFixedLockOperationLog.operation_type
            ).order_by(cast(MemberFixedLockOperationLog.created_at, Date))

            member_trends = self.session.exec(statement).all()
            trends['member_lock_operations'] = [
                {
                    'date': result.date.isoformat(),
                    'operation_type': result.operation_type.value,
                    'count': result.count
                }
                for result in member_trends
            ]

        return {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            },
            'trends': trends
        }

    def get_operator_statistics(self,
                              start_date: Optional[datetime] = None,
                              end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取操作人统计"""
        # 课程操作的操作人统计
        class_statement = select(
            ScheduledClassOperationLog.operator_id,
            ScheduledClassOperationLog.operator_name,
            ScheduledClassOperationLog.operator_type,
            func.count(ScheduledClassOperationLog.id).label('count')
        ).where(ScheduledClassOperationLog.tenant_id == self.tenant_id)

        if start_date:
            class_statement = class_statement.where(ScheduledClassOperationLog.created_at >= start_date)
        if end_date:
            class_statement = class_statement.where(ScheduledClassOperationLog.created_at <= end_date)

        class_statement = class_statement.group_by(
            ScheduledClassOperationLog.operator_id,
            ScheduledClassOperationLog.operator_name,
            ScheduledClassOperationLog.operator_type
        ).order_by(func.count(ScheduledClassOperationLog.id).desc())

        class_operators = self.session.exec(class_statement).all()

        # 教师时间段操作的操作人统计
        teacher_statement = select(
            TeacherFixedSlotOperationLog.operator_id,
            TeacherFixedSlotOperationLog.operator_name,
            TeacherFixedSlotOperationLog.operator_type,
            func.count(TeacherFixedSlotOperationLog.id).label('count')
        ).where(TeacherFixedSlotOperationLog.tenant_id == self.tenant_id)

        if start_date:
            teacher_statement = teacher_statement.where(TeacherFixedSlotOperationLog.created_at >= start_date)
        if end_date:
            teacher_statement = teacher_statement.where(TeacherFixedSlotOperationLog.created_at <= end_date)

        teacher_statement = teacher_statement.group_by(
            TeacherFixedSlotOperationLog.operator_id,
            TeacherFixedSlotOperationLog.operator_name,
            TeacherFixedSlotOperationLog.operator_type
        ).order_by(func.count(TeacherFixedSlotOperationLog.id).desc())

        teacher_operators = self.session.exec(teacher_statement).all()

        # 会员锁定操作的操作人统计
        member_statement = select(
            MemberFixedLockOperationLog.operator_id,
            MemberFixedLockOperationLog.operator_name,
            MemberFixedLockOperationLog.operator_type,
            func.count(MemberFixedLockOperationLog.id).label('count')
        ).where(MemberFixedLockOperationLog.tenant_id == self.tenant_id)

        if start_date:
            member_statement = member_statement.where(MemberFixedLockOperationLog.created_at >= start_date)
        if end_date:
            member_statement = member_statement.where(MemberFixedLockOperationLog.created_at <= end_date)

        member_statement = member_statement.group_by(
            MemberFixedLockOperationLog.operator_id,
            MemberFixedLockOperationLog.operator_name,
            MemberFixedLockOperationLog.operator_type
        ).order_by(func.count(MemberFixedLockOperationLog.id).desc())

        member_operators = self.session.exec(member_statement).all()

        return {
            'period': {
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            },
            'class_operation_operators': [
                {
                    'operator_id': op.operator_id,
                    'operator_name': op.operator_name,
                    'operator_type': op.operator_type,
                    'operation_count': op.count
                }
                for op in class_operators
            ],
            'teacher_slot_operation_operators': [
                {
                    'operator_id': op.operator_id,
                    'operator_name': op.operator_name,
                    'operator_type': op.operator_type,
                    'operation_count': op.count
                }
                for op in teacher_operators
            ],
            'member_lock_operation_operators': [
                {
                    'operator_id': op.operator_id,
                    'operator_name': op.operator_name,
                    'operator_type': op.operator_type,
                    'operation_count': op.count
                }
                for op in member_operators
            ]
        }
