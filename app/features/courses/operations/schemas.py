"""操作记录API数据模式"""

from pydantic import BaseModel, Field, field_validator
from typing import Optional, List
from datetime import datetime, date
from .models import (
    ClassOperationType, TeacherSlotOperationType, MemberLockOperationType,
    OperationStatus
)
from pydantic import ConfigDict
import json


# ==================== 课程操作记录Schema ====================

class ScheduledClassOperationLogCreate(BaseModel):
    """创建课程操作记录请求"""
    scheduled_class_id: int = Field(..., description="课程ID")
    operation_type: ClassOperationType = Field(..., description="操作类型")
    operation_description: str = Field(..., min_length=1, max_length=200, description="操作描述")
    operator_type: str = Field(..., max_length=20, description="操作人类型（admin/teacher/member）")
    operator_id: Optional[int] = Field(None, description="操作人ID")
    operator_name: Optional[str] = Field(None, max_length=50, description="操作人姓名")
    
    # 业务对象信息
    teacher_id: int = Field(..., description="教师ID")
    teacher_name: str = Field(..., max_length=50, description="教师姓名")
    member_id: Optional[int] = Field(None, description="会员ID")
    member_name: Optional[str] = Field(None, max_length=50, description="会员姓名")
    
    # 课程信息
    class_datetime: datetime = Field(..., description="课程时间")
    
    # 详细信息
    reason: Optional[str] = Field(None, max_length=100, description="操作原因")


class ScheduledClassOperationLogResponse(BaseModel):
    """课程操作记录响应"""
    id: int
    tenant_id: int
    scheduled_class_id: int
    operation_type: ClassOperationType
    operation_status: OperationStatus
    operation_description: str
    operator_id: Optional[int]
    operator_name: Optional[str]
    operator_type: str
    teacher_id: int
    teacher_name: str
    member_id: Optional[int]
    member_name: Optional[str]
    class_datetime: datetime
    reason: Optional[str]
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class ScheduledClassOperationLogQuery(BaseModel):
    """课程操作记录查询参数"""
    scheduled_class_id: Optional[int] = Field(None, description="课程ID")
    operation_type: Optional[ClassOperationType] = Field(None, description="操作类型")
    operation_status: Optional[OperationStatus] = Field(None, description="操作状态")
    teacher_id: Optional[int] = Field(None, description="教师ID")
    member_id: Optional[int] = Field(None, description="会员ID")
    operator_id: Optional[int] = Field(None, description="操作人ID")
    operator_type: Optional[str] = Field(None, description="操作人类型")
    class_datetime_from: Optional[datetime] = Field(None, description="课程时间范围-起始")
    class_datetime_to: Optional[datetime] = Field(None, description="课程时间范围-结束")
    created_from: Optional[datetime] = Field(None, description="创建时间范围-起始")
    created_to: Optional[datetime] = Field(None, description="创建时间范围-结束")
    operation_description: Optional[str] = Field(None, description="操作描述（模糊搜索）")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")


# ==================== 教师固定时间段操作记录Schema ====================

class TeacherFixedSlotOperationLogCreate(BaseModel):
    """创建教师固定时间段操作记录请求"""
    teacher_fixed_slot_id: int = Field(..., description="教师固定时间段ID")
    operation_type: TeacherSlotOperationType = Field(..., description="操作类型")
    operation_description: str = Field(..., min_length=1, max_length=500, description="操作描述")
    operator_type: str = Field(..., max_length=20, description="操作人类型（admin/teacher）")
    operator_id: Optional[int] = Field(None, description="操作人ID")
    operator_name: Optional[str] = Field(None, max_length=50, description="操作人姓名")
    
    # 业务对象信息
    teacher_id: int = Field(..., description="教师ID")
    teacher_name: str = Field(..., max_length=50, description="教师姓名")
    
    # 时间段信息
    weekday: int = Field(..., ge=1, le=7, description="星期几（1-7）")
    start_time: str = Field(..., max_length=8, description="开始时间（HH:MM格式）")
    duration_minutes: Optional[int] = Field(None, description="时长（分钟）")
    
    # 详细信息
    reason: Optional[str] = Field(None, max_length=100, description="操作原因")


class TeacherFixedSlotOperationLogResponse(BaseModel):
    """教师固定时间段操作记录响应"""
    id: int
    tenant_id: int
    teacher_fixed_slot_id: int
    operation_type: TeacherSlotOperationType
    operation_status: OperationStatus
    operation_description: str
    operator_id: Optional[int]
    operator_name: Optional[str]
    operator_type: str
    teacher_id: int
    teacher_name: str
    weekday: int
    start_time: str
    duration_minutes: Optional[int]
    reason: Optional[str]
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class TeacherFixedSlotOperationLogQuery(BaseModel):
    """教师固定时间段操作记录查询参数"""
    teacher_fixed_slot_id: Optional[int] = Field(None, description="教师固定时间段ID")
    operation_type: Optional[TeacherSlotOperationType] = Field(None, description="操作类型")
    operation_status: Optional[OperationStatus] = Field(None, description="操作状态")
    teacher_id: Optional[int] = Field(None, description="教师ID")
    operator_id: Optional[int] = Field(None, description="操作人ID")
    operator_type: Optional[str] = Field(None, description="操作人类型")
    weekday: Optional[int] = Field(None, ge=1, le=7, description="星期几")
    created_from: Optional[datetime] = Field(None, description="创建时间范围-起始")
    created_to: Optional[datetime] = Field(None, description="创建时间范围-结束")
    operation_description: Optional[str] = Field(None, description="操作描述（模糊搜索）")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")


# ==================== 会员固定位锁定操作记录Schema ====================

class MemberFixedLockOperationLogCreate(BaseModel):
    """创建会员固定位锁定操作记录请求"""
    member_fixed_slot_lock_id: int = Field(..., description="会员固定位锁定ID")
    operation_type: MemberLockOperationType = Field(..., description="操作类型")
    operation_description: str = Field(..., min_length=1, max_length=500, description="操作描述")
    operator_type: str = Field(..., max_length=20, description="操作人类型（admin/member）")
    operator_id: Optional[int] = Field(None, description="操作人ID")
    operator_name: Optional[str] = Field(None, max_length=50, description="操作人姓名")
    
    # 业务对象信息
    member_id: int = Field(..., description="会员ID")
    member_name: str = Field(..., max_length=50, description="会员姓名")
    teacher_id: int = Field(..., description="教师ID")
    teacher_name: str = Field(..., max_length=50, description="教师姓名")
    
    # 时间段信息
    weekday: int = Field(..., ge=1, le=7, description="星期几（1-7）")
    start_time: str = Field(..., max_length=8, description="开始时间（HH:MM格式）")
    reason: Optional[str] = Field(None, max_length=100, description="操作原因")


class MemberFixedLockOperationLogResponse(BaseModel):
    """会员固定位锁定操作记录响应"""
    id: int
    tenant_id: int
    member_fixed_slot_lock_id: int
    operation_type: MemberLockOperationType
    operation_status: OperationStatus
    operation_description: str
    operator_id: Optional[int]
    operator_name: Optional[str]
    operator_type: str
    member_id: int
    member_name: str
    teacher_id: int
    teacher_name: str
    weekday: int
    start_time: str
    reason: Optional[str]
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class MemberFixedLockOperationLogQuery(BaseModel):
    """会员固定位锁定操作记录查询参数"""
    member_fixed_slot_lock_id: Optional[int] = Field(None, description="会员固定位锁定ID")
    operation_type: Optional[MemberLockOperationType] = Field(None, description="操作类型")
    operation_status: Optional[OperationStatus] = Field(None, description="操作状态")
    member_id: Optional[int] = Field(None, description="会员ID")
    teacher_id: Optional[int] = Field(None, description="教师ID")
    operator_id: Optional[int] = Field(None, description="操作人ID")
    operator_type: Optional[str] = Field(None, description="操作人类型")
    weekday: Optional[int] = Field(None, ge=1, le=7, description="星期几")
    created_from: Optional[datetime] = Field(None, description="创建时间范围-起始")
    created_to: Optional[datetime] = Field(None, description="创建时间范围-结束")
    operation_description: Optional[str] = Field(None, description="操作描述（模糊搜索）")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")
