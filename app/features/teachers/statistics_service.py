"""教师统计数据服务"""

from sqlmodel import Session, select, func
from sqlalchemy import and_, text
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from decimal import Decimal

from app.features.base.base_service import TenantAwareService
from .statistics_models import (
    TeacherStatistics, 
    TeacherStatisticsCreate, 
    TeacherStatisticsUpdate,
    TeacherStatisticsIncrement,
    TeacherStatisticsAggregation,
    TeacherEarningsStats,
    TeacherRatingStats,
    TeacherClassStats
)
from .models import Teacher
from app.api.common.exceptions import BusinessException


class TeacherStatisticsService(TenantAwareService[TeacherStatistics]):
    """教师统计数据服务"""
    
    @property
    def model_class(self):
        return TeacherStatistics
    
    def get_or_create_statistics(self, teacher_id: int) -> TeacherStatistics:
        """获取或创建教师统计记录"""
        stats = self.session.get(TeacherStatistics, teacher_id)
        if not stats:
            # 创建新的统计记录
            stats_data = TeacherStatisticsCreate(
                teacher_id=teacher_id,
                tenant_id=self.tenant_id
            )
            stats = TeacherStatistics(**stats_data.model_dump())
            stats.created_at = datetime.now()
            self.session.add(stats)
            self.session.commit()
            self.session.refresh(stats)
        return stats
    
    def get_statistics(self, teacher_id: int) -> Optional[TeacherStatistics]:
        """获取教师统计信息"""
        return self.session.get(TeacherStatistics, teacher_id)
    
    def update_statistics(self, teacher_id: int, update_data: TeacherStatisticsUpdate) -> TeacherStatistics:
        """更新教师统计信息"""
        stats = self.get_or_create_statistics(teacher_id)
        
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            if value is not None:
                setattr(stats, field, value)
        
        stats.updated_at = datetime.now()
        self.session.add(stats)
        self.session.commit()
        self.session.refresh(stats)
        
        return stats
    
    def increment_statistics(self, teacher_id: int, increment_data: TeacherStatisticsIncrement) -> TeacherStatistics:
        """增量更新教师统计信息"""
        stats = self.get_or_create_statistics(teacher_id)
        
        # 更新课程统计
        if any([increment_data.class_completed, increment_data.class_cancelled, increment_data.class_no_show]):
            stats.total_classes += 1
            
            if increment_data.class_completed:
                stats.completed_classes += 1
                stats.last_class_at = datetime.now()
            elif increment_data.class_cancelled:
                stats.cancelled_classes += 1
            elif increment_data.class_no_show:
                stats.no_show_classes += 1
        
        # 更新收入统计
        if increment_data.earnings_added > 0:
            stats.total_earnings += increment_data.earnings_added
            # 更新当月收入（简化实现，实际可能需要更复杂的逻辑）
            stats.current_month_earnings += increment_data.earnings_added
        
        # 更新评分统计
        if increment_data.rating_added is not None:
            if stats.rating_count == 0:
                stats.avg_rating = increment_data.rating_added
                stats.rating_count = 1
            else:
                # 计算新的平均评分
                total_rating = stats.avg_rating * stats.rating_count + increment_data.rating_added
                stats.rating_count += 1
                stats.avg_rating = total_rating / stats.rating_count
        
        stats.updated_at = datetime.now()
        self.session.add(stats)
        self.session.commit()
        self.session.refresh(stats)
        
        return stats
    
    def get_teacher_statistics_list(self, 
                                   teacher_ids: List[int] = None,
                                   skip: int = 0, 
                                   limit: int = 100) -> List[TeacherStatistics]:
        """获取教师统计列表"""
        statement = select(TeacherStatistics)
        
        if teacher_ids:
            statement = statement.where(TeacherStatistics.teacher_id.in_(teacher_ids))
        
        statement = statement.offset(skip).limit(limit)
        return self.session.exec(statement).all()
    
    def get_tenant_aggregation(self) -> TeacherStatisticsAggregation:
        """获取租户级别的聚合统计"""
        # 总教师数
        total_teachers = self.session.exec(
            select(func.count(Teacher.id))
        ).first() or 0
        
        # 活跃教师数（有上课记录的）
        active_teachers = self.session.exec(
            select(func.count(TeacherStatistics.teacher_id)).where(
                TeacherStatistics.total_classes > 0
            )
        ).first() or 0
        
        # 统计汇总
        stats_summary = self.session.exec(
            select(
                func.sum(TeacherStatistics.total_classes).label('total_classes'),
                func.sum(TeacherStatistics.total_earnings).label('total_earnings'),
                func.sum(TeacherStatistics.completed_classes).label('completed_classes'),
                func.sum(TeacherStatistics.cancelled_classes).label('cancelled_classes'),
                func.sum(TeacherStatistics.no_show_classes).label('no_show_classes'),
                func.avg(TeacherStatistics.avg_rating).label('avg_teacher_rating')
            )
        ).first()
        
        total_classes = stats_summary.total_classes or 0
        total_earnings = stats_summary.total_earnings or 0
        completed_classes = stats_summary.completed_classes or 0
        cancelled_classes = stats_summary.cancelled_classes or 0
        no_show_classes = stats_summary.no_show_classes or 0
        avg_teacher_rating = float(stats_summary.avg_teacher_rating or 0)
        
        # 计算比率
        avg_classes_per_teacher = total_classes / total_teachers if total_teachers > 0 else 0
        avg_earnings_per_teacher = total_earnings // total_teachers if total_teachers > 0 else 0
        completion_rate = completed_classes / total_classes if total_classes > 0 else 0
        cancellation_rate = cancelled_classes / total_classes if total_classes > 0 else 0
        no_show_rate = no_show_classes / total_classes if total_classes > 0 else 0
        
        return TeacherStatisticsAggregation(
            total_teachers=total_teachers,
            active_teachers=active_teachers,
            total_classes=total_classes,
            total_earnings=total_earnings,
            avg_classes_per_teacher=round(avg_classes_per_teacher, 2),
            avg_earnings_per_teacher=avg_earnings_per_teacher,
            completion_rate=round(completion_rate, 4),
            cancellation_rate=round(cancellation_rate, 4),
            no_show_rate=round(no_show_rate, 4),
            avg_teacher_rating=round(avg_teacher_rating, 2)
        )
    
    def get_top_earning_teachers(self, limit: int = 10) -> List[TeacherEarningsStats]:
        """获取收入排行榜"""
        statement = select(
            TeacherStatistics.teacher_id,
            Teacher.name.label('teacher_name'),
            TeacherStatistics.total_earnings,
            TeacherStatistics.current_month_earnings,
            TeacherStatistics.total_classes
        ).join(
            Teacher, TeacherStatistics.teacher_id == Teacher.id
        ).order_by(
            TeacherStatistics.total_earnings.desc()
        ).limit(limit)
        
        results = self.session.exec(statement).all()
        
        return [
            TeacherEarningsStats(
                teacher_id=result.teacher_id,
                teacher_name=result.teacher_name,
                total_earnings=result.total_earnings,
                current_month_earnings=result.current_month_earnings,
                total_classes=result.total_classes,
                avg_earnings_per_class=result.total_earnings / result.total_classes if result.total_classes > 0 else 0
            )
            for result in results
        ]
    
    def get_top_rated_teachers(self, limit: int = 10) -> List[TeacherRatingStats]:
        """获取评分排行榜"""
        statement = select(
            TeacherStatistics.teacher_id,
            Teacher.name.label('teacher_name'),
            TeacherStatistics.avg_rating,
            TeacherStatistics.rating_count
        ).join(
            Teacher, TeacherStatistics.teacher_id == Teacher.id
        ).where(
            TeacherStatistics.rating_count >= 5  # 至少5个评价
        ).order_by(
            TeacherStatistics.avg_rating.desc(),
            TeacherStatistics.rating_count.desc()
        ).limit(limit)
        
        results = self.session.exec(statement).all()
        
        return [
            TeacherRatingStats(
                teacher_id=result.teacher_id,
                teacher_name=result.teacher_name,
                avg_rating=result.avg_rating,
                rating_count=result.rating_count
            )
            for result in results
        ]
    
    def get_teacher_class_stats(self, teacher_ids: List[int] = None) -> List[TeacherClassStats]:
        """获取教师课程统计"""
        statement = select(
            TeacherStatistics.teacher_id,
            Teacher.name.label('teacher_name'),
            TeacherStatistics.total_classes,
            TeacherStatistics.completed_classes,
            TeacherStatistics.cancelled_classes,
            TeacherStatistics.no_show_classes,
            TeacherStatistics.last_class_at
        ).join(
            Teacher, TeacherStatistics.teacher_id == Teacher.id
        )
        
        if teacher_ids:
            statement = statement.where(TeacherStatistics.teacher_id.in_(teacher_ids))
        
        results = self.session.exec(statement).all()
        
        return [
            TeacherClassStats(
                teacher_id=result.teacher_id,
                teacher_name=result.teacher_name,
                total_classes=result.total_classes,
                completed_classes=result.completed_classes,
                cancelled_classes=result.cancelled_classes,
                no_show_classes=result.no_show_classes,
                completion_rate=result.completed_classes / result.total_classes if result.total_classes > 0 else 0,
                cancellation_rate=result.cancelled_classes / result.total_classes if result.total_classes > 0 else 0,
                no_show_rate=result.no_show_classes / result.total_classes if result.total_classes > 0 else 0,
                last_class_at=result.last_class_at
            )
            for result in results
        ]
    
    def reset_monthly_statistics(self) -> int:
        """重置月度统计（每月初调用）"""
        statement = text("""
            UPDATE teacher_statistics 
            SET current_month_earnings = 0, updated_at = :now 
            WHERE tenant_id = :tenant_id
        """)
        
        result = self.session.exec(
            statement, 
            {"now": datetime.now(), "tenant_id": self.tenant_id}
        )
        self.session.commit()
        return result.rowcount


def get_teacher_statistics_service(session: Session, tenant_id: int) -> TeacherStatisticsService:
    """获取教师统计服务实例"""
    return TeacherStatisticsService(session, tenant_id)
