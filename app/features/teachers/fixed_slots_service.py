from typing import List, Optional, Tuple, Dict
from sqlmodel import Session, select, text, func, and_
from datetime import datetime, timezone, time

from app.features.base.base_service import TenantAwareService
from .fixed_slots_models import (
    TeacherFixedSlot, Weekday, TimeValidationConfig,
    check_time_overlap, get_weekday_name
)
from .fixed_slots_schemas import (
    TeacherFixedSlotCreate, TeacherFixedSlotUpdate, TeacherFixedSlotQuery,
    TeacherFixedSlotBatchCreate, TeacherFixedSlotBatchUpdate, TeacherFixedSlotBatchDelete,
    AvailableTimesQuery, TimeSlotConflictCheck, TeacherFixedSlotStats
)
from .models import Teacher
from .fixed_slots_exceptions import (
    TeacherFixedSlotNotFoundError, TeacherFixedSlotBusinessException
)
from app.features.courses.operations.operation_logger import get_operation_logger


class TeacherFixedSlotService(TenantAwareService[TeacherFixedSlot]):
    """教师固定时间占位表服务"""

    @property
    def model_class(self):
        return TeacherFixedSlot

    def __init__(self, session: Session, tenant_id: int):
        super().__init__(session, tenant_id)
        self.operation_logger = get_operation_logger(session, tenant_id)

    # ==================== 验证方法 ====================

    def validate_time_range_format(self, start_time_from: str, start_time_to: str) -> Tuple[time, time]:
        """验证时间范围格式并转换为time对象"""
        try:
            hour_from, minute_from = map(int, start_time_from.split(':'))
            start_time_from_obj = time(hour_from, minute_from)

            hour_to, minute_to = map(int, start_time_to.split(':'))
            start_time_to_obj = time(hour_to, minute_to)

            return start_time_from_obj, start_time_to_obj
        except ValueError:
            raise TeacherFixedSlotBusinessException.invalid_time_range(start_time_from, start_time_to)

    def validate_batch_update_fields(self, is_available: Optional[bool], is_visible_to_members: Optional[bool]) -> None:
        """验证批量更新时至少有一个字段"""
        if is_available is None and is_visible_to_members is None:
            raise TeacherFixedSlotBusinessException.general_error("至少需要指定一个要更新的字段")

    def validate_clear_operation(self, confirm: bool) -> None:
        """验证清空操作确认"""
        if not confirm:
            raise TeacherFixedSlotBusinessException.general_error("请确认清空操作")

    def validate_batch_create_data(self, slots_data: List[dict]) -> List[TeacherFixedSlotCreate]:
        """验证批量创建数据并转换为Schema对象"""
        valid_slots = []
        for slot_data in slots_data:
            try:
                # 复制数据避免修改原始数据
                slot_copy = slot_data.copy()
                
                # 处理时间字段
                if isinstance(slot_copy.get('start_time'), str):
                    hour, minute = map(int, slot_copy['start_time'].split(':'))
                    slot_copy['start_time'] = time(hour, minute)

                # 创建Schema对象（不包含teacher_id和created_by，这些会在路由层设置）
                slot_create = TeacherFixedSlotCreate(
                    teacher_id=0,  # 临时值，会在路由层设置
                    created_by=0,  # 临时值，会在路由层设置
                    **{k: v for k, v in slot_copy.items() if k not in ['teacher_id', 'created_by']}
                )
                valid_slots.append(slot_create)
            except Exception:
                # 跳过格式错误的数据
                continue

        if not valid_slots:
            raise TeacherFixedSlotBusinessException.general_error("没有有效的时间段数据")

        return valid_slots

    def validate_batch_update_data(self, updates: List[dict]) -> List[dict]:
        """验证批量更新数据并处理时间字段"""
        processed_updates = []
        for update_item in updates:
            processed_item = update_item.copy()

            # 处理时间字段
            if 'start_time' in processed_item and isinstance(processed_item['start_time'], str):
                try:
                    hour, minute = map(int, processed_item['start_time'].split(':'))
                    processed_item['start_time'] = time(hour, minute)
                except ValueError:
                    # 跳过格式错误的时间
                    continue

            processed_updates.append(processed_item)

        if not processed_updates:
            raise TeacherFixedSlotBusinessException.general_error("没有有效的更新数据")

        return processed_updates

    def parse_time_parameters(self, start_time_from: Optional[str], start_time_to: Optional[str]) -> Tuple[Optional[time], Optional[time]]:
        """解析时间参数"""
        start_time_from_obj = None
        start_time_to_obj = None

        if start_time_from:
            try:
                hour, minute = map(int, start_time_from.split(':'))
                start_time_from_obj = time(hour, minute)
            except ValueError:
                pass

        if start_time_to:
            try:
                hour, minute = map(int, start_time_to.split(':'))
                start_time_to_obj = time(hour, minute)
            except ValueError:
                pass

        return start_time_from_obj, start_time_to_obj

    def parse_weekdays_parameter(self, weekdays: Optional[str]) -> Optional[List[Weekday]]:
        """解析星期参数"""
        if not weekdays:
            return None

        try:
            return [Weekday(int(w.strip())) for w in weekdays.split(',')]
        except ValueError:
            return None

    # ==================== CRUD操作 ====================

    def create_slot(self, slot_data: TeacherFixedSlotCreate, created_by: int, operator_name: str) -> TeacherFixedSlot:
        """创建固定时间段"""
        # 验证教师是否存在
        teacher = self.session.get(Teacher, slot_data.teacher_id)
        if not teacher:
            raise TeacherFixedSlotBusinessException.teacher_not_found(slot_data.teacher_id)

        # 检查时间段冲突
        if self._check_time_conflict(
            slot_data.teacher_id, 
            slot_data.weekday, 
            slot_data.start_time,
            slot_data.duration_minutes or TimeValidationConfig.DEFAULT_DURATION_MINUTES
        ):
            raise TeacherFixedSlotBusinessException.time_slot_conflict(
                slot_data.teacher_id, slot_data.weekday, slot_data.start_time
            )

        # 创建时间段
        slot_dict = slot_data.model_dump(exclude={'created_by'})
        slot_dict['tenant_id'] = self.tenant_id
        slot_dict['created_by'] = created_by

        slot = TeacherFixedSlot(**slot_dict)
        now = datetime.now()
        slot.created_at = now
        slot.updated_at = now

        self.session.add(slot)
        self.session.commit()
        self.session.refresh(slot)

        # 记录操作日志
        self.operation_logger.log_teacher_slot_create(
            slot=slot,
            operator_id=created_by,
            operator_name=operator_name,
            operator_type="admin"
        )

        return slot

    def get_slot(self, slot_id: int) -> Optional[TeacherFixedSlot]:
        """根据ID获取固定时间段"""
        return self.session.get(TeacherFixedSlot, slot_id)

    def get_slots(self, query_params: TeacherFixedSlotQuery) -> Tuple[List[TeacherFixedSlot], int]:
        """获取固定时间段列表"""
        statement = select(TeacherFixedSlot)
        
        # 构建查询条件
        conditions = []
        
        if query_params.teacher_id is not None:
            conditions.append(TeacherFixedSlot.teacher_id == query_params.teacher_id)
        
        if query_params.weekday is not None:
            conditions.append(TeacherFixedSlot.weekday == query_params.weekday)
        
        if query_params.is_available is not None:
            conditions.append(TeacherFixedSlot.is_available == query_params.is_available)
        
        if query_params.is_visible_to_members is not None:
            conditions.append(TeacherFixedSlot.is_visible_to_members == query_params.is_visible_to_members)
        
        if query_params.start_time_from is not None:
            conditions.append(TeacherFixedSlot.start_time >= query_params.start_time_from)
        
        if query_params.start_time_to is not None:
            conditions.append(TeacherFixedSlot.start_time <= query_params.start_time_to)

        if conditions:
            statement = statement.where(and_(*conditions))

        # 获取总数
        count_statement = select(func.count(TeacherFixedSlot.id)).where(statement.whereclause)
        total = self.session.exec(count_statement).one()

        # 排序
        sort_fields = query_params.sort_by.split(',')
        for field in sort_fields:
            field = field.strip()
            if hasattr(TeacherFixedSlot, field):
                attr = getattr(TeacherFixedSlot, field)
                if query_params.sort_order == 'desc':
                    statement = statement.order_by(attr.desc())
                else:
                    statement = statement.order_by(attr.asc())

        # 分页
        offset = (query_params.page - 1) * query_params.size
        statement = statement.offset(offset).limit(query_params.size)

        slots = self.session.exec(statement).all()
        return slots, total

    def update_slot(self, slot_id: int, slot_data: TeacherFixedSlotUpdate, operator_id: Optional[int] = None, operator_name: Optional[str] = None, reason: Optional[str] = None) -> TeacherFixedSlot:
        """更新固定时间段"""
        slot = self.session.get(TeacherFixedSlot, slot_id)
        if not slot:
            raise TeacherFixedSlotNotFoundError(slot_id)

        update_data = slot_data.model_dump(exclude_unset=True)

        # 记录可见性变更（在更新前记录原值）
        old_visible = slot.is_visible_to_members
        will_log_visibility = 'is_visible_to_members' in update_data and operator_id

        # 如果更新时间相关字段，检查冲突
        if 'weekday' in update_data or 'start_time' in update_data:
            new_weekday = update_data.get('weekday', slot.weekday)
            new_start_time = update_data.get('start_time', slot.start_time)
            new_duration = update_data.get('duration_minutes', slot.duration_minutes or TimeValidationConfig.DEFAULT_DURATION_MINUTES)
            
            if self._check_time_conflict(slot.teacher_id, new_weekday, new_start_time, new_duration, exclude_id=slot_id):
                raise TeacherFixedSlotBusinessException.time_slot_conflict(
                    slot.teacher_id, new_weekday, new_start_time
                )

        # 更新字段
        for field, value in update_data.items():
            setattr(slot, field, value)

        slot.updated_at = datetime.now()

        self.session.add(slot)
        self.session.commit()
        self.session.refresh(slot)

        # 记录可见性变更日志
        if will_log_visibility and operator_name:
            new_visible = slot.is_visible_to_members
            self.operation_logger.log_teacher_slot_visibility_change(
                slot=slot,
                old_visible=old_visible,
                new_visible=new_visible,
                operator_id=operator_id,
                operator_name=operator_name,
                operator_type="admin",
                reason=reason
            )

        return slot

    def delete_slot(self, slot_id: int, operator_id: Optional[int] = None, operator_name: Optional[str] = None, reason: Optional[str] = None) -> None:
        """删除固定时间段"""
        slot = self.session.get(TeacherFixedSlot, slot_id)
        if not slot:
            raise TeacherFixedSlotNotFoundError(slot_id)

        # 记录操作日志（在删除前记录）
        if operator_id and operator_name:
            self.operation_logger.log_teacher_slot_delete(
                slot=slot,
                operator_id=operator_id,
                operator_name=operator_name,
                operator_type="admin",
                reason=reason
            )

        self.session.delete(slot)
        self.session.commit()

    # ==================== 时间段冲突检测 ====================

    def _check_time_conflict(self, teacher_id: int, weekday: Weekday, start_time: time, 
                           duration_minutes: int, exclude_id: Optional[int] = None) -> bool:
        """检查时间段冲突"""
        statement = select(TeacherFixedSlot).where(
            and_(
                TeacherFixedSlot.teacher_id == teacher_id,
                TeacherFixedSlot.weekday == weekday
            )
        )
        
        if exclude_id:
            statement = statement.where(TeacherFixedSlot.id != exclude_id)

        existing_slots = self.session.exec(statement).all()

        for existing_slot in existing_slots:
            existing_duration = existing_slot.duration_minutes or TimeValidationConfig.DEFAULT_DURATION_MINUTES
            if check_time_overlap(start_time, duration_minutes, existing_slot.start_time, existing_duration):
                return True

        return False

    def check_time_conflict(self, conflict_check: TimeSlotConflictCheck) -> bool:
        """检查时间段冲突（公开接口）"""
        return self._check_time_conflict(
            conflict_check.teacher_id,
            conflict_check.weekday,
            conflict_check.start_time,
            conflict_check.duration_minutes,
            conflict_check.exclude_id
        )

    # ==================== 可用时间查询 ====================

    def get_available_times(self, query: AvailableTimesQuery) -> List[TeacherFixedSlot]:
        """获取教师可用时间段"""
        # 构建查询条件
        stmt = select(TeacherFixedSlot).where(TeacherFixedSlot.tenant_id == self.tenant_id)

        # 应用筛选条件
        if query.teacher_id is not None:
            stmt = stmt.where(TeacherFixedSlot.teacher_id == query.teacher_id)
        
        if query.weekdays is not None:
            stmt = stmt.where(TeacherFixedSlot.weekday.in_(query.weekdays))
        
        if query.only_available:
            stmt = stmt.where(TeacherFixedSlot.is_available == True)
        
        if query.only_visible:
            stmt = stmt.where(TeacherFixedSlot.is_visible_to_members == True)
        
        if query.start_time_from is not None:
            stmt = stmt.where(TeacherFixedSlot.start_time >= query.start_time_from)
        
        if query.start_time_to is not None:
            stmt = stmt.where(TeacherFixedSlot.start_time <= query.start_time_to)

        # 排序
        stmt = stmt.order_by(TeacherFixedSlot.weekday, TeacherFixedSlot.start_time)

        slots = self.session.exec(stmt).all()
        return slots

    # ==================== 统计信息 ====================

    def get_slot_statistics(self, teacher_id: Optional[int] = None) -> TeacherFixedSlotStats:
        """获取时间段统计信息"""
        statement = select(TeacherFixedSlot)
        
        if teacher_id:
            statement = statement.where(TeacherFixedSlot.teacher_id == teacher_id)

        slots = self.session.exec(statement).all()

        total_slots = len(slots)
        available_slots = sum(1 for slot in slots if slot.is_available)
        visible_slots = sum(1 for slot in slots if slot.is_visible_to_members)

        # 按星期分布统计
        weekday_distribution = {}
        for weekday in Weekday:
            weekday_count = sum(1 for slot in slots if slot.weekday == weekday)
            weekday_distribution[get_weekday_name(weekday)] = weekday_count

        return TeacherFixedSlotStats(
            teacher_id=teacher_id or 0,
            total_slots=total_slots,
            available_slots=available_slots,
            visible_slots=visible_slots,
            weekday_distribution=weekday_distribution
        )

    # ==================== 批量操作 ====================

    def batch_create_slots(self, batch_data: TeacherFixedSlotBatchCreate, operator_name: str) -> List[TeacherFixedSlot]:
        """批量创建固定时间段"""
        # 验证教师是否存在
        teacher = self.session.get(Teacher, batch_data.teacher_id)
        if not teacher:
            raise TeacherFixedSlotBusinessException.teacher_not_found(batch_data.teacher_id)

        created_slots = []
        failed_slots = []

        for slot_data in batch_data.slots:
            try:
                # 检查时间段冲突（包括与已创建的时间段冲突）
                duration = slot_data.duration_minutes or TimeValidationConfig.DEFAULT_DURATION_MINUTES

                # 检查与数据库中现有时间段的冲突
                if self._check_time_conflict(slot_data.teacher_id, slot_data.weekday, slot_data.start_time, duration):
                    failed_slots.append(f"{get_weekday_name(slot_data.weekday)} {slot_data.start_time}")
                    continue

                # 检查与本次批量创建中其他时间段的冲突
                conflict_with_batch = False
                for created_slot in created_slots:
                    created_duration = created_slot.duration_minutes or TimeValidationConfig.DEFAULT_DURATION_MINUTES
                    if (created_slot.weekday == slot_data.weekday and
                        check_time_overlap(slot_data.start_time, duration, created_slot.start_time, created_duration)):
                        conflict_with_batch = True
                        break

                if conflict_with_batch:
                    failed_slots.append(f"{get_weekday_name(slot_data.weekday)} {slot_data.start_time}")
                    continue

                # 创建时间段
                slot_dict = slot_data.model_dump(exclude={'created_by'})
                slot_dict['tenant_id'] = self.tenant_id
                slot_dict['created_by'] = batch_data.created_by

                slot = TeacherFixedSlot(**slot_dict)
                now = datetime.now()
                slot.created_at = now
                slot.updated_at = now

                self.session.add(slot)
                created_slots.append(slot)

            except Exception as e:
                failed_slots.append(f"{get_weekday_name(slot_data.weekday)} {slot_data.start_time}: {str(e)}")

        # 提交事务
        if created_slots:
            self.session.commit()
            for slot in created_slots:
                self.session.refresh(slot)

            # 记录批量创建操作日志
            self.operation_logger.log_teacher_slots_batch_create(
                slots=created_slots,
                operator_id=batch_data.created_by,
                operator_name=operator_name,
                operator_type="admin"
            )

        # 如果有失败的操作，记录但不抛出异常
        if failed_slots:
            # 可以记录日志或返回失败信息
            pass

        return created_slots

    def batch_update_slots(self, batch_data: TeacherFixedSlotBatchUpdate) -> List[TeacherFixedSlot]:
        """批量更新固定时间段"""
        updated_slots = []
        failed_updates = []

        for update_item in batch_data.updates:
            try:
                slot_id = update_item['id']
                slot = self.session.get(TeacherFixedSlot, slot_id)

                if not slot:
                    failed_updates.append(f"ID {slot_id}: 时间段不存在")
                    continue

                # 提取更新数据（排除id字段）
                update_data = {k: v for k, v in update_item.items() if k != 'id'}

                # 如果更新时间相关字段，检查冲突
                if 'weekday' in update_data or 'start_time' in update_data:
                    new_weekday = update_data.get('weekday', slot.weekday)
                    new_start_time = update_data.get('start_time', slot.start_time)
                    new_duration = update_data.get('duration_minutes', slot.duration_minutes or TimeValidationConfig.DEFAULT_DURATION_MINUTES)

                    if self._check_time_conflict(slot.teacher_id, new_weekday, new_start_time, new_duration, exclude_id=slot_id):
                        failed_updates.append(f"ID {slot_id}: 时间段冲突")
                        continue

                # 更新字段
                for field, value in update_data.items():
                    setattr(slot, field, value)

                slot.updated_at = datetime.now()
                self.session.add(slot)
                updated_slots.append(slot)

            except Exception as e:
                failed_updates.append(f"ID {update_item.get('id', 'unknown')}: {str(e)}")

        # 提交事务
        if updated_slots:
            self.session.commit()
            for slot in updated_slots:
                self.session.refresh(slot)

        return updated_slots

    def batch_delete_slots(self, batch_data: TeacherFixedSlotBatchDelete) -> int:
        """批量删除固定时间段"""
        deleted_count = 0
        failed_deletes = []

        for slot_id in batch_data.ids:
            try:
                slot = self.session.get(TeacherFixedSlot, slot_id)

                if not slot:
                    failed_deletes.append(f"ID {slot_id}: 时间段不存在")
                    continue

                self.session.delete(slot)
                deleted_count += 1

            except Exception as e:
                failed_deletes.append(f"ID {slot_id}: {str(e)}")

        # 提交事务
        if deleted_count > 0:
            self.session.commit()

        return deleted_count

    # ==================== 便捷方法 ====================

    def get_teacher_slots_with_occupation_status(self, teacher_id: int) -> List[TeacherFixedSlot]:
        """获取教师时间段及占用状态（优化版，无需JOIN）"""
        statement = select(TeacherFixedSlot).where(
            and_(
                TeacherFixedSlot.teacher_id == teacher_id,
                TeacherFixedSlot.tenant_id == self.tenant_id
            )
        ).order_by(
            TeacherFixedSlot.weekday.asc(),
            TeacherFixedSlot.start_time.asc()
        )

        return self.session.exec(statement).all()

    def get_available_teachers_by_time_range(
        self,
        weekdays: List[int],
        start_time_from: time,
        start_time_to: time
    ) -> List[int]:
        """按时间范围获取有空闲时段的教师ID列表（优化版）"""

        statement = select(TeacherFixedSlot.teacher_id).distinct().where(
            and_(
                TeacherFixedSlot.tenant_id == self.tenant_id,
                TeacherFixedSlot.weekday.in_(weekdays),
                TeacherFixedSlot.start_time >= start_time_from,
                TeacherFixedSlot.start_time <= start_time_to,
                TeacherFixedSlot.is_available == True,
                TeacherFixedSlot.is_visible_to_members == True
            )
        )

        result = self.session.exec(statement).all()
        return list(result)

    def get_member_fixed_schedule(self, member_id: int) -> List[TeacherFixedSlot]:
        """获取会员的固定课程安排（优化版，通过JOIN获取会员锁定的固定时间段）"""
        from app.features.members.fixed_lock_models import MemberFixedSlotLock
        
        statement = select(TeacherFixedSlot).join(
            MemberFixedSlotLock,
            and_(
                MemberFixedSlotLock.tenant_id == self.tenant_id,
                MemberFixedSlotLock.teacher_fixed_slot_id == TeacherFixedSlot.id,
                MemberFixedSlotLock.member_id == member_id
            )
        ).where(
            TeacherFixedSlot.tenant_id == self.tenant_id
        ).order_by(
            TeacherFixedSlot.weekday.asc(),
            TeacherFixedSlot.start_time.asc()
        )

        return self.session.exec(statement).all()

    def get_teacher_weekly_schedule(self, teacher_id: int) -> Dict[str, List[TeacherFixedSlot]]:
        """获取教师一周的时间安排"""
        statement = select(TeacherFixedSlot).where(
            and_(
                TeacherFixedSlot.teacher_id == teacher_id,
                TeacherFixedSlot.is_available == True
            )
        ).order_by(TeacherFixedSlot.start_time.asc())

        slots = self.session.exec(statement).all()

        # 按星期分组
        weekly_schedule = {}
        for weekday in Weekday:
            weekday_name = get_weekday_name(weekday)
            weekday_slots = [slot for slot in slots if slot.weekday == weekday]
            weekly_schedule[weekday_name] = weekday_slots

        return weekly_schedule

    def clear_teacher_slots(self, teacher_id: int) -> int:
        """清空教师的所有固定时间段"""
        statement = select(TeacherFixedSlot).where(TeacherFixedSlot.teacher_id == teacher_id)
        slots = self.session.exec(statement).all()

        deleted_count = len(slots)
        for slot in slots:
            self.session.delete(slot)

        if deleted_count > 0:
            self.session.commit()

        return deleted_count

    def copy_teacher_slots(self, source_teacher_id: int, target_teacher_id: int, created_by: int) -> List[TeacherFixedSlot]:
        """复制教师的固定时间段到另一个教师"""
        # 验证目标教师是否存在
        target_teacher = self.session.get(Teacher, target_teacher_id)
        if not target_teacher:
            raise TeacherFixedSlotBusinessException.teacher_not_found(target_teacher_id)

        # 获取源教师的时间段
        statement = select(TeacherFixedSlot).where(TeacherFixedSlot.teacher_id == source_teacher_id)
        source_slots = self.session.exec(statement).all()

        if not source_slots:
            return []

        # 复制时间段
        copied_slots = []
        for source_slot in source_slots:
            # 检查目标教师是否已有相同时间段
            duration = source_slot.duration_minutes or TimeValidationConfig.DEFAULT_DURATION_MINUTES
            if not self._check_time_conflict(target_teacher_id, source_slot.weekday, source_slot.start_time, duration):
                slot_dict = {
                    'tenant_id': self.tenant_id,
                    'teacher_id': target_teacher_id,
                    'weekday': source_slot.weekday,
                    'start_time': source_slot.start_time,
                    'duration_minutes': source_slot.duration_minutes,
                    'is_available': source_slot.is_available,
                    'is_visible_to_members': source_slot.is_visible_to_members,
                    'created_by': created_by
                }

                slot = TeacherFixedSlot(**slot_dict)
                now = datetime.now()
                slot.created_at = now
                slot.updated_at = now

                self.session.add(slot)
                copied_slots.append(slot)

        if copied_slots:
            self.session.commit()
            for slot in copied_slots:
                self.session.refresh(slot)

        return copied_slots
