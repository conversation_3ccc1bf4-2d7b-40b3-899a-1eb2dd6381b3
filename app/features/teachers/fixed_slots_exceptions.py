from datetime import time
from enum import Enum

from app.api.common.exceptions import BusinessException, NotFoundError
from .fixed_slots_models import Weekday, get_weekday_name


class TeacherFixedSlotErrorCode(str, Enum):
    """教师固定时间段错误码"""
    TIME_SLOT_CONFLICT = "TIME_SLOT_CONFLICT"
    TEACHER_NOT_FOUND = "TEACHER_NOT_FOUND"
    INVALID_TIME_RANGE = "INVALID_TIME_RANGE"
    BATCH_OPERATION_FAILED = "BATCH_OPERATION_FAILED"
    SLOT_NOT_AVAILABLE = "SLOT_NOT_AVAILABLE"
    SLOT_NOT_VISIBLE = "SLOT_NOT_VISIBLE"


class TeacherFixedSlotNotFoundError(NotFoundError):
    """教师固定时间段未找到异常"""
    def __init__(self, slot_id: int):
        super().__init__(f"教师固定时间段不存在: ID {slot_id}")


class TeacherFixedSlotBusinessException(BusinessException):
    """教师固定时间段业务异常"""
    
    def __init__(self, message: str, code: str = "BUSINESS_ERROR"):
        super().__init__(message, code)
    
    @classmethod
    def time_slot_conflict(cls, teacher_id: int, weekday: Weekday, start_time: time):
        """时间段冲突异常"""
        weekday_name = get_weekday_name(weekday)
        message = f"教师 {teacher_id} 在 {weekday_name} {start_time} 的时间段已存在"
        return cls(message, TeacherFixedSlotErrorCode.TIME_SLOT_CONFLICT)
    
    @classmethod
    def teacher_not_found(cls, teacher_id: int):
        """教师不存在异常"""
        message = f"教师不存在: ID {teacher_id}"
        return cls(message, TeacherFixedSlotErrorCode.TEACHER_NOT_FOUND)
    
    @classmethod
    def invalid_time_range(cls, start_time: time, end_time: time):
        """无效时间范围异常"""
        message = f"无效的时间范围: {start_time} - {end_time}"
        return cls(message, TeacherFixedSlotErrorCode.INVALID_TIME_RANGE)
    
    @classmethod
    def batch_operation_failed(cls, operation: str, failed_count: int, total_count: int):
        """批量操作失败异常"""
        message = f"批量{operation}失败: {failed_count}/{total_count} 个操作失败"
        return cls(message, TeacherFixedSlotErrorCode.BATCH_OPERATION_FAILED)
    
    @classmethod
    def slot_not_available(cls, slot_id: int):
        """时间段不可用异常"""
        message = f"时间段不可用: ID {slot_id}"
        return cls(message, TeacherFixedSlotErrorCode.SLOT_NOT_AVAILABLE)
    
    @classmethod
    def slot_not_visible(cls, slot_id: int):
        """时间段对会员不可见异常"""
        message = f"时间段对会员不可见: ID {slot_id}"
        return cls(message, TeacherFixedSlotErrorCode.SLOT_NOT_VISIBLE)
    
    @classmethod
    def general_error(cls, message: str):
        """通用业务错误"""
        return cls(message, "BUSINESS_ERROR")


# 便捷的异常工厂函数
def slot_not_found(slot_id: int) -> TeacherFixedSlotNotFoundError:
    """创建时间段未找到异常"""
    return TeacherFixedSlotNotFoundError(slot_id)


def time_conflict(teacher_id: int, weekday: Weekday, start_time: time) -> TeacherFixedSlotBusinessException:
    """创建时间段冲突异常"""
    return TeacherFixedSlotBusinessException.time_slot_conflict(teacher_id, weekday, start_time)


def teacher_not_found(teacher_id: int) -> TeacherFixedSlotBusinessException:
    """创建教师不存在异常"""
    return TeacherFixedSlotBusinessException.teacher_not_found(teacher_id)


def invalid_time_range(start_time: time, end_time: time) -> TeacherFixedSlotBusinessException:
    """创建无效时间范围异常"""
    return TeacherFixedSlotBusinessException.invalid_time_range(start_time, end_time)


def batch_operation_failed(operation: str, failed_count: int, total_count: int) -> TeacherFixedSlotBusinessException:
    """创建批量操作失败异常"""
    return TeacherFixedSlotBusinessException.batch_operation_failed(operation, failed_count, total_count)


def slot_not_available(slot_id: int) -> TeacherFixedSlotBusinessException:
    """创建时间段不可用异常"""
    return TeacherFixedSlotBusinessException.slot_not_available(slot_id)


def slot_not_visible(slot_id: int) -> TeacherFixedSlotBusinessException:
    """创建时间段不可见异常"""
    return TeacherFixedSlotBusinessException.slot_not_visible(slot_id)
