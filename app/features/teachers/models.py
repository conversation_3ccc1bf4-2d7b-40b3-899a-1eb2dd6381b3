from sqlmodel import SQLModel, Field, UniqueConstraint, Index
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone, date
from sqlalchemy import String, Column, JSON
from enum import Enum
from app.features.users.models import Gender


class TeacherCategory(str, Enum):
    """教师分类枚举"""
    EUROPEAN = "european"      # 欧美教师
    SOUTH_AFRICAN = "south_african"  # 南非教师
    FILIPINO = "filipino"      # 菲律宾教师
    CHINESE = "chinese"        # 中教
    OTHER = "other"           # 其他


class TeacherRegion(str, Enum):
    """教师区域枚举"""
    EUROPE = "europe"         # 欧洲
    NORTH_AMERICA = "north_america"  # 北美
    SOUTH_AFRICA = "south_africa"    # 南非
    PHILIPPINES = "philippines"      # 菲律宾
    CHINA = "china"          # 中国
    OTHER = "other"          # 其他


class TeacherStatus(str, Enum):
    """教师状态枚举"""
    ACTIVE = "active"        # 激活
    INACTIVE = "inactive"    # 停用
    PENDING = "pending"      # 待审核
    SUSPENDED = "suspended"  # 暂停


class TeacherBase(SQLModel):
    """教师基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    # 基础信息
    name: str = Field(max_length=50, description="教师姓名")
    display_code: Optional[str] = Field(default=None, max_length=20, description="教师显示编号（给会员看的标识）")
    gender: Optional[Gender] = Field(default=None, description="性别")
    avatar: Optional[str] = Field(default=None, max_length=500, description="头像URL")
    phone: Optional[str] = Field(default=None, max_length=20, description="手机号")
    email: Optional[str] = Field(default=None, max_length=100, description="邮箱")
    
    # 教师属性
    price_per_class: int = Field(default=0, description="单节课价格（整数，单位：元）")
    teacher_category: TeacherCategory = Field(description="教师分类")
    region: TeacherRegion = Field(description="教师区域")
    
    # 微信绑定
    wechat_bound: bool = Field(default=False, description="是否绑定微信")
    wechat_openid: Optional[str] = Field(default=None, max_length=100, description="微信OpenID")
    wechat_unionid: Optional[str] = Field(default=None, max_length=100, description="微信UnionID")
    
    # 显示设置
    show_to_members: bool = Field(default=True, description="是否对会员端展示")
    
    # 状态
    status: TeacherStatus = Field(default=TeacherStatus.PENDING, description="教师状态")
    
    # 教学信息
    introduction: Optional[str] = Field(default=None, description="教师介绍")
    teaching_experience: Optional[int] = Field(default=None, description="教学经验(年)")
    specialties: List[str] = Field(default_factory=list, sa_column=Column(JSON), description="专业特长")
    certifications: List[str] = Field(default_factory=list, sa_column=Column(JSON), description="资质证书")
    
    # 排课优先级
    priority_level: int = Field(default=0, description="排课优先级(数字越大优先级越高)")
    
    # 备注
    notes: Optional[str] = Field(default=None, description="备注")


class Teacher(TeacherBase, table=True):
    """教师数据库模型"""
    __tablename__ = "teachers"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者")
    
    __table_args__ = (
        # 租户内邮箱唯一
        UniqueConstraint('tenant_id', 'email', name='uq_tenant_teacher_email'),
        # 租户内手机号唯一
        UniqueConstraint('tenant_id', 'phone', name='uq_tenant_teacher_phone'),
        # 租户内显示编号唯一
        UniqueConstraint('tenant_id', 'display_code', name='uq_tenant_teacher_display_code'),
        # 微信OpenID全局唯一
        UniqueConstraint('wechat_openid', name='uq_teacher_wechat_openid'),
        # 基础索引
        Index('idx_tenant_teachers', 'tenant_id', 'id'),
        Index('idx_teacher_status', 'tenant_id', 'status'),
        Index('idx_teacher_category', 'tenant_id', 'teacher_category'),
        Index('idx_teacher_region', 'tenant_id', 'region'),
        Index('idx_teacher_show_members', 'tenant_id', 'show_to_members'),
        Index('idx_teacher_priority', 'tenant_id', 'priority_level'),
        Index('idx_teacher_price', 'tenant_id', 'price_per_class'),
        Index('idx_teacher_created_by', 'tenant_id', 'created_by'),
        Index('idx_teacher_display_code', 'tenant_id', 'display_code'),

        # 复合索引 - 优化多维度筛选查询
        Index('idx_teacher_active_show', 'tenant_id', 'status', 'show_to_members'),
        Index('idx_teacher_category_region', 'tenant_id', 'teacher_category', 'region'),
        Index('idx_teacher_price_priority', 'tenant_id', 'price_per_class', 'priority_level'),
        Index('idx_teacher_status_category', 'tenant_id', 'status', 'teacher_category'),
        Index('idx_teacher_region_price', 'tenant_id', 'region', 'price_per_class'),

        # 排序优化索引
        Index('idx_teacher_priority_desc', 'tenant_id', 'priority_level', 'id'),
        Index('idx_teacher_created_desc', 'tenant_id', 'created_at', 'id'),
    )


class TeacherTagBase(SQLModel):
    """教师标签关联基础模型"""
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    teacher_id: int = Field(foreign_key="teachers.id", description="教师ID")
    tag_id: int = Field(foreign_key="tags.id", description="标签ID")


class TeacherTag(TeacherTagBase, table=True):
    """教师标签关联表"""
    __tablename__ = "teacher_tags"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    created_by: Optional[int] = Field(default=None, foreign_key="users.id", description="创建者")
    
    __table_args__ = (
        # 教师-标签组合唯一
        UniqueConstraint('tenant_id', 'teacher_id', 'tag_id', name='uq_tenant_teacher_tag'),
        # 索引
        Index('idx_teacher_tags_teacher', 'teacher_id'),
        Index('idx_teacher_tags_tag', 'tag_id'),
    )
