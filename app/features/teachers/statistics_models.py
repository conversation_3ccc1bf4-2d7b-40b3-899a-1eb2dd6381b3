"""教师统计数据模型"""

from sqlmodel import SQLModel, Field, Index
from typing import Optional
from datetime import datetime
from decimal import Decimal


class TeacherStatisticsBase(SQLModel):
    """教师统计基础模型"""
    # 课程统计
    total_classes: int = Field(default=0, description="总上课数")
    completed_classes: int = Field(default=0, description="完成上课数")
    cancelled_classes: int = Field(default=0, description="取消上课数")
    no_show_classes: int = Field(default=0, description="缺席上课数")
    
    # 收入统计
    total_earnings: int = Field(default=0, description="总收入（元）")
    current_month_earnings: int = Field(default=0, description="当月收入（元）")
    
    # 评价统计
    avg_rating: Decimal = Field(default=Decimal("0.0"), max_digits=3, decimal_places=1, description="平均评分")
    rating_count: int = Field(default=0, description="评价次数")
    
    # 时间信息
    last_class_at: Optional[datetime] = Field(default=None, description="最后上课时间")


class TeacherStatistics(TeacherStatisticsBase, table=True):
    """教师统计数据库模型"""
    __tablename__ = "teacher_statistics"
    
    # 主键使用teacher_id
    teacher_id: int = Field(primary_key=True, foreign_key="teachers.id", description="教师ID")
    tenant_id: int = Field(foreign_key="tenants.id", description="租户ID")
    
    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now(), description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    
    __table_args__ = (
        # 索引
        Index('idx_teacher_stats_tenant', 'tenant_id'),
        Index('idx_teacher_stats_total_earnings', 'tenant_id', 'total_earnings'),
        Index('idx_teacher_stats_rating', 'tenant_id', 'avg_rating'),
        Index('idx_teacher_stats_last_class', 'tenant_id', 'last_class_at'),
        Index('idx_teacher_stats_updated', 'tenant_id', 'updated_at'),
    )


# API响应模型
class TeacherStatisticsRead(TeacherStatisticsBase):
    """教师统计信息读取模型"""
    teacher_id: int
    tenant_id: int
    created_at: datetime
    updated_at: Optional[datetime]


# API请求模型
class TeacherStatisticsUpdate(SQLModel):
    """教师统计信息更新模型"""
    total_classes: Optional[int] = None
    completed_classes: Optional[int] = None
    cancelled_classes: Optional[int] = None
    no_show_classes: Optional[int] = None
    total_earnings: Optional[int] = None
    current_month_earnings: Optional[int] = None
    avg_rating: Optional[Decimal] = None
    rating_count: Optional[int] = None
    last_class_at: Optional[datetime] = None


class TeacherStatisticsCreate(TeacherStatisticsBase):
    """创建教师统计信息模型"""
    teacher_id: int
    tenant_id: int


# 统计增量更新模型
class TeacherStatisticsIncrement(SQLModel):
    """教师统计增量更新模型"""
    class_completed: bool = False
    class_cancelled: bool = False
    class_no_show: bool = False
    earnings_added: int = 0
    rating_added: Optional[Decimal] = None  # 新增评分


# 聚合统计模型
class TeacherStatisticsAggregation(SQLModel):
    """教师统计聚合信息"""
    total_teachers: int = Field(description="总教师数")
    active_teachers: int = Field(description="活跃教师数（有上课记录）")
    total_classes: int = Field(description="总课程数")
    total_earnings: int = Field(description="总收入（元）")
    avg_classes_per_teacher: float = Field(description="平均每教师上课数")
    avg_earnings_per_teacher: int = Field(description="平均每教师收入（元）")
    completion_rate: float = Field(description="课程完成率")
    cancellation_rate: float = Field(description="课程取消率")
    no_show_rate: float = Field(description="缺席率")
    avg_teacher_rating: float = Field(description="教师平均评分")


# 教师收入统计模型
class TeacherEarningsStats(SQLModel):
    """教师收入统计模型"""
    teacher_id: int
    teacher_name: str
    total_earnings: int = Field(description="总收入（元）")
    current_month_earnings: int = Field(description="当月收入（元）")
    total_classes: int = Field(description="总课程数")
    avg_earnings_per_class: float = Field(description="平均每课收入（元）")


# 教师评分统计模型
class TeacherRatingStats(SQLModel):
    """教师评分统计模型"""
    teacher_id: int
    teacher_name: str
    avg_rating: Decimal = Field(description="平均评分")
    rating_count: int = Field(description="评价次数")
    five_star_count: int = Field(default=0, description="五星评价数")
    four_star_count: int = Field(default=0, description="四星评价数")
    three_star_count: int = Field(default=0, description="三星评价数")
    two_star_count: int = Field(default=0, description="二星评价数")
    one_star_count: int = Field(default=0, description="一星评价数")


# 教师课程统计模型
class TeacherClassStats(SQLModel):
    """教师课程统计模型"""
    teacher_id: int
    teacher_name: str
    total_classes: int = Field(description="总课程数")
    completed_classes: int = Field(description="完成课程数")
    cancelled_classes: int = Field(description="取消课程数")
    no_show_classes: int = Field(description="缺席课程数")
    completion_rate: float = Field(description="完成率")
    cancellation_rate: float = Field(description="取消率")
    no_show_rate: float = Field(description="缺席率")
    last_class_at: Optional[datetime] = Field(description="最后上课时间")
