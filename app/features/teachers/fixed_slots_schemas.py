from sqlmodel import SQLModel, Field
from typing import Optional, List
from datetime import datetime, time
from pydantic import field_validator, ConfigDict

from .fixed_slots_models import (
    Weekday, TimeValidationConfig, 
    validate_time_format, validate_time_slot_basic,
    validate_duration_minutes, get_weekday_name
)


# 基础Schema模型
class TeacherFixedSlotBase(SQLModel):
    """教师固定时间占位表基础模型"""
    teacher_id: int = Field(description="教师ID")
    weekday: Weekday = Field(description="星期几（1-7，1为星期一）")
    start_time: time = Field(description="开始时间（HH:MM格式）")
    duration_minutes: Optional[int] = Field(
        default=TimeValidationConfig.DEFAULT_DURATION_MINUTES, 
        description="课节时长(分钟)，NULL时使用系统默认值"
    )
    is_available: bool = Field(default=True, description="是否开放给会员锁定")
    is_visible_to_members: bool = Field(default=True, description="是否对会员可见")


# API请求模型
class TeacherFixedSlotCreate(TeacherFixedSlotBase):
    """创建教师固定时间段请求模型"""
    created_by: int = Field(description="创建者ID")
    
    @field_validator('start_time')
    @classmethod
    def validate_start_time(cls, v):
        """验证开始时间"""
        if isinstance(v, str):
            if not validate_time_format(v):
                raise ValueError('时间格式错误，应为 HH:MM 格式')
            # 转换为time对象
            from .fixed_slots_models import parse_time_string
            v = parse_time_string(v)
        
        # 验证时间段
        duration = TimeValidationConfig.DEFAULT_DURATION_MINUTES
        valid, msg = validate_time_slot_basic(v, duration)
        if not valid:
            raise ValueError(msg)
        
        return v
    
    @field_validator('duration_minutes')
    @classmethod
    def validate_duration(cls, v):
        """验证课程时长"""
        if v is not None and not validate_duration_minutes(v):
            raise ValueError(f'课程时长必须在{TimeValidationConfig.MIN_DURATION_MINUTES}-{TimeValidationConfig.MAX_DURATION_MINUTES}分钟之间')
        return v


class TeacherFixedSlotUpdate(SQLModel):
    """更新教师固定时间段请求模型"""
    weekday: Optional[Weekday] = Field(default=None, description="星期几（1-7，1为星期一）")
    start_time: Optional[time] = Field(default=None, description="开始时间（HH:MM格式）")
    duration_minutes: Optional[int] = Field(default=None, description="课节时长(分钟)")
    is_available: Optional[bool] = Field(default=None, description="是否开放给会员锁定")
    is_visible_to_members: Optional[bool] = Field(default=None, description="是否对会员可见")
    
    @field_validator('start_time')
    @classmethod
    def validate_start_time(cls, v):
        """验证开始时间"""
        if v is None:
            return v
            
        if isinstance(v, str):
            if not validate_time_format(v):
                raise ValueError('时间格式错误，应为 HH:MM 格式')
            # 转换为time对象
            from .fixed_slots_models import parse_time_string
            v = parse_time_string(v)
        
        # 验证时间段
        duration = TimeValidationConfig.DEFAULT_DURATION_MINUTES
        valid, msg = validate_time_slot_basic(v, duration)
        if not valid:
            raise ValueError(msg)
        
        return v
    
    @field_validator('duration_minutes')
    @classmethod
    def validate_duration(cls, v):
        """验证课程时长"""
        if v is not None and not validate_duration_minutes(v):
            raise ValueError(f'课程时长必须在{TimeValidationConfig.MIN_DURATION_MINUTES}-{TimeValidationConfig.MAX_DURATION_MINUTES}分钟之间')
        return v


# 批量操作模型
class TeacherFixedSlotBatchCreate(SQLModel):
    """批量创建教师固定时间段请求模型"""
    teacher_id: int = Field(description="教师ID")
    slots: List[TeacherFixedSlotCreate] = Field(description="时间段列表")
    created_by: int = Field(description="创建者ID")
    
    @field_validator('slots')
    @classmethod
    def validate_slots(cls, v):
        """验证时间段列表"""
        if not v:
            raise ValueError('时间段列表不能为空')
        
        if len(v) > 50:  # 限制批量创建数量
            raise ValueError('单次批量创建不能超过50个时间段')
        
        # 检查是否有重复的时间段
        seen = set()
        for slot in v:
            key = (slot.weekday, slot.start_time)
            if key in seen:
                raise ValueError(f'存在重复的时间段：{get_weekday_name(slot.weekday)} {slot.start_time}')
            seen.add(key)
        
        return v


class TeacherFixedSlotBatchUpdate(SQLModel):
    """批量更新教师固定时间段请求模型"""
    updates: List[dict] = Field(description="更新列表，每个元素包含id和要更新的字段")
    
    @field_validator('updates')
    @classmethod
    def validate_updates(cls, v):
        """验证更新列表"""
        if not v:
            raise ValueError('更新列表不能为空')
        
        if len(v) > 50:  # 限制批量更新数量
            raise ValueError('单次批量更新不能超过50个时间段')
        
        # 验证每个更新项
        for update in v:
            if 'id' not in update:
                raise ValueError('每个更新项必须包含id字段')
            
            # 验证至少有一个要更新的字段
            update_fields = ['weekday', 'start_time', 'duration_minutes', 'is_available', 'is_visible_to_members']
            if not any(field in update for field in update_fields):
                raise ValueError('每个更新项至少需要包含一个要更新的字段')
        
        return v


class TeacherFixedSlotBatchDelete(SQLModel):
    """批量删除教师固定时间段请求模型"""
    ids: List[int] = Field(description="要删除的时间段ID列表")
    
    @field_validator('ids')
    @classmethod
    def validate_ids(cls, v):
        """验证ID列表"""
        if not v:
            raise ValueError('ID列表不能为空')
        
        if len(v) > 50:  # 限制批量删除数量
            raise ValueError('单次批量删除不能超过50个时间段')
        
        # 检查是否有重复的ID
        if len(set(v)) != len(v):
            raise ValueError('ID列表中存在重复项')
        
        return v


# 查询参数模型
class TeacherFixedSlotQuery(SQLModel):
    """查询教师固定时间段请求模型"""
    teacher_id: Optional[int] = Field(default=None, description="教师ID")
    weekday: Optional[Weekday] = Field(default=None, description="星期几")
    is_available: Optional[bool] = Field(default=None, description="是否可用")
    is_visible_to_members: Optional[bool] = Field(default=None, description="是否对会员可见")
    start_time_from: Optional[time] = Field(default=None, description="开始时间范围-起始")
    start_time_to: Optional[time] = Field(default=None, description="开始时间范围-结束")
    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页数量")
    sort_by: str = Field(default="weekday,start_time", description="排序字段")
    sort_order: str = Field(default="asc", description="排序方向")
    
    @field_validator('sort_order')
    @classmethod
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('排序方向必须是 asc 或 desc')
        return v


# API响应模型
class TeacherFixedSlotResponse(SQLModel):
    """教师固定时间段响应模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(description="主键ID")
    teacher_id: int = Field(description="教师ID")
    weekday: Weekday = Field(description="星期几（1-7，1为星期一）")
    weekday_name: str = Field(description="星期名称")
    start_time: time = Field(description="开始时间（HH:MM格式）")
    duration_minutes: Optional[int] = Field(description="课节时长(分钟)")
    is_available: bool = Field(description="是否开放给会员锁定")
    is_visible_to_members: bool = Field(description="是否对会员可见")
    created_by: Optional[int] = Field(description="创建者ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")


class TeacherFixedSlotList(SQLModel):
    """教师固定时间段列表响应模型"""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(description="主键ID")
    teacher_id: int = Field(description="教师ID")
    weekday: Weekday = Field(description="星期几")
    weekday_name: str = Field(description="星期名称")
    start_time: time = Field(description="开始时间")
    duration_minutes: Optional[int] = Field(description="课节时长(分钟)")
    is_available: bool = Field(description="是否可用")
    is_visible_to_members: bool = Field(description="是否对会员可见")


# 可用时间查询模型
class AvailableTimesQuery(SQLModel):
    """可用时间查询参数"""
    teacher_id: int = Field(description="教师ID")
    weekdays: Optional[List[Weekday]] = Field(default=None, description="星期列表")
    start_time_from: Optional[time] = Field(default=None, description="开始时间范围-起始")
    start_time_to: Optional[time] = Field(default=None, description="开始时间范围-结束")
    only_available: bool = Field(default=True, description="仅返回可用时间")
    only_visible: bool = Field(default=True, description="仅返回对会员可见的时间")


# 时间段冲突检测模型
class TimeSlotConflictCheck(SQLModel):
    """时间段冲突检测参数"""
    teacher_id: int = Field(description="教师ID")
    weekday: Weekday = Field(description="星期几")
    start_time: time = Field(description="开始时间")
    duration_minutes: Optional[int] = Field(
        default=TimeValidationConfig.DEFAULT_DURATION_MINUTES,
        description="课程时长"
    )
    exclude_id: Optional[int] = Field(default=None, description="排除的记录ID（用于更新时检测）")


# 统计信息模型
class TeacherFixedSlotStats(SQLModel):
    """教师固定时间段统计信息"""
    teacher_id: int = Field(description="教师ID")
    total_slots: int = Field(description="总时间段数")
    available_slots: int = Field(description="可用时间段数")
    visible_slots: int = Field(description="对会员可见的时间段数")
    weekday_distribution: dict = Field(description="按星期分布统计")


# 优化查询响应模型
class TeacherFixedSlotWithOccupation(SQLModel):
    """教师固定时间段及占用状态响应模型（优化版）"""
    model_config = ConfigDict(from_attributes=True)

    id: int = Field(description="主键ID")
    teacher_id: int = Field(description="教师ID")
    weekday: Weekday = Field(description="星期几")
    weekday_name: str = Field(description="星期名称")
    start_time: time = Field(description="开始时间")
    duration_minutes: Optional[int] = Field(description="课节时长(分钟)")
    is_available: bool = Field(description="是否可用")
    is_visible_to_members: bool = Field(description="是否对会员可见")

    # 占用状态信息（来自冗余字段）
    lock_status: str = Field(description="锁定状态")
    lock_status_display: str = Field(description="锁定状态显示名称")
    member_id: Optional[int] = Field(description="锁定的会员ID")
    member_name: Optional[str] = Field(description="锁定的会员姓名")
    member_phone: Optional[str] = Field(description="锁定的会员手机号")
    locked_at: Optional[datetime] = Field(description="锁定时间")


class MemberFixedScheduleItem(SQLModel):
    """会员固定课程安排项"""
    id: int = Field(description="时间段ID")
    teacher_id: int = Field(description="教师ID")
    weekday: Weekday = Field(description="星期几")
    weekday_name: str = Field(description="星期名称")
    start_time: time = Field(description="开始时间")
    duration_minutes: Optional[int] = Field(description="课节时长(分钟)")
    lock_status: str = Field(description="锁定状态")
    locked_at: Optional[datetime] = Field(description="锁定时间")
