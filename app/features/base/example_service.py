"""使用简化版基础类的示例服务 - 展示如何使用新的设计"""

from typing import List, Optional, Tuple
from sqlmodel import Session

# 假设的模型和异常（示例用）
from app.features.users.models import User  # 示例模型
from app.features.users.schemas import UserCreate, UserUpdate, UserSearchParams  # 示例schemas
from app.features.users.exceptions import UserBusinessException, UserNotFoundError  # 示例异常

# 导入新的基础类和工具函数
from .base_service import TenantAwareService
from .query_utils import (
    search_with_pagination, 
    find_by_unique_field, 
    exists_by_field,
    apply_text_search,
    apply_filters
)


class UserServiceV2(TenantAwareService[User]):
    """用户服务 - 使用简化版基础类的示例"""
    
    @property
    def model_class(self):
        return User
    
    def create_user(self, user_data: UserCreate, created_by: Optional[int] = None) -> User:
        """创建新用户 - 使用基础类的create方法"""
        
        # 业务验证 - 使用工具函数检查唯一性
        if exists_by_field(self.session, User, 'username', user_data.username):
            raise UserBusinessException.username_already_exists(user_data.username)
        
        if user_data.email and exists_by_field(self.session, User, 'email', user_data.email):
            raise UserBusinessException.email_already_exists(user_data.email)
        
        # 密码处理
        user_dict = user_data.model_dump()
        user_dict['password_hash'] = get_password_hash(user_dict.pop('password'))
        
        # 使用基础类的通用创建方法
        return self.create(user_dict, created_by)
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户 - 使用工具函数"""
        return find_by_unique_field(self.session, User, 'username', username)
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户 - 使用工具函数"""
        return find_by_unique_field(self.session, User, 'email', email)
    
    def search_users(self, params: UserSearchParams) -> Tuple[List[User], int]:
        """搜索用户 - 使用工具函数的一站式解决方案"""
        
        # 准备搜索字段
        search_fields = ['username', 'email', 'real_name']
        
        # 准备过滤条件
        filters = {}
        if params.role:
            filters['role'] = params.role
        if params.status:
            filters['status'] = params.status
        
        # 使用工具函数进行搜索和分页
        return search_with_pagination(
            session=self.session,
            model_class=User,
            search_term=params.search,
            search_fields=search_fields,
            filters=filters,
            page=params.page,
            size=params.size,
            sort_field='created_at',
            sort_desc=True
        )
    
    def update_user(self, user_id: int, user_data: UserUpdate, updated_by: Optional[int] = None) -> User:
        """更新用户信息 - 使用基础类的update方法"""
        
        # 检查用户是否存在
        if not self.get_by_id(user_id):
            raise UserNotFoundError()
        
        update_dict = user_data.model_dump(exclude_unset=True)
        
        # 业务验证 - 检查邮箱唯一性
        if 'email' in update_dict and update_dict['email']:
            if exists_by_field(self.session, User, 'email', update_dict['email'], exclude_id=user_id):
                raise UserBusinessException.email_already_exists(update_dict['email'])
        
        # 使用基础类的通用更新方法
        return self.update(user_id, update_dict, updated_by)
    
    def delete_user(self, user_id: int) -> bool:
        """删除用户 - 使用基础类的delete方法"""
        return self.delete(user_id)


# 对比：原始实现 vs 新实现的代码量对比
class UserServiceOriginal:
    """原始实现 - 展示重复代码"""
    
    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        # 重复的RLS设置代码
        self.session.exec(text(f"SET app.current_tenant_id = '{tenant_id}'"))
    
    def create_user(self, user_data: UserCreate, created_by: Optional[int] = None) -> User:
        """原始创建方法 - 大量重复代码"""
        
        # 重复的唯一性检查逻辑
        existing_user = self.session.exec(
            select(User).where(User.username == user_data.username)
        ).first()
        if existing_user:
            raise UserBusinessException.username_already_exists(user_data.username)
        
        if user_data.email:
            existing_email = self.session.exec(
                select(User).where(User.email == user_data.email)
            ).first()
            if existing_email:
                raise UserBusinessException.email_already_exists(user_data.email)
        
        # 重复的数据准备和审计字段设置
        user_dict = user_data.model_dump()
        user_dict['password_hash'] = get_password_hash(user_dict.pop('password'))
        user_dict['tenant_id'] = self.tenant_id
        user_dict['created_by'] = created_by
        
        user = User(**user_dict)
        # 重复的时间设置
        now = datetime.now()
        user.created_at = now
        user.updated_at = now
        
        # 重复的保存逻辑
        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)
        
        # 重复的验证逻辑
        db_user = self.session.get(User, user.id)
        if not db_user:
            raise UserBusinessException.general_error("用户创建失败")
        
        return user
    
    def search_users(self, params: UserSearchParams):
        """原始搜索方法 - 重复的查询构建逻辑"""
        
        # 重复的查询构建代码
        statement = select(User)
        
        if params.role:
            statement = statement.where(User.role == params.role)
        if params.status:
            statement = statement.where(User.status == params.status)
        if params.search:
            # 重复的搜索逻辑
            search_filter = (
                User.username.ilike(f"%{params.search}%") |
                User.email.ilike(f"%{params.search}%") |
                User.real_name.ilike(f"%{params.search}%")
            )
            statement = statement.where(search_filter)
        
        # 重复的计数逻辑
        count_statement = select(User)
        # ... 重复相同的过滤条件 ...
        total = len(self.session.exec(count_statement).all())
        
        # 重复的分页和排序逻辑
        offset = (params.page - 1) * params.size
        statement = statement.offset(offset).limit(params.size)
        statement = statement.order_by(User.created_at.desc())
        
        users = self.session.exec(statement).all()
        return users, total


# 使用工具函数的更高级示例
class AdvancedUserService(TenantAwareService[User]):
    """高级用户服务 - 展示工具函数的组合使用"""
    
    @property
    def model_class(self):
        return User
    
    def search_users_advanced(self, 
                             search_term: Optional[str] = None,
                             role_filter: Optional[str] = None,
                             status_filter: Optional[str] = None,
                             date_from: Optional[date] = None,
                             date_to: Optional[date] = None,
                             page: int = 1,
                             size: int = 20) -> Tuple[List[User], int]:
        """高级搜索 - 展示工具函数的灵活组合"""
        
        from sqlmodel import select
        from .query_utils import apply_text_search, apply_filters, apply_date_range, apply_sorting, paginate_query
        
        # 构建基础查询
        statement = select(User)
        
        # 应用文本搜索
        if search_term:
            statement = apply_text_search(
                statement, User, search_term, 
                ['username', 'email', 'real_name']
            )
        
        # 应用过滤条件
        filters = {}
        if role_filter:
            filters['role'] = role_filter
        if status_filter:
            filters['status'] = status_filter
        
        if filters:
            statement = apply_filters(statement, User, filters)
        
        # 应用日期范围
        if date_from or date_to:
            statement = apply_date_range(
                statement, User, 'created_at', 
                date_from, date_to
            )
        
        # 计算总数
        from sqlmodel import func
        count_statement = select(func.count()).select_from(statement.subquery())
        total = self.session.exec(count_statement).one()
        
        # 应用排序和分页
        statement = apply_sorting(statement, User, 'created_at', True)
        statement = paginate_query(statement, page, size)
        
        # 执行查询
        users = self.session.exec(statement).all()
        
        return users, total


# 工厂函数示例
def get_user_service_v2(session: Session, tenant_id: int) -> UserServiceV2:
    """获取用户服务实例 - 工厂函数"""
    return UserServiceV2(session, tenant_id) 