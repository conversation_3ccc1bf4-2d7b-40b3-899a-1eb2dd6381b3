"""简化版共享基础服务类 - 专注核心功能"""

from typing import Optional, TypeVar, Generic, Type, Any, List
from sqlmodel import Session, text, SQLModel
from app.utils.time import now_utc
from abc import ABC, abstractmethod

T = TypeVar('T', bound=SQLModel)


class BaseService(Generic[T], ABC):
    """精简版基础服务类 - 只处理核心CRUD和RLS"""
    
    def __init__(self, session: Session, tenant_id: Optional[int] = None):
        self.session = session
        self.tenant_id = tenant_id
        self._setup_rls()
    
    def _setup_rls(self):
        """设置RLS上下文"""
        if self.tenant_id is not None:
            self.session.exec(text("SET app.current_tenant_id = :tenant_id").params(tenant_id=str(self.tenant_id)))
        else:
            # 清除租户上下文（超级管理员）
            self.session.exec(text("RESET app.current_tenant_id"))
    
    @property
    @abstractmethod
    def model_class(self) -> Type[T]:
        """子类必须定义模型类"""
        pass
    
    def _set_audit_fields(self, entity: T, created_by: Optional[int] = None, is_update: bool = False):
        """设置审计字段 - 简化版本（统一使用UTC时间）"""
        now = now_utc()

        if not is_update:
            # 创建时设置
            if hasattr(entity, 'created_at'):
                entity.created_at = now
            if hasattr(entity, 'created_by') and created_by:
                entity.created_by = created_by
            if hasattr(entity, 'tenant_id') and self.tenant_id:
                entity.tenant_id = self.tenant_id
        
        # 更新时间总是设置
        if hasattr(entity, 'updated_at'):
            entity.updated_at = now
    
    def create(self, entity_data: Any, created_by: Optional[int] = None, auto_commit: bool = True) -> T:
        """通用创建方法

        Args:
            entity_data: 实体数据（字典或Pydantic模型）
            created_by: 创建者ID
            auto_commit: 是否自动提交事务，默认True

        Returns:
            创建的实体对象
        """
        # 转换为字典
        if hasattr(entity_data, 'model_dump'):
            entity_dict = entity_data.model_dump()
        else:
            entity_dict = entity_data if isinstance(entity_data, dict) else entity_data.__dict__

        # 创建实体
        entity = self.model_class(**entity_dict)

        # 设置审计字段
        self._set_audit_fields(entity, created_by)

        # 添加到会话
        self.session.add(entity)

        # 根据参数决定是否提交
        if auto_commit:
            self.session.commit()
            self.session.refresh(entity)
        else:
            self.session.flush()

        return entity

    def batch_create(self, entities_data: List[Any], created_by: Optional[int] = None) -> List[T]:
        """批量创建实体

        Args:
            entities_data: 实体数据列表
            created_by: 创建者ID

        Returns:
            创建的实体对象列表
        """
        entities = []

        for entity_data in entities_data:
            # 使用create方法但不自动提交
            entity = self.create(entity_data, created_by, auto_commit=False)
            entities.append(entity)

        # 一次性提交所有实体
        if entities:
            self.session.commit()
            # 刷新所有实体
            for entity in entities:
                self.session.refresh(entity)

        return entities

    def batch_update(self, entity_ids: List[int], update_data: Any, updated_by: Optional[int] = None) -> List[T]:
        """批量更新实体

        Args:
            entity_ids: 要更新的实体ID列表
            update_data: 更新数据（字典或Pydantic模型）
            updated_by: 更新者ID

        Returns:
            更新后的实体对象列表
        """
        from sqlmodel import select

        # 一次查询获取所有实体
        statement = select(self.model_class).where(self.model_class.id.in_(entity_ids))
        entities = self.session.exec(statement).all()

        # 验证所有实体都存在
        if len(entities) != len(entity_ids):
            found_ids = {entity.id for entity in entities}
            missing_ids = set(entity_ids) - found_ids
            raise ValueError(f"以下ID的实体不存在: {missing_ids}")

        # 转换更新数据为字典
        if hasattr(update_data, 'model_dump'):
            update_dict = update_data.model_dump(exclude_unset=True)
        else:
            update_dict = update_data if isinstance(update_data, dict) else update_data.__dict__

        # 批量更新
        for entity in entities:
            for field, value in update_dict.items():
                if hasattr(entity, field):
                    setattr(entity, field, value)

            # 设置审计字段
            self._set_audit_fields(entity, updated_by, is_update=True)
            self.session.add(entity)

        # 一次性提交
        self.session.commit()

        # 刷新所有实体
        for entity in entities:
            self.session.refresh(entity)

        return entities

    def get_by_id(self, entity_id: int) -> Optional[T]:
        """根据ID获取实体（RLS自动过滤租户）"""
        return self.session.get(self.model_class, entity_id)
    
    def update(self, entity_id: int, update_data: Any, updated_by: Optional[int] = None) -> Optional[T]:
        """通用更新方法"""
        entity = self.get_by_id(entity_id)
        if not entity:
            return None
        
        # 更新字段
        if hasattr(update_data, 'model_dump'):
            update_dict = update_data.model_dump(exclude_unset=True)
        else:
            update_dict = update_data if isinstance(update_data, dict) else update_data.__dict__
        
        for key, value in update_dict.items():
            if hasattr(entity, key):
                setattr(entity, key, value)
        
        # 设置审计字段
        self._set_audit_fields(entity, updated_by, is_update=True)
        
        self.session.add(entity)
        self.session.commit()
        self.session.refresh(entity)
        
        return entity
    
    def delete(self, entity: T):
        """通用删除方法-直接给entity的不用再查询了"""
        self.session.delete(entity)
        self.session.commit()

    def soft_delete(self, entity_id: int, deleted_by: Optional[int] = None) -> bool:
        """软删除方法 - 设置is_deleted标志

        Args:
            entity_id: 要软删除的实体ID
            deleted_by: 删除者ID

        Returns:
            是否成功软删除
        """
        entity = self.get_by_id(entity_id)
        if not entity:
            return False

        # 检查实体是否有is_deleted字段
        if not hasattr(entity, 'is_deleted'):
            raise ValueError(f"{self.model_class.__name__} 不支持软删除：缺少is_deleted字段")

        # 设置软删除标志
        entity.is_deleted = True

        # 设置删除相关的审计字段
        if hasattr(entity, 'deleted_by') and deleted_by:
            entity.deleted_by = deleted_by
        if hasattr(entity, 'deleted_at'):
            from datetime import datetime, timezone
            entity.deleted_at = datetime.now()

        # 设置更新时间
        self._set_audit_fields(entity, deleted_by, is_update=True)

        self.session.add(entity)
        self.session.commit()
        self.session.refresh(entity)

        return True


class TenantAwareService(BaseService[T]):
    """租户感知的服务基类 - 强制要求tenant_id"""
    
    def __init__(self, session: Session, tenant_id: int):
        if tenant_id is None:
            raise ValueError("TenantAwareService requires tenant_id")
        super().__init__(session, tenant_id)


class GlobalService(BaseService[T]):
    """全局服务基类 - 用于超级管理员操作"""
    
    def __init__(self, session: Session):
        super().__init__(session, tenant_id=None) 