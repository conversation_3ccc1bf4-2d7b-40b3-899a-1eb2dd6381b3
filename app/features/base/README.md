# 简化版共享组件设计 (V2)

## 🎯 设计目标

基于对现有模块的分析，创建简化版的共享组件来解决以下问题：

- **重复代码**：每个 Service 都有相同的 RLS 设置、CRUD 操作、审计字段处理
- **查询逻辑重复**：搜索、分页、过滤逻辑在各模块中重复实现
- **维护困难**：修改通用逻辑需要在多个文件中同步更新

## 📁 文件结构

```
app/features/base/
├── base_service.py     # 基础服务类
├── query_utils.py      # 查询工具函数
├── example_service.py  # 使用示例
└── README.md          # 说明文档
```

## 🚀 核心组件

### 1. BaseService - 基础服务类

**解决的问题：**

- ✅ RLS 上下文设置重复
- ✅ CRUD 操作重复
- ✅ 审计字段设置重复
- ✅ 数据库操作模式重复

**使用方式：**

```python
from app.features.shared_v2.base_service import TenantAwareService

class UserService(TenantAwareService[User]):
    @property
    def model_class(self):
        return User

    def create_user(self, user_data: UserCreate, created_by: int) -> User:
        # 业务验证...
        user_dict = user_data.model_dump()
        # 使用基础类的通用方法
        return self.create(user_dict, created_by)
```

**优势：**

- 代码减少 40-60 行重复代码
- RLS 逻辑集中管理
- 审计字段自动处理
- 类型安全的泛型支持

### 2. QueryUtils - 查询工具函数

**解决的问题：**

- ✅ 搜索逻辑重复
- ✅ 分页逻辑重复
- ✅ 过滤条件构建重复
- ✅ 计数查询重复

**核心函数：**

#### `search_with_pagination` - 一站式搜索分页

```python
from app.features.shared_v2.query_utils import search_with_pagination

# 简单使用
users, total = search_with_pagination(
    session=session,
    model_class=User,
    search_term="张三",
    search_fields=['username', 'real_name'],
    filters={'role': 'admin'},
    page=1,
    size=20
)
```

#### 其他实用函数

```python
# 唯一性检查
exists = exists_by_field(session, User, 'email', '<EMAIL>')

# 按字段查找
user = find_by_unique_field(session, User, 'username', 'admin')

# 灵活的查询构建
statement = select(User)
statement = apply_text_search(statement, User, "搜索词", ['name', 'email'])
statement = apply_filters(statement, User, {'status': 'active'})
statement = apply_date_range(statement, User, 'created_at', start_date, end_date)
```

## 📊 代码对比

### 原始实现 (约 80 行)

```python
class UserServiceOriginal:
    def __init__(self, session: Session, tenant_id: int):
        self.session = session
        self.tenant_id = tenant_id
        # 重复的RLS设置...
        self.session.exec(text(f"SET app.current_tenant_id = '{tenant_id}'"))

    def create_user(self, user_data: UserCreate, created_by: int) -> User:
        # 重复的唯一性检查...
        existing_user = self.session.exec(
            select(User).where(User.username == user_data.username)
        ).first()
        if existing_user:
            raise UserBusinessException.username_already_exists(user_data.username)

        # 重复的数据准备...
        user_dict = user_data.model_dump()
        user_dict['tenant_id'] = self.tenant_id
        user_dict['created_by'] = created_by

        user = User(**user_dict)
        # 重复的时间设置...
        now = datetime.now()
        user.created_at = now
        user.updated_at = now

        # 重复的保存逻辑...
        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)
        return user

    def search_users(self, params):
        # 30+ 行重复的查询构建、分页、计数逻辑...
```

### 新实现 (约 30 行)

```python
class UserServiceV2(TenantAwareService[User]):
    @property
    def model_class(self):
        return User

    def create_user(self, user_data: UserCreate, created_by: int) -> User:
        # 简洁的唯一性检查
        if exists_by_field(self.session, User, 'username', user_data.username):
            raise UserBusinessException.username_already_exists(user_data.username)

        user_dict = user_data.model_dump()
        # 使用基础类方法 - 自动处理RLS、审计字段、保存逻辑
        return self.create(user_dict, created_by)

    def search_users(self, params):
        # 一行解决搜索和分页
        return search_with_pagination(
            session=self.session,
            model_class=User,
            search_term=params.search,
            search_fields=['username', 'email', 'real_name'],
            filters={'role': params.role, 'status': params.status},
            page=params.page,
            size=params.size
        )
```

## ✅ 设计原则

### 1. YAGNI (You Aren't Gonna Need It)

- 只实现当前真正需要的抽象
- 避免过度工程化
- 保持简单和实用

### 2. 渐进式改进

- 与现有代码兼容
- 可以逐步迁移
- 不强制全部替换

### 3. 类型安全

- 使用泛型确保类型安全
- IDE 友好的代码提示
- 编译时错误检查

## 🔄 迁移策略

### 阶段 1：试点验证

- 在一个模块中试用新设计
- 验证功能完整性和性能
- 收集开发体验反馈

### 阶段 2：逐步迁移

- 选择重复代码最多的模块优先迁移
- 保留原有代码作为备份
- 逐个模块进行替换

### 阶段 3：统一标准

- 新模块强制使用新设计
- 更新开发规范
- 清理废弃代码

## 🎯 预期收益

### 开发效率

- **代码量减少 50%**：Service 类从 150+ 行减少到 80- 行
- **开发速度提升**：通用操作一行代码解决
- **Bug 减少**：重复逻辑集中管理，减少不一致

### 维护性

- **集中管理**：RLS、审计、分页逻辑统一维护
- **一致性保证**：所有模块使用相同的模式
- **易于扩展**：新功能可以快速添加到基础类

### 代码质量

- **类型安全**：泛型提供编译时检查
- **可读性提升**：业务逻辑更清晰
- **测试友好**：基础功能集中测试

## ⚠️ 注意事项

1. **学习成本**：团队需要熟悉新的基础类
2. **调试复杂性**：抽象层可能增加调试难度
3. **灵活性权衡**：通用方法可能不适合所有特殊场景

## 🧪 测试建议

```python
# 基础类测试
def test_base_service_create():
    service = UserServiceV2(session, tenant_id=1)
    user = service.create(user_data, created_by=1)
    assert user.tenant_id == 1
    assert user.created_by == 1
    assert user.created_at is not None

# 工具函数测试
def test_search_with_pagination():
    users, total = search_with_pagination(
        session, User, search_term="test",
        search_fields=['username'], page=1, size=10
    )
    assert len(users) <= 10
    assert total >= 0
```

## 📈 成功指标

- ✅ Service 类代码量减少 40%+
- ✅ 新模块开发时间减少 30%+
- ✅ 重复代码相关 bug 减少 50%+
- ✅ 代码 review 时间减少 20%+
