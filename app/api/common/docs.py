"""API文档配置和说明"""

from typing import Dict, Any

# 全局响应说明
GLOBAL_RESPONSES = {
    400: {
        "description": "**业务逻辑错误**\n\n请求在业务层面不符合要求时返回。常见情况：\n- 数据不存在或已被删除\n- 操作不被允许（如重复操作）\n- 业务规则验证失败\n\n**解决方法：**\n1. 检查请求的资源是否存在\n2. 确认当前操作是否被允许\n3. 根据错误信息调整请求参数",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": "操作失败，请检查请求参数",
                    "code": "BUSINESS_ERROR",
                    "level": "error",
                    "details": {},
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
        }
    },
    422: {
        "description": "**数据验证失败**\n\n当请求数据不符合API定义的格式要求时返回。常见情况：\n- 缺少必填字段\n- 字段类型不匹配\n- 字段值不符合验证规则\n\n**解决方法：**\n1. 检查请求体格式是否正确\n2. 确保所有必填字段都已提供\n3. 验证字段类型和值是否符合要求",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "message": "数据验证失败",
                    "code": "VALIDATION_ERROR",
                    "level": "warning",
                    "details": {
                        "errors": [
                            {
                                "loc": ["body", "email"],
                                "msg": "field required",
                                "type": "value_error.missing"
                            }
                        ]
                    },
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
        }
    }
}

# API文档的描述内容
API_DESCRIPTION = """
## 🎯 系统概述

这是一个现代化的课程预订系统后端API，基于FastAPI + SQLModel + PostgreSQL构建。
系统采用多租户架构，支持不同机构独立管理用户和会员数据。

## 🔐 认证机制

系统使用JWT（JSON Web Token）进行身份认证：

1. **获取Token**: 通过 `/auth/login` 接口登录获取access_token
2. **使用Token**: 在请求头中添加 `Authorization: Bearer <access_token>`
3. **Token刷新**: 使用refresh_token通过 `/auth/refresh` 接口刷新access_token

## 📊 响应格式

所有API响应都遵循统一的格式：


### 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "http_code": 200,
  "business_code": "SUCCESS",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 不分页列表响应

```json
{
  "success": true,
  "message": "获取列表成功",
  "http_code": 200,
  "business_code": "SUCCESS",
  "data": [ ... ],
  "total": 10,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 分页列表响应
```json
{
  "success": true,
  "message": "获取数据成功",
  "http_code": 200,
  "business_code": "SUCCESS",
  "data": [...],
  "total": 100,
  "page": 1,
  "size": 20,
  "pages": 5,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应

```json
{
  "success": false,
  "message": "错误描述",
  "http_code": 400,
  "business_code": "SPECIFIC_ERROR_CODE",
  "level": "error",
  "details": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🏷️ 错误码说明

| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| BUSINESS_ERROR | 通用业务错误 | 400 |
| VALIDATION_ERROR | 数据验证失败 | 422 |
| AUTHENTICATION_FAILED | 认证失败 | 401 |
| PERMISSION_DENIED | 权限不足 | 403 |

## 📝 开发指南

1. **分页查询**: 使用 `skip` 和 `limit` 参数进行分页
2. **数据验证**: 所有输入数据都会进行严格的格式验证
3. **多租户**: 大部分接口都会自动根据用户的租户ID进行数据隔离
4. **幂等性**: 支持幂等操作的接口会在文档中特别说明

## 🔧 环境配置

使用PostgreSQL数据库，需要配置相应的环境变量

## 📞 技术支持

如有问题，请联系开发团队或查看项目文档。
"""


def get_openapi_config() -> Dict[str, Any]:
    """获取OpenAPI配置"""
    return {
        "title": "课程预订系统API",
        "description": API_DESCRIPTION,
        "version": "1.0.0",
        "terms_of_service": "https://example.com/terms/",
        "contact": {
            "name": "开发团队",
            "email": "<EMAIL>",
        },
        "license_info": {
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT",
        },
        "responses": GLOBAL_RESPONSES,
    } 