"""
会员端 - 个人信息管理API
"""
from fastapi import APIRouter, Depends
from sqlmodel import Session

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, success_response
from app.features.members.schemas import MemberRead, MemberUpdate
from app.features.members.service import get_member_service

router = APIRouter()

@router.get("/me", response_model=DataResponse[MemberRead])
def get_my_profile(
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的个人信息"""
    member_service = get_member_service(session, member_context.tenant_context.tenant_id)
    member_with_stats = member_service.get_member_with_stats(member_context.member.id)
    return success_response(member_with_stats, "获取个人信息成功")
