"""
会员端 - 固定课程API
"""
from typing import List, Optional
from datetime import time
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session

from app.db.session import get_session
from app.core.dependencies import get_member_context, MemberContext
from app.api.common.responses import DataResponse, ListResponse, success_response, list_response
from app.api.common.exceptions import BusinessException

from app.features.teachers.fixed_slots_service import TeacherFixedSlotService
from app.features.teachers.fixed_slots_schemas import MemberFixedScheduleItem
from app.features.teachers.fixed_slots_models import get_weekday_name
from app.features.members.fixed_lock_models import get_weekday_display

from app.features.members.fixed_lock_service import MemberFixedSlotLockService
from app.features.members.fixed_lock_schemas import (
    AvailableSlotResponse, MemberFixedSlotLockCreate, MemberFixedSlotLockResponse
)

router = APIRouter()


@router.get(
    "/my-schedule",
    response_model=ListResponse[MemberFixedScheduleItem],
    summary="获取我的固定课程安排",
    description="""
    获取当前会员的固定课程安排
    
    **功能说明：**
    - 显示会员锁定的所有固定时间段
    - 包含教师信息和上课时间
    - 按星期和时间排序
    - 使用优化查询，性能更佳
    """
)
def get_my_fixed_schedule(
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的固定课程安排"""
    tenant_id = member_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)
    
    # 使用优化的查询方法
    slots = service.get_member_fixed_schedule(member_context.member.id)
    
    # 转换为响应格式
    schedule_items = []
    for slot in slots:
        item = MemberFixedScheduleItem(
            id=slot.id,
            teacher_id=slot.teacher_id,
            weekday=slot.weekday,
            weekday_name=get_weekday_name(slot.weekday),
            start_time=slot.start_time,
            duration_minutes=slot.duration_minutes,
            # locked_at=slot.locked_at
        )
        schedule_items.append(item)
    
    return list_response(schedule_items, len(schedule_items), "获取固定课程安排成功")


@router.get(
    "/available-slots",
    response_model=ListResponse[AvailableSlotResponse],
    summary="获取可锁定的时间段",
    description="""
    获取可以锁定的教师固定时间段
    
    **筛选条件：**
    - 支持按教师筛选
    - 支持按星期筛选
    - 只显示可用且对会员可见的时间段
    - 排除已被锁定的时间段
    """
)
def get_available_slots(
    teacher_id: Optional[int] = Query(None, description="教师ID"),
    weekdays: Optional[str] = Query(None, description="星期列表，逗号分隔，如：1,2,3"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取可锁定的时间段"""
    tenant_id = member_context.tenant_context.tenant_id
    
    # 解析星期列表
    weekday_list = None
    if weekdays:
        try:
            weekday_list = [int(w.strip()) for w in weekdays.split(',') if w.strip()]
        except ValueError:
            raise BusinessException(message="星期格式错误，应为逗号分隔的数字")
    
    from app.features.members.fixed_lock_schemas import AvailableSlotQuery
    query = AvailableSlotQuery(
        teacher_id=teacher_id,
        weekdays=weekday_list,
        only_available=True,
        only_visible=True,
        exclude_locked=True
    )
    
    service = MemberFixedSlotLockService(session, tenant_id)
    slots = service.get_available_slots(query)
    
    # 转换为响应格式
    response_data = []
    for slot in slots:
        slot_response = AvailableSlotResponse(
            id=slot.id,
            teacher_id=slot.teacher_id,
            weekday=slot.weekday.value,
            weekday_name=get_weekday_name(slot.weekday),
            start_time=slot.start_time,
            duration_minutes=slot.duration_minutes,
            is_available=slot.is_available,
            is_visible_to_members=slot.is_visible_to_members
        )
        response_data.append(slot_response)
    
    return list_response(response_data, len(response_data), "获取可锁定时间段成功")


@router.post(
    "/lock-request",
    response_model=DataResponse[MemberFixedSlotLockResponse],
    summary="申请锁定固定时间段",
    description="""
    会员申请锁定指定的固定时间段
    
    **业务规则：**
    - 只能锁定可用且对会员可见的时间段
    - 不能锁定已被其他会员锁定的时间段
    - 锁定后会自动同步教师时间段的占用状态
    """
)
def request_slot_lock(
    lock_data: MemberFixedSlotLockCreate,
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """申请锁定固定时间段"""
    tenant_id = member_context.tenant_context.tenant_id
    
    # 设置会员ID
    lock_data.member_id = member_context.member.id
    
    service = MemberFixedSlotLockService(session, tenant_id)
    lock = service.create_lock(lock_data, created_by=member_context.member.id)
    
    # 构建响应数据
    response_data = MemberFixedSlotLockResponse(
        id=lock.id,
        member_id=lock.member_id,
        member_name=lock.member_name,
        teacher_fixed_slot_id=lock.teacher_fixed_slot_id,
        teacher_id=lock.teacher_id,
        weekday=lock.weekday,
        weekday_name=get_weekday_display(lock.weekday),
        start_time=lock.start_time,
        time_slot_display=f"{get_weekday_display(lock.weekday)} {lock.start_time.strftime('%H:%M')}",
        locked_at=lock.locked_at,
        created_at=lock.created_at,
        updated_at=lock.updated_at
    )
    
    return success_response(response_data, "固定时间段锁定成功")


@router.get(
    "/my-locks",
    response_model=ListResponse[MemberFixedSlotLockResponse],
    summary="获取我的锁定记录",
    description="""
    获取当前会员的所有锁定记录
    
    **包含信息：**
    - 锁定的时间段信息
    - 锁定状态和时间
    - 教师信息
    """
)
def get_my_locks(
    status: Optional[str] = Query(None, description="锁定状态筛选"),
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """获取我的锁定记录"""
    tenant_id = member_context.tenant_context.tenant_id
    
    from app.features.members.fixed_lock_schemas import MemberFixedSlotLockQuery
    query = MemberFixedSlotLockQuery(
        member_id=member_context.member.id,
        status=status,
        page=1,
        size=100  # 会员端通常不需要分页
    )
    
    service = MemberFixedSlotLockService(session, tenant_id)
    locks, total = service.get_locks(query)
    
    # 转换为响应格式
    response_data = []
    for lock in locks:
        lock_response = MemberFixedSlotLockResponse(
            id=lock.id,
            member_id=lock.member_id,
            member_name=lock.member_name,
            teacher_fixed_slot_id=lock.teacher_fixed_slot_id,
            teacher_id=lock.teacher_id,
            weekday=lock.weekday,
            weekday_name=get_weekday_display(lock.weekday),
            start_time=lock.start_time,
            time_slot_display=f"{get_weekday_display(lock.weekday)} {lock.start_time.strftime('%H:%M')}",
            locked_at=lock.locked_at,
            created_at=lock.created_at,
            updated_at=lock.updated_at
        )
        response_data.append(lock_response)
    
    return list_response(response_data, len(response_data), "获取锁定记录成功")


@router.delete(
    "/locks/{lock_id}",
    response_model=DataResponse[str],
    summary="取消锁定",
    description="""
    会员取消自己的固定时间段锁定
    
    **注意：**
    - 只能取消自己的锁定
    - 取消后会清空教师时间段的占用状态
    - 取消操作会直接删除锁定记录
    """
)
def cancel_my_lock(
    lock_id: int,
    member_context: MemberContext = Depends(get_member_context),
    session: Session = Depends(get_session)
):
    """取消我的锁定"""
    tenant_id = member_context.tenant_context.tenant_id
    service = MemberFixedSlotLockService(session, tenant_id)
    
    # 验证锁定是否属于当前会员
    lock = service.get_lock(lock_id)
    if not lock or lock.member_id != member_context.member.id:
        raise BusinessException(message="锁定记录不存在或无权操作")
    
    # 取消锁定
    service.cancel_lock(lock_id)
    
    return success_response("success", "锁定已取消")
