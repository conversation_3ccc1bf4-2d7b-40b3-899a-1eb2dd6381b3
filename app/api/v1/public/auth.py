"""
公共API - 认证相关
"""
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, status
from sqlmodel import Session, select, text
from pydantic import BaseModel
from typing import Optional

from app.features.users.models import User, UserRole, UserStatus
from app.features.members.models import Member, MemberStatus
from app.features.members.schemas import MemberLogin, MemberBasicRead
from app.features.tenants.models import Tenant
from app.features.users.schemas import UserRead
from app.features.tenants.schemas import TenantRead
from app.db.session import get_session
from app.utils.security import verify_password, create_access_token
from app.core.config import settings

# 导入新的响应模型和异常处理
from app.api.common.responses import DataResponse, success_response
from app.api.common.exceptions import BusinessException, NotFoundError, AuthenticationError
from app.api.common.utils import ensure_found
from pydantic import Field

router = APIRouter()


class AdminLoginRequest(BaseModel):
    """管理员登录请求
    支持两种模式：
    1) 超级管理员：username + password
    2) 租户管理员：email + password + tenant_code
    """
    username: Optional[str] = Field(None, examples=["demo_admin"])
    email: Optional[str] = Field(None, examples=["<EMAIL>"])
    password: str = Field(..., examples=["demo123456"])
    tenant_code: Optional[str] = Field(None, examples=["demo_tenant"])

    @classmethod
    def validate_request(cls, values: dict):
        username = values.get("username")
        email = values.get("email")
        tenant_code = values.get("tenant_code")
        # 模式判断：
        # A: 仅 username（超管）
        # B: email + tenant_code（租户管理员）
        if username and not email and not tenant_code:
            return values
        if email and tenant_code and not username:
            return values
        raise ValueError("登录参数不合法：使用 username+password 或 email+password+tenant_code")

    # Pydantic v2 写法
    from pydantic import model_validator as _model_validator  # 避免全局导入名冲突

    @_model_validator(mode="before")
    def _validate_model(cls, values):
        if isinstance(values, dict):
            return cls.validate_request(values)
        return values


class AdminLoginResponse(BaseModel):
    """管理员登录响应"""
    access_token: str
    expires: datetime
    token_type: str
    user: UserRead
    tenant: Optional[TenantRead] = None


class MemberLoginResponse(BaseModel):
    """会员登录响应"""
    access_token: str
    token_type: str
    member: MemberBasicRead
    tenant: TenantRead


@router.post("/admin/login", response_model=DataResponse[AdminLoginResponse])  # 支持两种登录模式
def admin_login(
    login_data: AdminLoginRequest,
    session: Session = Depends(get_session)
):
    """CMS管理员登录"""
    # 清除任何可能存在的租户上下文，确保能查询到所有用户
    session.exec(text("RESET app.current_tenant_id"))
    
    # 查找用户 - 由于username是全局唯一的，可以直接查询
    # 按模式查找用户
    if login_data.username:
        user_stmt = select(User).where(User.username == login_data.username)
        user = session.exec(user_stmt).first()
    else:
        # email + tenant_code 模式
        tenant_stmt = select(Tenant).where(Tenant.code == login_data.tenant_code)
        tenant = ensure_found(session.exec(tenant_stmt).first(), "租户")
        # 设置租户上下文，按租户内 email 唯一查找
        session.exec(text("SET app.current_tenant_id = :tenant_id").params(tenant_id=str(tenant.id)))
        user_stmt = select(User).where(User.email == login_data.email, User.tenant_id == tenant.id)
        user = session.exec(user_stmt).first()

    if not user or not verify_password(login_data.password, user.password_hash):
        raise AuthenticationError("用户名或密码错误")
    
    if user.status != UserStatus.ACTIVE:
        raise AuthenticationError("用户账户已被禁用")
    
    tenant = None
    # 如果用户不是超级管理员，则需要获取其所属租户信息
    if user.role != UserRole.SUPER_ADMIN and user.tenant_id:
        tenant_stmt = select(Tenant).where(Tenant.id == user.tenant_id)
        tenant = session.exec(tenant_stmt).first()
        
        if not tenant:
            raise NotFoundError("租户")
        
        # 设置租户上下文
        session.exec(text("SET app.current_tenant_id = :tenant_id").params(tenant_id=str(tenant.id)))
    else:
        # 超级管理员登录，保持无租户上下文
        session.exec(text("RESET app.current_tenant_id"))
    
    # 更新登录信息（统一UTC）
    from app.utils.time import now_utc
    current_time = now_utc()
    user.last_login_at = current_time
    user.login_count += 1
    user.failed_login_attempts = 0
    session.add(user)
    session.commit()
    
    # 创建访问令牌 - 按照文档要求的格式
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    token_data = {
        "sub": str(user.id),  # 使用用户UUID作为主题
        "user_type": "admin",  # 按文档要求使用admin
        "role": user.role.value,
        "username": user.username
    }
    
    if tenant:
        token_data.update({
            "tenant_id": str(tenant.id),
            "tenant_code": tenant.code
        })
    
    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )
    
    # 计算过期时间为当前时间加上过期时间间隔
    expires_datetime = current_time + access_token_expires
    
    # 使用 UserRead 模型提供更完整的用户信息
    response_data = AdminLoginResponse(
        access_token=access_token,
        expires=expires_datetime,
        token_type="bearer",
        user=user,
        tenant=tenant
    )
    
    return success_response(response_data, "登录成功")


@router.post("/member/login", response_model=DataResponse[MemberLoginResponse])
def member_login(
    login_data: MemberLogin,
    session: Session = Depends(get_session)
):
    """会员登录（手机号+验证码）"""
    # 查找租户
    tenant_stmt = select(Tenant).where(Tenant.code == login_data.tenant_code)
    tenant = ensure_found(session.exec(tenant_stmt).first(), "租户")
    
    # 设置租户上下文
    session.exec(text("SET app.current_tenant_id = :tenant_id").params(tenant_id=str(tenant.id)))
    
    # 验证验证码（这里简化处理，实际应该从Redis或数据库验证）
    # TODO: 实现验证码验证逻辑
    if login_data.verification_code != "1234":  # 临时验证码
        raise AuthenticationError("验证码错误")
    
    # 查找会员
    member_stmt = select(Member).where(
        Member.phone == login_data.phone,
        Member.tenant_id == tenant.id
    )
    member = ensure_found(session.exec(member_stmt).first(), "会员")
    
    if member.member_status != MemberStatus.ACTIVE:
        raise AuthenticationError("会员账户已被禁用")
    
    # 更新登录信息（使用统计服务）
    from app.features.members.service import MemberService
    member_service = MemberService(session, tenant.id)
    member_service.update_login_time(member.id)
    
    # 创建访问令牌 - 按照文档要求的格式
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={
            "sub": str(member.id),  # 使用会员UUID作为主题
            "user_type": "member",
            "tenant_id": str(tenant.id),
            "tenant_code": tenant.code,
            "membership_level": member.member_type.value if member.member_type else "regular"
        },
        expires_delta=access_token_expires
    )
    
    # 使用 MemberBasicRead 模型提供更完整的会员信息
    response_data = MemberLoginResponse(
        access_token=access_token,
        token_type="bearer",
        member=member,
        tenant=tenant
    )
    
    return success_response(response_data, "登录成功")


@router.post("/member/send-code", response_model=DataResponse[dict])
def send_verification_code(
    phone: str,
    tenant_code: str,
    session: Session = Depends(get_session)
):
    """发送验证码"""
    # 查找租户
    tenant_stmt = select(Tenant).where(Tenant.code == tenant_code)
    tenant = ensure_found(session.exec(tenant_stmt).first(), "租户")
    
    # TODO: 实现发送验证码逻辑
    # 这里应该：
    # 1. 生成验证码
    # 2. 存储到Redis（带过期时间）
    # 3. 调用短信服务发送
    
    # 临时返回成功
    return success_response({"sent": True}, "验证码发送成功") 
