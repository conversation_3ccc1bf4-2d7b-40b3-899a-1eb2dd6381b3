"""
API v1 路由注册
"""
from fastapi import APIRouter
from app.api.common.responses import RESOURCE_RESPONSES

# 导入管理端路由
from .admin import tenants, users, members, teachers, tags, courses, member_cards, operation_logs, fixed_schedule
from .admin import members_fixed_locks, teachers_fixed_slots, courses_config

# 导入会员端路由
from .member import profile, courses as member_courses, cards, records, fixed_courses

# 导入公共路由
from .public import auth, info

# 导入测试路由（保持不变）
from .admin import test

def create_api_router() -> APIRouter:
    """创建API路由"""
    api_router = APIRouter()
    
    # ==================== 管理端路由组 ====================
    common_responses = RESOURCE_RESPONSES

    api_router.include_router(tenants.router, prefix="/admin/tenants", tags=["管理端-租户管理"], responses=common_responses)
    api_router.include_router(users.router, prefix="/admin/users", tags=["管理端-用户管理"], responses=common_responses)
    api_router.include_router(members.router, prefix="/admin/members", tags=["管理端-会员管理"], responses=common_responses)
    api_router.include_router(members_fixed_locks.router, prefix="/admin/members/fixed-locks", tags=["管理端-会员固定课位"], responses=common_responses)

    api_router.include_router(teachers.router, prefix="/admin/teachers", tags=["管理端-教师管理"], responses=common_responses)
    api_router.include_router(teachers_fixed_slots.router, prefix="/admin/teachers/fixed-slots", tags=["管理端-教师固定时间段"], responses=common_responses)

    api_router.include_router(tags.router, prefix="/admin/tags", tags=["管理端-标签管理"], responses=common_responses)
    api_router.include_router(courses.router, prefix="/admin/courses", tags=["管理端-课程管理"], responses=common_responses)
    api_router.include_router(courses_config.router, prefix="/admin/courses/config", tags=["管理端-课程配置"], responses=common_responses)
    api_router.include_router(member_cards.router, prefix="/admin/member-cards", tags=["管理端-会员卡管理"], responses=common_responses)
    api_router.include_router(operation_logs.router, prefix="/admin/operation-logs", tags=["管理端-操作记录"], responses=common_responses)
    api_router.include_router(fixed_schedule.router, prefix="/admin/fixed-schedule", tags=["管理端-固定课排课"], responses=common_responses)
    
    # # ==================== 会员端路由组 ====================
    api_router.include_router(profile.router, prefix="/member/profile", tags=["会员端-个人中心"])
    api_router.include_router(member_courses.router, prefix="/member/courses", tags=["会员端-课程预约"])
    api_router.include_router(fixed_courses.router, prefix="/member/fixed-courses", tags=["会员端-固定课程"])
    api_router.include_router(cards.router, prefix="/member/cards", tags=["会员端-会员卡"])
    api_router.include_router(records.router, prefix="/member/records", tags=["会员端-记录查询"])
    
    # ==================== 公共路由组 ====================
    api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
    api_router.include_router(info.router, prefix="/info", tags=["公开信息"])
    
    # ==================== 测试路由（保持不变） ====================
    # api_router.include_router(test.router, prefix="/admin/test", tags=["管理员-测试api"])
    
    return api_router

# 创建路由实例
api_router = create_api_router()
