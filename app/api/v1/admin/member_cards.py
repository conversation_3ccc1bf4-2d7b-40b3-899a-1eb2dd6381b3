"""
管理端 - 会员卡管理API
"""
from fastapi import APIRouter, Depends, status, Query
from sqlmodel import Session, select, func
from typing import List, Optional

from app.db.session import get_session
from app.core.dependencies import get_user_context, UserContext
from app.api.common import (
    DataResponse,
    MessageResponse,
    ListResponse,
    PageResponse,
    success_response,
    message_response,
    list_response,
    page_response,
    ensure_found,
    PaginationParams,
    get_pagination_params
)
from app.api.common.responses import create_error_responses, AUTH_ONLY_RESPONSES, PERMISSION_ONLY_RESPONSES

# 会员卡相关导入
from app.features.member_cards.template_service import MemberCardTemplateService
from app.features.member_cards.card_service import MemberCardService
from app.features.member_cards.recharge_service import RechargeService
from app.features.member_cards.consumption_service import ConsumptionService
from app.features.member_cards.schemas import (
    # 模板相关
    MemberCardTemplateCreate, MemberCardTemplateUpdate, MemberCardTemplateRead,
    MemberCardTemplateList,
    # 卡片相关
    MemberCardCreate, MemberCardUpdate, MemberCardRead, MemberCardList, MemberCardQuery,
    # 充值相关
    RechargeRequest, RechargeResponse, BatchRechargeRequest, BatchRechargeResponse,
    # 扣费相关
    DeductionRequest, DeductionResponse,
    # 操作记录相关
    MemberCardOperationRead, MemberCardOperationQuery
)
from app.features.member_cards.models import MemberCardOperation, MemberCardOperationType
from app.features.member_cards.exceptions import MemberCardErrorCode

router = APIRouter()

# 会员卡模块的错误响应文档
CARD_ERROR_RESPONSES = create_error_responses([
    MemberCardErrorCode.MEMBER_CARD_NOT_FOUND,
    MemberCardErrorCode.MEMBER_CARD_FROZEN
])

# ==================== 会员卡模板管理 ====================

@router.get("/templates", response_model=DataResponse[List[MemberCardTemplateList]])
def get_card_templates(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡模板列表"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    templates = service.get_all_templates()
    return success_response(templates, "获取模板列表成功")

@router.get("/templates/{template_id}", response_model=DataResponse[MemberCardTemplateRead])
def get_card_template(
    template_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡模板详情"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    template = service.get_template(template_id)
    return success_response(template, "获取模板详情成功")

@router.post("/templates", response_model=DataResponse[MemberCardTemplateRead], status_code=status.HTTP_201_CREATED)
def create_card_template(
    template_data: MemberCardTemplateCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员卡模板"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    template = service.create_template(template_data, created_by=user_context.user.id)
    return success_response(template, "创建模板成功")

@router.put("/templates/{template_id}", response_model=DataResponse[MemberCardTemplateRead])
def update_card_template(
    template_id: int,
    template_data: MemberCardTemplateUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新会员卡模板"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    template = service.update_template(template_id, template_data, updated_by=user_context.user.id)
    return success_response(template, "更新模板成功")

@router.delete("/templates/{template_id}", response_model=DataResponse[dict])
def delete_card_template(
    template_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除会员卡模板"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    service.delete_template(template_id)
    return success_response({"deleted": True}, "删除模板成功")

@router.patch("/templates/{template_id}/toggle-status", response_model=DataResponse[MemberCardTemplateRead])
def toggle_template_status(
    template_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """切换会员卡模板激活状态"""
    service = MemberCardTemplateService(session, user_context.tenant_context.tenant_id)
    template = service.toggle_template_status(template_id, updated_by=user_context.user.id)
    status_text = "激活" if template.is_active else "停用"
    return success_response(template, f"模板{status_text}成功")

# ==================== 会员卡管理 ====================

@router.get("/cards", response_model=PageResponse[MemberCardList])
def get_member_cards(
    query: MemberCardQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡列表"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    cards, total = service.get_cards(query)
    return page_response(cards, total, query.page, query.size, "获取会员卡列表成功")

@router.get("/cards/{card_id}", response_model=DataResponse[MemberCardRead])
def get_member_card(
    card_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡详情"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    card = service.get_card(card_id)
    return success_response(card, "获取会员卡详情成功")

@router.post("/cards", response_model=DataResponse[MemberCardRead], status_code=status.HTTP_201_CREATED)
def create_member_card(
    card_data: MemberCardCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员卡"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    card = service.create_card(card_data, created_by=user_context.user.id)
    return success_response(card, "创建会员卡成功")

@router.put("/cards/{card_id}", response_model=DataResponse[MemberCardRead])
def update_member_card(
    card_id: int,
    card_data: MemberCardUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新会员卡"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    card = service.update_card(card_id, card_data, updated_by=user_context.user.id)
    return success_response(card, "更新会员卡成功")

@router.patch("/cards/{card_id}/freeze", response_model=DataResponse[MemberCardRead])
def freeze_member_card(
    card_id: int,
    reason: str = "管理员冻结",
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """冻结会员卡"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    card = service.freeze_card(card_id, reason, updated_by=user_context.user.id)
    return success_response(card, "冻结会员卡成功")

@router.patch("/cards/{card_id}/unfreeze", response_model=DataResponse[MemberCardRead])
def unfreeze_member_card(
    card_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """解冻会员卡"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    card = service.unfreeze_card(card_id, updated_by=user_context.user.id)
    return success_response(card, "解冻会员卡成功")

@router.patch("/cards/{card_id}/cancel", response_model=DataResponse[MemberCardRead])
def cancel_member_card(
    card_id: int,
    reason: str = "管理员注销",
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """注销会员卡"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    card = service.cancel_card(card_id, reason, updated_by=user_context.user.id)
    return success_response(card, "注销会员卡成功")

@router.get("/members/{member_id}/cards", response_model=DataResponse[List[MemberCardRead]])
def get_member_cards_by_member(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取指定会员的所有卡片"""
    service = MemberCardService(session, user_context.tenant_context.tenant_id)
    cards = service.get_member_cards(member_id)
    # 转换为响应模型
    cards_response = [MemberCardRead.model_validate(card) for card in cards]
    return success_response(cards_response, "获取会员卡片列表成功")

# ==================== 充值管理 ====================

@router.post("/cards/{card_id}/recharge", response_model=DataResponse[RechargeResponse])
def recharge_member_card(
    card_id: int,
    recharge_data: RechargeRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """会员卡充值"""
    # 设置会员卡ID
    recharge_data.member_card_id = card_id
    service = RechargeService(session, user_context.tenant_context.tenant_id)
    result = service.recharge(recharge_data, operator_id=user_context.user.id)
    return success_response(result, "充值成功")

@router.post("/recharge/batch", response_model=DataResponse[BatchRechargeResponse])
def batch_recharge_member_cards(
    batch_request: BatchRechargeRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量充值会员卡"""
    service = RechargeService(session, user_context.tenant_context.tenant_id)
    result = service.batch_recharge(batch_request, operator_id=user_context.user.id)
    return success_response(result, f"批量充值完成，成功{result.success_count}笔，失败{result.failed_count}笔")

@router.get("/cards/{card_id}/recharge-history", response_model=DataResponse[List[MemberCardOperationRead]])
def get_card_recharge_history(
    card_id: int,
    limit: int = 50,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡充值历史"""
    service = RechargeService(session, user_context.tenant_context.tenant_id)
    operations = service.get_recharge_history(card_id, limit)
    # 转换为响应模型
    operations_response = [MemberCardOperationRead.model_validate(op) for op in operations]
    return success_response(operations_response, "获取充值历史成功")

@router.get("/members/{member_id}/recharge-statistics", response_model=DataResponse[dict])
def get_member_recharge_statistics(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员充值统计"""
    service = RechargeService(session, user_context.tenant_context.tenant_id)
    statistics = service.get_member_recharge_statistics(member_id)
    return success_response(statistics, "获取充值统计成功")

@router.get("/recharge/daily-statistics", response_model=DataResponse[dict])
def get_daily_recharge_statistics(
    start_date: str,
    end_date: str,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取每日充值统计"""
    from datetime import datetime
    start_dt = datetime.fromisoformat(start_date)
    end_dt = datetime.fromisoformat(end_date)

    service = RechargeService(session, user_context.tenant_context.tenant_id)
    statistics = service.get_daily_recharge_statistics(start_dt, end_dt)
    return success_response(statistics, "获取每日充值统计成功")

# ==================== 扣费管理 ====================

@router.post("/cards/{card_id}/deduct", response_model=DataResponse[DeductionResponse])
def deduct_member_card(
    card_id: int,
    deduction_data: DeductionRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """会员卡扣费"""
    # 设置会员卡ID
    deduction_data.member_card_id = card_id
    service = RechargeService(session, user_context.tenant_context.tenant_id)
    result = service.deduct(deduction_data, operator_id=user_context.user.id)
    return success_response(result, "扣费成功")

# ==================== 操作记录查询 ====================

@router.get(
    "/operations", 
    response_model=PageResponse[MemberCardOperationRead],
    responses=AUTH_ONLY_RESPONSES,
    summary="获取会员卡操作记录",
    description="""
    分页获取会员卡操作记录列表，支持按会员ID、卡ID和操作类型筛选
    
    **必填参数：**
    - `member_id`: 会员ID
    
    **可选参数：**
    - `member_card_id`: 会员卡ID
    - `operation_types`: 操作类型列表，支持多选
    
    **分页参数：**
    - `page`: 页码，从1开始（默认1）
    - `size`: 每页大小，默认20，最大100
    """
)
def get_member_card_operations(
    member_id: Optional[int] = None,
    member_card_id: Optional[int] = None,
    operation_types: List[str] = Query(None),  # 使用Query明确指定是列表
    pagination: PaginationParams = Depends(get_pagination_params),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """分页获取会员卡操作记录"""
    service = ConsumptionService(session, user_context.tenant_context.tenant_id)
    
    # 使用现有的查询参数模型
    from app.features.member_cards.schemas import MemberCardOperationQuery
    # 构建查询参数
    query_params = MemberCardOperationQuery(
        member_id=member_id,
        member_card_id=member_card_id,
        operation_types=operation_types,
        page=pagination.page,
        size=pagination.size
    )
    
    # 使用服务层方法获取数据
    operations, total = service.get_operations(query_params)
    
    return page_response(
        operations, 
        total, 
        pagination.page, 
        pagination.size, 
        "获取会员卡操作记录成功"
    )

@router.get("/operations/{operation_id}", response_model=DataResponse[MemberCardOperationRead])
def get_card_operation_detail(
    operation_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取操作记录详情"""
    service = ConsumptionService(session, user_context.tenant_context.tenant_id)
    operation = service.get_operation(operation_id)
    operation_response = MemberCardOperationRead.model_validate(operation)
    return success_response(operation_response, "获取操作记录详情成功")

@router.get("/cards/{card_id}/consumption-history", response_model=DataResponse[List[MemberCardOperationRead]])
def get_card_consumption_history(
    card_id: int,
    limit: int = 50,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡消费历史"""
    service = ConsumptionService(session, user_context.tenant_context.tenant_id)
    operations = service.get_consumption_history(card_id, limit)
    # 转换为响应模型
    operations_response = [MemberCardOperationRead.model_validate(op) for op in operations]
    return success_response(operations_response, "获取消费历史成功")

@router.get("/members/{member_id}/consumption-statistics", response_model=DataResponse[dict])
def get_member_consumption_statistics(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员消费统计"""
    service = ConsumptionService(session, user_context.tenant_context.tenant_id)
    statistics = service.get_member_consumption_statistics(member_id)
    return success_response(statistics, "获取消费统计成功")

@router.get("/consumption/daily-statistics", response_model=DataResponse[dict])
def get_daily_consumption_statistics(
    start_date: str,
    end_date: str,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取每日消费统计"""
    from datetime import datetime
    start_dt = datetime.fromisoformat(start_date)
    end_dt = datetime.fromisoformat(end_date)

    service = ConsumptionService(session, user_context.tenant_context.tenant_id)
    statistics = service.get_daily_consumption_statistics(start_dt, end_dt)
    return success_response(statistics, "获取每日消费统计成功")

@router.get("/cards/{card_id}/operation-summary", response_model=DataResponse[dict])
def get_card_operation_summary(
    card_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员卡操作汇总统计"""
    service = ConsumptionService(session, user_context.tenant_context.tenant_id)
    summary = service.get_card_operation_summary(card_id)
    return success_response(summary, "获取操作汇总成功")
