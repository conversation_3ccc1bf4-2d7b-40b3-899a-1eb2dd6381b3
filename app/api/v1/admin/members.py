"""
管理端 - 会员管理API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session

from app.features.members.schemas import MemberCreate, MemberUpdate, MemberRead
from app.features.members.models import  MemberType, MemberStatus
from app.features.members.service import MemberService
from app.features.members.exceptions import MemberErrorCode
from app.db.session import get_session
from app.core.context import UserContext
from app.core.dependencies import get_user_context
from app.api.common import (
    DataResponse,
    MessageResponse,
    ListResponse,
    PageResponse,
    success_response,
    message_response,
    list_response,
    page_response,
    ensure_found,
    ensure_success,
    PaginationParams,
    get_pagination_params
)
from app.api.common.responses import create_error_responses, ErrorResponse, AUTH_ONLY_RESPONSES, PERMISSION_ONLY_RESPONSES, RESOURCE_RESPONSES

router = APIRouter()

# 会员模块的错误响应文档
MEMBER_ERROR_RESPONSES = create_error_responses([
    MemberErrorCode.PHONE_EXISTS,
    MemberErrorCode.EMAIL_EXISTS,
    MemberErrorCode.ACCOUNT_FROZEN
])

@router.post(
    "/", 
    response_model=DataResponse[MemberRead], 
    status_code=status.HTTP_201_CREATED,
    responses=MEMBER_ERROR_RESPONSES,
    summary="创建会员",
    description="""
    创建新会员账户
    
    **可能的错误码：**
    - `MEMBER_PHONE_EXISTS`: 手机号已存在
    - `MEMBER_EMAIL_EXISTS`: 邮箱已存在
    - `VALIDATION_ERROR`: 数据验证失败
    """
)
def create_member(
    member_data: MemberCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)

    member = member_service.create_member(member_data)
    # 获取包含统计信息的完整数据返回
    member_with_stats = member_service.get_member_with_stats(member.id)
    return success_response(member_with_stats, "会员创建成功")


@router.get(
    "/", 
    response_model=PageResponse[MemberRead],
    summary="获取会员列表",
    description="""
    分页获取会员列表
    
    **分页参数：**
    - `page`: 页码，从1开始（默认1）
    - `size`: 每页大小，默认20，最大100
    
    **筛选参数：**
    - `member_type`: 会员类型
    - `member_status`: 会员状态
    - `agent_id`: 代理ID
    - `search_keyword`: 搜索关键词
    """
)
def get_members(
    pagination: PaginationParams = Depends(get_pagination_params),
    member_type: Optional[MemberType] = None,
    member_status: Optional[MemberStatus] = None,
    agent_id: Optional[int] = None,
    search_keyword: Optional[str] = None,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """分页获取会员列表"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    members = member_service.get_members_with_stats(
        skip=pagination.skip,
        limit=pagination.limit,
        member_type=member_type,
        member_status=member_status,
        agent_id=agent_id,
        search_keyword=search_keyword
    )
    
    total = member_service.count_members(
        member_type=member_type,
        member_status=member_status,
        agent_id=agent_id
    )
    
    return page_response(members, total, pagination.page, pagination.size, "获取会员列表成功")


@router.get("/count", response_model=DataResponse[int])
def count_members(
    member_type: Optional[MemberType] = None,
    member_status: Optional[MemberStatus] = None,
    agent_id: Optional[int] = None,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """统计会员数量"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    count = member_service.count_members(
        member_type=member_type,
        member_status=member_status,
        agent_id=agent_id
    )
    return success_response(count, "获取会员数量成功")


@router.get("/{member_id}", response_model=DataResponse[MemberRead])
def get_member(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员详情"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    member = member_service.get_member_with_stats(member_id)
    member = ensure_found(member, "会员")
    return success_response(member, "获取会员详情成功")


@router.post("/{member_id}/update", response_model=DataResponse[MemberRead], responses=RESOURCE_RESPONSES)
def update_member(
    member_id: int,
    member_data: MemberUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新会员信息"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    updated_member = member_service.update_member(member_id, member_data)
    updated_member = ensure_found(updated_member, "会员")
    # 获取包含统计信息的完整数据返回
    member_with_stats = member_service.get_member_with_stats(member_id)
    return success_response(member_with_stats, "会员信息更新成功")


@router.post("/{member_id}/update-status", response_model=DataResponse[MemberRead])
def update_member_status(
    member_id: int,
    status: MemberStatus,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新会员状态"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    updated_member = member_service.update_member_status(member_id, status)
    updated_member = ensure_found(updated_member, "会员")
    return success_response(updated_member, "会员状态更新成功")


@router.post("/{member_id}/update-stats", response_model=DataResponse[MemberRead])
def update_member_stats(
    member_id: int,
    class_completed: bool = False,
    class_cancelled: bool = False,
    class_no_show: bool = False,
    amount_spent: float = 0.0,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新会员统计信息"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    updated_member = member_service.update_member_stats(
        member_id,
        class_completed=class_completed,
        class_cancelled=class_cancelled,
        class_no_show=class_no_show,
        amount_spent=int(amount_spent)  # 确保转换为整数
    )

    updated_member = ensure_found(updated_member, "会员")
    # 获取包含最新统计信息的完整数据返回
    member_with_stats = member_service.get_member_with_stats(member_id)
    return success_response(member_with_stats, "会员统计信息更新成功")


@router.post("/{member_id}/delete", response_model=MessageResponse)
def delete_member(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除会员"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    success = member_service.delete_member(member_id)
    ensure_found(success, "会员不存在")
    return message_response("会员删除成功")


@router.post("/{member_id}/deactivate", response_model=MessageResponse, responses=RESOURCE_RESPONSES)
def deactivate_member(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """停用会员"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    updated_member = member_service.update_member_status(member_id, MemberStatus.FROZEN)
    updated_member = ensure_found(updated_member, "会员")
    return success_response(message="会员已停用")


@router.post("/{member_id}/activate", response_model=MessageResponse, responses=RESOURCE_RESPONSES)
def activate_member(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """激活会员"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    member_service = MemberService(session, tenant_id)
    
    updated_member = member_service.update_member_status(member_id, MemberStatus.ACTIVE)
    updated_member = ensure_found(updated_member, "会员")
    return success_response(message="会员已激活") 
