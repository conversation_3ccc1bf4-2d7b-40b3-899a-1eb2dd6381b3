"""操作记录管理API"""

from fastapi import APIRouter, Depends, Query
from typing import Optional, List
from datetime import datetime

from app.api.common.responses import DataResponse, PageResponse
from app.core.dependencies import get_current_user
from app.features.users.models import User
from app.db.session import Session, get_session

from app.features.courses.operations.service import (
    ScheduledClassOperationLogService,
    TeacherFixedSlotOperationLogService,
    MemberFixedLockOperationLogService,
    OperationLogStatisticsService
)
from app.features.courses.operations.schemas import (
    ScheduledClassOperationLogResponse,
    ScheduledClassOperationLogQuery,
    TeacherFixedSlotOperationLogResponse,
    TeacherFixedSlotOperationLogQuery,
    MemberFixedLockOperationLogResponse,
    MemberFixedLockOperationLogQuery
)
from app.features.courses.operations.models import (
    ClassOperationType, TeacherSlotOperationType, MemberLockOperationType,
    OperationStatus
)

router = APIRouter(tags=["操作记录管理"])


# ==================== 课程操作记录API ====================

@router.get("/class-operations", response_model=PageResponse[ScheduledClassOperationLogResponse])
async def get_class_operation_logs(
    scheduled_class_id: Optional[int] = Query(None, description="课程ID"),
    operation_type: Optional[ClassOperationType] = Query(None, description="操作类型"),
    operation_status: Optional[OperationStatus] = Query(None, description="操作状态"),
    teacher_id: Optional[int] = Query(None, description="教师ID"),
    member_id: Optional[int] = Query(None, description="会员ID"),
    operator_id: Optional[int] = Query(None, description="操作人ID"),
    operator_type: Optional[str] = Query(None, description="操作人类型"),
    class_datetime_from: Optional[datetime] = Query(None, description="课程时间范围-起始"),
    class_datetime_to: Optional[datetime] = Query(None, description="课程时间范围-结束"),
    created_from: Optional[datetime] = Query(None, description="创建时间范围-起始"),
    created_to: Optional[datetime] = Query(None, description="创建时间范围-结束"),
    operation_description: Optional[str] = Query(None, description="操作描述（模糊搜索）"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取课程操作记录列表"""
    service = ScheduledClassOperationLogService(session, current_user.tenant_id)
    
    query = ScheduledClassOperationLogQuery(
        scheduled_class_id=scheduled_class_id,
        operation_type=operation_type,
        operation_status=operation_status,
        teacher_id=teacher_id,
        member_id=member_id,
        operator_id=operator_id,
        operator_type=operator_type,
        class_datetime_from=class_datetime_from,
        class_datetime_to=class_datetime_to,
        created_from=created_from,
        created_to=created_to,
        operation_description=operation_description,
        page=page,
        size=size,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    logs, total = service.query_operation_logs(query)
    
    return PageResponse(
        items=[ScheduledClassOperationLogResponse.model_validate(log) for log in logs],
        total=total,
        page=page,
        size=size
    )


@router.get("/class-operations/{log_id}", response_model=DataResponse[ScheduledClassOperationLogResponse])
async def get_class_operation_log(
    log_id: int,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取课程操作记录详情"""
    service = ScheduledClassOperationLogService(session, current_user.tenant_id)
    log = service.get_operation_log(log_id)
    
    return DataResponse(data=ScheduledClassOperationLogResponse.model_validate(log))


@router.get("/class-operations/by-class/{class_id}", response_model=DataResponse[List[ScheduledClassOperationLogResponse]])
async def get_class_operation_logs_by_class(
    class_id: int,
    limit: int = Query(50, ge=1, le=200, description="限制数量"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取指定课程的操作记录"""
    service = ScheduledClassOperationLogService(session, current_user.tenant_id)
    logs = service.get_class_operation_logs(class_id, limit)
    
    return DataResponse(data=[ScheduledClassOperationLogResponse.model_validate(log) for log in logs])


# ==================== 教师固定时间段操作记录API ====================

@router.get("/teacher-slot-operations", response_model=PageResponse[TeacherFixedSlotOperationLogResponse])
async def get_teacher_slot_operation_logs(
    teacher_fixed_slot_id: Optional[int] = Query(None, description="教师固定时间段ID"),
    operation_type: Optional[TeacherSlotOperationType] = Query(None, description="操作类型"),
    operation_status: Optional[OperationStatus] = Query(None, description="操作状态"),
    teacher_id: Optional[int] = Query(None, description="教师ID"),
    operator_id: Optional[int] = Query(None, description="操作人ID"),
    operator_type: Optional[str] = Query(None, description="操作人类型"),
    weekday: Optional[int] = Query(None, ge=1, le=7, description="星期几"),
    created_from: Optional[datetime] = Query(None, description="创建时间范围-起始"),
    created_to: Optional[datetime] = Query(None, description="创建时间范围-结束"),
    operation_description: Optional[str] = Query(None, description="操作描述（模糊搜索）"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取教师固定时间段操作记录列表"""
    service = TeacherFixedSlotOperationLogService(session, current_user.tenant_id)
    
    query = TeacherFixedSlotOperationLogQuery(
        teacher_fixed_slot_id=teacher_fixed_slot_id,
        operation_type=operation_type,
        operation_status=operation_status,
        teacher_id=teacher_id,
        operator_id=operator_id,
        operator_type=operator_type,
        weekday=weekday,
        created_from=created_from,
        created_to=created_to,
        operation_description=operation_description,
        page=page,
        size=size,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    logs, total = service.query_operation_logs(query)
    
    return PageResponse(
        items=[TeacherFixedSlotOperationLogResponse.model_validate(log) for log in logs],
        total=total,
        page=page,
        size=size
    )


@router.get("/teacher-slot-operations/{log_id}", response_model=DataResponse[TeacherFixedSlotOperationLogResponse])
async def get_teacher_slot_operation_log(
    log_id: int,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取教师固定时间段操作记录详情"""
    service = TeacherFixedSlotOperationLogService(session, current_user.tenant_id)
    log = service.get_operation_log(log_id)
    
    return DataResponse(data=TeacherFixedSlotOperationLogResponse.model_validate(log))


@router.get("/teacher-slot-operations/by-teacher/{teacher_id}", response_model=DataResponse[List[TeacherFixedSlotOperationLogResponse]])
async def get_teacher_slot_operation_logs_by_teacher(
    teacher_id: int,
    limit: int = Query(50, ge=1, le=200, description="限制数量"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取指定教师的时间段操作记录"""
    service = TeacherFixedSlotOperationLogService(session, current_user.tenant_id)
    logs = service.get_teacher_operation_logs(teacher_id, limit)
    
    return DataResponse(data=[TeacherFixedSlotOperationLogResponse.model_validate(log) for log in logs])


# ==================== 会员固定位锁定操作记录API ====================

@router.get("/member-lock-operations", response_model=PageResponse[MemberFixedLockOperationLogResponse])
async def get_member_lock_operation_logs(
    member_fixed_slot_lock_id: Optional[int] = Query(None, description="会员固定位锁定ID"),
    operation_type: Optional[MemberLockOperationType] = Query(None, description="操作类型"),
    operation_status: Optional[OperationStatus] = Query(None, description="操作状态"),
    member_id: Optional[int] = Query(None, description="会员ID"),
    teacher_id: Optional[int] = Query(None, description="教师ID"),
    operator_id: Optional[int] = Query(None, description="操作人ID"),
    operator_type: Optional[str] = Query(None, description="操作人类型"),
    weekday: Optional[int] = Query(None, ge=1, le=7, description="星期几"),
    created_from: Optional[datetime] = Query(None, description="创建时间范围-起始"),
    created_to: Optional[datetime] = Query(None, description="创建时间范围-结束"),
    operation_description: Optional[str] = Query(None, description="操作描述（模糊搜索）"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取会员固定位锁定操作记录列表"""
    service = MemberFixedLockOperationLogService(session, current_user.tenant_id)
    
    query = MemberFixedLockOperationLogQuery(
        member_fixed_slot_lock_id=member_fixed_slot_lock_id,
        operation_type=operation_type,
        operation_status=operation_status,
        member_id=member_id,
        teacher_id=teacher_id,
        operator_id=operator_id,
        operator_type=operator_type,
        weekday=weekday,
        created_from=created_from,
        created_to=created_to,
        operation_description=operation_description,
        page=page,
        size=size,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    logs, total = service.query_operation_logs(query)
    
    return PageResponse(
        items=[MemberFixedLockOperationLogResponse.model_validate(log) for log in logs],
        total=total,
        page=page,
        size=size
    )


@router.get("/member-lock-operations/{log_id}", response_model=DataResponse[MemberFixedLockOperationLogResponse])
async def get_member_lock_operation_log(
    log_id: int,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取会员固定位锁定操作记录详情"""
    service = MemberFixedLockOperationLogService(session, current_user.tenant_id)
    log = service.get_operation_log(log_id)
    
    return DataResponse(data=MemberFixedLockOperationLogResponse.model_validate(log))


@router.get("/member-lock-operations/by-member/{member_id}", response_model=DataResponse[List[MemberFixedLockOperationLogResponse]])
async def get_member_lock_operation_logs_by_member(
    member_id: int,
    limit: int = Query(50, ge=1, le=200, description="限制数量"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取指定会员的锁定操作记录"""
    service = MemberFixedLockOperationLogService(session, current_user.tenant_id)
    logs = service.get_member_operation_logs(member_id, limit)
    
    return DataResponse(data=[MemberFixedLockOperationLogResponse.model_validate(log) for log in logs])


# ==================== 操作记录统计API ====================

@router.get("/statistics/class-operations", response_model=DataResponse[dict])
async def get_class_operation_statistics(
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取课程操作统计"""
    service = OperationLogStatisticsService(session, current_user.tenant_id)
    statistics = service.get_class_operation_statistics(start_date, end_date)

    return DataResponse(data=statistics)


@router.get("/statistics/teacher-slot-operations", response_model=DataResponse[dict])
async def get_teacher_slot_operation_statistics(
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取教师时间段操作统计"""
    service = OperationLogStatisticsService(session, current_user.tenant_id)
    statistics = service.get_teacher_slot_operation_statistics(start_date, end_date)

    return DataResponse(data=statistics)


@router.get("/statistics/member-lock-operations", response_model=DataResponse[dict])
async def get_member_lock_operation_statistics(
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取会员锁定操作统计"""
    service = OperationLogStatisticsService(session, current_user.tenant_id)
    statistics = service.get_member_lock_operation_statistics(start_date, end_date)

    return DataResponse(data=statistics)


@router.get("/statistics/comprehensive", response_model=DataResponse[dict])
async def get_comprehensive_statistics(
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取综合操作统计"""
    service = OperationLogStatisticsService(session, current_user.tenant_id)
    statistics = service.get_comprehensive_statistics(start_date, end_date)

    return DataResponse(data=statistics)


@router.get("/statistics/daily-trends", response_model=DataResponse[dict])
async def get_daily_operation_trends(
    start_date: datetime = Query(..., description="开始时间"),
    end_date: datetime = Query(..., description="结束时间"),
    operation_type: str = Query("all", pattern="^(all|class|teacher_slot|member_lock)$", description="操作类型"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取每日操作趋势"""
    service = OperationLogStatisticsService(session, current_user.tenant_id)
    trends = service.get_daily_operation_trends(start_date, end_date, operation_type)

    return DataResponse(data=trends)


@router.get("/statistics/operators", response_model=DataResponse[dict])
async def get_operator_statistics(
    start_date: Optional[datetime] = Query(None, description="开始时间"),
    end_date: Optional[datetime] = Query(None, description="结束时间"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取操作人统计"""
    service = OperationLogStatisticsService(session, current_user.tenant_id)
    statistics = service.get_operator_statistics(start_date, end_date)

    return DataResponse(data=statistics)
