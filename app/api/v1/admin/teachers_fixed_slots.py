"""教师固定时间段API路由"""
from typing import List, Optional
from fastapi import APIRouter, Depends, status, Query
from sqlmodel import Session, SQLModel, Field

from app.features.teachers.fixed_slots_models import Weekday
from app.features.teachers.fixed_slots_schemas import (
    TeacherFixedSlotCreate, TeacherFixedSlotUpdate, TeacherFixedSlotResponse,
    TeacherFixedSlotList, TeacherFixedSlotQuery, TeacherFixedSlotBatchCreate,
    TeacherFixedSlotBatchUpdate, TeacherFixedSlotBatchDelete, AvailableTimesQuery,
    TimeSlotConflictCheck, TeacherFixedSlotStats
)
from app.features.teachers.fixed_slots_service import TeacherFixedSlotService
from app.features.teachers.fixed_slots_exceptions import TeacherFixedSlotErrorCode
from app.db.session import get_session
from app.core.context import UserContext
from app.core.dependencies import get_user_context
from app.api.common import (
    DataResponse,
    MessageResponse,
    ListResponse,
    PageResponse,
    success_response,
    message_response,
    list_response,
    page_response,
    ensure_found,
    ensure_success
)
from app.api.common.responses import create_error_responses, ErrorResponse, AUTH_ONLY_RESPONSES, PERMISSION_ONLY_RESPONSES, RESOURCE_RESPONSES

router = APIRouter()


def get_operator_name(user_context: UserContext) -> str:
    """获取操作人姓名 - 使用用户名"""
    return user_context.user.username

# 固定时间段模块的错误响应文档
FIXED_SLOT_ERROR_RESPONSES = create_error_responses([
    TeacherFixedSlotErrorCode.TIME_SLOT_CONFLICT,
    TeacherFixedSlotErrorCode.TEACHER_NOT_FOUND,
    TeacherFixedSlotErrorCode.INVALID_TIME_RANGE,
    TeacherFixedSlotErrorCode.BATCH_OPERATION_FAILED
])


# ==================== 特殊功能接口（放在前面避免路由冲突） ====================

@router.post(
    "/check-conflict",
    response_model=DataResponse[bool],
    summary="检测时间段冲突",
    description="""
    检测指定时间段是否与现有时间段冲突

    **功能说明：**
    - 用于在创建或更新时间段前预检测冲突
    - 支持排除指定ID（用于更新时检测）
    - 返回true表示有冲突，false表示无冲突
    """
)
def check_time_conflict(
    conflict_check: TimeSlotConflictCheck,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """检测时间段冲突"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    has_conflict = service.check_time_conflict(conflict_check)

    message = "存在时间冲突" if has_conflict else "无时间冲突"
    return success_response(has_conflict, message)


@router.post(
    "/available-times",
    response_model=ListResponse[TeacherFixedSlotList],
    summary="获取教师可用时间段",
    description="""
    获取教师的可用时间段列表

    **功能说明：**
    - 支持按可用性和可见性筛选
    - 支持按星期和时间范围筛选
    - 按星期和时间排序返回
    """
)
def get_available_times(
    query: AvailableTimesQuery,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师可用时间段"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    available_slots = service.get_available_times(query)

    # 转换为响应格式
    from app.features.teachers.fixed_slots_models import get_weekday_name
    slot_list = []
    for slot in available_slots:
        slot_dict = slot.model_dump()
        slot_dict['weekday_name'] = get_weekday_name(slot.weekday)
        slot_list.append(TeacherFixedSlotList(**slot_dict))

    return list_response(slot_list, message=f"获取到{len(slot_list)}个可用时间段")


@router.get(
    "/statistics",
    response_model=DataResponse[TeacherFixedSlotStats],
    summary="获取固定时间段统计信息",
    description="获取教师固定时间段的统计信息，包括总数、可用数、按星期分布等"
)
def get_slot_statistics(
    teacher_id: Optional[int] = Query(default=None, description="教师ID，不指定则统计所有教师"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取固定时间段统计信息"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    stats = service.get_slot_statistics(teacher_id)

    return success_response(stats, "获取统计信息成功")


class BatchQueryRequest(SQLModel):
    """批量查询教师时间表请求模型"""
    teacher_ids: List[int] = Field(description="教师ID列表")
    weekdays: Optional[List[Weekday]] = Field(default=None, description="星期列表")
    start_time_from: Optional[str] = Field(default=None, description="开始时间范围-起始（HH:MM格式）")
    start_time_to: Optional[str] = Field(default=None, description="开始时间范围-结束（HH:MM格式）")
    only_available: bool = Field(default=True, description="仅返回可用时间段")
    only_visible: bool = Field(default=True, description="仅返回对会员可见的时间段")


@router.post(
    "/teachers/batch-query",
    response_model=DataResponse[dict],
    summary="批量查询多个教师的固定时间表",
    description="""
    批量查询多个教师的固定时间表

    **功能说明：**
    - 支持同时查询多个教师的时间安排
    - 可以按星期、时间范围等条件筛选
    - 返回按教师分组的时间表数据

    **返回格式：**
    ```json
    {
      "teacher_1": {
        "teacher_info": {...},
        "slots": [...]
      },
      "teacher_2": {
        "teacher_info": {...},
        "slots": [...]
      }
    }
    ```
    """
)
def batch_query_teacher_schedules(
    request: BatchQueryRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量查询教师固定时间表"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 处理时间参数
    start_time_from_obj, start_time_to_obj = service.parse_time_parameters(
        request.start_time_from, request.start_time_to
    )

    # 批量查询教师时间表
    result = {}
    from app.features.teachers.service import TeacherService
    teacher_service = TeacherService(session, tenant_id)

    for teacher_id in request.teacher_ids:
        # 获取教师信息
        teacher = teacher_service.get_teacher(teacher_id)
        if not teacher:
            continue

        # 构建查询条件
        query = AvailableTimesQuery(
            teacher_id=teacher_id,
            weekdays=request.weekdays,
            start_time_from=start_time_from_obj,
            start_time_to=start_time_to_obj,
            only_available=request.only_available,
            only_visible=request.only_visible
        )

        # 获取时间段
        slots = service.get_available_times(query)

        # 转换为响应格式
        from app.features.teachers.fixed_slots_models import get_weekday_name
        slot_list = []
        for slot in slots:
            slot_dict = slot.model_dump()
            slot_dict['weekday_name'] = get_weekday_name(slot.weekday)
            slot_list.append(slot_dict)

        result[f"teacher_{teacher_id}"] = {
            "teacher_info": {
                "id": teacher.id,
                "name": teacher.name,
                "display_code": teacher.display_code,
                "category": teacher.teacher_category,
                "region": teacher.region
            },
            "slots": slot_list
        }

    return success_response(result, f"批量查询{len(result)}个教师的时间表成功")


@router.get(
    "/time-slots/by-time-range",
    response_model=ListResponse[dict],
    summary="按时间范围查询所有教师的可用时间段",
    description="""
    按时间范围查询所有教师的可用时间段

    **功能说明：**
    - 查询指定时间范围内所有教师的可用时间段
    - 支持按教师分类、区域等条件筛选
    - 按时间排序返回，便于排课使用
    """
)
def get_slots_by_time_range(
    start_time_from: str = Query(description="开始时间范围-起始（HH:MM格式）"),
    start_time_to: str = Query(description="开始时间范围-结束（HH:MM格式）"),
    weekdays: Optional[str] = Query(default=None, description="星期列表，逗号分隔（1-7）"),
    teacher_category: Optional[str] = Query(default=None, description="教师分类筛选"),
    teacher_region: Optional[str] = Query(default=None, description="教师区域筛选"),
    only_available: bool = Query(default=True, description="仅返回可用时间段"),
    only_visible: bool = Query(default=True, description="仅返回对会员可见的时间段"),
    limit: int = Query(default=100, ge=1, le=500, description="返回数量限制"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """按时间范围查询可用时间段"""
    from datetime import time
    from sqlmodel import select, and_
    from app.features.teachers.models import Teacher

    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 处理时间参数
    start_time_from_obj, start_time_to_obj = service.validate_time_range_format(start_time_from, start_time_to)

    # 处理星期参数
    weekday_list = service.parse_weekdays_parameter(weekdays)

    # 构建教师查询条件
    teacher_conditions = []
    if teacher_category:
        from app.features.teachers.models import TeacherCategory
        try:
            category = TeacherCategory(teacher_category)
            teacher_conditions.append(Teacher.teacher_category == category)
        except ValueError:
            pass

    if teacher_region:
        from app.features.teachers.models import TeacherRegion
        try:
            region = TeacherRegion(teacher_region)
            teacher_conditions.append(Teacher.region == region)
        except ValueError:
            pass

    # 查询符合条件的教师
    teacher_statement = select(Teacher)
    if teacher_conditions:
        teacher_statement = teacher_statement.where(and_(*teacher_conditions))

    teachers = session.exec(teacher_statement).all()

    # 收集所有时间段
    all_slots = []
    for teacher in teachers:
        query = AvailableTimesQuery(
            teacher_id=teacher.id,
            weekdays=weekday_list,
            start_time_from=start_time_from_obj,
            start_time_to=start_time_to_obj,
            only_available=only_available,
            only_visible=only_visible
        )

        slots = service.get_available_times(query)

        # 添加教师信息到时间段
        from app.features.teachers.fixed_slots_models import get_weekday_name
        for slot in slots:
            slot_dict = slot.model_dump()
            slot_dict['weekday_name'] = get_weekday_name(slot.weekday)
            slot_dict['teacher_name'] = teacher.name
            slot_dict['teacher_display_code'] = teacher.display_code
            slot_dict['teacher_category'] = teacher.teacher_category
            slot_dict['teacher_region'] = teacher.region
            all_slots.append(slot_dict)

    # 按时间排序
    all_slots.sort(key=lambda x: (x['weekday'], x['start_time']))

    # 限制返回数量
    if len(all_slots) > limit:
        all_slots = all_slots[:limit]

    return list_response(all_slots, f"查询到{len(all_slots)}个可用时间段")


@router.get(
    "/time-slots/conflicts",
    response_model=ListResponse[dict],
    summary="查询时间段冲突情况",
    description="""
    查询系统中的时间段冲突情况

    **功能说明：**
    - 检测同一教师的时间段重叠情况
    - 返回冲突的时间段详情
    - 用于数据清理和问题排查
    """
)
def get_time_slot_conflicts(
    teacher_id: Optional[int] = Query(default=None, description="教师ID，不指定则检查所有教师"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """查询时间段冲突情况"""
    from sqlmodel import select, and_
    from app.features.teachers.fixed_slots_models import TeacherFixedSlot, check_time_overlap, get_weekday_name, TimeValidationConfig

    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 构建查询条件
    statement = select(TeacherFixedSlot)
    if teacher_id:
        statement = statement.where(TeacherFixedSlot.teacher_id == teacher_id)

    statement = statement.order_by(
        TeacherFixedSlot.teacher_id.asc(),
        TeacherFixedSlot.weekday.asc(),
        TeacherFixedSlot.start_time.asc()
    )

    all_slots = session.exec(statement).all()

    # 检测冲突
    conflicts = []
    for i, slot1 in enumerate(all_slots):
        for j, slot2 in enumerate(all_slots[i+1:], i+1):
            # 只检查同一教师同一星期的时间段
            if (slot1.teacher_id == slot2.teacher_id and
                slot1.weekday == slot2.weekday):

                duration1 = slot1.duration_minutes or TimeValidationConfig.DEFAULT_DURATION_MINUTES
                duration2 = slot2.duration_minutes or TimeValidationConfig.DEFAULT_DURATION_MINUTES

                if check_time_overlap(slot1.start_time, duration1, slot2.start_time, duration2):
                    conflicts.append({
                        "teacher_id": slot1.teacher_id,
                        "weekday": slot1.weekday,
                        "weekday_name": get_weekday_name(slot1.weekday),
                        "slot1": {
                            "id": slot1.id,
                            "start_time": slot1.start_time,
                            "duration_minutes": duration1,
                            "is_available": slot1.is_available
                        },
                        "slot2": {
                            "id": slot2.id,
                            "start_time": slot2.start_time,
                            "duration_minutes": duration2,
                            "is_available": slot2.is_available
                        }
                    })

    return list_response(conflicts, f"发现{len(conflicts)}个时间段冲突")


# ==================== 时间段可用性管理接口 ====================

class AvailabilityUpdateRequest(SQLModel):
    """时间段可用性更新请求模型"""
    slot_ids: List[int] = Field(description="时间段ID列表")
    is_available: Optional[bool] = Field(default=None, description="是否可用")
    is_visible_to_members: Optional[bool] = Field(default=None, description="是否对会员可见")


class TeacherAvailabilityUpdateRequest(SQLModel):
    """教师时间段可用性批量更新请求模型"""
    teacher_id: int = Field(description="教师ID")
    weekdays: Optional[List[Weekday]] = Field(default=None, description="星期列表，不指定则更新所有星期")
    start_time_from: Optional[str] = Field(default=None, description="开始时间范围-起始（HH:MM格式）")
    start_time_to: Optional[str] = Field(default=None, description="开始时间范围-结束（HH:MM格式）")
    is_available: Optional[bool] = Field(default=None, description="是否可用")
    is_visible_to_members: Optional[bool] = Field(default=None, description="是否对会员可见")


@router.post(
    "/availability/batch-update",
    response_model=DataResponse[dict],
    summary="批量更新时间段可用性",
    description="""
    批量更新指定时间段的可用性设置

    **功能说明：**
    - 支持批量设置时间段的可用性
    - 支持批量设置时间段对会员的可见性
    - 可以同时更新多个时间段的状态

    **返回信息：**
    - 更新成功的时间段数量
    - 更新失败的时间段数量和原因
    """
)
def batch_update_availability(
    request: AvailabilityUpdateRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量更新时间段可用性"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 验证至少有一个更新字段
    service.validate_batch_update_fields(request.is_available, request.is_visible_to_members)

    # 构建更新数据
    updates = []
    for slot_id in request.slot_ids:
        update_data = {"id": slot_id}
        if request.is_available is not None:
            update_data["is_available"] = request.is_available
        if request.is_visible_to_members is not None:
            update_data["is_visible_to_members"] = request.is_visible_to_members
        updates.append(update_data)

    # 执行批量更新
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotBatchUpdate
    batch_update = TeacherFixedSlotBatchUpdate(updates=updates)
    updated_slots = service.batch_update_slots(batch_update)

    result = {
        "total_requested": len(request.slot_ids),
        "updated_count": len(updated_slots),
        "failed_count": len(request.slot_ids) - len(updated_slots),
        "updated_slot_ids": [slot.id for slot in updated_slots]
    }

    return success_response(result, f"批量更新完成，成功更新{len(updated_slots)}个时间段")


@router.post(
    "/availability/teacher-batch-update",
    response_model=DataResponse[dict],
    summary="按教师批量更新时间段可用性",
    description="""
    按教师和条件批量更新时间段可用性

    **功能说明：**
    - 支持按教师ID批量更新时间段
    - 可以按星期、时间范围等条件筛选
    - 适用于教师请假、调整可用时间等场景

    **使用场景：**
    - 教师请假：将某个时间段设为不可用
    - 教师调整：批量修改可见性设置
    - 管理员操作：批量调整教师时间段状态
    """
)
def batch_update_teacher_availability(
    request: TeacherAvailabilityUpdateRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """按教师批量更新时间段可用性"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 验证至少有一个更新字段
    service.validate_batch_update_fields(request.is_available, request.is_visible_to_members)

    # 处理时间参数
    start_time_from_obj, start_time_to_obj = service.parse_time_parameters(
        request.start_time_from, request.start_time_to
    )

    # 构建查询条件，找到符合条件的时间段
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotQuery
    query_params = TeacherFixedSlotQuery(
        teacher_id=request.teacher_id,
        weekday=None,  # 会在下面处理
        start_time_from=start_time_from_obj,
        start_time_to=start_time_to_obj,
        page=1,
        size=100  # 设置最大允许值来获取所有匹配的记录
    )

    # 如果指定了星期，需要分别查询每个星期
    all_slots = []
    if request.weekdays:
        for weekday in request.weekdays:
            query_params.weekday = weekday
            slots, _ = service.get_slots(query_params)
            all_slots.extend(slots)
    else:
        slots, _ = service.get_slots(query_params)
        all_slots = slots

    if not all_slots:
        return success_response({
            "total_found": 0,
            "updated_count": 0,
            "failed_count": 0,
            "updated_slot_ids": []
        }, "未找到符合条件的时间段")

    # 构建批量更新数据
    updates = []
    for slot in all_slots:
        update_data = {"id": slot.id}
        if request.is_available is not None:
            update_data["is_available"] = request.is_available
        if request.is_visible_to_members is not None:
            update_data["is_visible_to_members"] = request.is_visible_to_members
        updates.append(update_data)

    # 执行批量更新
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotBatchUpdate
    batch_update = TeacherFixedSlotBatchUpdate(updates=updates)
    updated_slots = service.batch_update_slots(batch_update)

    result = {
        "total_found": len(all_slots),
        "updated_count": len(updated_slots),
        "failed_count": len(all_slots) - len(updated_slots),
        "updated_slot_ids": [slot.id for slot in updated_slots]
    }

    return success_response(result, f"按教师批量更新完成，成功更新{len(updated_slots)}个时间段")


@router.get(
    "/availability/statistics",
    response_model=DataResponse[dict],
    summary="获取时间段可用性统计",
    description="""
    获取时间段可用性的统计信息

    **功能说明：**
    - 统计各种状态的时间段数量
    - 按教师、星期等维度分组统计
    - 提供可用性分析数据

    **统计维度：**
    - 总体统计：总数、可用数、可见数
    - 按教师统计：每个教师的时间段分布
    - 按星期统计：每个星期的时间段分布
    - 按时间段统计：不同时间段的分布
    """
)
def get_availability_statistics(
    teacher_id: Optional[int] = Query(default=None, description="教师ID，不指定则统计所有教师"),
    include_teacher_breakdown: bool = Query(default=False, description="是否包含按教师的详细统计"),
    include_weekday_breakdown: bool = Query(default=True, description="是否包含按星期的详细统计"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取时间段可用性统计"""
    from sqlmodel import select, func, and_
    from app.features.teachers.fixed_slots_models import TeacherFixedSlot, get_weekday_name

    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 基础查询条件
    base_conditions = []
    if teacher_id:
        base_conditions.append(TeacherFixedSlot.teacher_id == teacher_id)

    # 总体统计
    total_statement = select(func.count(TeacherFixedSlot.id))
    available_statement = select(func.count(TeacherFixedSlot.id)).where(TeacherFixedSlot.is_available == True)
    visible_statement = select(func.count(TeacherFixedSlot.id)).where(TeacherFixedSlot.is_visible_to_members == True)
    both_statement = select(func.count(TeacherFixedSlot.id)).where(
        and_(TeacherFixedSlot.is_available == True, TeacherFixedSlot.is_visible_to_members == True)
    )

    if base_conditions:
        condition = and_(*base_conditions)
        total_statement = total_statement.where(condition)
        available_statement = available_statement.where(and_(condition, TeacherFixedSlot.is_available == True))
        visible_statement = visible_statement.where(and_(condition, TeacherFixedSlot.is_visible_to_members == True))
        both_statement = both_statement.where(and_(condition, TeacherFixedSlot.is_available == True, TeacherFixedSlot.is_visible_to_members == True))

    total_count = session.exec(total_statement).one()
    available_count = session.exec(available_statement).one()
    visible_count = session.exec(visible_statement).one()
    both_count = session.exec(both_statement).one()

    result = {
        "overall": {
            "total_slots": total_count,
            "available_slots": available_count,
            "visible_slots": visible_count,
            "available_and_visible_slots": both_count,
            "unavailable_slots": total_count - available_count,
            "invisible_slots": total_count - visible_count
        }
    }

    # 按星期统计
    if include_weekday_breakdown:
        weekday_stats = {}
        for weekday in Weekday:
            weekday_condition = [TeacherFixedSlot.weekday == weekday]
            if base_conditions:
                weekday_condition.extend(base_conditions)

            weekday_total = session.exec(
                select(func.count(TeacherFixedSlot.id)).where(and_(*weekday_condition))
            ).one()

            weekday_available = session.exec(
                select(func.count(TeacherFixedSlot.id)).where(
                    and_(*weekday_condition, TeacherFixedSlot.is_available == True)
                )
            ).one()

            weekday_visible = session.exec(
                select(func.count(TeacherFixedSlot.id)).where(
                    and_(*weekday_condition, TeacherFixedSlot.is_visible_to_members == True)
                )
            ).one()

            weekday_name = get_weekday_name(weekday)
            weekday_stats[weekday_name] = {
                "total": weekday_total,
                "available": weekday_available,
                "visible": weekday_visible
            }

        result["weekday_breakdown"] = weekday_stats

    # 按教师统计
    if include_teacher_breakdown and not teacher_id:
        from app.features.teachers.models import Teacher
        teacher_statement = select(Teacher)
        teachers = session.exec(teacher_statement).all()

        teacher_stats = {}
        for teacher in teachers:
            teacher_condition = [TeacherFixedSlot.teacher_id == teacher.id]

            teacher_total = session.exec(
                select(func.count(TeacherFixedSlot.id)).where(and_(*teacher_condition))
            ).one()

            if teacher_total > 0:  # 只统计有时间段的教师
                teacher_available = session.exec(
                    select(func.count(TeacherFixedSlot.id)).where(
                        and_(*teacher_condition, TeacherFixedSlot.is_available == True)
                    )
                ).one()

                teacher_visible = session.exec(
                    select(func.count(TeacherFixedSlot.id)).where(
                        and_(*teacher_condition, TeacherFixedSlot.is_visible_to_members == True)
                    )
                ).one()

                teacher_stats[f"teacher_{teacher.id}"] = {
                    "teacher_name": teacher.name,
                    "teacher_display_code": teacher.display_code,
                    "total": teacher_total,
                    "available": teacher_available,
                    "visible": teacher_visible
                }

        result["teacher_breakdown"] = teacher_stats

    return success_response(result, "获取可用性统计信息成功")


# ==================== 批量操作接口（固定路径，放在参数路径前面） ====================

class BatchCreateSlotsRequest(SQLModel):
    """批量创建时间段请求模型"""
    teacher_id: int = Field(description="教师ID")
    slots: List[dict] = Field(description="时间段列表")
    created_by: int = Field(description="创建者ID")


class BatchUpdateSlotsRequest(SQLModel):
    """批量更新时间段请求模型"""
    updates: List[dict] = Field(description="更新列表，每个元素包含id和要更新的字段")


class BatchDeleteSlotsRequest(SQLModel):
    """批量删除时间段请求模型"""
    slot_ids: List[int] = Field(description="要删除的时间段ID列表")


class CopySlotsRequest(SQLModel):
    """复制时间段请求模型"""
    source_teacher_id: int = Field(description="源教师ID")
    target_teacher_id: int = Field(description="目标教师ID")
    weekdays: Optional[List[Weekday]] = Field(default=None, description="要复制的星期列表，不指定则复制所有")
    overwrite_existing: bool = Field(default=False, description="是否覆盖已存在的时间段")
    created_by: int = Field(description="创建者ID")


class ClearSlotsRequest(SQLModel):
    """清空时间段请求模型"""
    teacher_id: int = Field(description="教师ID")
    weekdays: Optional[List[Weekday]] = Field(default=None, description="要清空的星期列表，不指定则清空所有")
    confirm: bool = Field(description="确认清空操作")


@router.post(
    "/batch/create",
    response_model=DataResponse[dict],
    responses=FIXED_SLOT_ERROR_RESPONSES,
    summary="批量创建时间段",
    description="""
    批量创建教师固定时间段

    **功能说明：**
    - 支持一次性创建多个时间段
    - 自动检测时间冲突
    - 返回创建成功和失败的详细信息

    **请求格式：**
    ```json
    {
      "teacher_id": 1,
      "slots": [
        {
          "weekday": 1,
          "start_time": "08:30",
          "duration_minutes": 25,
          "is_available": true,
          "is_visible_to_members": true
        }
      ],
      "created_by": 1
    }
    ```
    """
)
def batch_create_slots(
    request: BatchCreateSlotsRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量创建时间段"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 验证并转换数据
    slots_data = service.validate_batch_create_data(request.slots)
    
    # 添加必要字段
    for slot_data in slots_data:
        slot_data.teacher_id = request.teacher_id
        slot_data.created_by = request.created_by

    # 执行批量创建
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotBatchCreate
    batch_create = TeacherFixedSlotBatchCreate(
        teacher_id=request.teacher_id,
        slots=slots_data,
        created_by=request.created_by
    )

    operator_name = get_operator_name(user_context)
    created_slots = service.batch_create_slots(batch_create, operator_name)

    result = {
        "total_requested": len(request.slots),
        "valid_slots": len(slots_data),
        "created_count": len(created_slots),
        "failed_count": len(slots_data) - len(created_slots),
        "created_slot_ids": [slot.id for slot in created_slots]
    }

    return success_response(result, f"批量创建完成，成功创建{len(created_slots)}个时间段")


@router.post(
    "/batch/update",
    response_model=DataResponse[dict],
    responses=FIXED_SLOT_ERROR_RESPONSES,
    summary="批量更新时间段",
    description="""
    批量更新教师固定时间段

    **功能说明：**
    - 支持一次性更新多个时间段
    - 支持部分字段更新
    - 自动检测时间冲突

    **请求格式：**
    ```json
    {
      "updates": [
        {
          "id": 1,
          "is_available": false
        },
        {
          "id": 2,
          "duration_minutes": 30,
          "is_visible_to_members": false
        }
      ]
    }
    ```
    """
)
def batch_update_slots(
    request: BatchUpdateSlotsRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量更新时间段"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 验证并处理更新数据
    processed_updates = service.validate_batch_update_data(request.updates)

    # 执行批量更新
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotBatchUpdate
    batch_update = TeacherFixedSlotBatchUpdate(updates=processed_updates)
    updated_slots = service.batch_update_slots(batch_update)

    result = {
        "total_requested": len(request.updates),
        "valid_updates": len(processed_updates),
        "updated_count": len(updated_slots),
        "failed_count": len(processed_updates) - len(updated_slots),
        "updated_slot_ids": [slot.id for slot in updated_slots]
    }

    return success_response(result, f"批量更新完成，成功更新{len(updated_slots)}个时间段")


@router.post(
    "/batch/delete",
    response_model=DataResponse[dict],
    summary="批量删除时间段",
    description="""
    批量删除教师固定时间段

    **功能说明：**
    - 支持一次性删除多个时间段
    - 返回删除成功和失败的详细信息
    - 不存在的时间段会被跳过
    """
)
def batch_delete_slots(
    request: BatchDeleteSlotsRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量删除时间段"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 执行批量删除
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotBatchDelete
    batch_delete = TeacherFixedSlotBatchDelete(ids=request.slot_ids)
    deleted_count = service.batch_delete_slots(batch_delete)

    result = {
        "total_requested": len(request.slot_ids),
        "deleted_count": deleted_count,
        "failed_count": len(request.slot_ids) - deleted_count
    }

    return success_response(result, f"批量删除完成，成功删除{deleted_count}个时间段")


@router.post(
    "/batch/copy",
    response_model=DataResponse[dict],
    summary="复制教师时间段",
    description="""
    复制一个教师的时间段到另一个教师

    **功能说明：**
    - 支持复制教师的所有时间段或指定星期的时间段
    - 可选择是否覆盖目标教师已存在的时间段
    - 自动跳过冲突的时间段

    **使用场景：**
    - 新教师入职，复制相似教师的时间安排
    - 教师调整，快速设置新的时间安排
    - 批量设置，提高操作效率
    """
)
def copy_teacher_slots(
    request: CopySlotsRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """复制教师时间段"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 如果需要覆盖已存在的时间段，先清空目标教师的相关时间段
    if request.overwrite_existing:
        if request.weekdays:
            # 只清空指定星期的时间段
            for weekday in request.weekdays:
                from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotQuery
                query = TeacherFixedSlotQuery(
                    teacher_id=request.target_teacher_id,
                    weekday=weekday,
                    page=1,
                    size=100
                )
                existing_slots, _ = service.get_slots(query)

                if existing_slots:
                    slot_ids = [slot.id for slot in existing_slots]
                    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotBatchDelete
                    batch_delete = TeacherFixedSlotBatchDelete(ids=slot_ids)
                    service.batch_delete_slots(batch_delete)
        else:
            # 清空所有时间段
            service.clear_teacher_slots(request.target_teacher_id)

    # 执行复制操作
    if request.weekdays:
        # 分别复制指定星期的时间段
        all_copied_slots = []
        for weekday in request.weekdays:
            # 获取源教师指定星期的时间段
            from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotQuery
            query = TeacherFixedSlotQuery(
                teacher_id=request.source_teacher_id,
                weekday=weekday,
                page=1,
                size=100
            )
            source_slots, _ = service.get_slots(query)

            # 复制每个时间段
            for source_slot in source_slots:
                try:
                    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotCreate
                    slot_create = TeacherFixedSlotCreate(
                        teacher_id=request.target_teacher_id,
                        weekday=source_slot.weekday,
                        start_time=source_slot.start_time,
                        duration_minutes=source_slot.duration_minutes,
                        is_available=source_slot.is_available,
                        is_visible_to_members=source_slot.is_visible_to_members,
                        created_by=request.created_by
                    )

                    copied_slot = service.create_slot(slot_create, request.created_by)
                    all_copied_slots.append(copied_slot)
                except Exception:
                    # 跳过冲突或错误的时间段
                    continue

        copied_slots = all_copied_slots
    else:
        # 复制所有时间段
        copied_slots = service.copy_teacher_slots(
            request.source_teacher_id,
            request.target_teacher_id,
            request.created_by
        )

    result = {
        "source_teacher_id": request.source_teacher_id,
        "target_teacher_id": request.target_teacher_id,
        "copied_count": len(copied_slots),
        "copied_slot_ids": [slot.id for slot in copied_slots],
        "weekdays_copied": request.weekdays or "all"
    }

    return success_response(result, f"复制完成，成功复制{len(copied_slots)}个时间段")


@router.post(
    "/batch/clear",
    response_model=DataResponse[dict],
    summary="清空教师时间段",
    description="""
    清空教师的固定时间段

    **功能说明：**
    - 支持清空教师的所有时间段或指定星期的时间段
    - 需要确认操作以防误删
    - 返回清空的时间段数量

    **注意：**
    - 此操作不可逆，请谨慎使用
    - 建议在清空前先备份数据
    """
)
def clear_teacher_slots(
    request: ClearSlotsRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """清空教师时间段"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 验证确认操作
    service.validate_clear_operation(request.confirm)

    deleted_count = 0

    if request.weekdays:
        # 只清空指定星期的时间段
        for weekday in request.weekdays:
            from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotQuery
            query = TeacherFixedSlotQuery(
                teacher_id=request.teacher_id,
                weekday=weekday,
                page=1,
                size=100
            )
            slots, _ = service.get_slots(query)

            if slots:
                slot_ids = [slot.id for slot in slots]
                from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotBatchDelete
                batch_delete = TeacherFixedSlotBatchDelete(ids=slot_ids)
                weekday_deleted = service.batch_delete_slots(batch_delete)
                deleted_count += weekday_deleted
    else:
        # 清空所有时间段
        deleted_count = service.clear_teacher_slots(request.teacher_id)

    result = {
        "teacher_id": request.teacher_id,
        "deleted_count": deleted_count,
        "weekdays_cleared": request.weekdays or "all"
    }

    return success_response(result, f"清空完成，成功删除{deleted_count}个时间段")


# ==================== 基本CRUD接口 ====================

@router.post(
    "/",
    response_model=DataResponse[TeacherFixedSlotResponse],
    status_code=status.HTTP_201_CREATED,
    responses=FIXED_SLOT_ERROR_RESPONSES,
    summary="创建教师固定时间段",
    description="""
    为教师创建固定时间段

    **功能说明：**
    - 教师可以设置每周固定的上课时间段
    - 系统会自动检测时间冲突
    - 支持设置可用性和对会员的可见性

    **可能的错误码：**
    - `TIME_SLOT_CONFLICT`: 时间段冲突
    - `TEACHER_NOT_FOUND`: 教师不存在
    - `VALIDATION_ERROR`: 数据验证失败
    """
)
def create_fixed_slot(
    slot_data: TeacherFixedSlotCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建教师固定时间段"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    operator_name = get_operator_name(user_context)
    slot = service.create_slot(slot_data, user_context.user.id, operator_name)

    # 构建响应数据
    response_data = TeacherFixedSlotResponse.model_validate(slot, from_attributes=True)

    return success_response(response_data, "固定时间段创建成功")


@router.get(
    "/",
    response_model=PageResponse[TeacherFixedSlotList],
    summary="获取教师固定时间段列表",
    description="获取教师固定时间段列表，支持分页、筛选和排序"
)
def get_fixed_slots(
    teacher_id: Optional[int] = Query(default=None, description="教师ID"),
    weekday: Optional[Weekday] = Query(default=None, description="星期几"),
    is_available: Optional[bool] = Query(default=None, description="是否可用"),
    is_visible_to_members: Optional[bool] = Query(default=None, description="是否对会员可见"),
    start_time_from: Optional[str] = Query(default=None, description="开始时间范围-起始（HH:MM格式）"),
    start_time_to: Optional[str] = Query(default=None, description="开始时间范围-结束（HH:MM格式）"),
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query(default="weekday,start_time", description="排序字段"),
    sort_order: str = Query(default="asc", description="排序方向：asc/desc"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师固定时间段列表"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)
    
    # 处理时间参数
    start_time_from_obj, start_time_to_obj = service.parse_time_parameters(start_time_from, start_time_to)
    
    # 构建查询参数
    query_params = TeacherFixedSlotQuery(
        teacher_id=teacher_id,
        weekday=weekday,
        is_available=is_available,
        is_visible_to_members=is_visible_to_members,
        start_time_from=start_time_from_obj,
        start_time_to=start_time_to_obj,
        page=page,
        size=size,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    slots, total = service.get_slots(query_params)
    
    # 转换为响应格式
    from app.features.teachers.fixed_slots_models import get_weekday_name
    slot_list = []
    for slot in slots:
        slot_dict = slot.model_dump()
        slot_dict['weekday_name'] = get_weekday_name(slot.weekday)
        slot_list.append(TeacherFixedSlotList(**slot_dict))
    
    return page_response(slot_list, total, page, size, "获取固定时间段列表成功")


# ==================== 参数路径接口（放在最后） ====================

@router.get(
    "/teachers/{teacher_id}/weekly-schedule",
    response_model=DataResponse[dict],
    summary="获取教师周时间安排",
    description="""
    获取指定教师的一周时间安排

    **返回格式：**
    ```json
    {
      "星期一": [时间段列表],
      "星期二": [时间段列表],
      ...
    }
    ```
    """
)
def get_teacher_weekly_schedule(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师周时间安排"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    weekly_schedule = service.get_teacher_weekly_schedule(teacher_id)

    return success_response(weekly_schedule, f"获取教师{teacher_id}的周时间安排成功")


@router.post(
    "/{slot_id}/toggle-availability",
    response_model=DataResponse[TeacherFixedSlotResponse],
    summary="切换时间段可用性状态",
    description="""
    切换指定时间段的可用性状态

    **功能说明：**
    - 如果当前可用，则设为不可用
    - 如果当前不可用，则设为可用
    - 返回更新后的时间段信息
    """
)
def toggle_slot_availability(
    slot_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """切换时间段可用性状态"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    # 获取当前时间段
    slot = service.get_slot(slot_id)
    slot = ensure_found(slot, "固定时间段")

    # 切换可用性状态
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotUpdate
    update_data = TeacherFixedSlotUpdate(is_available=not slot.is_available)
    operator_name = get_operator_name(user_context)
    updated_slot = service.update_slot(slot_id, update_data, operator_id=user_context.user.id, operator_name=operator_name)

    # 构建响应数据
    response_data = TeacherFixedSlotResponse.model_validate(updated_slot, from_attributes=True)

    status_text = "可用" if updated_slot.is_available else "不可用"
    return success_response(response_data, f"时间段状态已切换为{status_text}")


@router.post(
    "/{slot_id}/toggle-visibility",
    response_model=DataResponse[TeacherFixedSlotResponse],
    summary="切换时间段对会员可见性",
    description="""
    切换指定时间段对会员的可见性

    **功能说明：**
    - 如果当前对会员可见，则设为不可见
    - 如果当前对会员不可见，则设为可见
    - 返回更新后的时间段信息
    """
)
def toggle_slot_visibility(
    slot_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """切换时间段对会员可见性"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    print("toggle_slot_visibility:", slot_id)
    # 获取当前时间段
    slot = service.get_slot(slot_id)
    slot = ensure_found(slot, "固定时间段")

    # 切换可见性状态
    from app.features.teachers.fixed_slots_schemas import TeacherFixedSlotUpdate
    update_data = TeacherFixedSlotUpdate(is_visible_to_members=not slot.is_visible_to_members)
    operator_name = get_operator_name(user_context)
    updated_slot = service.update_slot(slot_id, update_data, operator_id=user_context.user.id, operator_name=operator_name)

    # 确保对象状态正常，重新从数据库获取最新数据
    # session.refresh(updated_slot)

    # 构建响应数据
    response_data = TeacherFixedSlotResponse.model_validate(updated_slot, from_attributes=True)

    visibility_text = "可见" if updated_slot.is_visible_to_members else "不可见"
    return success_response(response_data, f"时间段对会员的可见性已切换为{visibility_text}")


@router.get(
    "/{slot_id}",
    response_model=DataResponse[TeacherFixedSlotResponse],
    summary="获取固定时间段详情",
    description="根据ID获取固定时间段详情"
)
def get_fixed_slot(
    slot_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取固定时间段详情"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)
    
    slot = service.get_slot(slot_id)
    slot = ensure_found(slot, "固定时间段")
    
    # 构建响应数据
    response_data = TeacherFixedSlotResponse.model_validate(slot, from_attributes=True)
    
    return success_response(response_data, "获取固定时间段详情成功")


@router.post(
    "/{slot_id}/update",
    response_model=DataResponse[TeacherFixedSlotResponse],
    responses=FIXED_SLOT_ERROR_RESPONSES,
    summary="更新固定时间段",
    description="""
    更新教师固定时间段信息

    **功能说明：**
    - 支持部分字段更新
    - 更新时间相关字段时会自动检测冲突
    - 更新后会自动刷新更新时间

    **可能的错误码：**
    - `TIME_SLOT_CONFLICT`: 时间段冲突
    - `VALIDATION_ERROR`: 数据验证失败
    """
)
def update_fixed_slot(
    slot_id: int,
    slot_data: TeacherFixedSlotUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新固定时间段"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    operator_name = get_operator_name(user_context)
    updated_slot = service.update_slot(slot_id, slot_data, operator_id=user_context.user.id, operator_name=operator_name)

    # 构建响应数据
    response_data = TeacherFixedSlotResponse.model_validate(updated_slot, from_attributes=True)

    return success_response(response_data, "固定时间段更新成功")


@router.post(
    "/{slot_id}/delete",
    response_model=MessageResponse,
    summary="删除固定时间段",
    description="删除指定的固定时间段"
)
def delete_fixed_slot(
    slot_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除固定时间段"""
    tenant_id = user_context.tenant_context.tenant_id
    service = TeacherFixedSlotService(session, tenant_id)

    operator_name = get_operator_name(user_context)
    service.delete_slot(slot_id, operator_id=user_context.user.id, operator_name=operator_name)

    return message_response("固定时间段删除成功")


