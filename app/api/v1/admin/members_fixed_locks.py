"""
管理端 - 会员固定课位管理API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session

from app.api.common.exceptions import BusinessException, NotFoundError, AuthenticationError

from app.features.members.fixed_lock_schemas import (
    MemberFixedSlotLockCreate, MemberFixedSlotLockResponse,
    MemberFixedSlotLockList, MemberFixedSlotLockQuery,
    MemberFixedSlotLockBatchCreate, MemberFixedSlotLockBatchDelete,
    AvailableSlotQuery, AvailableSlotResponse,
    LockConflictCheck, LockConflictResult, LockOperationResult, BatchLockOperationResult
)
from app.features.members.fixed_lock_models import MemberFixedSlotLock
from app.features.members.fixed_lock_service import MemberFixedSlotLockService
from app.features.members.fixed_lock_exceptions import MemberFixedSlotLockErrorCode
from app.db.session import get_session
from app.core.context import UserContext
from app.core.dependencies import get_user_context
from app.api.common import (
    DataResponse,
    MessageResponse,
    ListResponse,
    PageResponse,
    success_response,
    message_response,
    list_response,
    page_response,
    ensure_found,
    ensure_success
)
from app.api.common.responses import create_error_responses, ErrorResponse, AUTH_ONLY_RESPONSES, PERMISSION_ONLY_RESPONSES, RESOURCE_RESPONSES

router = APIRouter()


def get_operator_name(user_context: UserContext) -> str:
    """获取操作人姓名 - 使用用户名"""
    return user_context.user.username

# 会员固定课位锁定模块的错误响应文档
LOCK_ERROR_RESPONSES = create_error_responses([
    MemberFixedSlotLockErrorCode.MEMBER_NOT_FOUND,
    MemberFixedSlotLockErrorCode.TEACHER_SLOT_NOT_FOUND,
    MemberFixedSlotLockErrorCode.SLOT_NOT_AVAILABLE,
    MemberFixedSlotLockErrorCode.SLOT_NOT_VISIBLE,
    MemberFixedSlotLockErrorCode.SLOT_ALREADY_LOCKED,
    MemberFixedSlotLockErrorCode.LOCK_CONFLICT
])


# ==================== 锁定时间段接口 ====================

@router.post(
    "/", 
    response_model=DataResponse[MemberFixedSlotLockResponse], 
    status_code=status.HTTP_201_CREATED,
    summary="创建会员固定课位锁定",
    description="""
    创建会员固定课位锁定记录
    
    **可能的错误码：**
    - `MEMBER_NOT_FOUND`: 会员不存在
    - `TEACHER_SLOT_NOT_FOUND`: 教师固定时间段不存在
    - `SLOT_NOT_AVAILABLE`: 时间段不可用
    - `SLOT_NOT_VISIBLE`: 时间段对会员不可见
    - `SLOT_ALREADY_LOCKED`: 时间段已被锁定
    """
)
def create_member_fixed_slot_lock(
    lock_data: MemberFixedSlotLockCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建会员固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_id = user_context.user.id
    
    service = MemberFixedSlotLockService(session, tenant_id)
    operator_name = get_operator_name(user_context)
    lock = service.create_lock(lock_data, created_by=user_id, operator_name=operator_name)
    
    # 构建响应数据
    response_data = _build_lock_response(lock)
    return success_response(response_data, "固定课位锁定创建成功")


@router.post(
    "/batch", 
    response_model=DataResponse[BatchLockOperationResult], 
    status_code=status.HTTP_201_CREATED,
    summary="批量创建会员固定课位锁定",
    description="""
    批量创建会员固定课位锁定记录
    
    **业务规则：**
    - 跳过不存在或不可用的时间段
    - 跳过已被锁定的时间段
    - 返回成功和失败的详情
    """
)
def batch_create_member_fixed_slot_locks(
    batch_data: MemberFixedSlotLockBatchCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量创建会员固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_id = user_context.user.id
    
    service = MemberFixedSlotLockService(session, tenant_id)
    operator_name = get_operator_name(user_context)
    created_locks = service.batch_create_locks(batch_data, created_by=user_id, operator_name=operator_name)
    
    total_count = len(batch_data.teacher_fixed_slot_ids)
    success_count = len(created_locks)
    failed_count = total_count - success_count
    
    result = BatchLockOperationResult(
        total_count=total_count,
        success_count=success_count,
        failed_count=failed_count,
        success_ids=[lock.id for lock in created_locks],
        failed_items=[],  # 具体失败项可以根据需要添加详情
        message=f"批量锁定完成，成功 {success_count} 个，失败 {failed_count} 个"
    )
    
    return success_response(result, "批量固定课位锁定完成")


# ==================== 查询锁定列表接口 ====================

@router.get(
    "/", 
    response_model=PageResponse[MemberFixedSlotLockList],
    summary="获取会员固定课位锁定列表",
    description="查询会员固定课位锁定记录列表，支持多种筛选条件"
)
def get_member_fixed_slot_locks(
    member_id: Optional[int] = Query(None, description="会员ID"),
    teacher_id: Optional[int] = Query(None, description="教师ID"),
    teacher_fixed_slot_id: Optional[int] = Query(None, description="教师固定时间段ID"),
    weekday: Optional[int] = Query(None, ge=1, le=7, description="星期几（1-7）"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("weekday,start_time", description="排序字段"),
    sort_order: str = Query("asc", description="排序方向"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员固定课位锁定列表"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    
    service = MemberFixedSlotLockService(session, tenant_id)
    
    query_params = MemberFixedSlotLockQuery(
        member_id=member_id,
        teacher_id=teacher_id,
        teacher_fixed_slot_id=teacher_fixed_slot_id,
        weekday=weekday,
        page=page,
        size=size,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    locks, total = service.get_locks(query_params)
    
    # 构建列表响应数据
    response_data = [_build_lock_list_item(lock) for lock in locks]
    return page_response(response_data, total, page, size, "获取锁定列表成功")


@router.get(
    "/member/{member_id}", 
    response_model=ListResponse[MemberFixedSlotLockList],
    summary="获取指定会员的固定课位锁定",
    description="获取指定会员的所有固定课位锁定记录"
)
def get_member_locks(
    member_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取指定会员的固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    
    service = MemberFixedSlotLockService(session, tenant_id)
    locks = service.get_member_locks(member_id)
    
    response_data = [_build_lock_list_item(lock) for lock in locks]
    return list_response(response_data, len(response_data), "获取会员锁定记录成功")


@router.get(
    "/teacher/{teacher_id}", 
    response_model=ListResponse[MemberFixedSlotLockList],
    summary="获取指定教师的固定课位锁定情况",
    description="获取指定教师所有时间段的锁定情况"
)
def get_teacher_slot_locks(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取指定教师的固定课位锁定情况"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    
    service = MemberFixedSlotLockService(session, tenant_id)
    locks = service.get_teacher_slot_locks(teacher_id)
    
    response_data = [_build_lock_list_item(lock) for lock in locks]
    return list_response(response_data, len(response_data), "获取教师时间段锁定情况成功")


# ==================== 可用时间段查询接口 ====================

@router.get(
    "/available-slots", 
    response_model=ListResponse[AvailableSlotResponse],
    summary="获取可锁定的时间段",
    description="查询可以被会员锁定的教师固定时间段"
)
def get_available_slots(
    teacher_id: Optional[int] = Query(None, description="教师ID"),
    weekdays: Optional[str] = Query(None, description="星期列表，逗号分隔，如：1,2,3"),
    only_available: bool = Query(True, description="仅返回可用时间段"),
    only_visible: bool = Query(True, description="仅返回对会员可见的时间段"),
    exclude_locked: bool = Query(True, description="排除已被锁定的时间段"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取可锁定的时间段"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None

    # 解析星期列表
    weekday_list = None
    if weekdays:
        try:
            weekday_list = [int(w.strip()) for w in weekdays.split(',') if w.strip()]
        except ValueError:
            raise BusinessException(
                message="星期格式错误，应为逗号分隔的数字"
            )
        
    query = AvailableSlotQuery(
        teacher_id=teacher_id,
        weekdays=weekday_list,
        only_available=only_available,
        only_visible=only_visible,
        exclude_locked=exclude_locked
    )
    
    service = MemberFixedSlotLockService(session, tenant_id)
    slots = service.get_available_slots(query)
    response_data = [_build_available_slot_response(slot) for slot in slots]
    return list_response(response_data, len(response_data), "获取可锁定时间段成功")


# ==================== 冲突检测接口 ====================

@router.post(
    "/conflict-check", 
    response_model=DataResponse[LockConflictResult],
    summary="检测锁定冲突",
    description="检测指定时间段是否存在锁定冲突"
)
def check_lock_conflict(
    conflict_check: LockConflictCheck,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """检测锁定冲突"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    
    service = MemberFixedSlotLockService(session, tenant_id)
    result = service.check_lock_conflict(conflict_check)
    
    return success_response(result, "冲突检测完成")

# ==================== 批量锁定操作接口 ====================


@router.post(
    "/batch/delete", 
    response_model=DataResponse[BatchLockOperationResult],
    summary="批量删除固定课位锁定",
    description="批量删除多个固定课位锁定记录"
)
def batch_delete_locks(
    batch_data: MemberFixedSlotLockBatchDelete,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量删除固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    
    service = MemberFixedSlotLockService(session, tenant_id)
    deleted_count = service.batch_delete_locks(batch_data)
    
    total_count = len(batch_data.lock_ids)
    failed_count = total_count - deleted_count
    
    result = BatchLockOperationResult(
        total_count=total_count,
        success_count=deleted_count,
        failed_count=failed_count,
        success_ids=[],  # 删除操作不返回具体ID
        failed_items=[],
        message=f"批量删除完成，成功 {deleted_count} 个，失败 {failed_count} 个"
    )
    
    return success_response(result, "批量删除完成")


@router.post(
    "/{lock_id}/delete", 
    response_model=MessageResponse,
    summary="删除固定课位锁定",
    description="删除指定的固定课位锁定记录"
)
def delete_lock(
    lock_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    
    service = MemberFixedSlotLockService(session, tenant_id)
    operator_name = get_operator_name(user_context)
    success = service.delete_lock(lock_id, operator_id=user_context.user.id, operator_name=operator_name, is_member_operation=False)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="固定课位锁定不存在"
        )
    
    return message_response("固定课位锁定删除成功")


# ==================== 锁定状态管理接口 ====================

@router.post(
    "/{lock_id}/cancel",
    response_model=MessageResponse,
    summary="取消固定课位锁定",
    description="取消指定的固定课位锁定（会员取消会删除记录）"
)
def cancel_lock(
    lock_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """取消固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None

    service = MemberFixedSlotLockService(session, tenant_id)
    service.cancel_lock(lock_id)

    return message_response("固定课位锁定已取消")


@router.post(
    "/{lock_id}/admin-cancel",
    response_model=DataResponse[MemberFixedSlotLockResponse],
    summary="管理员取消固定课位锁定",
    description="管理员取消指定的固定课位锁定，保留记录并标记为已取消"
)
def admin_cancel_lock(
    lock_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """管理员取消固定课位锁定"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None

    service = MemberFixedSlotLockService(session, tenant_id)
    lock = service.admin_cancel_lock(lock_id)

    response_data = _build_lock_response(lock)
    return success_response(response_data, "锁定已取消")


# ==================== 单个资源操作接口 ====================

@router.get(
    "/{lock_id}", 
    response_model=DataResponse[MemberFixedSlotLockResponse],
    summary="获取固定课位锁定详情",
    description="根据ID获取固定课位锁定的详细信息"
)
def get_member_fixed_slot_lock(
    lock_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取固定课位锁定详情"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    
    service = MemberFixedSlotLockService(session, tenant_id)
    lock = service.get_lock(lock_id)
    lock = ensure_found(lock, "固定课位锁定")
    
    response_data = _build_lock_response(lock)
    return success_response(response_data, "获取锁定详情成功")


# ==================== 辅助函数 ====================

def _build_lock_response(lock: MemberFixedSlotLock) -> MemberFixedSlotLockResponse:
    """构建锁定详情响应数据"""
    from app.features.members.fixed_lock_models import get_weekday_display, format_time_slot
    
    return MemberFixedSlotLockResponse(
        id=lock.id,
        member_id=lock.member_id,
        member_name=None,  # 需要时可以通过关联查询获取
        teacher_fixed_slot_id=lock.teacher_fixed_slot_id,
        teacher_id=lock.teacher_id,
        teacher_name=None,  # 需要时可以通过关联查询获取
        weekday=lock.weekday,
        weekday_name=get_weekday_display(lock.weekday),
        start_time=lock.start_time,
        time_slot_display=format_time_slot(lock.weekday, lock.start_time),
        locked_at=lock.locked_at,
        created_by=lock.created_by,
        created_at=lock.created_at,
        updated_at=lock.updated_at
    )


def _build_lock_list_item(lock: MemberFixedSlotLock) -> MemberFixedSlotLockList:
    """构建锁定列表项响应数据"""
    from app.features.members.fixed_lock_models import get_weekday_display, format_time_slot
    
    return MemberFixedSlotLockList(
        id=lock.id,
        member_id=lock.member_id,
        member_name=lock.member_name,
        member_phone=lock.member_phone,
        teacher_id=lock.teacher_id,
        teacher_name=None,  # 需要时可以通过关联查询获取
        weekday=lock.weekday,
        weekday_name=get_weekday_display(lock.weekday),
        start_time=lock.start_time,
        time_slot_display=format_time_slot(lock.weekday, lock.start_time),
        locked_at=lock.locked_at
    )


def _build_available_slot_response(slot) -> AvailableSlotResponse:
    """构建可用时间段响应数据"""
    from app.features.members.fixed_lock_models import get_weekday_display, format_time_slot
    
    return AvailableSlotResponse(
        teacher_fixed_slot_id=slot.id,
        teacher_id=slot.teacher_id,
        teacher_name=None,  # 需要时可以通过关联查询获取
        weekday=slot.weekday,
        weekday_name=get_weekday_display(slot.weekday),
        start_time=slot.start_time,
        duration_minutes=slot.duration_minutes,
        time_slot_display=format_time_slot(slot.weekday, slot.start_time),
        is_available=slot.is_available,
        is_visible_to_members=slot.is_visible_to_members
    )


# ==================== 数据一致性检查接口 ====================

@router.post(
    "/data-consistency/check",
    response_model=DataResponse[dict],
    summary="检查冗余字段数据一致性",
    description="检查并修复会员固定课位锁定表中的冗余字段与主表数据的一致性"
)
def check_data_consistency(
    fix_inconsistencies: bool = Query(False, description="是否自动修复发现的不一致数据"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """检查冗余字段数据一致性"""
    tenant_id = user_context.tenant_context.tenant_id
    service = MemberFixedSlotLockService(session, tenant_id)

    if fix_inconsistencies:
        # 检查并修复
        result = service.check_and_fix_data_consistency()
        message = f"数据一致性检查完成，共检查 {result['total_checked']} 条记录，发现 {result['inconsistent_count']} 条不一致，已修复 {result['fixed_count']} 条"
    else:
        # 只检查不修复
        result = service.validate_data_consistency()
        message = f"数据一致性验证完成，共检查 {result['total_checked']} 条记录，发现 {result['inconsistent_count']} 条不一致"

    return success_response(result, message)


@router.get(
    "/data-consistency/validate/{lock_id}",
    response_model=DataResponse[dict],
    summary="验证特定锁定记录的数据一致性",
    description="验证指定会员固定课位锁定记录的冗余字段与主表数据的一致性"
)
def validate_lock_consistency(
    lock_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """验证特定锁定记录的数据一致性"""
    tenant_id = user_context.tenant_context.tenant_id
    service = MemberFixedSlotLockService(session, tenant_id)

    result = service.validate_data_consistency(lock_id)

    if "error" in result:
        raise NotFoundError(f"锁定记录(ID:{lock_id})")

    status_text = "一致" if result["is_consistent"] else "不一致"
    message = f"锁定记录 {lock_id} 数据一致性验证完成：{status_text}"

    return success_response(result, message)