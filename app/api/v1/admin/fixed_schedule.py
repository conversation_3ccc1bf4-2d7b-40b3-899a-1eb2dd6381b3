"""固定课排课管理API"""

from fastapi import APIRouter, Depends, Query, BackgroundTasks
from typing import Optional
from datetime import datetime, date

from app.api.common.responses import DataResponse, PageResponse
from app.core.dependencies import get_current_user
from app.features.users.models import User
from app.db.session import Session, get_session

from app.features.courses.scheduling.service import (
    FixedScheduleTaskService,
    FixedScheduleTaskLogService,
    FixedScheduleTaskExecutor
)
from app.features.courses.scheduling.schemas import (
    FixedScheduleTaskCreate,
    FixedScheduleTaskUpdate,
    FixedScheduleTaskQuery,
    FixedScheduleTaskResponse,
    FixedScheduleTaskLogQuery,
    FixedScheduleTaskLogResponse,
    ScheduleTaskStatsResponse,
    ScheduleTaskExecuteRequest
)
from app.features.courses.scheduling.models import (
    ScheduleTaskStatus,
    TeacherPriorityRule,
    BalanceInsufficientAction
)

router = APIRouter(tags=["固定课排课管理"])


# ==================== 手动触发固定课排课API ====================

@router.post("/tasks", response_model=DataResponse[FixedScheduleTaskResponse], status_code=201)
async def create_schedule_task(
    task_data: FixedScheduleTaskCreate,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """创建固定课排课任务"""
    service = FixedScheduleTaskService(session, current_user.tenant_id)
    task = service.create_task(task_data, current_user.id)
    
    return DataResponse(data=FixedScheduleTaskResponse.model_validate(task))


@router.post("/tasks/{task_id}/execute", response_model=DataResponse[dict])
async def execute_schedule_task(
    task_id: int,
    background_tasks: BackgroundTasks,
    force_restart: bool = Query(False, description="是否强制重新开始"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """执行固定课排课任务"""
    executor = FixedScheduleTaskExecutor(session, current_user.tenant_id)
    
    # 在后台执行排课任务
    background_tasks.add_task(executor.execute_task, task_id, force_restart)
    
    return DataResponse(data={
        "task_id": task_id,
        "message": "排课任务已开始执行",
        "status": "started"
    })


@router.post("/tasks/create-and-execute", response_model=DataResponse[dict])
async def create_and_execute_schedule_task(
    task_data: FixedScheduleTaskCreate,
    background_tasks: BackgroundTasks,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """创建并立即执行固定课排课任务"""
    # 创建任务
    service = FixedScheduleTaskService(session, current_user.tenant_id)
    task = service.create_task(task_data, current_user.id)
    
    # 在后台执行排课任务
    executor = FixedScheduleTaskExecutor(session, current_user.tenant_id)
    background_tasks.add_task(executor.execute_task, task.id, False)
    
    return DataResponse(data={
        "task_id": task.id,
        "task_name": task.task_name,
        "message": "排课任务已创建并开始执行",
        "status": "started"
    })


@router.post("/tasks/{task_id}/retry", response_model=DataResponse[dict])
async def retry_failed_task(
    task_id: int,
    background_tasks: BackgroundTasks,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """重试失败的排课任务"""
    executor = FixedScheduleTaskExecutor(session, current_user.tenant_id)
    
    # 在后台重试任务
    background_tasks.add_task(executor.retry_failed_task, task_id)
    
    return DataResponse(data={
        "task_id": task_id,
        "message": "失败任务已开始重试",
        "status": "retrying"
    })


@router.post("/tasks/{task_id}/cancel", response_model=DataResponse[dict])
async def cancel_running_task(
    task_id: int,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """取消正在执行的排课任务"""
    executor = FixedScheduleTaskExecutor(session, current_user.tenant_id)
    result = executor.cancel_running_task(task_id)
    
    return DataResponse(data=result)


@router.get("/tasks/{task_id}/status", response_model=DataResponse[dict])
async def get_task_execution_status(
    task_id: int,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取任务执行状态"""
    executor = FixedScheduleTaskExecutor(session, current_user.tenant_id)
    status = executor.get_task_execution_status(task_id)
    
    return DataResponse(data=status)


# ==================== 固定课排课任务查询API ====================

@router.get("/tasks", response_model=PageResponse[FixedScheduleTaskResponse])
async def get_schedule_tasks(
    task_name: Optional[str] = Query(None, description="任务名称（模糊搜索）"),
    status: Optional[ScheduleTaskStatus] = Query(None, description="任务状态"),
    start_date_from: Optional[date] = Query(None, description="开始日期范围-起始"),
    start_date_to: Optional[date] = Query(None, description="开始日期范围-结束"),
    created_by: Optional[int] = Query(None, description="创建人ID"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取固定课排课任务列表"""
    service = FixedScheduleTaskService(session, current_user.tenant_id)
    
    query = FixedScheduleTaskQuery(
        task_name=task_name,
        status=status,
        start_date_from=start_date_from,
        start_date_to=start_date_to,
        created_by=created_by,
        page=page,
        size=size,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    tasks, total = service.query_tasks(query)
    
    return PageResponse.create(
        [FixedScheduleTaskResponse.model_validate(task) for task in tasks],
        total,
        page,
        size
    )


@router.get("/tasks/statistics", response_model=DataResponse[ScheduleTaskStatsResponse])
async def get_task_statistics(
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取任务统计信息"""
    service = FixedScheduleTaskService(session, current_user.tenant_id)
    statistics = service.get_task_statistics()

    return DataResponse(data=statistics)


@router.get("/tasks/{task_id}", response_model=DataResponse[FixedScheduleTaskResponse])
async def get_schedule_task(
    task_id: int,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取固定课排课任务详情"""
    service = FixedScheduleTaskService(session, current_user.tenant_id)

    task = service.get_task(task_id)
    return DataResponse(data=FixedScheduleTaskResponse.model_validate(task))


@router.put("/tasks/{task_id}", response_model=DataResponse[FixedScheduleTaskResponse])
async def update_schedule_task(
    task_id: int,
    task_data: FixedScheduleTaskUpdate,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """更新固定课排课任务"""
    service = FixedScheduleTaskService(session, current_user.tenant_id)
    task = service.update_task(task_id, task_data)

    return DataResponse(data=FixedScheduleTaskResponse.model_validate(task))


@router.delete("/tasks/{task_id}", response_model=DataResponse[dict])
async def delete_schedule_task(
    task_id: int,
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """删除固定课排课任务"""
    service = FixedScheduleTaskService(session, current_user.tenant_id)
    service.delete_task(task_id)

    return DataResponse(data={
        "task_id": task_id,
        "message": "任务已删除"
    })


# ==================== 固定课排课日志查询API ====================

@router.get("/tasks/{task_id}/logs", response_model=PageResponse[FixedScheduleTaskLogResponse])
async def get_task_logs(
    task_id: int,
    log_level: Optional[str] = Query(None, description="日志级别"),
    teacher_id: Optional[int] = Query(None, description="教师ID"),
    member_id: Optional[int] = Query(None, description="会员ID"),
    operation_type: Optional[str] = Query(None, description="操作类型"),
    created_from: Optional[datetime] = Query(None, description="创建时间范围-起始"),
    created_to: Optional[datetime] = Query(None, description="创建时间范围-结束"),
    message: Optional[str] = Query(None, description="日志消息（模糊搜索）"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取指定任务的日志列表"""
    service = FixedScheduleTaskLogService(session, current_user.tenant_id)
    
    query = FixedScheduleTaskLogQuery(
        task_id=task_id,
        log_level=log_level,
        teacher_id=teacher_id,
        member_id=member_id,
        operation_type=operation_type,
        created_from=created_from,
        created_to=created_to,
        message=message,
        page=page,
        size=size,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    logs, total = service.query_logs(query)
    
    return PageResponse.create(
        [FixedScheduleTaskLogResponse.model_validate(log) for log in logs],
        total,
        page,
        size
    )


@router.get("/logs", response_model=PageResponse[FixedScheduleTaskLogResponse])
async def get_all_logs(
    task_id: Optional[int] = Query(None, description="任务ID"),
    log_level: Optional[str] = Query(None, description="日志级别"),
    teacher_id: Optional[int] = Query(None, description="教师ID"),
    member_id: Optional[int] = Query(None, description="会员ID"),
    operation_type: Optional[str] = Query(None, description="操作类型"),
    created_from: Optional[datetime] = Query(None, description="创建时间范围-起始"),
    created_to: Optional[datetime] = Query(None, description="创建时间范围-结束"),
    message: Optional[str] = Query(None, description="日志消息（模糊搜索）"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", pattern="^(asc|desc)$", description="排序方向"),
    session: Session = Depends(get_session),
    current_user: User = Depends(get_current_user)
):
    """获取所有排课日志列表"""
    service = FixedScheduleTaskLogService(session, current_user.tenant_id)
    
    query = FixedScheduleTaskLogQuery(
        task_id=task_id,
        log_level=log_level,
        teacher_id=teacher_id,
        member_id=member_id,
        operation_type=operation_type,
        created_from=created_from,
        created_to=created_to,
        message=message,
        page=page,
        size=size,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    logs, total = service.query_logs(query)
    
    return PageResponse.create(
        [FixedScheduleTaskLogResponse.model_validate(log) for log in logs],
        total,
        page,
        size
    )
