"""
课程系统配置API路由
"""
from typing import Optional, Union
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session

from app.db.session import get_session
from app.core.dependencies import get_user_context
from app.api.common.responses import (
    DataResponse, MessageResponse,
    success_response, message_response
)
from app.api.common.exceptions import NotFoundError, ValidationError
from app.core.context import UserContext
from app.features.courses.config_service import CourseSystemConfigService
from app.features.courses.config_schemas import (
    CourseSystemConfigResponse,
    CourseSystemConfigDetail,
    CourseSystemConfigUpdate,
    CourseSystemConfigQuery
)
from app.features.courses.config_exceptions import (
    CourseConfigNotFoundError,
    CourseConfigBusinessException
)

router = APIRouter()


@router.get(
    "/config",
    response_model=DataResponse[CourseSystemConfigDetail],
    summary="获取课程系统配置",
    description="获取当前租户的课程系统配置信息，如果不存在则创建默认配置"
)
def get_course_config(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取课程系统配置"""
    tenant_id = user_context.tenant_context.tenant_id
    service = CourseSystemConfigService(session, tenant_id)
    
    # 获取配置，如果不存在则创建默认配置
    config = service.get_config_or_create_default(created_by=user_context.user.id)
    
    # 转换为详情响应模型
    config_detail = CourseSystemConfigDetail.model_validate(config)
    
    return success_response(config_detail, "获取课程系统配置成功")


@router.post(
    "/config/update",
    response_model=DataResponse[CourseSystemConfigDetail],
    summary="更新课程系统配置",
    description="更新当前租户的课程系统配置信息"
)
def update_course_config(
    config_update: CourseSystemConfigUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新课程系统配置"""
    tenant_id = user_context.tenant_context.tenant_id
    service = CourseSystemConfigService(session, tenant_id)
    
    # 更新配置
    updated_config = service.update_config(
        config_update, 
        updated_by=user_context.user.id
    )
    
    # 转换为详情响应模型
    config_detail = CourseSystemConfigDetail.model_validate(updated_config)
    
    return success_response(config_detail, "更新课程系统配置成功")


@router.post(
    "/config/reset",
    response_model=DataResponse[CourseSystemConfigDetail],
    summary="重置课程系统配置",
    description="将当前租户的课程系统配置重置为默认值"
)
def reset_course_config(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """重置课程系统配置为默认值"""
    tenant_id = user_context.tenant_context.tenant_id
    service = CourseSystemConfigService(session, tenant_id)
    
    # 重置配置为默认值
    reset_config = service.reset_config_to_default(reset_by=user_context.user.id)
    
    # 转换为详情响应模型
    config_detail = CourseSystemConfigDetail.model_validate(reset_config)
    
    return success_response(config_detail, "重置课程系统配置成功")


from pydantic import BaseModel

class UpdateFieldRequest(BaseModel):
    field_name: str
    field_value: Union[str, int, float, bool]

@router.post(
    "/config/field",
    response_model=DataResponse[CourseSystemConfigDetail],
    summary="更新单个配置字段",
    description="更新课程系统配置的单个字段值"
)
def update_config_field(
    request: UpdateFieldRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新单个配置字段"""
    tenant_id = user_context.tenant_context.tenant_id
    service = CourseSystemConfigService(session, tenant_id)
    
    # 更新单个字段
    updated_config = service.update_config_field(
        request.field_name, 
        request.field_value, 
        updated_by=user_context.user.id
    )
    
    # 转换为详情响应模型
    config_detail = CourseSystemConfigDetail.model_validate(updated_config)
    
    return success_response(config_detail, f"更新配置字段 {request.field_name} 成功")


@router.get(
    "/config/field/{field_name}",
    response_model=DataResponse[dict],
    summary="获取单个配置字段值",
    description="获取课程系统配置的单个字段值"
)
def get_config_field(
    field_name: str,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取单个配置字段值"""
    tenant_id = user_context.tenant_context.tenant_id
    service = CourseSystemConfigService(session, tenant_id)
    
    # 获取字段值
    field_value = service.get_config_by_field(field_name)
    
    return success_response(
        data={
            "field_name": field_name,
            "field_value": field_value
        },
        message=f"获取配置字段 {field_name} 成功"
    )


@router.get(
    "/config/booking-time-range",
    response_model=DataResponse[dict],
    summary="获取预约时间范围",
    description="获取课程系统的预约操作时间范围配置"
)
def get_booking_time_range(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取预约时间范围"""
    tenant_id = user_context.tenant_context.tenant_id
    service = CourseSystemConfigService(session, tenant_id)
    
    # 获取预约时间范围
    time_range = service.get_booking_time_range()
    
    return success_response(time_range, "获取预约时间范围成功")


@router.get(
    "/config/teacher-permissions",
    response_model=DataResponse[dict],
    summary="获取教师权限配置",
    description="获取教师相关的权限配置信息"
)
def get_teacher_permissions(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师权限配置"""
    tenant_id = user_context.tenant_context.tenant_id
    service = CourseSystemConfigService(session, tenant_id)
    
    # 获取教师权限配置
    permissions = service.get_teacher_permissions()
    
    return success_response(permissions, "获取教师权限配置成功")


@router.get(
    "/config/schedule-config",
    response_model=DataResponse[dict],
    summary="获取排课配置",
    description="获取自动排课相关的配置信息"
)
def get_schedule_config(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取排课配置"""
    tenant_id = user_context.tenant_context.tenant_id
    service = CourseSystemConfigService(session, tenant_id)
    
    # 获取排课配置
    schedule_config = service.get_schedule_config()
    
    return success_response(schedule_config, "获取排课配置成功")


@router.post(
    "/config/validate-consistency",
    response_model=DataResponse[dict],
    summary="验证配置一致性",
    description="验证当前租户的课程系统配置是否一致"
)
def validate_config_consistency(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """验证配置一致性"""
    tenant_id = user_context.tenant_context.tenant_id
    service = CourseSystemConfigService(session, tenant_id)
    
    # 验证配置一致性
    is_valid = service.validate_config_consistency()
    
    return success_response({"is_valid": is_valid}, "配置一致性验证完成") 