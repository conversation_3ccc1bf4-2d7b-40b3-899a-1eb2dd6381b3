"""
已排课表API路由
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session
from datetime import datetime, date, time

from app.db.session import get_session
from app.core.dependencies import get_user_context
from app.api.common.responses import (
    DataResponse, MessageResponse, PageResponse,
    success_response, message_response, page_response
)
from app.api.common.utils import ensure_found
from app.core.context import UserContext
from app.features.courses.scheduled_classes_service import ScheduledClassService
from app.features.courses.scheduled_classes_schemas import (
    ScheduledClassCreate, ScheduledClassUpdate, ScheduledClassRead, ScheduledClassList,
    TeacherClassCreate, MemberClassBooking, AdminClassCreate, ClassBookingData,
    ClassBooking, ClassCancellation, ClassStatusUpdate, BatchClassStatusUpdate,
    ScheduledClassQuery, AvailableClassQuery,
    ConflictCheckRequest, ConflictCheckResponse, BatchTeacherClassCreate,
    BatchCancelResult, BatchBookResult
)
from app.features.courses.scheduled_classes_models import ClassStatus

router = APIRouter()

# ==================== 课程查询接口（具体路径优先） ====================

@router.get(
    "/search",
    response_model=PageResponse[ScheduledClassList],
    summary="搜索课程",
    description="按多维度条件搜索课程列表"
)
def search_classes(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    teacher_id: Optional[int] = Query(None, gt=0, description="教师ID"),
    member_id: Optional[int] = Query(None, gt=0, description="会员ID"),
    class_type: Optional[str] = Query(None, description="课程类型"),
    status: Optional[str] = Query(None, description="课程状态"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    time_from: Optional[time] = Query(None, description="时间段开始"),
    time_to: Optional[time] = Query(None, description="时间段结束"),
    search: Optional[str] = Query(None, max_length=100, description="搜索关键词"),
    sort_by: str = Query("class_datetime", description="排序字段"),
    sort_order: str = Query("asc", description="排序方向"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """搜索课程列表"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 构建查询参数
    query_params = ScheduledClassQuery(
        page=page,
        size=size,
        teacher_id=teacher_id,
        member_id=member_id,
        class_type=class_type,
        status=status,
        date_from=date_from,
        date_to=date_to,
        time_from=time_from,
        time_to=time_to,
        search=search,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    # 获取课程列表
    classes_list, total = service.get_scheduled_classes(query_params)
    
    # 转换为响应模型
    classes_response = [
        ScheduledClassList.model_validate(cls) 
        for cls in classes_list
    ]
    
    return page_response(
        items=classes_response,
        total=total,
        page=page,
        size=size,
        message="获取课程列表成功"
    )


@router.get(
    "/available",
    response_model=PageResponse[ScheduledClassList],
    summary="获取可预约课程",
    description="会员端获取可预约的课程列表"
)
def get_available_classes(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    teacher_id: Optional[int] = Query(None, gt=0, description="教师ID"),
    teacher_category: Optional[str] = Query(None, description="教师分类"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    time_from: Optional[time] = Query(None, description="时间段开始"),
    time_to: Optional[time] = Query(None, description="时间段结束"),
    max_price: Optional[int] = Query(None, ge=0, description="最高价格"),
    sort_by: str = Query("class_datetime", description="排序字段"),
    sort_order: str = Query("asc", description="排序方向"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取可预约课程列表"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 获取可用课程列表
    available_classes = service.get_available_classes(
        teacher_id=teacher_id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None
    )
    
    # 转换为响应模型
    classes_response = [
        ScheduledClassList.model_validate(cls) 
        for cls in available_classes
    ]
    
    return page_response(
        items=classes_response,
        total=len(classes_response),
        page=page,
        size=size,
        message="获取可预约课程列表成功"
    )


@router.get(
    "/teacher/{teacher_id}",
    response_model=DataResponse[List[ScheduledClassList]],
    summary="获取教师课程",
    description="获取指定教师的课程列表"
)
def get_teacher_classes(
    teacher_id: int,
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    status: Optional[str] = Query(None, description="课程状态"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师课程列表"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 获取教师课程
    teacher_classes = service.get_teacher_classes(
        teacher_id=teacher_id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None,
        status=status
    )
    
    # 转换为响应模型
    classes_response = [
        ScheduledClassList.model_validate(cls) 
        for cls in teacher_classes
    ]
    
    return success_response(classes_response, f"获取教师 {teacher_id} 的课程列表成功")


@router.get(
    "/member/{member_id}",
    response_model=DataResponse[List[ScheduledClassList]],
    summary="获取会员课程",
    description="获取指定会员的课程列表"
)
def get_member_classes(
    member_id: int,
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    status: Optional[str] = Query(None, description="课程状态"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取会员课程列表"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 获取会员课程
    member_classes = service.get_member_classes(
        member_id=member_id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None,
        status=status
    )
    
    # 转换为响应模型
    classes_response = [
        ScheduledClassList.model_validate(cls) 
        for cls in member_classes
    ]
    
    return success_response(classes_response, f"获取会员 {member_id} 的课程列表成功")


# ==================== 课程预约和取消接口 ====================

@router.post(
    "/book/{class_id}",
    response_model=DataResponse[ScheduledClassRead],
    summary="预约课程",
    description="会员预约指定课程（支持会员卡自动扣费）"
)
def book_class(
    class_id: int,
    member_id: int = Query(..., gt=0, description="会员ID"),
    booking_data: Optional[ClassBookingData] = None,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """预约课程 - 支持会员卡自动扣费"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)

    # 转换 ClassBookingData 为 MemberClassBooking
    if booking_data:
        member_booking_data = MemberClassBooking(
            class_id=class_id,
            member_card_id=booking_data.member_card_id,
            member_card_name=booking_data.member_card_name,
            material_name=booking_data.material_name,
            booking_remark=booking_data.booking_remark
        )
    else:
        # 如果没有提供预约数据，创建基本的预约数据
        member_booking_data = MemberClassBooking(
            class_id=class_id,
            booking_remark="管理员代为预约"
        )

    # 使用会员预约方法（支持会员卡集成）
    scheduled_class = service.book_member_class(
        member_id=member_id,
        member_booking_data=member_booking_data,
        created_by=user_context.user.id
    )

    # 转换为响应模型
    class_response = ScheduledClassRead.model_validate(scheduled_class)

    return success_response(class_response, "课程预约成功")


@router.post(
    "/cancel/{class_id}",
    response_model=DataResponse[ScheduledClassRead],
    summary="取消课程预约",
    description="取消指定课程的预约"
)
def cancel_booking(
    class_id: int,
    cancellation_data: Optional[ClassCancellation] = None,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """取消课程预约"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 取消预约
    scheduled_class = service.cancel_booking(
        class_id=class_id,
        cancellation_data=cancellation_data,
        operator_name=user_context.user.real_name
    )
    
    # 转换为响应模型
    class_response = ScheduledClassRead.model_validate(scheduled_class)
    
    return success_response(class_response, "取消课程预约成功")


@router.post(
    "/batch/book",
    response_model=DataResponse[BatchBookResult],
    summary="批量预约课程",
    description="给同一个会员批量预约多个课程（支持会员卡自动扣费）"
)
def batch_book_classes(
    member_id: int = Query(..., gt=0, description="会员ID"),
    class_ids: List[int] = Query(..., description="要预约的课程ID列表"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量预约课程 - 给同一个会员批量预约多个课程"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)

    # 批量预约
    batch_result = service.batch_book_classes(
        member_id=member_id,
        class_ids=class_ids,
        operator_name=user_context.user.real_name
    )
    
    # 构建响应消息
    success_count = batch_result.success_count
    failed_count = batch_result.failed_count
    
    if failed_count == 0:
        message = f"批量预约 {success_count} 个课程成功"
    elif success_count == 0:
        message = f"批量预约失败，{failed_count} 个课程预约失败"
    else:
        message = f"批量预约完成：{success_count} 个成功，{failed_count} 个失败"
    
    return success_response(batch_result, message)


@router.post(
    "/batch/book-atomic",
    response_model=DataResponse[BatchBookResult],
    summary="批量预约课程（原子事务）",
    description="给同一个会员批量预约多个课程，全部成功或全部失败（原子事务）"
)
def batch_book_classes_atomic(
    member_id: int = Query(..., gt=0, description="会员ID"),
    class_ids: List[int] = Query(..., description="要预约的课程ID列表"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量预约课程（原子事务版本）- 全部成功或全部失败"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)

    # 批量预约（原子事务）
    batch_result = service.batch_book_classes_atomic(
        member_id=member_id,
        class_ids=class_ids,
        operator_name=user_context.user.real_name
    )

    # 构建响应消息
    success_count = batch_result.success_count
    failed_count = batch_result.failed_count

    if failed_count == 0:
        message = f"原子事务批量预约 {success_count} 个课程成功"
    else:
        message = f"原子事务批量预约失败，所有 {len(class_ids)} 个课程预约都已回滚"

    return success_response(batch_result, message)


@router.post(
    "/batch/cancel",
    response_model=DataResponse[BatchCancelResult],
    summary="批量取消课程预约",
    description="批量取消多个课程的预约"
)
def batch_cancel_bookings(
    class_ids: List[int],
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量取消课程预约"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 批量取消
    batch_result = service.batch_cancel_bookings(
        class_ids=class_ids,
        operator_name=user_context.user.real_name
    )
    
    # 构建响应消息
    success_count = batch_result.success_count
    failed_count = batch_result.failed_count
    
    if failed_count == 0:
        message = f"批量取消 {success_count} 个课程预约成功"
    elif success_count == 0:
        message = f"批量取消失败，{failed_count} 个课程取消失败"
    else:
        message = f"批量取消完成：{success_count} 个成功，{failed_count} 个失败"
    
    return success_response(batch_result, message)


@router.post(
    "/check-conflict",
    response_model=DataResponse[ConflictCheckResponse],
    summary="检查时间冲突",
    description="检查指定时间是否存在冲突"
)
def check_time_conflict(
    conflict_request: ConflictCheckRequest,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """检查时间冲突"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 检查冲突
    conflict_result = service.check_time_conflict(
        teacher_id=conflict_request.teacher_id,
        member_id=conflict_request.member_id,
        class_datetime=conflict_request.class_datetime,
        duration_minutes=conflict_request.duration_minutes,
        exclude_class_id=conflict_request.exclude_class_id
    )
    
    # 转换为响应模型
    conflict_response = ConflictCheckResponse(**conflict_result)
    
    return success_response(conflict_response, "时间冲突检查完成")


@router.post(
    "/status/{class_id}",
    response_model=DataResponse[ScheduledClassRead],
    summary="更新课程状态",
    description="更新指定课程的状态"
)
def update_class_status(
    class_id: int,
    status_data: ClassStatusUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新课程状态"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 更新状态
    scheduled_class = service.update_class_status(
        class_id=class_id,
        status_data=status_data,
        operator_name=user_context.user.real_name
    )
    
    # 转换为响应模型
    class_response = ScheduledClassRead.model_validate(scheduled_class)
    
    return success_response(class_response, "课程状态更新成功")


@router.post(
    "/batch/status",
    response_model=DataResponse[List[ScheduledClassRead]],
    summary="批量更新课程状态",
    description="批量更新多个课程的状态"
)
def batch_update_status(
    batch_data: BatchClassStatusUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量更新课程状态"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 批量更新状态
    updated_classes = service.batch_update_status(
        class_ids=batch_data.class_ids,
        new_status=batch_data.new_status,
        updated_by=user_context.user.id
    )
    
    # 转换为响应模型
    classes_response = [
        ScheduledClassRead.model_validate(cls) 
        for cls in updated_classes
    ]
    
    return success_response(
        classes_response, 
        f"批量更新 {len(updated_classes)} 个课程状态成功"
    )


@router.post(
    "/batch/delete",
    response_model=MessageResponse,
    summary="批量删除课程",
    description="批量删除多个课程（软删除）"
)
def batch_delete_classes(
    class_ids: List[int],
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量删除课程（软删除）"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 批量删除
    deleted_count = service.batch_delete_classes(class_ids=class_ids)
    
    return message_response(f"批量删除 {deleted_count} 个课程成功")


# ==================== 课程统计接口 ====================

@router.get(
    "/stats/count",
    response_model=DataResponse[dict],
    summary="课程统计",
    description="获取课程数量统计"
)
def get_class_statistics(
    teacher_id: Optional[int] = Query(None, gt=0, description="教师ID"),
    member_id: Optional[int] = Query(None, gt=0, description="会员ID"),
    date_from: Optional[date] = Query(None, description="开始日期"),
    date_to: Optional[date] = Query(None, description="结束日期"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取课程统计数据"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 获取统计数据
    stats = service.count_classes_by_status(
        teacher_id=teacher_id,
        member_id=member_id,
        date_from=datetime.combine(date_from, time.min) if date_from else None,
        date_to=datetime.combine(date_to, time.max) if date_to else None
    )
    
    return success_response(stats, "获取课程统计成功")


# ==================== 临时课程创建接口 ====================

@router.post(
    "/temp/create",
    response_model=DataResponse[ScheduledClassRead],
    summary="创建临时课程",
    description="管理员创建临时课程（直接约课）"
)
def create_temp_class(
    class_data: AdminClassCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建临时课程"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 创建临时课程
    scheduled_class = service.create_admin_class(
        class_data, 
        created_by=user_context.user.id
    )
    
    # 转换为响应模型
    class_response = ScheduledClassRead.model_validate(scheduled_class)
    
    return success_response(class_response, "创建临时课程成功")


@router.post(
    "/teacher/create",
    response_model=DataResponse[ScheduledClassRead],
    summary="教师创建课程",
    description="教师开放可预约课节"
)
def create_teacher_class(
    teacher_id: int,
    class_data: TeacherClassCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """教师创建课程"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 教师创建课程
    scheduled_class = service.create_teacher_class(
        teacher_id,
        class_data, 
        created_by=user_context.user.id
    )
    
    # 转换为响应模型
    class_response = ScheduledClassRead.model_validate(scheduled_class)
    
    return success_response(class_response, "教师创建课程成功")


@router.post(
    "/batch/teacher/create",
    response_model=DataResponse[List[ScheduledClassRead]],
    summary="教师批量创建课程",
    description="教师批量开放可预约课节"
)
def batch_create_teacher_classes(
    batch_data: BatchTeacherClassCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """教师批量创建课程"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 批量创建课程
    scheduled_classes = service.batch_create_teacher_classes(
        teacher_id=batch_data.teacher_id,
        class_datetimes=batch_data.class_datetimes,
        duration_minutes=batch_data.duration_minutes,
        price=batch_data.price,
        material_name=batch_data.material_name,
        is_visible_to_member=batch_data.is_visible_to_member,
        created_by=user_context.user.id
    )
    
    # 转换为响应模型
    classes_response = [
        ScheduledClassRead.model_validate(cls) 
        for cls in scheduled_classes
    ]
    
    return success_response(
        classes_response, 
        f"批量创建 {len(scheduled_classes)} 个课程成功"
    )


# ==================== 参数路径接口（放在最后） ====================

@router.get(
    "/{class_id}",
    response_model=DataResponse[ScheduledClassRead],
    summary="获取课程详情",
    description="根据课程ID获取详细信息"
)
def get_class_detail(
    class_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取课程详情"""
    tenant_id = user_context.tenant_context.tenant_id
    service = ScheduledClassService(session, tenant_id)
    
    # 获取课程详情
    scheduled_class = service.get_scheduled_class(class_id)
    scheduled_class = ensure_found(scheduled_class, "课程")
    
    # 转换为响应模型
    class_response = ScheduledClassRead.model_validate(scheduled_class)
    
    return success_response(class_response, "获取课程详情成功") 