"""
管理端 - 教师管理API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, status, Query, UploadFile, File
from sqlmodel import Session

from app.features.teachers.models import Teacher<PERSON>ategory, TeacherRegion, TeacherStatus
from app.features.teachers.schemas import (
    <PERSON><PERSON><PERSON>, Teacher<PERSON>p<PERSON>, Teacher<PERSON><PERSON>, Teacher<PERSON><PERSON>, Teacher<PERSON><PERSON><PERSON>,
    Teacher<PERSON><PERSON><PERSON>, Teacher<PERSON><PERSON>Assign, TeacherTagBatch, TeacherStatusUpdate
)
from app.features.teachers.service import TeacherService
from app.features.teachers.exceptions import TeacherErrorCode
from app.db.session import get_session
from app.core.context import UserContext
from app.core.dependencies import get_user_context
from app.api.common import (
    DataResponse,
    MessageResponse,
    ListResponse,
    PageResponse,
    success_response,
    message_response,
    list_response,
    page_response,
    ensure_found,
    ensure_success
)
from app.api.common.responses import create_error_responses, ErrorResponse, AUTH_ONLY_RESPONSES, PERMISSION_ONLY_RESPONSES, RESOURCE_RESPONSES

router = APIRouter()

# 教师模块的错误响应文档
TEACHER_ERROR_RESPONSES = create_error_responses([
    TeacherErrorCode.EMAIL_EXISTS,
    TeacherErrorCode.PHONE_EXISTS,
    TeacherErrorCode.WECHAT_BOUND,
    TeacherErrorCode.STATUS_INVALID,
    TeacherErrorCode.TAG_NOT_FOUND,
    TeacherErrorCode.TAG_ALREADY_ASSIGNED,
    TeacherErrorCode.SLOT_CONFLICT,
    TeacherErrorCode.SLOT_NOT_FOUND,
    TeacherErrorCode.INVALID_TIME_RANGE,
    TeacherErrorCode.BATCH_OPERATION_FAILED,
    TeacherErrorCode.SLOT_NOT_BELONG_TO_TEACHER,
    TeacherErrorCode.INVALID_OPERATION,
    TeacherErrorCode.INVALID_FILE_TYPE,
    TeacherErrorCode.FILE_TOO_LARGE,
    TeacherErrorCode.INVALID_TAG_IDS,
    TeacherErrorCode.INVALID_TIME_FORMAT
])


# ==================== 教师统计接口 ====================

@router.get(
    "/statistics",
    response_model=DataResponse[dict],
    summary="获取教师统计信息",
    description="获取教师数量统计和分布信息"
)
def get_teacher_statistics(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师统计信息"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    
    stats = teacher_service.get_teachers_statistics()
    return success_response(stats, "获取教师统计信息成功")


# ==================== 教师高级查询接口 ====================

@router.get(
    "/priority",
    response_model=ListResponse[TeacherList],
    summary="按优先级获取教师",
    description="按优先级排序获取激活状态的教师列表"
)
def get_teachers_by_priority(
    limit: int = Query(default=10, ge=1, le=50, description="返回数量限制"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """按优先级获取教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    teachers = teacher_service.get_teachers_by_priority(limit=limit)

    # 转换为TeacherList格式
    teacher_list = []
    for teacher in teachers:
        teacher_tags = teacher_service.get_teacher_tags(teacher.id)
        teacher_dict = teacher.model_dump()
        teacher_dict['tag_count'] = len(teacher_tags)
        teacher_list.append(TeacherList(**teacher_dict))

    return list_response(teacher_list, len(teacher_list), "获取优先级教师列表成功")


@router.get(
    "/available",
    response_model=ListResponse[TeacherList],
    summary="获取对会员可见的教师",
    description="获取激活且对会员端展示的教师列表"
)
def get_available_teachers_for_members(
    region: Optional[TeacherRegion] = Query(default=None, description="教师区域筛选"),
    category: Optional[TeacherCategory] = Query(default=None, description="教师分类筛选"),
    max_price: Optional[int] = Query(default=None, ge=0, description="最高价格筛选"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取对会员可见的教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    teachers = teacher_service.get_available_teachers_for_members(
        region=region,
        category=category,
        max_price=max_price
    )

    # 转换为TeacherList格式
    teacher_list = []
    for teacher in teachers:
        teacher_tags = teacher_service.get_teacher_tags(teacher.id)
        teacher_dict = teacher.model_dump()
        teacher_dict['tag_count'] = len(teacher_tags)
        teacher_list.append(TeacherList(**teacher_dict))

    return list_response(teacher_list, len(teacher_list), "获取可用教师列表成功")


@router.get(
    "/search",
    response_model=ListResponse[TeacherList],
    summary="搜索教师",
    description="根据关键词搜索教师"
)
def search_teachers(
    keyword: str = Query(description="搜索关键词"),
    limit: int = Query(default=20, ge=1, le=100, description="返回数量限制"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """搜索教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    teachers = teacher_service.search_teachers(keyword, limit=limit)

    # 转换为TeacherList格式
    teacher_list = []
    for teacher in teachers:
        teacher_tags = teacher_service.get_teacher_tags(teacher.id)
        teacher_dict = teacher.model_dump()
        teacher_dict['tag_count'] = len(teacher_tags)
        teacher_list.append(TeacherList(**teacher_dict))

    return list_response(teacher_list, len(teacher_list), "搜索教师成功")


# ==================== 教师标签管理接口 ====================

@router.post(
    "/tags/batch",
    response_model=MessageResponse,
    summary="批量管理教师标签",
    description="批量为多个教师分配或移除标签"
)
def batch_manage_teacher_tags(
    batch_data: TeacherTagBatch,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """批量管理教师标签"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    # 验证操作类型
    teacher_service.validate_batch_operation(batch_data.operation)

    if batch_data.operation == "add":
        teacher_service.batch_assign_tags(
            batch_data.teacher_ids,
            batch_data.tag_ids,
            created_by=user_context.user.id
        )
        return message_response("批量分配标签成功")
    elif batch_data.operation == "remove":
        teacher_service.batch_remove_tags(batch_data.teacher_ids, batch_data.tag_ids)
        return message_response("批量移除标签成功")


# ==================== 教师基础CRUD接口 ====================

@router.post(
    "/", 
    response_model=DataResponse[TeacherRead], 
    status_code=status.HTTP_201_CREATED,
    responses=TEACHER_ERROR_RESPONSES,
    summary="创建教师",
    description="""
    创建新教师
    
    **可能的错误码：**
    - `TEACHER_EMAIL_EXISTS`: 邮箱已存在
    - `TEACHER_PHONE_EXISTS`: 手机号已存在
    - `TEACHER_WECHAT_BOUND`: 微信已绑定其他教师
    - `VALIDATION_ERROR`: 数据验证失败
    """
)
def create_teacher(
    teacher_data: TeacherCreate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """创建教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    
    teacher = teacher_service.create_teacher(teacher_data, created_by=user_context.user.id)
    return success_response(teacher, "教师创建成功")

@router.get(
    "/",
    response_model=PageResponse[TeacherList],
    summary="获取教师列表",
    description="获取教师列表，支持分页、筛选和排序"
)
def get_teachers(
    query: TeacherQuery = Depends(),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师列表"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    # 使用优化的方法一次性获取教师和标签数据
    teachers_list, total = teacher_service.get_teachers_with_tags_in_one_query(query)

    # 不再需要转换，直接返回
    return page_response(teachers_list, total, query.page, query.size, "获取教师列表成功")


@router.get(
    "/all",
    response_model=ListResponse[TeacherList],
    summary="获取所有教师列表",
    description="获取所有教师列表，不分页，包含标签信息"
)
def get_all_teachers(
    status: Optional[TeacherStatus] = Query(default=None, description="教师状态筛选"),
    show_to_members: Optional[bool] = Query(default=None, description="是否对会员端展示"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取所有教师列表"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    # 使用专门的方法获取所有教师和标签数据
    teachers_list = teacher_service.get_all_teachers_with_tags(
        status=status,
        show_to_members=show_to_members
    )

    return list_response(teachers_list, len(teachers_list), "获取所有教师列表成功")

@router.get(
    "/{teacher_id}",
    response_model=DataResponse[TeacherDetail],
    summary="获取教师详情",
    description="根据ID获取教师详情，包含关联的标签信息"
)
def get_teacher(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师详情"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    
    teacher = teacher_service.get_teacher_with_stats(teacher_id)
    teacher = ensure_found(teacher, "教师")

    # 获取教师标签
    teacher_tags = teacher_service.get_teacher_tags(teacher_id)

    # 构建详情响应
    teacher_detail = TeacherDetail(**teacher.model_dump(), tags=teacher_tags)

    return success_response(teacher_detail, "获取教师详情成功")


@router.post(
    "/{teacher_id}/update",
    response_model=DataResponse[TeacherRead],
    summary="更新教师信息",
    description="更新教师基本信息"
)
def update_teacher(
    teacher_id: int,
    teacher_data: TeacherUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新教师信息"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    
    updated_teacher = teacher_service.update_teacher(teacher_id, teacher_data)
    # 获取包含统计信息的完整数据返回
    teacher_with_stats = teacher_service.get_teacher_with_stats(teacher_id)
    return success_response(teacher_with_stats, "教师信息更新成功")


@router.post(
    "/{teacher_id}/delete",
    response_model=MessageResponse,
    summary="删除教师",
    description="删除指定教师"
)
def delete_teacher(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    
    teacher_service.delete_teacher(teacher_id)
    return message_response("教师删除成功")


# ==================== 教师状态管理接口 ====================

@router.post(
    "/{teacher_id}/status",
    response_model=DataResponse[TeacherRead],
    summary="更新教师状态",
    description="更新教师状态（激活/停用/暂停）"
)
def update_teacher_status(
    teacher_id: int,
    status_data: TeacherStatusUpdate,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新教师状态"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    
    updated_teacher = teacher_service.update_teacher_status(teacher_id, status_data)
    return success_response(updated_teacher, "教师状态更新成功")


@router.post("/{teacher_id}/update-stats", response_model=DataResponse[TeacherRead])
def update_teacher_stats(
    teacher_id: int,
    class_completed: bool = False,
    class_cancelled: bool = False,
    class_no_show: bool = False,
    earnings_added: int = 0,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新教师统计信息"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    updated_teacher = teacher_service.update_teacher_stats(
        teacher_id,
        class_completed=class_completed,
        class_cancelled=class_cancelled,
        class_no_show=class_no_show,
        earnings_added=earnings_added
    )

    updated_teacher = ensure_found(updated_teacher, "教师")
    # 获取包含最新统计信息的完整数据返回
    teacher_with_stats = teacher_service.get_teacher_with_stats(teacher_id)
    return success_response(teacher_with_stats, "教师统计信息更新成功")


@router.post(
    "/{teacher_id}/activate",
    response_model=DataResponse[TeacherRead],
    summary="激活教师",
    description="激活指定教师"
)
def activate_teacher(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """激活教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    
    activated_teacher = teacher_service.activate_teacher(teacher_id)
    return success_response(activated_teacher, "教师激活成功")


@router.post(
    "/{teacher_id}/deactivate",
    response_model=DataResponse[TeacherRead],
    summary="停用教师",
    description="停用指定教师"
)
def deactivate_teacher(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """停用教师"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    
    deactivated_teacher = teacher_service.deactivate_teacher(teacher_id)
    return success_response(deactivated_teacher, "教师停用成功")


# ==================== 教师标签管理接口 ====================

@router.post(
    "/{teacher_id}/tags",
    response_model=MessageResponse,
    summary="为教师分配标签",
    description="为指定教师分配一个或多个标签"
)
def assign_tags_to_teacher(
    teacher_id: int,
    tag_data: TeacherTagAssign,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """为教师分配标签"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    # 使用路径参数中的teacher_id，忽略请求体中的teacher_id
    teacher_service.assign_tags_to_teacher(
        teacher_id,
        tag_data.tag_ids,
        created_by=user_context.user.id
    )
    return message_response("教师标签分配成功")


@router.post(
    "/{teacher_id}/tags/remove",
    response_model=MessageResponse,
    summary="移除教师标签",
    description="移除教师的指定标签"
)
def remove_tags_from_teacher(
    teacher_id: int,
    tag_ids: str = Query(description="要移除的标签ID列表，逗号分隔"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """移除教师标签"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    # 解析标签ID列表
    tag_id_list = teacher_service.validate_tag_ids(tag_ids)

    teacher_service.remove_tags_from_teacher(teacher_id, tag_id_list)
    return message_response("教师标签移除成功")


@router.get(
    "/{teacher_id}/tags",
    response_model=ListResponse[dict],
    summary="获取教师标签列表",
    description="获取指定教师的所有标签"
)
def get_teacher_tags(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取教师标签列表"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    teacher_tags = teacher_service.get_teacher_tags(teacher_id)
    return list_response(teacher_tags, len(teacher_tags), "获取教师标签列表成功")


# ==================== 教师头像上传接口 ====================

@router.post(
    "/{teacher_id}/avatar",
    response_model=DataResponse[dict],
    summary="上传教师头像",
    description="上传教师头像图片（基础版本，返回文件路径）"
)
async def upload_teacher_avatar(
    teacher_id: int,
    file: UploadFile = File(..., description="头像图片文件"),
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """上传教师头像"""
    import uuid
    from pathlib import Path

    # 读取文件内容
    content = await file.read()
    file_size = len(content)

    # 验证文件类型和大小
    teacher_service.validate_file_upload(file.content_type, file_size)

    # 验证教师是否存在
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)
    teacher = teacher_service.get_teacher(teacher_id)
    teacher = ensure_found(teacher, "教师")

    # 生成文件名和路径
    file_extension = Path(file.filename).suffix if file.filename else '.jpg'
    filename = f"teacher_{teacher_id}_{uuid.uuid4().hex[:8]}{file_extension}"

    # 创建上传目录
    upload_dir = Path("uploads/teachers/avatars")
    upload_dir.mkdir(parents=True, exist_ok=True)

    # 保存文件
    file_path = upload_dir / filename
    with open(file_path, "wb") as buffer:
        buffer.write(content)

    # 生成访问URL（这里简化处理，实际应该配置静态文件服务）
    avatar_url = f"/uploads/teachers/avatars/{filename}"

    # 更新教师头像URL
    from app.features.teachers.schemas import TeacherUpdate
    teacher_update = TeacherUpdate(avatar=avatar_url)
    teacher_service.update_teacher(teacher_id, teacher_update)

    return success_response({
        "avatar_url": avatar_url,
        "filename": filename,
        "file_size": file_size
    }, "头像上传成功")


@router.post(
    "/{teacher_id}/avatar/delete",
    response_model=MessageResponse,
    summary="删除教师头像",
    description="删除教师头像"
)
def delete_teacher_avatar(
    teacher_id: int,
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """删除教师头像"""
    tenant_id = user_context.tenant_context.tenant_id
    teacher_service = TeacherService(session, tenant_id)

    # 验证教师是否存在
    teacher = teacher_service.get_teacher(teacher_id)
    teacher = ensure_found(teacher, "教师")

    # 删除头像文件（如果存在）
    if teacher.avatar and teacher.avatar.startswith("/uploads/"):
        import os
        file_path = teacher.avatar.lstrip("/")
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
            except OSError:
                pass  # 忽略文件删除错误

    # 清空教师头像URL
    from app.features.teachers.schemas import TeacherUpdate
    teacher_update = TeacherUpdate(avatar=None)
    teacher_service.update_teacher(teacher_id, teacher_update)

    return message_response("头像删除成功")