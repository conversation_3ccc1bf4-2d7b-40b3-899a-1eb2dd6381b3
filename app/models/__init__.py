"""
统一模型导入

确保所有模型都被导入，以便 SQLModel.metadata.create_all() 能够创建所有表
"""

from app.features.tenants.models import *
from app.features.users.models import *
from app.features.members.models import *
from app.features.members.fixed_lock_models import *
from app.features.members.statistics_models import *
from app.features.member_cards.models import *
from app.features.tags.models import *
from app.features.teachers.models import *
from app.features.teachers.fixed_slots_models import *
from app.features.teachers.statistics_models import *
from app.features.member_cards.models import *
from app.features.courses.config_models import *
from app.features.courses.scheduled_classes_models import *
from app.features.courses.scheduling.models import *
from app.features.courses.operations.models import *

# # Tenant models moved to app.features.tenants
# from app.models.shared.system import SystemConfig, SystemAdmin, SystemConfigCreate, SystemAdminCreate
# from app.models.shared.usage_stats import TenantUsageStats, TenantUsageMonthly, UsageStatsCreate
# from app.models.shared.audit import SystemAuditLog, AuditLogCreate

# __all__ = [
#     "SystemConfig", "SystemAdmin", "SystemConfigCreate", "SystemAdminCreate",
#     "TenantUsageStats", "TenantUsageMonthly", "UsageStatsCreate",
#     "SystemAuditLog", "AuditLogCreate"
# ]