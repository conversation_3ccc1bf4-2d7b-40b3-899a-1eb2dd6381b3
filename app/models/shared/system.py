from sqlmodel import SQLModel, Field, Column
from sqlalchemy import <PERSON><PERSON><PERSON>
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum


class ConfigType(str, Enum):
    """配置类型枚举"""
    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "boolean"
    JSON = "json"


class AdminRole(str, Enum):
    """管理员角色枚举"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    BILLING_ADMIN = "billing_admin"
    SUPPORT = "support"


class AdminStatus(str, Enum):
    """管理员状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    LOCKED = "locked"


# 系统配置模型
class SystemConfigBase(SQLModel):
    """系统配置基础模型"""
    config_key: str = Field(max_length=100, unique=True, description="配置键")
    config_value: Optional[str] = Field(default=None, description="配置值")
    config_type: ConfigType = Field(default=ConfigType.STRING, description="配置类型")
    description: Optional[str] = Field(default=None, description="配置描述")
    category: Optional[str] = Field(default=None, max_length=50, description="配置分类")
    is_active: bool = Field(default=True, description="是否激活")
    is_public: bool = Field(default=False, description="是否对租户可见")


class SystemConfig(SystemConfigBase, table=True):
    """系统配置数据库模型"""
    __tablename__ = "system_configs"

    id: Optional[int] = Field(default=None, primary_key=True)
    tenant_id: Optional[int] = Field(default=None, foreign_key="tenants.id", description="租户ID（可选）")
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    updated_at: Optional[datetime] = Field(default=None)


# 系统管理员模型
class SystemAdminBase(SQLModel):
    """系统管理员基础模型"""
    username: str = Field(max_length=50, unique=True, description="用户名")
    email: str = Field(max_length=100, unique=True, description="邮箱")
    real_name: Optional[str] = Field(default=None, max_length=50, description="真实姓名")
    role: AdminRole = Field(description="角色")
    status: AdminStatus = Field(default=AdminStatus.ACTIVE, description="状态")


class SystemAdmin(SystemAdminBase, table=True):
    """系统管理员数据库模型"""
    __tablename__ = "system_admins"

    id: Optional[int] = Field(default=None, primary_key=True)
    password_hash: str = Field(max_length=255, description="密码哈希")
    
    # 权限信息 (JSON字段)
    permissions: Optional[Dict[str, Any]] = Field(default=None, sa_column=Column(JSON), description="权限列表")

    # 登录信息
    last_login_at: Optional[datetime] = Field(default=None, description="最后登录时间")
    login_count: int = Field(default=0, description="登录次数")
    failed_login_attempts: int = Field(default=0, description="失败登录次数")
    locked_until: Optional[datetime] = Field(default=None, description="锁定到期时间")

    # 审计字段
    created_at: datetime = Field(default_factory=lambda: datetime.now())
    updated_at: Optional[datetime] = Field(default=None)
    created_by: Optional[int] = Field(default=None, foreign_key="system_admins.id", description="创建者ID")


# API模型
class SystemConfigCreate(SystemConfigBase):
    """创建系统配置请求模型"""
    pass


class SystemAdminCreate(SystemAdminBase):
    """创建系统管理员请求模型"""
    password: str = Field(min_length=8, description="密码") 