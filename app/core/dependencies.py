from typing import Optional, Union
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlmodel import Session, select, text
from app.db.session import get_session, get_global_session
from app.features.users.models import User, UserRole
from app.features.members.models import Member
from app.features.tenants.models import Tenant
from app.utils.security import verify_token
from .context import TenantContext, UserContext, MemberContext
from app.api.common.exceptions import PermissionError, AuthenticationError, NotFoundError


# HTTP Bearer token 认证
security = HTTPBearer(auto_error=False)


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session: Session = Depends(get_session)
) -> User:
    """获取当前CMS用户（管理员）"""
    
    # 检查是否提供了认证信息
    if not credentials:
        raise AuthenticationError("未提供认证凭据")
    
    # 验证token并获取用户信息
    token_data = verify_token(credentials.credentials)
    if not token_data or not token_data.get('sub'):
        raise AuthenticationError("无效的认证凭据")
    
    # 从token中获取信息并做类型校验
    try:
        user_id = int(token_data.get('sub'))
    except Exception:
        raise AuthenticationError("无效的用户ID")

    tenant_id_raw = token_data.get('tenant_id')  # 可能为None（超级管理员）
    user_type = token_data.get('user_type')

    # 验证用户类型
    if user_type != 'admin':
        raise AuthenticationError("用户类型不匹配")

    # 设置租户上下文（如果有）
    if tenant_id_raw is not None and tenant_id_raw != "":
        try:
            tenant_id_int = int(tenant_id_raw)
        except Exception:
            raise AuthenticationError("无效的租户ID")
        session.exec(text("SET app.current_tenant_id = :tenant_id").params(tenant_id=str(tenant_id_int)))
    else:
        # 超级管理员，清除租户上下文
        session.exec(text("RESET app.current_tenant_id"))

    # 查找用户（RLS会自动过滤）
    user = session.get(User, user_id)
    
    if user is None:
        raise AuthenticationError("用户不存在")
    
    if user.status != "active":
        raise AuthenticationError("用户账户已被禁用")
    
    return user


async def get_current_member(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    session: Session = Depends(get_session)
) -> Member:
    """获取当前会员"""
    
    # 检查是否提供了认证信息
    if not credentials:
        raise AuthenticationError("未提供认证凭据")
    
    # 验证token并获取会员信息
    token_data = verify_token(credentials.credentials)
    if not token_data:
        raise AuthenticationError("无效的认证凭据")
    
    # 从token中获取信息并做类型校验
    try:
        member_id = int(token_data.get('sub'))
    except Exception:
        raise AuthenticationError("无效的会员ID")

    tenant_id_raw = token_data.get('tenant_id')
    user_type = token_data.get('user_type')

    # 验证用户类型和必要字段
    if user_type != 'member':
        raise AuthenticationError("用户类型不匹配")

    if tenant_id_raw is None or tenant_id_raw == "":
        raise AuthenticationError("租户ID缺失")

    # 设置租户上下文
    try:
        tenant_id_int = int(tenant_id_raw)
    except Exception:
        raise AuthenticationError("无效的租户ID")

    session.exec(text("SET app.current_tenant_id = :tenant_id").params(tenant_id=str(tenant_id_int)))

    # 查找会员（RLS会自动过滤）
    member = session.get(Member, member_id)
    
    if member is None:
        raise AuthenticationError("会员不存在")
    
    if member.member_status != "active":
        raise AuthenticationError("会员账户已被禁用")
    
    return member


async def get_user_context(
    user: User = Depends(get_current_user),
    session: Session = Depends(get_session)
) -> UserContext:
    """获取用户上下文"""
    tenant_context = None
    
    if user.tenant_id:  # 非超级管理员
        tenant = session.get(Tenant, user.tenant_id)
        if not tenant:
            raise NotFoundError("租户")
        tenant_context = TenantContext(tenant_id=user.tenant_id, tenant=tenant)
    
    return UserContext(user=user, tenant_context=tenant_context)


async def get_member_context(
    member: Member = Depends(get_current_member),
    session: Session = Depends(get_session)
) -> MemberContext:
    """获取会员上下文"""
    tenant = session.get(Tenant, member.tenant_id)
    if not tenant:
        raise NotFoundError("租户")
    
    tenant_context = TenantContext(tenant_id=member.tenant_id, tenant=tenant)
    return MemberContext(member=member, tenant_context=tenant_context)


def require_roles(*allowed_roles: UserRole):
    """角色权限装饰器"""
    def role_checker(user_context: UserContext = Depends(get_user_context)):
        if user_context.user.role not in allowed_roles:
            raise PermissionError("角色权限不足")
        return user_context
    return role_checker


def require_super_admin(user_context: UserContext = Depends(get_user_context)):
    """要求超级管理员权限"""
    if not user_context.is_super_admin:
        raise PermissionError("需要超级管理员权限")
    return user_context


# 便捷的依赖注入函数（当前未使用，避免误导，先删除）
# 如需提供，会话依赖应显式 yield Session，并在进入前设置 GUC。
