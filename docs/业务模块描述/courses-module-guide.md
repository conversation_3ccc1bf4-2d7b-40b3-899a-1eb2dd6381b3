# 课程系统模块开发指南

> 版本：v1.0  
> 更新时间：2025-01-03  
> 模块路径：`app/features/courses/`

## 📋 模块概述

课程系统模块是整个英语教学平台的核心模块，负责管理课程配置和已排课表。该模块采用功能拆分设计，分为两个主要子模块：

### 子模块结构

```
app/features/courses/
├── config_*           # 课程系统配置子模块
│   ├── config_models.py      # 配置数据模型
│   ├── config_schemas.py     # 配置API模式
│   ├── config_service.py     # 配置业务逻辑
│   ├── config_router.py      # 配置API路由(已转移到app/api目录下)
│   ├── config_exceptions.py  # 配置异常处理
│   └── config_utils.py       # 配置工具函数
└── scheduled_classes_*       # 已排课表子模块
    ├── scheduled_classes_models.py        # 课表数据模型
    ├── scheduled_classes_schemas.py       # 课表API模式
    ├── scheduled_classes_service.py       # 课表业务逻辑
    ├── scheduled_classes_router.py        # 课表API路由
    ├── scheduled_classes_exceptions.py    # 课表异常处理
    ├── scheduled_classes_validators.py    # 课表验证器
    └── scheduled_classes_business_rules.py # 课表业务规则
```

## 🔧 课程系统配置子模块 (config\_\*)

### 功能职责

- 管理租户级别的课程系统配置
- 提供课程预约、排课、时间管理等参数配置
- 支持配置的动态更新和一致性验证

### 核心配置项

#### 基础配置

- `default_slot_duration_minutes`: 默认课节时长(分钟)
- `default_slot_interval_minutes`: 默认课节间隔(分钟)

#### 直接约课配置

- `direct_booking_enabled`: 是否启用直接约课
- `max_advance_days`: 会员最多可预约 x 天后的课程
- `booking_deadline_hours`: 预约截止时间：上课前 x 小时
- `cancel_deadline_hours`: 取消截止时间：上课前 x 小时
- `booking_time_from/to`: 预约操作时间限制
- `require_material`: 会员预约时是否必须选教材

#### 教师权限配置

- `teacher_can_add_slots`: 教师是否可自主增加课时
- `teacher_can_delete_empty_slots`: 教师是否可删除空课时
- `teacher_can_cancel_booking`: 教师是否可取消学生预约
- `teacher_need_confirm`: 学生预约后是否需要教师确认

#### 固定课表配置

- `fixed_booking_enabled`: 是否启用固定课表约课
- `auto_schedule_enabled`: 是否启用自动排课
- `auto_schedule_day`: 每月自动排课日期(1-28 号)
- `auto_schedule_time`: 自动排课时间
- `default_schedule_weeks`: 默认排课周数(1-12 周)

### API 端点

```http
GET    /api/v1/courses/config              # 获取配置
POST   /api/v1/courses/config/update       # 更新配置
POST   /api/v1/courses/config/field        # 更新单个字段
POST   /api/v1/courses/config/validate     # 验证配置一致性
POST   /api/v1/courses/config/reset        # 重置为默认配置
```

### 使用示例

```python
from app.features.courses.config_service import CourseSystemConfigService

# 获取配置服务
service = CourseSystemConfigService(session, tenant_id)

# 获取或创建默认配置
config = service.get_config_or_create_default(created_by=user_id)

# 更新配置
update_data = CourseSystemConfigUpdate(
    default_slot_duration_minutes=30,
    booking_deadline_hours=4
)
updated_config = service.update_config(update_data, updated_by=user_id)

# 验证配置一致性
is_valid = service.validate_config_consistency()
```

## 📅 已排课表子模块 (scheduled*classes*\*)

### 功能职责

- 管理具体的课程安排和预约
- 处理课程状态流转和业务规则
- 支持多种预约模式（直接预约、固定课表预约）

### 核心实体

#### ScheduledClass 模型

```python
class ScheduledClass:
    # 基础信息
    tenant_id: int              # 租户ID
    teacher_id: int             # 教师ID
    teacher_name: str           # 教师姓名
    teacher_number: str         # 教师编号

    # 时间信息
    class_date: date            # 上课日期
    start_time: time            # 开始时间
    duration_minutes: int       # 课程时长

    # 会员信息（预约后填充）
    member_id: Optional[int]    # 会员ID
    member_card_id: Optional[int] # 会员卡ID
    member_name: Optional[str]  # 会员姓名

    # 课程信息
    material: Optional[str]     # 教材
    price: int                  # 课程价格（分）

    # 状态管理
    status: ScheduledClassStatus # 课程状态
    booking_type: BookingType   # 预约类型

    # 特殊状态
    teacher_no_show: bool       # 教师缺席
    member_no_show: bool        # 会员缺席
```

#### 状态流转

```mermaid
graph TD
    A[AVAILABLE] --> B[BOOKED]
    A --> C[CANCELLED]
    B --> D[CONFIRMED]
    B --> E[CANCELLED]
    D --> G[NO_SHOW]
    D --> H[CANCELLED]
```

### 业务规则

#### 预约规则

1. **时间限制**: 必须在预约截止时间前预约
2. **冲突检测**: 同一时间段教师和会员不能有冲突
3. **余额检查**: 会员余额必须足够支付课程费用
4. **教材要求**: 根据配置决定是否必须选择教材

#### 取消规则

1. **时间限制**: 必须在取消截止时间前取消
2. **状态限制**: 只有特定状态的课程可以取消
3. **费用处理**: 取消时自动退还费用到会员账户

#### 状态转换规则

- `AVAILABLE` → `BOOKED`: 会员预约
- `BOOKED` → `CONFIRMED`: 教师确认（如果需要）
- `CONFIRMED` → `NO_SHOW`: 缺席处理

### API 端点

```http
# 管理员接口
POST   /api/v1/courses/classes/admin/create     # 创建课程
GET    /api/v1/courses/classes/admin/list       # 获取课程列表
PUT    /api/v1/courses/classes/admin/{id}       # 更新课程
DELETE /api/v1/courses/classes/admin/{id}       # 删除课程

# 教师接口
POST   /api/v1/courses/classes/teacher/create   # 教师创建课程
GET    /api/v1/courses/classes/teacher/my       # 教师的课程
POST   /api/v1/courses/classes/teacher/{id}/confirm # 确认预约

# 会员接口
POST   /api/v1/courses/classes/member/book      # 会员预约
POST   /api/v1/courses/classes/member/{id}/cancel # 取消预约
GET    /api/v1/courses/classes/member/my        # 会员的课程
```

### 使用示例

```python
from app.features.courses.scheduled_classes_service import ScheduledClassService

# 获取服务
service = ScheduledClassService(session, tenant_id)

# 管理员创建课程
admin_create_data = AdminScheduledClassCreate(
    teacher_id=1,
    class_date="2025-01-10",
    start_time="10:00",
    duration_minutes=25,
    price=5000  # 50元，以分为单位
)
scheduled_class = service.create_scheduled_class_admin(admin_create_data, created_by=admin_id)

# 会员预约课程
member_book_data = MemberScheduledClassCreate(
    member_id=1,
    material="新概念英语第一册"
)
booked_class = service.book_class_member(class_id, member_book_data, booked_by=member_id)

# 取消预约
service.cancel_booking_member(class_id, cancelled_by=member_id)
```

## 🔍 业务流程

### 1. 直接约课流程

1. 教师/管理员创建可用课程时段
2. 会员浏览可用时段
3. 会员选择时段并预约（扣费）
4. 教师确认预约（如果需要）
5. 课程进行
6. 标记课程完成

### 2. 固定课表约课流程

1. 教师设置固定时间段
2. 会员锁定固定位置
3. 系统自动排课（按配置周期）
4. 生成具体课程安排
5. 课程进行
6. 标记课程完成

## ⚠️ 注意事项

### 开发注意事项

1. **时区处理**: 所有时间都使用 UTC 存储，显示时转换为本地时区
2. **金额处理**: 价格以分为单位存储，避免浮点数精度问题
3. **并发控制**: 预约操作需要考虑并发冲突
4. **数据一致性**: 状态变更需要保证数据一致性

### 性能优化

1. **索引优化**: 为常用查询字段添加索引
2. **缓存策略**: 配置信息可以缓存
3. **分页查询**: 大量数据查询必须分页
4. **批量操作**: 支持批量创建和更新

### 错误处理

1. **业务异常**: 使用统一的异常处理机制
2. **错误码**: 定义清晰的错误码便于前端处理
3. **日志记录**: 重要操作需要记录审计日志

## 🧪 测试指南

### 单元测试

- 测试所有业务逻辑方法
- 覆盖正常流程和异常情况
- 验证状态转换的正确性

### 集成测试

- 测试 API 端点的完整流程
- 验证权限控制
- 测试多租户隔离

### 性能测试

- 测试大量数据下的查询性能
- 验证并发预约的处理能力
- 测试批量操作的效率

## 📚 相关文档

- [数据库设计文档](../tasks/database-design/final-course-system-design.md)
- [API 设计指南](../API_DESIGN_GUIDE.md)
- [测试开发指南](../tests/QUICK_TEST_GUIDE.md)
- [开发经验总结](../tasks/development-tips/development-tips.md)
