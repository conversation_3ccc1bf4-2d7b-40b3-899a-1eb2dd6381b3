# 教师管理模块开发指南

> 版本：v1.0  
> 更新时间：2025-01-03  
> 模块路径：`app/features/teachers/`

## 📋 模块概述

教师管理模块负责管理教师信息和教师的固定时间段设置。该模块采用功能拆分设计，分为主模块和固定时间段子模块。

### 模块结构

```
app/features/teachers/
├── models.py                    # 教师基础数据模型
├── schemas.py                   # 教师API模式
├── service.py                   # 教师业务逻辑
├── router.py                    # 教师API路由
├── exceptions.py                # 教师异常处理
└── fixed_slots_*               # 固定时间段子模块
    ├── fixed_slots_models.py      # 时间段数据模型
    ├── fixed_slots_schemas.py     # 时间段API模式
    ├── fixed_slots_service.py     # 时间段业务逻辑
    ├── fixed_slots_router.py      # 时间段API路由
    └── fixed_slots_exceptions.py  # 时间段异常处理
```

## 👨‍🏫 教师基础模块

### 核心实体

#### Teacher 模型

```python
class Teacher:
    # 基础信息
    tenant_id: int                    # 租户ID
    name: str                         # 教师姓名
    teacher_number: str               # 教师编号
    email: str                        # 邮箱
    phone: Optional[str]              # 手机号

    # 分类信息
    teacher_category: TeacherCategory # 教师分类
    region: TeacherRegion            # 教师区域

    # 业务信息
    price_per_class: Int             # 时薪
    status: TeacherStatus            # 教师状态
    show_to_members: bool            # 是否对会员端展示

    # 个人信息
    gender: Optional[Gender]         # 性别
    birthday: Optional[date]         # 生日
    nationality: Optional[str]       # 国籍

    # 教学信息
    certifications: List[str]               # 资质证书
    specialties: List[str]                  # 专业特长

    # 微信集成
    wechat_openid: Optional[str]     # 微信OpenID
    wechat_unionid: Optional[str]    # 微信UnionID
```

#### 枚举类型

```python
class TeacherCategory(str, Enum):
    """教师分类枚举"""
    EUROPEAN = "european"      # 欧美教师
    SOUTH_AFRICAN = "south_african"  # 南非教师
    FILIPINO = "filipino"      # 菲律宾教师
    CHINESE = "chinese"        # 中教
    OTHER = "other"           # 其他

class TeacherRegion(str, Enum):
    DOMESTIC = "domestic"       # 国内
    OVERSEAS = "overseas"       # 海外

class TeacherStatus(str, Enum):
    ACTIVE = "active"           # 激活
    INACTIVE = "inactive"       # 未激活
    SUSPENDED = "suspended"     # 暂停
    TERMINATED = "terminated"   # 终止
```

### 主要功能

#### 1. 教师管理

- 创建、更新、删除教师
- 教师状态管理
- 教师信息查询和筛选

#### 2. 标签管理

- 为教师分配标签
- 批量标签操作
- 标签查询和筛选

#### 3. 权限控制

- 不同角色的访问权限
- 会员端可见性控制

### API 端点

```http
# 基础CRUD
POST   /api/v1/teachers/                    # 创建教师
GET    /api/v1/teachers/                    # 获取教师列表
GET    /api/v1/teachers/{id}                # 获取教师详情
PUT    /api/v1/teachers/{id}                # 更新教师信息
DELETE /api/v1/teachers/{id}                # 删除教师

# 状态管理
POST   /api/v1/teachers/{id}/activate       # 激活教师
POST   /api/v1/teachers/{id}/suspend        # 暂停教师
POST   /api/v1/teachers/{id}/terminate      # 终止教师

# 标签管理
POST   /api/v1/teachers/{id}/tags           # 分配标签
DELETE /api/v1/teachers/{id}/tags/{tag_id}  # 移除标签
POST   /api/v1/teachers/batch/tags          # 批量标签操作

# 特殊查询
GET    /api/v1/teachers/available           # 获取对会员可见的教师
GET    /api/v1/teachers/search              # 搜索教师
```

## ⏰ 固定时间段子模块 (fixed*slots*\*)

### 功能职责

- 管理教师的固定时间段设置
- 支持周期性时间安排
- 提供时间冲突检测

### 核心实体

#### TeacherFixedSlot 模型

```python
class TeacherFixedSlot:
    # 基础信息
    tenant_id: int              # 租户ID
    teacher_id: int             # 教师ID

    # 时间信息
    weekday: Weekday           # 星期几（1-7）
    start_time: time           # 开始时间
    duration_minutes: int      # 时长（分钟）

    # 状态信息
    status: SlotStatus         # 时间段状态
    is_recurring: bool         # 是否循环

    # 有效期
    effective_from: date       # 生效开始日期
    effective_until: Optional[date] # 生效结束日期
```

#### 枚举类型

```python
class Weekday(int, Enum):
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

class SlotStatus(str, Enum):
    ACTIVE = "active"           # 激活
    PAUSED = "paused"          # 暂停
    CANCELLED = "cancelled"     # 取消
```

### 主要功能

#### 1. 时间段管理

- 创建、更新、删除固定时间段
- 批量操作支持
- 时间冲突检测

#### 2. 状态管理

- 激活/暂停时间段
- 批量状态更新

#### 3. 查询功能

- 按教师查询时间段
- 按时间范围查询
- 可用时间段查询

### API 端点

```http
# 基础CRUD
POST   /api/v1/teachers/fixed-slots/              # 创建时间段
GET    /api/v1/teachers/fixed-slots/              # 获取时间段列表
GET    /api/v1/teachers/fixed-slots/{id}          # 获取时间段详情
PUT    /api/v1/teachers/fixed-slots/{id}          # 更新时间段
DELETE /api/v1/teachers/fixed-slots/{id}          # 删除时间段

# 批量操作
POST   /api/v1/teachers/fixed-slots/batch/create  # 批量创建
PUT    /api/v1/teachers/fixed-slots/batch/update  # 批量更新
DELETE /api/v1/teachers/fixed-slots/batch/delete  # 批量删除

# 状态管理
POST   /api/v1/teachers/fixed-slots/batch/status  # 批量状态更新
POST   /api/v1/teachers/fixed-slots/{id}/status   # 单个状态更新

# 查询功能
GET    /api/v1/teachers/fixed-slots/available     # 获取可用时间段
POST   /api/v1/teachers/fixed-slots/conflict-check # 时间冲突检测
GET    /api/v1/teachers/fixed-slots/stats         # 时间段统计
```

### 使用示例

```python
from app.features.teachers.service import TeacherService
from app.features.teachers.fixed_slots_service import TeacherFixedSlotService

# 创建教师
teacher_service = TeacherService(session, tenant_id)
teacher_data = TeacherCreate(
    name="张老师",
    teacher_number="T001",
    email="<EMAIL>",
    teacher_category=TeacherCategory.CHINESE,
    region=TeacherRegion.DOMESTIC,
    price_per_class=Int
)
teacher = teacher_service.create_teacher(teacher_data, created_by=admin_id)

# 创建固定时间段
slot_service = TeacherFixedSlotService(session, tenant_id)
slot_data = TeacherFixedSlotCreate(
    teacher_id=teacher.id,
    weekday=Weekday.MONDAY,
    start_time=time(9, 0),
    duration_minutes=25,
    effective_from=date.today()
)
slot = slot_service.create_slot(slot_data, created_by=admin_id)

# 批量创建时间段
batch_data = TeacherFixedSlotBatchCreate(
    teacher_id=teacher.id,
    slots=[
        {"weekday": 1, "start_time": "09:00", "duration_minutes": 25},
        {"weekday": 1, "start_time": "09:30", "duration_minutes": 25},
        {"weekday": 2, "start_time": "09:00", "duration_minutes": 25}
    ],
    effective_from=date.today()
)
slots = slot_service.batch_create_slots(batch_data, created_by=admin_id)
```

## 🔍 业务规则

### 教师管理规则

1. **唯一性约束**: 邮箱和手机号在租户内唯一
2. **状态转换**: 只允许合法的状态转换
3. **删除限制**: 有关联数据的教师不能删除

### 时间段管理规则

1. **时间冲突**: 同一教师不能在同一时间有多个时间段
2. **有效期**: 时间段必须在有效期内
3. **状态限制**: 只有激活状态的时间段可以被预约

## ⚠️ 注意事项

### 开发注意事项

1. **时区处理**: 时间段使用本地时间，需要明确时区
2. **冗余字段**: 为了查询性能，部分字段进行了冗余
3. **级联操作**: 删除教师时需要处理关联的时间段

### 性能优化

1. **索引设计**: 为常用查询字段添加复合索引
2. **分页查询**: 大量数据必须分页
3. **缓存策略**: 教师基础信息可以缓存

### 错误处理

1. **业务异常**: 使用统一的异常处理机制
2. **数据验证**: 严格的数据格式验证
3. **并发控制**: 防止并发操作导致的数据不一致

## 🧪 测试指南

### 单元测试重点

- 教师 CRUD 操作
- 状态转换逻辑
- 时间段冲突检测
- 批量操作功能

### 集成测试重点

- API 端点完整流程
- 权限控制验证
- 多租户数据隔离

## 📚 相关文档

- [会员固定课位锁定模块](./members-fixed-lock-guide.md)
- [课程系统模块](./courses-module-guide.md)
- [标签管理模块](./tags-module-guide.md)
- [数据库设计文档](../tasks/database-design/final-course-system-design.md)
