# 会员管理模块开发指南

> 版本：v1.0  
> 更新时间：2025-01-03  
> 模块路径：`app/features/members/`

## 📋 模块概述

会员管理模块负责管理学员信息和会员的固定课位锁定。该模块是整个教学平台的核心用户管理模块，采用功能拆分设计。

### 模块结构

```
app/features/members/
├── models.py                      # 会员基础数据模型
├── schemas.py                     # 会员API模式
├── service.py                     # 会员业务逻辑
├── router.py                      # 会员API路由
├── exceptions.py                  # 会员异常处理
└── fixed_lock_*                  # 固定课位锁定子模块
    ├── fixed_lock_models.py         # 锁定数据模型
    ├── fixed_lock_schemas.py        # 锁定API模式
    ├── fixed_lock_service.py        # 锁定业务逻辑
    ├── fixed_lock_router.py         # 锁定API路由
    └── fixed_lock_exceptions.py     # 锁定异常处理
```

## 👥 会员基础模块

### 核心实体

#### Member 模型

```python
class Member:
    # 基础信息
    tenant_id: int                    # 租户ID
    name: str                         # 姓名
    phone: str                        # 手机号
    email: Optional[str]              # 邮箱
    gender: Optional[Gender]          # 性别
    birthday: Optional[date]          # 生日
    avatar_url: Optional[str]         # 头像URL

    # 会员信息
    member_type: MemberType           # 会员类型
    member_status: MemberStatus       # 会员状态

    # 财务信息
    total_spent: Decimal              # 累计消费（分）
    total_classes: int                # 累计课程数

    # 归属关系
    sales_id: Optional[int]           # 销售人员ID
    agent_id: Optional[int]           # 代理人员ID

    # 地址信息
    address: Optional[str]            # 地址
    city: Optional[str]               # 城市
    province: Optional[str]           # 省份
    country: str                      # 国家
    postal_code: Optional[str]        # 邮编

    # 扩展信息
    notes: Optional[str]              # 备注
    tags: List[str]                   # 标签

    # 时间信息
    registered_at: datetime           # 注册时间
    last_login_at: Optional[datetime] # 最后登录时间
    last_class_at: Optional[datetime] # 最后上课时间
```

#### 枚举类型

```python
class MemberType(str, Enum):
    TRIAL = "trial"      # 试用会员
    FORMAL = "formal"    # 正式会员
    VIP = "vip"         # VIP会员

class MemberStatus(str, Enum):
    ACTIVE = "active"        # 活跃
    SILENT = "silent"        # 沉默
    FROZEN = "frozen"        # 冻结
    CANCELLED = "cancelled"  # 注销
```

### 主要功能

#### 1. 会员管理

- 创建、更新、删除会员
- 会员状态管理
- 会员信息查询和筛选

#### 2. 财务管理

- 余额查询和更新
- 消费记录统计
- 充值和扣费操作

#### 3. 数据分析

- 会员行为分析
- 消费统计
- 活跃度分析

### API 端点

```http
# 基础CRUD
POST   /api/v1/members/                    # 创建会员
GET    /api/v1/members/                    # 获取会员列表
GET    /api/v1/members/{id}                # 获取会员详情
PUT    /api/v1/members/{id}                # 更新会员信息
DELETE /api/v1/members/{id}                # 删除会员

# 状态管理
POST   /api/v1/members/{id}/activate       # 激活会员
POST   /api/v1/members/{id}/freeze         # 冻结会员
POST   /api/v1/members/{id}/cancel         # 注销会员

# 财务操作
GET    /api/v1/members/{id}/balance        # 查询余额
POST   /api/v1/members/{id}/recharge       # 充值
POST   /api/v1/members/{id}/deduct         # 扣费

# 查询功能
GET    /api/v1/members/search              # 搜索会员
GET    /api/v1/members/stats               # 会员统计
```

## 🔒 固定课位锁定子模块 (fixed*lock*\*)

### 功能职责

- 管理会员对教师固定时间段的锁定
- 支持固定课表预约模式
- 提供锁定冲突检测和管理

### 核心实体

#### MemberFixedSlotLock 模型

```python
class MemberFixedSlotLock:
    # 基础信息
    tenant_id: int                    # 租户ID
    member_id: int                    # 会员ID
    teacher_fixed_slot_id: int        # 教师固定时间段ID

    # 冗余字段（便于查询）
    teacher_id: int                   # 教师ID
    weekday: int                      # 星期几（1-7）
    start_time: time                  # 开始时间

    # 状态信息
    status: MemberFixedSlotLockStatus # 锁定状态
    locked_at: datetime               # 锁定时间

    # 优先级和排序
    priority: int                     # 优先级
    sort_order: int                   # 排序

    # 备注信息
    notes: Optional[str]              # 备注
```

#### 枚举类型

```python
class MemberFixedSlotLockStatus(str, Enum):
    ACTIVE = "active"           # 激活
    PAUSED = "paused"          # 暂停
    CANCELLED = "cancelled"     # 取消
```

### 主要功能

#### 1. 锁定管理

- 创建、更新、删除锁定
- 批量锁定操作
- 锁定冲突检测

#### 2. 状态管理

- 激活/暂停锁定
- 批量状态更新
- 锁定历史记录

#### 3. 查询功能

- 按会员查询锁定
- 按教师查询锁定
- 可用时间段查询

### API 端点

```http
# 基础CRUD
POST   /api/v1/members/fixed-locks/              # 创建锁定
GET    /api/v1/members/fixed-locks/              # 获取锁定列表
GET    /api/v1/members/fixed-locks/{id}          # 获取锁定详情
PUT    /api/v1/members/fixed-locks/{id}          # 更新锁定
DELETE /api/v1/members/fixed-locks/{id}          # 删除锁定

# 批量操作
POST   /api/v1/members/fixed-locks/batch/create  # 批量创建
PUT    /api/v1/members/fixed-locks/batch/update  # 批量更新
DELETE /api/v1/members/fixed-locks/batch/delete  # 批量删除

# 状态管理
POST   /api/v1/members/fixed-locks/batch/status  # 批量状态更新
POST   /api/v1/members/fixed-locks/{id}/status   # 单个状态更新

# 查询功能
GET    /api/v1/members/fixed-locks/available     # 获取可用时间段
POST   /api/v1/members/fixed-locks/conflict-check # 冲突检测
GET    /api/v1/members/fixed-locks/my             # 我的锁定
```

### 使用示例

```python
from app.features.members.service import MemberService
from app.features.members.fixed_lock_service import MemberFixedSlotLockService

# 创建会员
member_service = MemberService(session, tenant_id)
member_data = MemberCreate(
    name="张同学",
    phone="13800138001",
    email="<EMAIL>",
    member_type=MemberType.FORMAL
)
member = member_service.create_member(member_data, created_by=admin_id)

# 创建固定课位锁定
lock_service = MemberFixedSlotLockService(session, tenant_id)
lock_data = MemberFixedSlotLockCreate(
    member_id=member.id,
    teacher_fixed_slot_id=slot_id
)
lock = lock_service.create_lock(lock_data, created_by=member.id)

# 批量创建锁定
batch_data = MemberFixedSlotLockBatchCreate(
    member_id=member.id,
    teacher_fixed_slot_ids=[slot1_id, slot2_id, slot3_id]
)
locks = lock_service.batch_create_locks(batch_data, created_by=member.id)

# 查询可用时间段
available_slots = lock_service.get_available_slots(
    member_id=member.id,
    weekdays=[1, 2, 3],  # 周一到周三
    start_time=time(9, 0),
    end_time=time(18, 0)
)
```

## 🔍 业务规则

### 会员管理规则

1. **唯一性约束**: 手机号在租户内唯一
2. **状态转换**: 只允许合法的状态转换
3. **余额限制**: 余额不能为负数

### 固定课位锁定规则

1. **唯一性约束**: 同一时间段只能被一个会员锁定
2. **冲突检测**: 会员不能锁定时间冲突的时间段
3. **状态限制**: 只有激活状态的锁定才有效

## 🔄 业务流程

### 1. 会员注册流程

1. 填写基础信息
2. 验证手机号/邮箱
3. 创建会员账户
4. 设置初始状态和类型

### 2. 固定课位锁定流程

1. 会员浏览教师的固定时间段
2. 选择合适的时间段
3. 检查时间冲突
4. 创建锁定记录
5. 系统自动排课（按配置周期）

### 3. 课程消费流程

1. 会员预约课程
2. 系统扣除相应费用
3. 更新消费统计
4. 课程完成后更新记录

## ⚠️ 注意事项

### 开发注意事项

1. **数据隐私**: 会员信息涉及隐私，需要严格的权限控制
2. **财务安全**: 余额操作需要事务保证和审计日志
3. **并发控制**: 锁定操作需要防止并发冲突

### 性能优化

1. **索引设计**: 为常用查询字段添加索引
2. **分页查询**: 大量会员数据必须分页
3. **缓存策略**: 会员基础信息可以缓存

### 错误处理

1. **业务异常**: 使用统一的异常处理机制
2. **数据验证**: 严格的数据格式验证
3. **审计日志**: 重要操作需要记录日志

## 🧪 测试指南

### 单元测试重点

- 会员 CRUD 操作
- 余额计算逻辑
- 锁定冲突检测
- 状态转换验证

### 集成测试重点

- API 端点完整流程
- 权限控制验证
- 多租户数据隔离
- 财务操作安全性

## 📚 相关文档

- [教师固定时间段模块](./teachers-module-guide.md)
- [课程系统模块](./courses-module-guide.md)
- [用户管理模块](./users-module-guide.md)
- [数据库设计文档](../tasks/database-design/final-course-system-design.md)
