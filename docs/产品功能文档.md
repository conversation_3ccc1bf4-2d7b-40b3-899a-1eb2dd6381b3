# KS English Admin Backend - 产品功能文档

> **目标读者**: 产品经理、业务分析师  
> **文档目的**: 快速了解项目功能，制定未来 PRD  
> **更新时间**: 2025-01-14  
> **基于版本**: v1.0 (后端开发完成)

## 📋 产品概述

KS English Admin Backend 是一个**多租户在线网约课系统**，为外教课机构提供完整的 SaaS 管理解决方案。系统支持机构独立运营，实现学员自主预约、教师时间管理、会员卡消费等核心业务流程。

### 核心价值

- **降本增效**: 自动化排课减少 80%人工调度成本
- **多端服务**: 同时支持管理后台和会员前端
- **数据安全**: 多租户架构确保机构数据完全隔离
- **灵活配置**: 支持不同机构的个性化业务规则

## 🎯 用户角色与权限

### 1. 超级管理员 (super_admin)

**权限范围**: 跨租户全局管理
**主要职责**:

- 平台运营管理
- 租户开通与管理
- 系统配置维护

### 2. 租户管理员 (admin) - **前端开发优先级最高**

**权限范围**: 租户内全部功能
**主要职责**:

- 机构运营管理
- 用户权限分配
- 业务数据分析

### 3. 代理人员 (agent)

**权限范围**: 租户内部分功能
**主要职责**:

- 会员服务支持
- 课程协调安排
- 客户关系维护

### 5. 会员/学员 (member) - **前端开发次优先级**

**权限范围**: 个人数据管理
**主要职责**:

- 课程预约管理
- 个人信息维护
- 学习记录查看

## 🏢 管理员功能详述 (优先级最高)

### 1. 租户管理 (仅超级管理员)

**功能概述**: 多租户平台的核心管理功能

**核心功能**:

- ✅ **租户开通**: 创建新机构账户，分配独立数据空间
- ✅ **租户配置**: 设置机构基本信息、联系方式、业务参数
- ✅ **状态管理**: 租户激活、暂停、删除等生命周期管理
- ✅ **计划管理**: 租户套餐配置、功能权限控制
- ✅ **数据隔离**: 确保租户间数据完全隔离和安全

**业务价值**: 支持平台化运营，实现多机构独立管理

### 2. 用户管理

**功能概述**: 机构内部用户账户和权限管理

**核心功能**:

- ✅ **用户创建**: 添加管理员、代理等不同角色用户
- ✅ **权限分配**: 基于角色的权限控制，精确控制功能访问
- ✅ **状态管理**: 用户激活、禁用、锁定等状态控制
- ✅ **信息维护**: 用户基本信息、联系方式、头像等管理
- ✅ **登录安全**: 密码策略、登录记录、失败锁定等安全机制

**业务价值**: 规范机构内部管理，确保系统安全使用

### 3. 会员管理

**功能概述**: 学员档案和学习数据的全生命周期管理

**核心功能**:

- ✅ **会员档案**: 学员基本信息、联系方式、学习偏好管理
- ✅ **分类管理**: 会员类型(VIP/普通/试用)、状态管理
- ✅ **关系绑定**: 代理人员关系维护
- ✅ **统计分析**: 学习时长、消费金额、活跃度等数据统计
- ✅ **批量操作**: 会员信息批量导入、更新、状态变更
- ✅ **搜索筛选**: 多维度会员信息查询和筛选

**业务价值**: 精细化会员运营，提升服务质量和续费率

### 4. 教师管理

**功能概述**: 外教资源的招聘、管理和调度

**核心功能**:

- ✅ **教师档案**: 基本信息、教学资质、专业背景管理
- ✅ **分类标签**: 教师区域(欧美/菲教/南非)、专业标签管理
- ✅ **价格管理**: 教师课时费、会员可见价格设置
- ✅ **时间管理**: 教师固定时间段设置、可用性管理
- ✅ **状态控制**: 教师激活、暂停、对会员可见性控制
- ✅ **统计分析**: 教师工作量、收入、评价等数据统计
- ✅ **优先级管理**: 教师排课优先级设置

**业务价值**: 优化教师资源配置，提高教学质量和效率

### 5. 课程管理

**功能概述**: 课程安排、预约和调度的核心管理

**核心功能**:

- ✅ **系统配置**: 课程时长、预约规则、取消政策等参数设置
- ✅ **课程创建**: 教师开放课程时间段，设置课程信息
- ✅ **预约管理**: 会员课程预约、取消、改期等操作管理
- ✅ **状态跟踪**: 课程状态(待上课/已完成/已取消)实时更新
- ✅ **冲突检测**: 时间冲突自动检测和提醒
- ✅ **批量操作**: 批量创建课程、批量状态更新

**业务价值**: 提高课程安排效率，减少人工协调成本

### 6. 会员卡管理

**功能概述**: 会员卡产品和财务管理的核心系统

**核心功能**:

- ✅ **卡模板管理**: 会员卡产品定义、价格策略、有效期设置
- ✅ **卡片发放**: 为会员创建、激活会员卡
- ✅ **充值管理**: 会员卡余额充值、充值记录管理
- ✅ **消费扣费**: 课程消费自动扣费、手动调整
- ✅ **余额查询**: 实时余额查询、余额预警
- ✅ **操作记录**: 完整的充值、消费、调整操作审计
- ✅ **财务统计**: 收入统计、消费分析、资金流水

**业务价值**: 规范财务管理，提供清晰的资金流水和统计

### 7. 标签管理

**功能概述**: 灵活的分类标签系统，支持多维度数据组织

**核心功能**:

- ✅ **分类管理**: 标签分类创建、层级管理
- ✅ **标签创建**: 标签定义、描述、状态管理
- ✅ **关联管理**: 教师、会员等实体的标签关联
- ✅ **批量操作**: 标签批量分配、移除
- ✅ **统计分析**: 标签使用统计、分布分析

**业务价值**: 提供灵活的数据分类能力，支持精细化运营

### 8. 固定课位管理

**功能概述**: 会员固定时间段锁定和管理

**核心功能**:

- ✅ **固定位锁定**: 会员锁定教师的固定时间段
- ✅ **批量管理**: 批量创建、更新、删除固定位
- ✅ **冲突检测**: 固定位时间冲突检测和解决
- ✅ **状态管理**: 固定位激活、暂停、取消状态管理
- ✅ **可用性查询**: 查询教师可锁定的时间段

**业务价值**: 满足固定时间学习需求，提高会员满意度

### 9. 操作记录管理

**功能概述**: 完整的业务操作审计和追踪

**核心功能**:

- ✅ **操作日志**: 记录所有关键业务操作
- ✅ **分类记录**: 课程操作、教师操作、会员操作分类管理
- ✅ **查询统计**: 操作记录查询、统计分析
- ✅ **数据导出**: 操作记录数据导出和备份

**业务价值**: 提供完整的操作审计，支持问题追踪和数据分析

## 👥 会员功能详述 (优先级次高)

### 1. 个人中心

**功能概述**: 会员个人信息和账户管理

**核心功能**:

- ✅ **个人信息**: 查看和更新基本信息、联系方式
- ✅ **账户状态**: 查看会员状态、等级、权益
- ✅ **统计信息**: 学习时长、课程数量、消费统计

### 2. 课程预约

**功能概述**: 会员自主预约和管理课程

**核心功能**:

- ✅ **课程浏览**: 查看可预约课程列表，支持筛选
- ✅ **在线预约**: 选择教师和时间进行课程预约
- ✅ **我的课程**: 查看已预约课程，支持取消和改期
- ✅ **自动扣费**: 预约时自动从会员卡扣除费用

### 3. 会员卡管理

**功能概述**: 会员卡余额和使用记录管理

**核心功能**:

- ✅ **卡片查询**: 查看我的会员卡列表和详情
- ✅ **余额查询**: 实时查看会员卡余额
- ✅ **主卡管理**: 查看和使用主要会员卡

### 4. 记录查询

**功能概述**: 个人学习和消费记录查询

**核心功能**:

- ✅ **操作记录**: 查看会员卡充值、消费记录
- ✅ **学习记录**: 查看课程学习历史和统计
- ✅ **筛选查询**: 按时间、类型等条件筛选记录

## 🔐 认证与安全

### 认证方式

- **管理员登录**: 邮箱+密码+租户代码
- **会员登录**: 手机号+验证码+租户代码
- **令牌管理**: JWT 令牌，支持自动续期

### 安全机制

- **数据隔离**: 基于 PostgreSQL RLS 的多租户数据隔离
- **权限控制**: 基于角色的访问控制(RBAC)
- **操作审计**: 完整的操作记录和审计日志
- **密码安全**: bcrypt 加密，登录失败锁定

## 📊 数据统计与分析

### 已实现统计功能

- ✅ **会员统计**: 会员数量、类型分布、活跃度分析
- ✅ **教师统计**: 教师数量、区域分布、工作量统计
- ✅ **课程统计**: 课程数量、状态分布、完成率统计
- ✅ **财务统计**: 收入统计、消费分析、资金流水

### 规划中的高级统计 (第二期)

- 🚧 **收入分析**: 充值收入、课程消费收入趋势
- 🚧 **课程分析**: 出勤率、取消率、教师工作量分析
- 🚧 **会员分析**: 消费行为、活跃度、留存分析
- 🚧 **报表导出**: Excel/PDF 格式报表导出

## 🚀 技术特性

### 系统特性

- **高性能**: FastAPI 异步框架，支持高并发
- **类型安全**: SQLModel 提供完整类型检查
- **自动文档**: OpenAPI/Swagger 自动生成 API 文档
- **测试完备**: 578 个测试用例，全面覆盖

### 扩展能力

- **模块化设计**: 新功能可独立开发部署
- **配置驱动**: 业务规则可灵活配置
- **多租户支持**: 支持无限租户扩展
- **API 标准**: RESTful API 设计，易于集成

---

_本文档基于实际功能实现编写，确保信息准确性。如需了解技术实现细节，请参考技术文档。_
