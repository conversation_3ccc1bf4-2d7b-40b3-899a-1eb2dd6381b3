# API 修改工作流程规范

## 概述

本文档定义了 API 修改的标准工作流程，确保每次修改都有完整的文档记录、验证步骤和测试覆盖。

## 工作流程

### 1. 需求分析
- 明确修改目标和范围
- 分析对现有功能的影响
- 确定性能和兼容性要求

### 2. 代码实现
- **Schema 修改**: 更新相关的 Pydantic 模型
- **Service 层修改**: 实现业务逻辑，注意避免 N+1 查询
- **API 层修改**: 更新路由和响应格式

### 3. 测试验证
- **更新现有测试**: 确保兼容性
- **新增专门测试**: 验证新功能
- **运行完整测试套件**: 确保无回归

### 4. 文档和验证
- **创建修改记录**: 详细说明修改内容和原因
- **编写验证脚本**: 验证 API Schema 和功能
- **更新 API 文档**: 确保文档与实现一致

### 5. 文件组织规范

每次 API 修改都应在 `docs/changelog/fix-{feature-name}/` 目录下创建：

```
docs/changelog/fix-{feature-name}/
├── README.md              # 修改说明文档
├── verify_changes.py      # 验证脚本
```

## 验证步骤清单

### 必须验证项
- [ ] 所有相关测试用例通过
- [ ] API Schema 正确生成
- [ ] 响应格式符合预期

### 推荐验证项
- [ ] 类型检查通过
- [ ] 错误处理正确

## 文档产出标准

### README.md 必须包含
1. **修改概述**: 简要说明修改内容
2. **技术实现**: 关键代码变更点
3. **API 变更**: 请求/响应格式变化
4. **测试验证**: 测试用例和验证结果
5. **使用示例**: 实际调用示例

### 验证脚本要求
- 能够独立运行
- 验证核心功能
- 输出清晰的结果
- 包含错误处理

## 命令模板

### 创建修改目录
```bash
mkdir -p docs/changelog/fix-{feature-name}
```

### 运行测试验证
```bash
# 运行相关测试
python -m pytest tests/integration/api/v1/admin/test_{module}.py -v

# 运行验证脚本
cd docs/changelog/fix-{feature-name}
python verify_changes.py
```

### 检查代码质量
```bash
# 类型检查（如果使用 mypy）
mypy app/features/{module}/

# 代码格式检查
black --check app/features/{module}/
```

## 示例参考

参考 `docs/changelog/fix-member-cards-feature/` 目录下的实现示例。

## 注意事项

1. **路径问题**: 验证脚本需要调整 Python 路径以正确导入项目模块
2. **环境依赖**: 确保脚本在项目环境中运行
3. **数据安全**: 验证脚本不应修改生产数据
4. **版本控制**: 所有文档和脚本都应纳入版本控制

---

**维护者**: 开发团队  
**更新时间**: 2024-07-26
