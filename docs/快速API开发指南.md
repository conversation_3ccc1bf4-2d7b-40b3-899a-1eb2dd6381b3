# 快速 API 开发指南

## 目录

- [开发流程](#开发流程)
- [目录结构](#目录结构)
- [添加新 API 接口](#添加新api接口)
- [修改现有 API 接口](#修改现有api接口)
- [测试 API 接口](#测试api接口)
- [常见问题](#常见问题)

## 开发流程

API 开发的标准流程如下：

1. **理解需求**：明确接口的功能、输入输出、权限要求等
2. **设计 API**：设计路由、请求参数、响应格式
3. **实现服务层**：在对应的`service.py`中实现业务逻辑
4. **实现 API 路由**：在对应的路由文件中添加 API 端点
5. **编写测试**：为新 API 添加集成测试
6. **运行测试**：确保新功能正常工作且不破坏现有功能
7. **提交代码**：提交代码并创建 PR

## 目录结构

项目采用分层架构，主要目录结构如下：

```
app/
├── api/                 # API路由层
│   ├── common/          # 通用工具
│   └── v1/              # API版本1
│       ├── admin/       # 管理端API
│       ├── member/      # 会员端API
│       ├── public/      # 公共API
│       └── teacher/     # 教师端API
├── features/            # 业务逻辑层
│   ├── users/           # 用户模块
│   │   ├── models.py    # 数据模型
│   │   ├── schemas.py   # API数据模式
│   │   ├── service.py   # 业务逻辑
│   │   └── exceptions.py # 业务异常
│   └── ...              # 其他业务模块
└── core/                # 核心通用组件
    ├── config.py        # 配置管理
    ├── dependencies.py  # 依赖注入
    └── security.py      # 安全工具
```

## Tips

### 1. FastAPI + SQLModel 接口响应 model 典型套路

- 内部用 dict → model_dump()
- 对外响应 → model_validate(slot, from_attributes=True)

  1. 查询并一次性预加载
     stmt = select(TeacherFixedSlot).options(selectinload(TeacherFixedSlot.items))
     slot = session.exec(stmt).first()

  2. 直接转成对外 DTO
     return TeacherFixedSlotResponse.model_validate(slot, from_attributes=True) # 字段可不同，可嵌套

### 2. 避免 SQLModel 对象过期

用 session.refresh(obj) 手动刷新（Session 还开着时）。

## 添加新 API 接口

### 1. 添加服务层方法

在对应模块的`service.py`文件中添加新方法：

```python
# app/features/users/service.py
def get_salesmen(self) -> List[User]:
    """获取所有销售人员（角色为AGENT，状态为ACTIVE的用户）"""
    statement = select(User).where(
        (User.role.in_([UserRole.AGENT])) &
        (User.status == UserStatus.ACTIVE)
    )
    return self.session.exec(statement).all()
```

### 2. 添加 API 路由

在对应的路由文件中添加新端点：

```python
# app/api/v1/admin/users.py
@router.get("/salesman/list", response_model=ListResponse[UserRead])
def get_salesmen(
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """获取销售人员列表（角色为AGENT，状态为ACTIVE的用户）"""
    tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
    user_service = UserService(session, tenant_id)
    salesmen = user_service.get_salesmen()
    return list_response(salesmen, len(salesmen), "获取销售人员列表成功")
```

### 3. 添加测试用例

在对应的测试文件中添加测试用例：

```python
# tests/integration/api/v1/admin/test_users.py
def test_get_salesmen_list(self, client: TestClient, created_tenant, admin_token):
    """测试获取销售人员列表"""
    # 创建测试数据
    # ...

    # 调用API
    response = client.get(
        "/api/v1/admin/users/salesman/list",
        headers={"Authorization": f"Bearer {admin_token}"}
    )

    # 验证结果
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    # ...更多断言
```

## 修改现有 API 接口

### 1. 修改服务层方法

找到对应的服务方法，进行修改：

```python
# 修改前
def get_user(self, user_id: int) -> Optional[User]:
    return self.session.get(User, user_id)

# 修改后
def get_user(self, user_id: int) -> Optional[User]:
    """添加了新的功能描述"""
    statement = select(User).where(User.id == user_id)
    # 添加了新的过滤条件
    if self.some_condition:
        statement = statement.where(User.status == UserStatus.ACTIVE)
    return self.session.exec(statement).first()
```

### 2. 修改 API 路由

修改对应的路由端点：

```python
# 修改前
@router.get("/{user_id}", response_model=DataResponse[UserRead])
def get_user(user_id: int, ...):
    # ...

# 修改后
@router.get("/{user_id}", response_model=DataResponse[UserRead])
def get_user(
    user_id: int,
    new_param: Optional[str] = None,  # 添加新参数
    user_context: UserContext = Depends(get_user_context),
    session: Session = Depends(get_session)
):
    """更新了API文档"""
    # ...修改实现
```

### 3. 更新测试用例

修改或添加测试用例以覆盖新功能：

```python
# 添加新的测试场景
def test_get_user_with_new_param(self, client: TestClient, ...):
    """测试使用新参数获取用户"""
    # ...
```

## 测试 API 接口

### 1. 运行单个测试

```bash
# 运行单个测试用例
python -m pytest tests/integration/api/v1/admin/test_users.py::TestAdminUserAPI::test_get_salesmen_list -v

# 运行整个测试文件
python -m pytest tests/integration/api/v1/admin/test_users.py -v
```

### 2. 运行所有测试

```bash
# 运行所有集成测试
python -m pytest tests/integration

# 运行所有测试
python -m pytest
```

## 响应格式规范

所有 API 响应必须遵循统一的格式：

### 成功响应

```json
{
  "success": true,
  "message": "操作成功",
  "http_code": 200,
  "business_code": "SUCCESS",
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 列表响应

```json
{
  "success": true,
  "message": "获取列表成功",
  "http_code": 200,
  "business_code": "SUCCESS",
  "data": [ ... ],
  "total": 10,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应

```json
{
  "success": false,
  "message": "错误描述",
  "http_code": 400,
  "business_code": "SPECIFIC_ERROR_CODE",
  "level": "error",
  "details": { ... },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 常见问题

### 1. 依赖注入

使用`Depends`注入常用依赖：

```python
# 获取用户上下文
user_context: UserContext = Depends(get_user_context)

# 获取数据库会话
session: Session = Depends(get_session)
```

### 2. 错误处理

使用统一的异常处理机制：

```python
# 业务异常
if not user:
    raise UserNotFoundError()

# 通用业务异常
if some_condition:
    raise BusinessException("错误描述")

# 特定业务异常
if user.email_exists:
    raise UserBusinessException.email_already_exists(email)
```

### 3. 权限控制

通过`UserContext`进行权限控制：

```python
# 检查用户角色
if user_context.user.role != UserRole.ADMIN:
    raise BusinessException("权限不足")

# 获取租户ID
tenant_id = user_context.tenant_context.tenant_id if user_context.tenant_context else None
```

---

**版本**: v1.0  
**更新时间**: 2024-07-20  
**维护者**: 开发团队
