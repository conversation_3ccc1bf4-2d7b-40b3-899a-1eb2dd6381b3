# 前端联调

## 数据准备

生成租户 1，教师 3，会员 5

```
python scripts/create_demo_data.py basic --test-db course_booking_db

# 创建自定义课程数据
python scripts/create_demo_data.py custom \
  --fixed-slots-per-teacher 8 \
  --member-lock-ratio 0.5 \
  --classes-per-teacher 10 \
  --force \
  --test-db course_booking_db
```

## 接口导出

Apifox 目录

/Users/<USER>/Documents/Apifox

/api/v1/auth/admin/login

1.指定 url 过滤 json，产出 filter.json

```shell
sh generate_merged_filters.sh
```

2.生成请求和响应代码文件

使用自定义模版

```shell
openapi-generator generate -g typescript-axios -i filtered.json -o ./CodeGenerator --skip-validate-spec -p withSeparateModelsAndApi=true -p modelPackage=models -p apiPackage=apis -p withComments=false -p supportsES6=true -p removeComments=true -p useUnionTypes=true -t ./custom-templates
```

3.生成自定义 api 文件

```
python generate_custom_api.py
```

### 检查指令

查看 path

```
jq '.paths | keys' filtered.json
```

查看 schemas

```
jq '.components.schemas | keys' filtered.json
```

## 接口

### 1. 登陆

/api/v1/auth/admin/login

```
curl -X 'POST' \
  'http://127.0.0.1:3000/api/v1/auth/admin/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "username": "demo_admin",
  "password": "demo123456"
}'
```

```json
{
  "username": "demo_admin",
  "password": "demo123456"
}
```

返回数据

```json
{
  "success": true,
  "message": "登录成功",
  "http_code": 200,
  "business_code": "SUCCESS",
  "timestamp": null,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************._Y43T21hY4FUBXPJ0f_p2DO9j-2Yc9JJCCxObnEKlzA",
    "token_type": "bearer",
    "user": {
      "id": "1",
      "username": "demo_admin",
      "email": "<EMAIL>",
      "role": "admin"
    },
    "tenant": {
      "id": "1",
      "code": "demo_tenant",
      "name": "演示机构"
    }
  }
}
```

## 备用提示词

### 后端 FastAPI 框架下文档生成的最佳实践

```
探讨下后端FastAPI框架下文档生成的最佳实践

目前我后端项目开发基本完成了，但是有几个问题
1. 后端按照CRUD通用性原则为每个业务模块生成了最大化的API集合，导致API数量过多
2. 框架生成的api文档似乎配置的也不大合理，导致生成的文档更是巨大
```

```
我即将开始前端开发工作，需要优化API交付流程以提高前端开发效率。当前面临以下具体问题：

**当前API现状问题：**
1. 后端按照CRUD通用性原则为每个业务模块生成了最大化的API集合，导致API数量过多
2. 未考虑前端实际使用场景，预估至少50%的API在前端开发中用不到
3. 使用 `npx openapi-typescript http://localhost:3000/api/v1/openapi.json --output types/api.ts` 生成的TypeScript类型文件超过万行，完全不可用

**期望的解决方案要求：**
1. 寻找整体便捷的方案，避免大幅改动现有架构
2. 支持在前端开发过程中根据页面场景同步调整所需API
3. 重点解决API交互的高效开发问题

**具体需要的建议：**
1. 分析当前项目（FastAPI + SQLModel + PostgreSQL多租户在线英语辅导系统）的最佳API管理方案
2. 提供API文档生成和类型定义的优化策略
3. 推荐前后端协作的工作流程改进方案
4. 考虑Vue 3 + TypeScript + Element Plus前端技术栈的兼容性

请基于项目实际情况提供可操作的解决方案，包括具体的实施步骤和工具推荐。
```

## 登录接口对接

```
我们要开始对接后端接口了

@/http 这是项目现有的api请求工具类

@前端对接指南.md 这是后端提供的对接指南

帮我实现，不需要vue页面的逻辑，我们专注构建api封装对接即可

这是成功的登录请求
curl -X 'POST' \
  'http://127.0.0.1:3000/api/v1/auth/admin/login' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "username": "demo_admin",
  "password": "demo123456"
}'

这是成功的会员列表请求
curl -X 'GET' \
  'http://127.0.0.1:3000/api/v1/admin/members/?skip=0&limit=100' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwidXNlcl90eXBlIjoiYWRtaW4iLCJyb2xlIjoiYWRtaW4iLCJ1c2VybmFtZSI6ImRlbW9fYWRtaW4iLCJ0ZW5hbnRfaWQiOiIxIiwidGVuYW50X2NvZGUiOiJkZW1vX3RlbmFudCIsImV4cCI6MTc1Mjg1MzUzMH0.smbUamBF2c4EyfL_-zdEc-V8kxXw9Tin8wPXKFc5PVs'
```
