# 2025-08-22 整体排查 TODO（by GPT‑5）

本文件用于记录需要全局梳理与同步修复的技术事项。范围覆盖所有 features/* 模块与横切层（api/common、core、db）。请按优先级执行，并在完成后勾选与备注。

## A. users 模块密码接口语义修正（高优先）

- 问题：路由层将对象当作布尔值使用（`ensure_success(success, ...)`），而 service 实际返回的是实体对象或通过异常表达失败。
- 目标：
  - 路由直接返回成功消息或返回实体数据，不使用 `ensure_success` 判断对象真值。
  - 巡检其他模块是否存在类似“对象当作布尔”的错误用法（members、teachers、tags、courses、member_cards 等）。
- 参考位置：
  - app/api/v1/admin/users.py（`change_password`, `reset_password`）
- 任务：
  - [ ] 修正 users 路由实现
  - [ ] 全仓搜“ensure_success(”联动排查错误用法
  - [ ] 补充/回归测试

## B. 分页总数统计 COUNT 优化（高优先）

- 问题：`total = len(session.exec(statement).all())` 对大表性能差。
- 目标：
  - 统一改为 `select(func.count())` 的 COUNT 聚合获取总数。
- 参考位置：
  - app/features/users/service.py:`get_users_with_pagination`
- 扩展影响：
  - 其他 service 层分页方法（members、teachers、tags、courses/scheduled_classes、member_cards 等）可能同类问题。
- 任务：
  - [ ] users 改造为 COUNT
  - [ ] 全仓审计与批量修复
  - [ ] 基准对比与回归测试

## C. 统一时间/时区策略与工具（高优先）

- 问题：存在 `datetime.now()`（naive）与 `datetime.now(timezone.utc)`（aware）混用，DB 引擎设置 Asia/Shanghai；JWT、审计字段、序列化存在潜在不一致。
- 目标：
  - 制定统一策略：内部使用 UTC-aware（推荐），对外展示与数据库层明确约定；文档化。
  - 引入 `utils/time.py`（建议）：
    - `now_utc()`、`now_local(tz='Asia/Shanghai')`
    - 统一审计字段赋值入口（BaseService 等）
  - 全仓替换零散 `datetime.now()` 调用。
- 任务：
  - [ ] 新增 `utils/time.py` 与使用示例
  - [ ] BaseService 与 service 层替换
  - [ ] 序列化与 JWT 过期时间复核
  - [ ] 文档补充（README/开发规范）

## D. 安全工具统一（中高优先）

- 问题：`app/core/security.py` 与 `app/utils/security.py` 职责重叠且被混用，未来易出现“签验不一致/性能参数不同”的事故。
- 目标：
  - 统一到单一实现（建议保留 `app/utils/security.py`，支持 BCRYPT_ROUNDS/环境策略）。
  - 全仓替换 import；保留向后兼容别名仅在迁移窗口期（如必要）。
- 任务：
  - [ ] 合并实现，移除重复逻辑
  - [ ] 替换依赖注入与 auth 流程引用
  - [ ] 补充回归测试（登录、换密、验证码流程）

## E. OpenAPI 常见错误响应“去重/合并”策略（中优先）

- 现状：为避免啰嗦而在 `app/main.py` 删除 400/401/403/404/422 响应；这会降低文档有效性。
- 目标：
  - 不删除响应；通过“集中定义 + 引用”的方式减少重复：
    - 在 `components.responses` 定义 `UnauthorizedError`、`ForbiddenError`、`ValidationError`、`NotFoundError`、`BusinessError` 等统一模型；
    - 在 `include_router(..., responses={...})` 层面为路由组一次性附加通用响应（而非每个端点各自写）。
  - 如仍嫌冗长：在 `custom_openapi` 中将重复的 inline responses 统一替换为 `#/components/responses/*` 的 `$ref`，减少文档体积，但不移除。
- 任务：
  - [ ] 定义 components.responses（参考 `app/api/common/responses.py`）
  - [ ] 为 admin/member/public 路由组集中挂载 `responses`
  - [ ] 如需，在 `custom_openAPI` 内做 `$ref` 替换去重

## F. JWT sub 类型一致化（中优先）

- 问题：token `sub` 为字符串，依赖注入 `session.get(Model, sub)` 依赖隐式转换；不严谨。
- 目标：
  - 生成与解析统一为 `int`；解析时显式 `int()` 并对异常抛出 `AuthenticationError`。
- 任务：
  - [ ] `get_current_user` / `get_current_member` 强制 `sub` → `int`
  - [ ] 对非数字 `sub` 的处理（异常）
  - [ ] 回归测试

## G. 依赖注入生成器返回问题（低优先/可清理）

- 现状：`get_tenant_session_dep`/`get_member_session_dep` 返回生成器对象（错误），但当前未被使用。
- 建议：
  - [ ] 直接删除两者以降低误导；或
  - [ ] 修正为 `yield Session` 的正确依赖写法，并考虑是否在路由层采用（与当前“在 service 层设置 RLS”的模式做取舍）。

---

执行建议：优先处理 A/B/C；随后 D/E/F；最后 G。每一项完成请在此文件勾选并在提交信息中引用本 TODO。
