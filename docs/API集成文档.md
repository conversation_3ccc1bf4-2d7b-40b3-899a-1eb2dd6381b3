# KS English Admin Backend - API 集成文档

> **目标读者**: 前端开发工程师  
> **文档目的**: 快速集成后端 API，补充自动生成文档的不足  
> **更新时间**: 2025-01-14  
> **API 版本**: v1.0

## 📋 概述

本文档为前端开发者提供 API 集成指南，重点介绍集成模式、认证流程、错误处理等关键信息，补充 FastAPI 自动生成文档的不足。

### 自动生成文档地址

- **Swagger UI**: `http://localhost:8012/docs`
- **ReDoc**: `http://localhost:8012/redoc`
- **OpenAPI JSON**: `http://localhost:8012/api/v1/openapi.json`

## 🔗 API 基础信息

### 基础 URL

```
开发环境: http://localhost:8012/api/v1
生产环境: https://api.yourdomain.com/api/v1
```

### API 分组

```
管理端API: /api/v1/admin/*     # 需要管理员权限
会员端API:  /api/v1/member/*    # 需要会员权限
公共API:   /api/v1/*           # 无需认证或公开访问
```

## 🔐 认证集成

### 1. 管理员登录

```typescript
// 登录请求
interface AdminLoginRequest {
  email: string; // 管理员邮箱
  password: string; // 密码
  tenant_code?: string; // 租户代码(超级管理员可为空)
}

// 登录响应
interface AdminLoginResponse {
  access_token: string; // JWT访问令牌
  token_type: string; // "bearer"
  expires_in: number; // 过期时间(秒)
  user_info: {
    id: number;
    username: string;
    role: string; // "super_admin" | "admin" | "agent" | "sale"
    tenant_id?: number;
  };
}

// 示例请求数据
// {
//   "username": "demo_admin",
//   "password": "demo123456"
// }

// 示例代码
const adminLogin = async (credentials: AdminLoginRequest) => {
  const response = await fetch("/api/v1/auth/admin/login", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(credentials),
  });

  if (!response.ok) {
    throw new Error("登录失败");
  }

  const data = await response.json();
  // 存储令牌
  localStorage.setItem("admin_token", data.data.access_token);
  return data;
};
```

### 2. 会员登录

```typescript
// 会员登录请求
interface MemberLoginRequest {
  phone: string; // 手机号
  verification_code: string; // 验证码
  tenant_code: string; // 租户代码(必须)
}

// 示例代码
const memberLogin = async (credentials: MemberLoginRequest) => {
  const response = await fetch("/api/v1/auth/member/login", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(credentials),
  });

  const data = await response.json();
  localStorage.setItem("member_token", data.data.access_token);
  return data;
};
```

### 3. 请求认证

```typescript
// HTTP拦截器示例
const apiClient = axios.create({
  baseURL: "/api/v1",
});

// 请求拦截器 - 自动添加认证头
apiClient.interceptors.request.use((config) => {
  const token =
    localStorage.getItem("admin_token") || localStorage.getItem("member_token");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器 - 处理认证失败
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // 清除令牌，跳转登录页
      localStorage.removeItem("admin_token");
      localStorage.removeItem("member_token");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);
```

## 📊 统一响应格式

### 响应类型定义

```typescript
// 基础响应
interface BaseResponse {
  success: boolean;
  message: string;
}

// 单个数据响应
interface DataResponse<T> extends BaseResponse {
  data: T;
}

// 列表响应
interface ListResponse<T> extends BaseResponse {
  data: T[];
  total: number;
}

// 分页响应
interface PageResponse<T> extends BaseResponse {
  data: T[];
  total: number;
  page: number;
  size: number;
}

// 错误响应
interface ErrorResponse extends BaseResponse {
  success: false;
  error: string; // 错误码
  message: string; // 错误描述
  details?: any; // 详细错误信息
}
```

### 响应处理示例

```typescript
// 通用响应处理函数
const handleApiResponse = async <T>(response: Response): Promise<T> => {
  const data = await response.json();

  if (!data.success) {
    throw new ApiError(data.error, data.message, data.details);
  }

  return data.data;
};

// 自定义错误类
class ApiError extends Error {
  constructor(
    public code: string,
    public message: string,
    public details?: any
  ) {
    super(message);
    this.name = "ApiError";
  }
}
```

## 🎯 核心 API 集成示例

### 1. 用户管理 API

```typescript
interface User {
  id: number;
  username: string;
  email: string;
  role: "admin" | "agent" | "sale";
  status: "active" | "inactive" | "locked";
  created_at: string;
}

// 获取用户列表
const getUserList = async (params: {
  skip?: number;
  limit?: number;
  role?: string;
  status?: string;
  search?: string;
}) => {
  const response = await apiClient.get("/admin/users", { params });
  return response.data as ListResponse<User>;
};

// 创建用户
const createUser = async (userData: {
  username: string;
  email: string;
  password: string;
  role: string;
}) => {
  const response = await apiClient.post("/admin/users", userData);
  return response.data as DataResponse<User>;
};
```

### 2. 会员管理 API

```typescript
interface Member {
  id: number;
  name: string;
  phone: string;
  email?: string;
  member_type: "vip" | "regular" | "trial";
  member_status: "active" | "inactive" | "frozen";
  balance: number;
  total_classes: number;
}

// 获取会员列表
const getMemberList = async (params: {
  skip?: number;
  limit?: number;
  member_type?: string;
  member_status?: string;
  search_keyword?: string;
}) => {
  const response = await apiClient.get("/admin/members", { params });
  return response.data as ListResponse<Member>;
};
```

### 3. 课程管理 API

```typescript
interface ScheduledClass {
  id: number;
  teacher_id: number;
  teacher_name: string;
  member_id?: number;
  member_name?: string;
  class_date: string;
  start_time: string;
  duration_minutes: number;
  status: "available" | "booked" | "completed" | "cancelled";
  price?: number;
}

// 获取可预约课程
const getAvailableClasses = async (params: {
  teacher_id?: number;
  date_from?: string;
  date_to?: string;
}) => {
  const response = await apiClient.get("/admin/courses/classes/available", {
    params,
  });
  return response.data as ListResponse<ScheduledClass>;
};

// 预约课程
const bookClass = async (classId: number, memberId: number) => {
  const response = await apiClient.post(
    `/admin/courses/classes/book/${classId}`,
    {
      member_id: memberId,
    }
  );
  return response.data as DataResponse<ScheduledClass>;
};
```

## ⚠️ 错误处理

### 常见错误码

```typescript
// 系统级错误
const SYSTEM_ERRORS = {
  UNAUTHORIZED: "UNAUTHORIZED", // 未认证
  FORBIDDEN: "FORBIDDEN", // 无权限
  NOT_FOUND: "NOT_FOUND", // 资源不存在
  VALIDATION_ERROR: "VALIDATION_ERROR", // 数据验证失败
  INTERNAL_ERROR: "INTERNAL_ERROR", // 服务器内部错误
};

// 业务级错误
const BUSINESS_ERRORS = {
  USER_NOT_FOUND: "USER_NOT_FOUND",
  EMAIL_EXISTS: "EMAIL_EXISTS",
  MEMBER_NOT_FOUND: "MEMBER_NOT_FOUND",
  INSUFFICIENT_BALANCE: "INSUFFICIENT_BALANCE",
  CLASS_NOT_AVAILABLE: "CLASS_NOT_AVAILABLE",
};
```

### 错误处理示例

```typescript
const handleApiError = (error: ApiError) => {
  switch (error.code) {
    case "UNAUTHORIZED":
      // 跳转登录页
      router.push("/login");
      break;

    case "FORBIDDEN":
      // 显示权限不足提示
      message.error("权限不足");
      break;

    case "EMAIL_EXISTS":
      // 显示邮箱已存在提示
      message.error("邮箱已存在，请使用其他邮箱");
      break;

    case "INSUFFICIENT_BALANCE":
      // 显示余额不足提示
      message.error("会员卡余额不足，请先充值");
      break;

    default:
      // 显示通用错误提示
      message.error(error.message || "操作失败");
  }
};
```

## 🔄 分页和筛选

### 分页参数

```typescript
interface PaginationParams {
  page?: number; // 页码，从1开始
  size?: number; // 每页数量，默认20，最大100
  skip?: number; // 跳过记录数(部分API使用)
  limit?: number; // 限制记录数(部分API使用)
}

// 分页组件示例
const PaginatedList = ({ apiCall, filters }) => {
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(20);

  const loadData = async () => {
    try {
      const response = await apiCall({
        page,
        size,
        ...filters,
      });
      setData(response.data);
      setTotal(response.total);
    } catch (error) {
      handleApiError(error);
    }
  };

  useEffect(() => {
    loadData();
  }, [page, size, filters]);

  return (
    <div>
      {/* 数据列表 */}
      <List data={data} />

      {/* 分页组件 */}
      <Pagination
        current={page}
        pageSize={size}
        total={total}
        onChange={setPage}
        onShowSizeChange={(_, newSize) => setSize(newSize)}
      />
    </div>
  );
};
```

## 🚀 性能优化建议

### 1. 请求优化

```typescript
// 使用防抖减少搜索请求
const useDebounceSearch = (searchFn: Function, delay = 300) => {
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm) {
        searchFn(searchTerm);
      }
    }, delay);

    return () => clearTimeout(timer);
  }, [searchTerm, delay]);

  return [searchTerm, setSearchTerm];
};

// 请求缓存
const apiCache = new Map();

const cachedApiCall = async (url: string, params: any) => {
  const cacheKey = `${url}?${JSON.stringify(params)}`;

  if (apiCache.has(cacheKey)) {
    return apiCache.get(cacheKey);
  }

  const response = await apiClient.get(url, { params });
  apiCache.set(cacheKey, response.data);

  // 5分钟后清除缓存
  setTimeout(() => apiCache.delete(cacheKey), 5 * 60 * 1000);

  return response.data;
};
```

### 2. 数据预加载

```typescript
// 预加载常用数据
const preloadCommonData = async () => {
  const promises = [
    getUserList({ limit: 100 }),
    getTeacherList({ limit: 100 }),
    getTagList({ status: "active" }),
  ];

  const [users, teachers, tags] = await Promise.all(promises);

  // 存储到全局状态或缓存
  store.dispatch(setUsers(users.data));
  store.dispatch(setTeachers(teachers.data));
  store.dispatch(setTags(tags.data));
};
```

## 📱 移动端适配

### 响应式设计考虑

```typescript
// 移动端API调用优化
const isMobile = window.innerWidth < 768;

const getMobileOptimizedData = async (params: any) => {
  // 移动端减少数据量
  const mobileParams = {
    ...params,
    size: isMobile ? 10 : 20, // 移动端每页显示更少数据
    fields: isMobile ? "id,name,status" : undefined, // 只获取必要字段
  };

  return await apiCall(mobileParams);
};
```

## 🔧 开发工具推荐

### 1. API 调试工具

- **Postman**: 创建 API 集合，导入 OpenAPI 文档
- **Insomnia**: 轻量级 API 客户端
- **VS Code REST Client**: 在编辑器中直接测试 API

### 2. TypeScript 类型生成

```bash
# 从OpenAPI文档生成TypeScript类型
npx openapi-typescript http://localhost:8012/api/v1/openapi.json --output types/api.ts
npx openapi-typescript http://localhost:3000/api/v1/openapi.json --output types/api.ts
```

### 3. Mock 数据

```typescript
// 开发环境使用Mock数据
if (process.env.NODE_ENV === "development") {
  // 拦截API请求，返回Mock数据
  apiClient.interceptors.response.use((response) => {
    if (response.config.url?.includes("/admin/users")) {
      return {
        ...response,
        data: mockUserListResponse,
      };
    }
    return response;
  });
}
```

---

_本文档与 FastAPI 自动生成文档配合使用，提供完整的 API 集成指南。建议开发过程中同时参考两份文档。_
