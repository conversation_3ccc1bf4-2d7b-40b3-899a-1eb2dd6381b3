# 操作日志记录功能使用指南

## 概述

操作日志记录功能已完全集成到系统的核心业务逻辑中，自动记录所有关键操作，为系统提供完整的审计追踪能力。

## 功能特性

### ✅ 已集成的操作类型

**教师固定时间段操作 (TeacherFixedSlot)**
- ✅ 创建时间段 (`CREATE`)
- ✅ 删除时间段 (`DELETE`) 
- ✅ 设置对会员可见 (`SET_VISIBLE`)
- ✅ 设置对会员不可见 (`SET_INVISIBLE`)

**会员固定位锁定操作 (MemberFixedSlotLock)**
- ✅ 创建锁定 (`CREATE`)
- ✅ 管理员删除锁定 (`DELETE_BY_ADMIN`)
- ✅ 会员删除锁定 (`DELETE_BY_MEMBER`)

### ✅ 自动记录的信息

**基础信息**
- 操作类型和状态
- 操作时间
- 操作描述

**操作人信息**
- 操作人ID和姓名
- 操作人类型 (admin/teacher/member)
- 操作原因 (可选)

**业务对象信息 (冗余存储)**
- 教师/会员ID和姓名
- 时间段信息 (星期、时间、时长)
- 会员手机号 (锁定操作)

## 使用方式

### 1. Service层自动记录

所有操作日志记录已集成到Service层，**无需手动调用**：

```python
# 教师时间段操作 - 自动记录日志
teacher_service = TeacherFixedSlotService(session, tenant_id)

# 创建时间段 - 自动记录CREATE日志
slot = teacher_service.create_slot(slot_data, created_by=user_id)

# 删除时间段 - 自动记录DELETE日志  
teacher_service.delete_slot(slot_id, operator_id=user_id, reason="不再需要")

# 更新可见性 - 自动记录SET_VISIBLE/SET_INVISIBLE日志
teacher_service.update_slot(slot_id, update_data, operator_id=user_id)

# 会员锁定操作 - 自动记录日志
member_service = MemberFixedSlotLockService(session, tenant_id)

# 创建锁定 - 自动记录CREATE日志
lock = member_service.create_lock(lock_data, created_by=user_id)

# 删除锁定 - 自动记录DELETE_BY_ADMIN或DELETE_BY_MEMBER日志
member_service.delete_lock(lock_id, operator_id=user_id, is_member_operation=False)
```

### 2. API层传递操作者信息

API接口已更新，自动传递操作者信息：

```python
# 教师时间段API
@router.post("/")
def create_fixed_slot(slot_data: TeacherFixedSlotCreate, user_context: UserContext = Depends(get_user_context)):
    service = TeacherFixedSlotService(session, user_context.tenant_context.tenant_id)
    # created_by参数自动传递用户ID，触发日志记录
    slot = service.create_slot(slot_data, user_context.user.id)

@router.post("/{slot_id}/delete")  
def delete_fixed_slot(slot_id: int, user_context: UserContext = Depends(get_user_context)):
    service = TeacherFixedSlotService(session, user_context.tenant_context.tenant_id)
    # operator_id参数自动传递，触发日志记录
    service.delete_slot(slot_id, operator_id=user_context.user.id)
```

### 3. 查询操作日志

使用专门的日志服务查询操作记录：

```python
# 查询教师时间段操作日志
log_service = TeacherFixedSlotOperationLogService(session, tenant_id)

# 获取特定教师的操作记录
teacher_logs = log_service.get_teacher_operation_logs(teacher_id, limit=50)

# 获取特定时间段的操作记录  
slot_logs = log_service.get_teacher_slot_operation_logs(slot_id, limit=50)

# 分页查询操作日志
from app.features.courses.operations.schemas import TeacherFixedSlotOperationLogQuery
query = TeacherFixedSlotOperationLogQuery(
    teacher_id=teacher_id,
    operation_type=TeacherSlotOperationType.CREATE,
    created_from=datetime(2024, 1, 1),
    created_to=datetime(2024, 12, 31),
    page=1,
    size=20
)
logs, total = log_service.query_operation_logs(query)

# 查询会员锁定操作日志
member_log_service = MemberFixedLockOperationLogService(session, tenant_id)

# 获取特定会员的锁定操作记录
member_logs = member_log_service.get_member_lock_operation_logs(member_id, limit=50)

# 获取特定教师的锁定操作记录
teacher_lock_logs = member_log_service.get_teacher_lock_operation_logs(teacher_id, limit=50)
```

## API接口

### 查询操作日志API

**教师时间段操作日志**
```http
# 分页查询教师时间段操作日志
GET /api/v1/admin/operation-logs/teacher-slot-operations
Query Parameters:
- teacher_id: 教师ID (可选)
- operation_type: 操作类型 (可选)
- created_from: 开始时间 (可选)
- created_to: 结束时间 (可选)
- page: 页码 (默认1)
- size: 每页数量 (默认20)

# 获取特定教师的时间段操作记录
GET /api/v1/admin/operation-logs/teacher-slot-operations/by-teacher/{teacher_id}
Query Parameters:
- limit: 限制数量 (默认50)

# 获取特定时间段的操作记录
GET /api/v1/admin/operation-logs/teacher-slot-operations/by-slot/{slot_id}
Query Parameters:
- limit: 限制数量 (默认50)
```

**会员锁定操作日志**
```http
# 分页查询会员锁定操作日志
GET /api/v1/admin/operation-logs/member-lock-operations
Query Parameters:
- member_id: 会员ID (可选)
- teacher_id: 教师ID (可选)
- operation_type: 操作类型 (可选)
- created_from: 开始时间 (可选)
- created_to: 结束时间 (可选)
- page: 页码 (默认1)
- size: 每页数量 (默认20)

# 获取特定会员的锁定操作记录
GET /api/v1/admin/operation-logs/member-lock-operations/by-member/{member_id}
Query Parameters:
- limit: 限制数量 (默认50)

# 获取特定教师的锁定操作记录
GET /api/v1/admin/operation-logs/member-lock-operations/by-teacher/{teacher_id}
Query Parameters:
- limit: 限制数量 (默认50)
```

## 数据结构

### 教师时间段操作日志

```json
{
  "id": 1,
  "tenant_id": 1,
  "teacher_fixed_slot_id": 123,
  "operation_type": "create",
  "operation_status": "success", 
  "operation_description": "创建教师固定时间段：周一 09:00",
  "operator_id": 1,
  "operator_name": "管理员",
  "operator_type": "admin",
  "teacher_id": 456,
  "teacher_name": "李老师",
  "weekday": 1,
  "start_time": "09:00",
  "duration_minutes": 25,
  "reason": "新增课程时间段",
  "created_at": "2024-07-28T10:30:00"
}
```

### 会员锁定操作日志

```json
{
  "id": 1,
  "tenant_id": 1,
  "member_fixed_slot_lock_id": 789,
  "operation_type": "create",
  "operation_status": "success",
  "operation_description": "会员锁定固定时间段：周一 09:00", 
  "operator_id": 1,
  "operator_name": "管理员",
  "operator_type": "admin",
  "member_id": 123,
  "member_name": "张三",
  "member_phone": "13800138000",
  "teacher_id": 456,
  "teacher_name": "李老师",
  "weekday": 1,
  "start_time": "09:00",
  "reason": "会员申请锁定",
  "created_at": "2024-07-28T10:30:00"
}
```

## 测试验证

### 运行单元测试

```bash
# 测试操作日志记录器
pytest tests/unit/features/courses/operations/test_operation_logger.py -v

# 测试操作日志服务
pytest tests/unit/features/courses/test_operation_service.py -v
```

### 运行集成测试

```bash
# 测试完整的日志记录流程
pytest tests/integration/features/courses/operations/test_operation_logging_integration.py -v

# 测试API接口的日志记录
pytest tests/integration/api/v1/admin/test_teacher_fixed_slots.py -v
pytest tests/integration/api/v1/admin/test_members_fixed_locks.py -v
```

## 监控和维护

### 1. 日志数据量监控

```sql
-- 查看各类型操作日志的数量
SELECT operation_type, COUNT(*) as count 
FROM teacher_fixed_slot_operation_logs 
WHERE tenant_id = 1 
GROUP BY operation_type;

SELECT operation_type, COUNT(*) as count 
FROM member_fixed_lock_operation_logs 
WHERE tenant_id = 1 
GROUP BY operation_type;
```

### 2. 性能监控

```sql
-- 查看最近的操作日志
SELECT * FROM teacher_fixed_slot_operation_logs 
WHERE tenant_id = 1 
ORDER BY created_at DESC 
LIMIT 10;

-- 查看特定时间段的操作频率
SELECT DATE(created_at) as date, COUNT(*) as operations
FROM teacher_fixed_slot_operation_logs 
WHERE tenant_id = 1 AND created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY date;
```

### 3. 数据清理

建议定期清理旧的操作日志数据：

```sql
-- 删除6个月前的操作日志
DELETE FROM teacher_fixed_slot_operation_logs 
WHERE created_at < NOW() - INTERVAL '6 months';

DELETE FROM member_fixed_lock_operation_logs 
WHERE created_at < NOW() - INTERVAL '6 months';
```

## 故障排除

### 常见问题

1. **日志记录失败**
   - 检查操作者ID是否有效
   - 确认数据库连接正常
   - 查看应用日志中的错误信息

2. **日志查询慢**
   - 确认索引是否正常创建
   - 考虑添加时间范围限制
   - 使用分页查询避免大量数据

3. **日志数据不一致**
   - 检查业务对象是否存在
   - 验证冗余字段的同步逻辑
   - 运行数据一致性检查

## 总结

操作日志记录功能已完全集成到系统中，提供：

- ✅ **自动记录**：所有关键操作自动记录，无需手动干预
- ✅ **完整追踪**：记录操作人、时间、原因等完整信息
- ✅ **高性能**：优化的索引设计，支持高效查询
- ✅ **易维护**：统一的日志格式和查询接口
- ✅ **可扩展**：模块化设计，易于添加新的操作类型

系统现在具备了完整的审计能力，可以追踪所有教师时间段和会员锁定的操作历史。
